#!/usr/bin/env python3
"""
🧪 Test Real-time TP/SL Tracking with Realistic Signals
"""

import os
import time
import json
from datetime import datetime
from typing import Dict, Any

# Mock classes for testing
class MockNotifier:
    def __init__(self):
        self.messages_sent = []
    
    def send_message(self, message: str, parse_mode: str = None, chat_id: str = None) -> bool:
        print(f"📤 Telegram Message Sent:")
        print(f"Message: {message[:150]}...")
        self.messages_sent.append({
            'message': message,
            'parse_mode': parse_mode,
            'chat_id': chat_id,
            'timestamp': time.time()
        })
        return True
    
    def send_tp_sl_update_notification(self, coin: str, signal: Dict[str, Any], 
                                     current_price: float, message: str, 
                                     use_html: bool = True) -> bool:
        print(f"🔄 TP/SL Update Notification for {coin}")
        return self.send_message(message, "HTML" if use_html else None)

class MockDataFetcher:
    def __init__(self):
        self.current_price = 44000.0
    
    def get_current_price(self, coin: str) -> float:
        return self.current_price
    
    def set_price(self, price: float):
        self.current_price = price

class MockDataLogger:
    def __init__(self):
        self.logged_signals = []
    
    def log_signal(self, signal_data: Dict[str, Any]) -> None:
        self.logged_signals.append(signal_data)

def test_realistic_tp_sl_tracking():
    """🧪 Test with realistic signal parameters."""
    print("🧪 Testing Real-time TP/SL Tracking with Realistic Signals...")
    print("=" * 70)
    
    # Import the actual TradeTracker
    import trade_tracker
    
    # Initialize mock components
    notifier = MockNotifier()
    fetcher = MockDataFetcher()
    logger = MockDataLogger()
    
    # Initialize TradeTracker
    print("📈 Initializing TradeTracker...")
    tracker = trade_tracker.TradeTracker(
        notifier=notifier,
        data_fetcher=fetcher,
        data_logger=logger,
        max_active_signals=10,
        backup_interval=0,
        backup_dir=None
    )
    
    # Create realistic signal with proper risk management (1.5% risk)
    print("\n🧪 Creating realistic BUY signal...")
    realistic_signal = {
        'coin': 'BTCUSDT',
        'signal_type': 'BUY',
        'entry': 44000.0,
        'take_profit': 45320.0,  # 3% profit target
        'stop_loss': 43340.0,    # 1.5% risk (within 2% limit)
        'confidence': 0.85,
        'contributing_models': ['LSTM', 'XGBoost', 'RandomForest'],
        'volume_spike_detected': True,
        'signal_id': 'REALISTIC_BTC_001',
        'technical_strength': 0.8,
        'market_trend': 'bullish'
    }
    
    # Calculate and display risk/reward
    entry = realistic_signal['entry']
    tp = realistic_signal['take_profit']
    sl = realistic_signal['stop_loss']
    
    risk_pct = ((entry - sl) / entry) * 100
    reward_pct = ((tp - entry) / entry) * 100
    rr_ratio = reward_pct / risk_pct
    
    print(f"📊 Signal Analysis:")
    print(f"  Entry: ${entry:,.2f}")
    print(f"  Take Profit: ${tp:,.2f} (+{reward_pct:.2f}%)")
    print(f"  Stop Loss: ${sl:,.2f} (-{risk_pct:.2f}%)")
    print(f"  Risk/Reward Ratio: {rr_ratio:.2f}")
    
    # Add the signal
    success = tracker.add_signal(realistic_signal)
    print(f"\nSignal added: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if not success:
        print("❌ Signal was rejected. Exiting test.")
        return False
    
    # Test price movements and TP/SL updates
    print("\n🧪 Testing price movements and TP/SL updates...")
    
    price_scenarios = [
        (44220, "Small profit (+0.5%)"),
        (44660, "Moderate profit (+1.5%)"),
        (44880, "Good profit (+2.0%) - Should trigger auto-trailing"),
        (45100, "Strong profit (+2.5%)"),
        (45320, "Target reached (+3.0%)")
    ]
    
    for price, description in price_scenarios:
        print(f"\n📊 {description}")
        print(f"  Setting price to: ${price:,.2f}")
        
        # Update mock price
        fetcher.set_price(price)
        
        # Check signals (this should trigger TP/SL logic)
        closed_signals = tracker.check_tracked_signals()
        
        if closed_signals:
            print(f"  🎯 Signal closed! Reason: Target reached")
            break
        else:
            print(f"  📈 Signal still active")
            
            # Check if signal was updated
            active_signals = tracker.get_active_signals()
            if active_signals:
                signal = active_signals[0]
                trailing_enabled = signal.get('trailing_stop_enabled', False)
                tp_updates = signal.get('tp_updates', 0)
                sl_updates = signal.get('sl_updates', 0)
                trailing_updates = signal.get('trailing_stop_updates', 0)
                
                print(f"    - Trailing Stop: {'✅ ENABLED' if trailing_enabled else '❌ DISABLED'}")
                print(f"    - TP Updates: {tp_updates}")
                print(f"    - SL Updates: {sl_updates}")
                print(f"    - Trailing Updates: {trailing_updates}")
        
        time.sleep(1)
    
    # Get final summary
    print("\n📊 Final TP/SL Tracking Summary:")
    summary = tracker.get_tp_sl_tracking_summary()
    
    print(f"  Active Signals: {summary.get('total_active_signals', 0)}")
    print(f"  Signals with Updates: {summary.get('signals_with_updates', 0)}")
    print(f"  Signals with Trailing: {summary.get('signals_with_trailing', 0)}")
    
    stats = summary.get('update_statistics', {})
    print(f"  Total TP Updates: {stats.get('total_tp_updates', 0)}")
    print(f"  Total SL Updates: {stats.get('total_sl_updates', 0)}")
    print(f"  Total Trailing Updates: {stats.get('total_trailing_updates', 0)}")
    
    # Send final report
    print("\n📤 Sending final TP/SL tracking report...")
    tracker.send_tp_sl_tracking_report()
    
    print(f"\n📱 Total Telegram messages sent: {len(notifier.messages_sent)}")
    
    print("\n" + "=" * 70)
    print("✅ Realistic TP/SL Tracking Test Complete!")
    
    return True

if __name__ == "__main__":
    test_realistic_tp_sl_tracking()
