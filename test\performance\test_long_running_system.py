#!/usr/bin/env python3
"""
🔍 Long Running System Test - Monitor hệ thống trong thời gian dài để phát hiện lỗi
"""

import os
import sys
import time
import threading
import requests
from datetime import datetime, timedelta
from collections import defaultdict
import json

class LongRunningSystemMonitor:
    """🔍 Monitor hệ thống chạy lâu dài để phát hiện lỗi."""
    
    def __init__(self):
        self.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        # Statistics
        self.start_time = datetime.now()
        self.error_count = 0
        self.success_count = 0
        self.error_log = []
        self.analysis_stats = defaultdict(int)
        self.error_types = defaultdict(int)
        
        # Monitoring settings
        self.test_duration_hours = 2  # Test for 2 hours
        self.check_interval_seconds = 30  # Check every 30 seconds
        self.report_interval_minutes = 10  # Report every 10 minutes
        
        print(f"🔍 LONG RUNNING SYSTEM MONITOR INITIALIZED")
        print(f"⏰ Start time: {self.start_time.strftime('%H:%M:%S %d/%m/%Y')}")
        print(f"🕐 Test duration: {self.test_duration_hours} hours")
        print(f"🔄 Check interval: {self.check_interval_seconds} seconds")
        print(f"📊 Report interval: {self.report_interval_minutes} minutes")
    
    def send_telegram_message(self, message: str) -> bool:
        """📱 Send message to Telegram."""
        try:
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            response = requests.post(f"{self.base_url}/sendMessage", data=data, timeout=10)
            return response.status_code == 200 and response.json().get('ok', False)
        except Exception as e:
            print(f"❌ Error sending Telegram message: {e}")
            return False
    
    def check_system_health(self) -> dict:
        """🔍 Check system health by monitoring logs and processes."""
        health_status = {
            'timestamp': datetime.now(),
            'errors_found': [],
            'warnings_found': [],
            'analysis_activity': {},
            'system_status': 'HEALTHY'
        }
        
        try:
            # Check for common error patterns in recent output
            error_patterns = [
                'UnboundLocalError',
                'NoneType.*has no attribute',
                'Unknown analysis type',
                'Error creating.*caption',
                'Error generating.*chart',
                'Error sending.*report',
                'Traceback',
                'Exception',
                'Failed to',
                'Connection error',
                'Timeout'
            ]
            
            # Simulate checking system logs (in real implementation, you'd read actual logs)
            # For now, we'll check if the main bot process is responsive
            
            # Check if charts directory exists and has recent files
            charts_dir = "charts"
            if os.path.exists(charts_dir):
                chart_files = [f for f in os.listdir(charts_dir) if f.endswith('.png')]
                recent_charts = []
                now = time.time()
                
                for chart_file in chart_files:
                    chart_path = os.path.join(charts_dir, chart_file)
                    if os.path.exists(chart_path):
                        file_time = os.path.getmtime(chart_path)
                        if now - file_time < 300:  # Files created in last 5 minutes
                            recent_charts.append(chart_file)
                
                health_status['analysis_activity']['recent_charts'] = len(recent_charts)
                health_status['analysis_activity']['total_charts'] = len(chart_files)
            
            # Check system responsiveness
            if health_status['analysis_activity'].get('recent_charts', 0) > 0:
                health_status['system_status'] = 'ACTIVE'
            else:
                health_status['system_status'] = 'IDLE'
            
            return health_status
            
        except Exception as e:
            health_status['errors_found'].append(f"Health check error: {str(e)}")
            health_status['system_status'] = 'ERROR'
            return health_status
    
    def log_error(self, error_info: dict):
        """📝 Log error information."""
        self.error_count += 1
        self.error_log.append(error_info)
        self.error_types[error_info.get('type', 'UNKNOWN')] += 1
        
        # Keep only last 50 errors to prevent memory issues
        if len(self.error_log) > 50:
            self.error_log = self.error_log[-50:]
    
    def generate_status_report(self) -> str:
        """📊 Generate comprehensive status report."""
        current_time = datetime.now()
        runtime = current_time - self.start_time
        
        report = f"""🔍 <b>LONG RUNNING SYSTEM STATUS REPORT</b>

⏰ <b>RUNTIME INFORMATION</b>
├ 🕐 Start Time: <code>{self.start_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 Current Time: <code>{current_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ ⏱️ Runtime: <code>{str(runtime).split('.')[0]}</code>
└ 🎯 Target Duration: <code>{self.test_duration_hours} hours</code>

📊 <b>SYSTEM STATISTICS</b>
├ ✅ Success Count: <code>{self.success_count}</code>
├ ❌ Error Count: <code>{self.error_count}</code>
├ 📈 Success Rate: <code>{(self.success_count / max(1, self.success_count + self.error_count) * 100):.1f}%</code>
└ 🔍 Checks Performed: <code>{self.success_count + self.error_count}</code>"""

        # Add error breakdown if there are errors
        if self.error_count > 0:
            report += f"\n\n❌ <b>ERROR BREAKDOWN</b>"
            for error_type, count in self.error_types.items():
                report += f"\n├ {error_type}: <code>{count}</code>"
        
        # Add recent errors
        if self.error_log:
            report += f"\n\n🔍 <b>RECENT ERRORS (Last 3)</b>"
            for error in self.error_log[-3:]:
                timestamp = error.get('timestamp', 'Unknown')
                error_msg = error.get('message', 'Unknown error')[:100]
                report += f"\n├ <code>{timestamp}</code>: {error_msg}"
        
        # Add system health
        health = self.check_system_health()
        report += f"\n\n🏥 <b>CURRENT SYSTEM HEALTH</b>"
        report += f"\n├ 🎯 Status: <b>{health['system_status']}</b>"
        report += f"\n├ 📊 Recent Charts: <code>{health['analysis_activity'].get('recent_charts', 0)}</code>"
        report += f"\n└ 📁 Total Charts: <code>{health['analysis_activity'].get('total_charts', 0)}</code>"
        
        if health['errors_found']:
            report += f"\n\n⚠️ <b>CURRENT ISSUES</b>"
            for error in health['errors_found'][:3]:
                report += f"\n├ {error}"
        
        report += f"\n\n🔍 <i>Continuous monitoring active...</i>"
        
        return report
    
    def monitor_system(self):
        """🔍 Main monitoring loop."""
        print(f"🔍 Starting system monitoring...")
        
        # Send initial notification
        initial_message = f"""🔍 <b>LONG RUNNING SYSTEM TEST STARTED</b>

⏰ <b>Test Configuration:</b>
├ 🕐 Duration: <code>{self.test_duration_hours} hours</code>
├ 🔄 Check Interval: <code>{self.check_interval_seconds} seconds</code>
├ 📊 Report Interval: <code>{self.report_interval_minutes} minutes</code>
└ 🎯 Purpose: <b>Detect system errors and stability issues</b>

🔍 <b>Monitoring will check for:</b>
├ ❌ Runtime errors and exceptions
├ 📊 Chart generation issues
├ 📱 Telegram sending problems
├ 💾 Memory and resource issues
└ 🔄 System responsiveness

<b>🚀 MONITORING STARTED - SYSTEM UNDER OBSERVATION</b>"""
        
        self.send_telegram_message(initial_message)
        
        end_time = self.start_time + timedelta(hours=self.test_duration_hours)
        last_report_time = self.start_time
        
        try:
            while datetime.now() < end_time:
                current_time = datetime.now()
                
                # Perform health check
                try:
                    health_status = self.check_system_health()
                    
                    if health_status['errors_found']:
                        # Log errors
                        for error in health_status['errors_found']:
                            error_info = {
                                'timestamp': current_time.strftime('%H:%M:%S'),
                                'type': 'SYSTEM_ERROR',
                                'message': error
                            }
                            self.log_error(error_info)
                    else:
                        self.success_count += 1
                    
                    # Send periodic reports
                    if current_time - last_report_time >= timedelta(minutes=self.report_interval_minutes):
                        report = self.generate_status_report()
                        self.send_telegram_message(report)
                        last_report_time = current_time
                        print(f"📊 Status report sent at {current_time.strftime('%H:%M:%S')}")
                
                except Exception as e:
                    error_info = {
                        'timestamp': current_time.strftime('%H:%M:%S'),
                        'type': 'MONITOR_ERROR',
                        'message': f"Monitoring error: {str(e)}"
                    }
                    self.log_error(error_info)
                    print(f"❌ Monitoring error: {e}")
                
                # Wait for next check
                time.sleep(self.check_interval_seconds)
                
                # Print progress
                runtime = current_time - self.start_time
                remaining = end_time - current_time
                print(f"🔍 Runtime: {str(runtime).split('.')[0]} | Remaining: {str(remaining).split('.')[0]} | Errors: {self.error_count} | Success: {self.success_count}")
        
        except KeyboardInterrupt:
            print(f"\n🛑 Monitoring stopped by user")
        
        # Send final report
        final_report = self.generate_final_report()
        self.send_telegram_message(final_report)
        print(f"📊 Final report sent")
    
    def generate_final_report(self) -> str:
        """📊 Generate final comprehensive report."""
        end_time = datetime.now()
        total_runtime = end_time - self.start_time
        
        report = f"""🎯 <b>LONG RUNNING SYSTEM TEST - FINAL REPORT</b>

⏰ <b>TEST SUMMARY</b>
├ 🕐 Start: <code>{self.start_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 End: <code>{end_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ ⏱️ Total Runtime: <code>{str(total_runtime).split('.')[0]}</code>
└ 🎯 Target: <code>{self.test_duration_hours} hours</code>

📊 <b>FINAL STATISTICS</b>
├ ✅ Total Successful Checks: <code>{self.success_count}</code>
├ ❌ Total Errors Found: <code>{self.error_count}</code>
├ 📈 Overall Success Rate: <code>{(self.success_count / max(1, self.success_count + self.error_count) * 100):.1f}%</code>
└ 🔍 Total Checks: <code>{self.success_count + self.error_count}</code>"""

        # System stability assessment
        if self.error_count == 0:
            stability = "🟢 EXCELLENT"
        elif self.error_count < 5:
            stability = "🟡 GOOD"
        elif self.error_count < 15:
            stability = "🟠 FAIR"
        else:
            stability = "🔴 POOR"
        
        report += f"\n\n🏥 <b>SYSTEM STABILITY ASSESSMENT</b>"
        report += f"\n├ 🎯 Stability Rating: <b>{stability}</b>"
        report += f"\n├ 📊 Error Frequency: <code>{(self.error_count / max(1, total_runtime.total_seconds() / 3600)):.2f} errors/hour</code>"
        
        if self.error_count > 0:
            report += f"\n\n❌ <b>ERROR ANALYSIS</b>"
            for error_type, count in self.error_types.items():
                percentage = (count / self.error_count) * 100
                report += f"\n├ {error_type}: <code>{count}</code> ({percentage:.1f}%)"
        
        # Recommendations
        report += f"\n\n💡 <b>RECOMMENDATIONS</b>"
        if self.error_count == 0:
            report += f"\n├ ✅ System is stable and ready for production"
            report += f"\n└ 🚀 No issues detected during monitoring period"
        elif self.error_count < 5:
            report += f"\n├ ✅ System is generally stable"
            report += f"\n└ 🔍 Monitor the few errors found for patterns"
        else:
            report += f"\n├ ⚠️ System has stability issues"
            report += f"\n├ 🔧 Review and fix the most common error types"
            report += f"\n└ 🔍 Consider extending monitoring period"
        
        report += f"\n\n🎯 <b>LONG RUNNING TEST COMPLETED</b>"
        
        return report

def main():
    """🚀 Main function to run long running system test."""
    print(f"🔍 LONG RUNNING SYSTEM TEST")
    print(f"=" * 60)
    
    # Check environment variables
    if not os.getenv("TELEGRAM_BOT_TOKEN") or not os.getenv("TELEGRAM_CHAT_ID"):
        print(f"❌ Missing environment variables")
        print(f"Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID")
        return
    
    # Create and start monitor
    monitor = LongRunningSystemMonitor()
    
    try:
        monitor.monitor_system()
    except Exception as e:
        print(f"❌ Fatal error in monitoring: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"🎯 Long running system test completed")

def run_bot_integration_test():
    """🤖 Run integration test with actual bot."""
    print(f"\n🤖 STARTING BOT INTEGRATION TEST...")

    try:
        # Import and run main bot for a limited time
        import subprocess
        import signal

        print(f"🚀 Starting main bot process...")

        # Start main bot as subprocess
        bot_process = subprocess.Popen([
            sys.executable, "main_bot.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # Monitor for 30 minutes
        monitor_duration = 30 * 60  # 30 minutes
        start_time = time.time()

        print(f"🔍 Monitoring bot for {monitor_duration // 60} minutes...")

        while time.time() - start_time < monitor_duration:
            # Check if process is still running
            if bot_process.poll() is not None:
                print(f"❌ Bot process terminated unexpectedly!")
                stdout, stderr = bot_process.communicate()
                print(f"STDOUT: {stdout[-500:]}")  # Last 500 chars
                print(f"STDERR: {stderr[-500:]}")  # Last 500 chars
                break

            print(f"✅ Bot running... ({int((time.time() - start_time) // 60)} min)")
            time.sleep(60)  # Check every minute

        # Terminate bot gracefully
        print(f"🛑 Stopping bot process...")
        bot_process.terminate()

        try:
            bot_process.wait(timeout=10)
        except subprocess.TimeoutExpired:
            print(f"⚠️ Force killing bot process...")
            bot_process.kill()

        print(f"✅ Bot integration test completed")

    except Exception as e:
        print(f"❌ Error in bot integration test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f"🔍 LONG RUNNING SYSTEM TEST OPTIONS:")
    print(f"1. Monitor system health only (2 hours)")
    print(f"2. Run bot integration test (30 minutes)")
    print(f"3. Run both tests")

    choice = input(f"\nSelect option (1/2/3): ").strip()

    if choice == "1":
        main()
    elif choice == "2":
        run_bot_integration_test()
    elif choice == "3":
        print(f"🚀 Running both tests...")
        run_bot_integration_test()
        print(f"\n" + "="*60)
        main()
    else:
        print(f"❌ Invalid choice. Running default system health monitor...")
        main()
