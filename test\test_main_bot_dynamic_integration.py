#!/usr/bin/env python3
"""
🧪 TEST: Main Bot Dynamic TP/SL/Entry Integration
Test để kiểm tra tích hợp hệ thống dynamic TP/SL/Entry vào main bot
"""

import sys
import os
import pandas as pd
import numpy as np

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_comprehensive_tp_sl_input():
    """Create comprehensive TP/SL input data for testing"""
    # Create mock OHLCV data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    
    base_price = 50000
    returns = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    ohlcv_data = pd.DataFrame({
        'open': prices[:-1],
        'high': [p * 1.02 for p in prices[:-1]],
        'low': [p * 0.98 for p in prices[:-1]],
        'close': prices[:-1],
        'volume': [np.random.uniform(1000, 10000) for _ in range(len(prices)-1)]
    }, index=dates)
    
    # Comprehensive analysis data
    tp_sl_input = {
        "signal_type": "BUY",
        "current_price": prices[-2],
        "ohlcv_data": ohlcv_data,
        
        # AI Analysis
        "ai_prediction": {
            "status": "success",
            "confidence": 0.85,
            "predictions": {
                "predicted_price": 51000.0,
                "price_targets": {
                    "upside_target": 53000.0,
                    "downside_risk": 48000.0
                }
            },
            "has_tp_sl": True,
            "trading_levels": {
                "entry_price": 50500.0,
                "take_profit": 52500.0,
                "stop_loss": 49000.0,
                "confidence_metrics": {"overall_confidence": 0.85}
            }
        },
        
        # Volume Profile Analysis
        "volume_profile_data": {
            "status": "success",
            "vpoc": {"price": 50200.0},
            "value_area": {"high": 51000.0, "low": 49500.0},
            "support_resistance": {
                "support_levels": [
                    {"price": 49000.0, "strength": 0.8},
                    {"price": 49500.0, "strength": 0.6}
                ],
                "resistance_levels": [
                    {"price": 51500.0, "strength": 0.7},
                    {"price": 52000.0, "strength": 0.9}
                ]
            },
            "distribution_metrics": {"concentration_ratio": 0.75}
        },
        
        # Fibonacci Analysis
        "fibonacci_levels": {
            "status": "success",
            "trend_direction": "UPTREND",
            "pivot_high": 52000.0,
            "pivot_low": 48000.0,
            "retracement_levels": [
                {"level": 0.236, "price": 51056.0},
                {"level": 0.382, "price": 50472.0},
                {"level": 0.618, "price": 49528.0}
            ],
            "extension_levels": [
                {"level": 1.272, "price": 53088.0},
                {"level": 1.618, "price": 54472.0}
            ]
        },
        
        # Consensus Analysis
        "consensus_data": {
            "status": "success",
            "consensus_signal": "BUY",
            "confidence": 0.78,
            "algorithm_results": {
                "ai_analyzer": {"signal": "BUY", "confidence": 0.85},
                "volume_profile": {"signal": "BUY", "confidence": 0.75},
                "point_figure": {"signal": "BUY", "confidence": 0.70}
            }
        },
        
        # Orderbook Analysis
        "orderbook_analysis": {
            "status": "success",
            "significant_levels": [
                {"price": 49400.0, "type": "support", "strength": 0.8},
                {"price": 51600.0, "type": "resistance", "strength": 0.9}
            ],
            "imbalance_zones": [
                {"price": 49600.0, "bias": "bullish", "strength": 0.6}
            ]
        },
        
        # Additional algorithm data
        "fourier_analysis": {
            "status": "success",
            "cycle_analysis": {"dominant_cycle_length": 24, "average_amplitude": 0.025},
            "harmonic_levels": {
                "support_harmonics": [{"price": 49300.0, "strength": 0.7}],
                "resistance_harmonics": [{"price": 51700.0, "strength": 0.8}]
            },
            "signal_strength": 0.72
        },
        
        "point_figure_analysis": {
            "status": "success",
            "support_resistance": {
                "support_levels": [{"price": 49200.0, "strength": 0.8}],
                "resistance_levels": [{"price": 51200.0, "strength": 0.7}]
            }
        },
        
        "volume_pattern_analysis": {
            "status": "success",
            "volume_signals": [{"price": 49700.0, "signal": "BUY", "strength": 0.7}],
            "accumulation_zones": [{"price": 49600.0, "strength": 0.8}],
            "pattern_strength": 0.65
        },
        
        "dump_analysis": {
            "status": "success",
            "dump_probability": 0.15,
            "recovery_potential": 0.75
        }
    }
    
    return tp_sl_input

def test_main_bot_dynamic_integration():
    """Test main bot's dynamic TP/SL integration"""
    print("🧪 === TESTING MAIN BOT DYNAMIC TP/SL INTEGRATION ===")
    
    try:
        # Import main bot components
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        # Initialize analyzer like main bot does
        analyzer = IntelligentTPSLAnalyzer(
            atr_period=14,
            volatility_multiplier=3.5,
            min_rr_ratio=1.2,
            max_rr_ratio=12.0,
            fibonacci_levels=[0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.414, 1.618, 2.0, 2.618, 3.618],
            volume_profile_weight=0.3,
            pf_weight=0.2,
            crypto_mode=True
        )
        
        # Create comprehensive test data
        tp_sl_input = create_comprehensive_tp_sl_input()
        
        print(f"📊 Test Data Created:")
        print(f"  Signal Type: {tp_sl_input['signal_type']}")
        print(f"  Current Price: ${tp_sl_input['current_price']:,.2f}")
        print(f"  Analysis Sources: {len([k for k in tp_sl_input.keys() if k.endswith('_analysis') or k.endswith('_data') or k.endswith('_prediction')])} algorithms")
        
        # Test dynamic calculation (like main bot would call)
        print(f"\n🚀 Testing Dynamic TP/SL Calculation...")
        
        # Prepare analysis data like main bot does
        analysis_data = {
            "ai_prediction": tp_sl_input.get("ai_prediction", {}),
            "volume_profile_data": tp_sl_input.get("volume_profile_data", {}),
            "fibonacci_levels": tp_sl_input.get("fibonacci_levels", {}),
            "consensus_data": tp_sl_input.get("consensus_data", {}),
            "orderbook_analysis": tp_sl_input.get("orderbook_analysis", {}),
            "fourier_analysis": tp_sl_input.get("fourier_analysis", {}),
            "point_figure_analysis": tp_sl_input.get("point_figure_analysis", {}),
            "volume_pattern_analysis": tp_sl_input.get("volume_pattern_analysis", {}),
            "dump_analysis": tp_sl_input.get("dump_analysis", {})
        }
        
        # Call dynamic method
        result = analyzer.calculate_dynamic_entry_tp_sl(
            signal_type=tp_sl_input["signal_type"],
            ohlcv_data=tp_sl_input["ohlcv_data"],
            analysis_data=analysis_data
        )
        
        if result["status"] == "success":
            entry_price = result["entry_price"]
            take_profit = result["take_profit"]
            stop_loss = result["stop_loss"]
            rr_ratio = result["risk_reward_ratio"]
            confidence = result["confidence"]
            algorithms_used = result.get("algorithms_used", [])
            
            print(f"✅ Dynamic Calculation SUCCESS:")
            print(f"  Entry Price: ${entry_price:,.8f}")
            print(f"  Take Profit: ${take_profit:,.8f}")
            print(f"  Stop Loss: ${stop_loss:,.8f}")
            print(f"  R:R Ratio: {rr_ratio:.2f}")
            print(f"  Confidence: {confidence:.2f}")
            print(f"  Algorithms Used: {len(algorithms_used)}")
            
            # Validate results
            current_price = tp_sl_input["current_price"]
            
            # Check entry price is reasonable
            entry_diff_pct = abs(entry_price - current_price) / current_price * 100
            if entry_diff_pct <= 3.0:  # Within 3% for crypto
                print(f"  ✅ Entry price reasonable: {entry_diff_pct:.2f}% from current")
            else:
                print(f"  ⚠️ Entry price deviation: {entry_diff_pct:.2f}% from current")
            
            # Check TP/SL order for BUY signal
            if tp_sl_input["signal_type"] == "BUY":
                if stop_loss < entry_price < take_profit:
                    print(f"  ✅ BUY signal order correct: SL < Entry < TP")
                else:
                    print(f"  ❌ BUY signal order incorrect")
            
            # Check SL < TP distance constraint
            sl_distance = abs(entry_price - stop_loss)
            tp_distance = abs(take_profit - entry_price)
            
            if sl_distance < tp_distance:
                print(f"  ✅ SL distance < TP distance constraint satisfied")
            else:
                print(f"  ❌ SL distance >= TP distance constraint violated")
            
            # Check minimum R:R ratio
            if rr_ratio >= analyzer.min_rr_ratio:
                print(f"  ✅ R:R ratio >= minimum ({analyzer.min_rr_ratio})")
            else:
                print(f"  ❌ R:R ratio < minimum")
            
            # Check algorithm integration
            if len(algorithms_used) >= 5:
                print(f"  ✅ Good algorithm integration: {len(algorithms_used)} methods")
            else:
                print(f"  ⚠️ Limited algorithm integration: {len(algorithms_used)} methods")
            
            return True
        else:
            print(f"❌ Dynamic calculation failed: {result.get('message', 'Unknown error')}")
            return False
        
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_behavior():
    """Test fallback behavior with minimal data"""
    print("\n🧪 === TESTING FALLBACK BEHAVIOR ===")
    
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        analyzer = IntelligentTPSLAnalyzer(crypto_mode=True)
        
        # Create minimal OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=20, freq='1H')
        ohlcv_data = pd.DataFrame({
            'open': [50000] * 20,
            'high': [50100] * 20,
            'low': [49900] * 20,
            'close': [50000] * 20,
            'volume': [1000] * 20
        }, index=dates)
        
        # Minimal analysis data
        minimal_analysis = {
            "ai_prediction": {"status": "success", "confidence": 0.5}
        }
        
        result = analyzer.calculate_dynamic_entry_tp_sl(
            signal_type="BUY",
            ohlcv_data=ohlcv_data,
            analysis_data=minimal_analysis
        )
        
        if result["status"] == "success":
            print(f"✅ Fallback behavior working:")
            print(f"  Entry: ${result['entry_price']:,.8f}")
            print(f"  TP: ${result['take_profit']:,.8f}")
            print(f"  SL: ${result['stop_loss']:,.8f}")
            print(f"  R:R: {result['risk_reward_ratio']:.2f}")
            return True
        else:
            print(f"❌ Fallback failed")
            return False
        
    except Exception as e:
        print(f"❌ Fallback test error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🧪 === MAIN BOT DYNAMIC TP/SL/ENTRY INTEGRATION TEST ===")
    
    test1 = test_main_bot_dynamic_integration()
    test2 = test_fallback_behavior()
    
    if test1 and test2:
        print("\n🎉 SUCCESS: Main Bot Dynamic TP/SL/Entry Integration Complete!")
        print("✅ Dynamic Entry/TP/SL calculation working")
        print("✅ All algorithm data properly integrated")
        print("✅ Constraint validation working")
        print("✅ Fallback behavior functional")
        print("✅ Ready for production deployment")
    else:
        print("\n❌ FAILED: Some integration tests failed")
    
    return test1 and test2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
