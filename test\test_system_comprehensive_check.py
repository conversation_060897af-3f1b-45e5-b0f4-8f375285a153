#!/usr/bin/env python3
"""
🧪 COMPREHENSIVE SYSTEM CHECK
============================

Kiểm tra toàn diện hệ thống để đảm bảo:
1. ✅ Tất cả signals đã có dynamic TP/SL
2. ✅ Signal limit enforcement hoạt động
3. ✅ Integration layer hoàn chỉnh
4. ✅ Hệ thống ổn định và đồng nhất
"""

import sys
import os
import pandas as pd
import numpy as np

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_ohlcv_data():
    """Create test OHLCV data"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    
    base_price = 50000
    returns = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    return pd.DataFrame({
        'open': prices[:-1],
        'high': [p * 1.02 for p in prices[:-1]],
        'low': [p * 0.98 for p in prices[:-1]],
        'close': prices[:-1],
        'volume': [np.random.uniform(1000, 10000) for _ in range(len(prices)-1)]
    }, index=dates)

def test_all_signal_types_dynamic_tp_sl():
    """Test that all signal types use dynamic TP/SL"""
    print("🧪 === TESTING ALL SIGNAL TYPES DYNAMIC TP/SL ===")
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Initialize integration
        integration = SignalManagerIntegration(
            telegram_notifier=None,
            trade_tracker=None
        )
        
        ohlcv_data = create_test_ohlcv_data()
        current_price = 50000
        
        # Test data for each signal type
        test_cases = [
            {
                "name": "AI Analysis",
                "method": "_extract_ai_signal_data",
                "data": {
                    "ensemble_signal": "BUY",
                    "ensemble_confidence": 0.85,
                    "trading_levels": {"has_trading_levels": False}
                }
            },
            {
                "name": "Fibonacci",
                "method": "_extract_fibonacci_signal_data",
                "data": {
                    "signals": {"primary_signal": "SELL", "confidence": 0.75},
                    "trading_levels": {"has_trading_levels": False},
                    "retracement_levels": [{"level": 0.618, "price": 48000}]
                }
            },
            {
                "name": "Volume Profile",
                "method": "_extract_volume_profile_signal_data",
                "data": {
                    "signals": {"primary_signal": "BUY", "confidence": 0.80},
                    "trading_levels": {"has_trading_levels": False},
                    "vpoc": {"price": 49500}
                }
            },
            {
                "name": "Point & Figure",
                "method": "_extract_point_figure_signal_data",
                "data": {
                    "signals": {"primary_signal": "SELL", "confidence": 0.70},
                    "trading_levels": {"has_trading_levels": False},
                    "price_targets": [{"price": 48500}]
                }
            },
            {
                "name": "Orderbook",
                "method": "_extract_orderbook_signal_data",
                "data": {
                    "signals": {"primary_signal": "BUY", "confidence": 0.65},
                    "trading_levels": {"has_trading_levels": False},
                    "support_resistance_levels": [{"price": 49800}]
                }
            },
            {
                "name": "Fourier",
                "method": "_extract_fourier_signal_data",
                "data": {
                    "signals": {"primary_signal": "SELL", "confidence": 0.72},
                    "trading_levels": {"has_trading_levels": False},
                    "dominant_cycle": 24
                }
            }
        ]
        
        results = {}
        
        for test_case in test_cases:
            print(f"\n📊 Testing {test_case['name']} signal extraction:")
            
            try:
                # Get the method
                method = getattr(integration, test_case['method'])
                
                # Call the method with test data
                signal_data = method(test_case['data'], "BTC/USDT", current_price, ohlcv_data)
                
                # Check for dynamic TP/SL fields
                has_dynamic_fields = all(field in signal_data for field in [
                    'tp_sl_confidence', 'risk_reward_ratio', 'algorithms_used'
                ])
                
                has_valid_values = (
                    signal_data.get('entry', 0) > 0 and
                    signal_data.get('take_profit', 0) > 0 and
                    signal_data.get('stop_loss', 0) > 0
                )
                
                results[test_case['name']] = {
                    'has_dynamic_fields': has_dynamic_fields,
                    'has_valid_values': has_valid_values,
                    'signal_type': signal_data.get('signal_type'),
                    'tp_sl_confidence': signal_data.get('tp_sl_confidence', 0),
                    'risk_reward_ratio': signal_data.get('risk_reward_ratio', 0),
                    'algorithms_count': len(signal_data.get('algorithms_used', []))
                }
                
                print(f"  ✅ Signal Type: {signal_data.get('signal_type')}")
                print(f"  ✅ Entry: ${signal_data.get('entry', 0):.2f}")
                print(f"  ✅ TP: ${signal_data.get('take_profit', 0):.2f}")
                print(f"  ✅ SL: ${signal_data.get('stop_loss', 0):.2f}")
                print(f"  ✅ R:R: {signal_data.get('risk_reward_ratio', 0):.2f}")
                print(f"  ✅ TP/SL Confidence: {signal_data.get('tp_sl_confidence', 0):.2f}")
                print(f"  ✅ Algorithms Used: {len(signal_data.get('algorithms_used', []))}")
                
                if has_dynamic_fields and has_valid_values:
                    print(f"  ✅ {test_case['name']}: DYNAMIC TP/SL WORKING")
                else:
                    print(f"  ❌ {test_case['name']}: DYNAMIC TP/SL MISSING")
                
            except Exception as e:
                print(f"  ❌ {test_case['name']}: ERROR - {e}")
                results[test_case['name']] = {'error': str(e)}
        
        # Summary
        print(f"\n📊 DYNAMIC TP/SL INTEGRATION SUMMARY:")
        working_count = 0
        total_count = len(test_cases)
        
        for name, result in results.items():
            if 'error' not in result:
                if result['has_dynamic_fields'] and result['has_valid_values']:
                    print(f"  ✅ {name}: Dynamic TP/SL integrated")
                    working_count += 1
                else:
                    print(f"  ❌ {name}: Dynamic TP/SL missing")
            else:
                print(f"  ❌ {name}: Error - {result['error']}")
        
        success_rate = (working_count / total_count) * 100
        print(f"\n🎯 SUCCESS RATE: {working_count}/{total_count} ({success_rate:.1f}%)")
        
        return working_count == total_count
        
    except Exception as e:
        print(f"❌ System test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_limit_enforcement():
    """Test signal limit enforcement across all types"""
    print("\n🧪 === TESTING SIGNAL LIMIT ENFORCEMENT ===")
    
    try:
        # Check main_bot.py for signal limit checks
        with open("main_bot.py", "r", encoding="utf-8") as f:
            main_bot_content = f.read()
        
        # Check signal_manager_integration.py for limit checks
        with open("signal_manager_integration.py", "r", encoding="utf-8") as f:
            integration_content = f.read()
        
        # Required signal limit checks
        required_checks = [
            ("Money Flow", "can_send_signal(\"money_flow\")" in main_bot_content),
            ("Whale Activity", "can_send_signal(\"whale_activity\")" in main_bot_content),
            ("Manipulation", "can_send_signal(\"manipulation_detection\")" in main_bot_content),
            ("Cross Asset", "can_send_signal(\"cross_asset\")" in main_bot_content),
            ("Consensus Fallback", "can_send_signal(\"consensus\")" in main_bot_content),
            ("AI Analysis", "_send_signal_via_ultra_tracker" in integration_content),
            ("Fibonacci", "_send_signal_via_ultra_tracker" in integration_content),
            ("Volume Profile", "can_send_new_signal(AnalyzerType.VOLUME_PROFILE)" in integration_content),
            ("Point Figure", "can_send_new_signal(AnalyzerType.POINT_FIGURE)" in integration_content),
            ("Orderbook", "can_send_new_signal(AnalyzerType.ORDERBOOK)" in integration_content),
            ("Fourier", "can_send_new_signal(AnalyzerType.FOURIER)" in integration_content)
        ]
        
        print(f"📊 Checking signal limit enforcement:")
        
        all_checks_passed = True
        for check_name, check_result in required_checks:
            if check_result:
                print(f"  ✅ {check_name}: Signal limit check found")
            else:
                print(f"  ❌ {check_name}: Signal limit check missing")
                all_checks_passed = False
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ Signal limit test error: {e}")
        return False

def test_system_integration():
    """Test overall system integration"""
    print("\n🧪 === TESTING SYSTEM INTEGRATION ===")
    
    try:
        # Test imports
        print(f"📊 Testing imports:")
        
        try:
            from signal_manager_integration import SignalManagerIntegration
            print(f"  ✅ SignalManagerIntegration import successful")
        except Exception as e:
            print(f"  ❌ SignalManagerIntegration import failed: {e}")
            return False
        
        try:
            from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
            print(f"  ✅ IntelligentTPSLAnalyzer import successful")
        except Exception as e:
            print(f"  ❌ IntelligentTPSLAnalyzer import failed: {e}")
            return False
        
        try:
            from main_bot_signal_integration import MainBotSignalIntegration
            print(f"  ✅ MainBotSignalIntegration import successful")
        except Exception as e:
            print(f"  ❌ MainBotSignalIntegration import failed: {e}")
            return False
        
        # Test initialization
        print(f"\n📊 Testing initialization:")
        
        try:
            integration = SignalManagerIntegration()
            print(f"  ✅ SignalManagerIntegration initialization successful")
            
            # Check if intelligent TP/SL analyzer is initialized
            if hasattr(integration, 'intelligent_tp_sl'):
                print(f"  ✅ Intelligent TP/SL analyzer initialized")
            else:
                print(f"  ❌ Intelligent TP/SL analyzer missing")
                return False
                
        except Exception as e:
            print(f"  ❌ Integration initialization failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ System integration test error: {e}")
        return False

def main():
    """Run comprehensive system check"""
    print("🧪 === COMPREHENSIVE SYSTEM CHECK ===")
    print("=" * 60)
    
    test1 = test_all_signal_types_dynamic_tp_sl()
    test2 = test_signal_limit_enforcement()
    test3 = test_system_integration()
    
    print("\n" + "=" * 60)
    print("🎯 COMPREHENSIVE SYSTEM STATUS")
    print("=" * 60)
    
    if test1 and test2 and test3:
        print("🎉 SUCCESS: SYSTEM FULLY INTEGRATED AND STABLE!")
        print("✅ All 6 signal types use dynamic TP/SL calculation")
        print("✅ All 11 signal sources respect 20-signal limits")
        print("✅ Signal integration layer complete")
        print("✅ Intelligent TP/SL analyzer integrated")
        print("✅ System ready for production deployment")
        print("✅ All components working harmoniously")
    else:
        print("❌ FAILED: System integration incomplete")
        print(f"  Dynamic TP/SL: {'✅' if test1 else '❌'}")
        print(f"  Signal Limits: {'✅' if test2 else '❌'}")
        print(f"  Integration: {'✅' if test3 else '❌'}")
    
    return test1 and test2 and test3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
