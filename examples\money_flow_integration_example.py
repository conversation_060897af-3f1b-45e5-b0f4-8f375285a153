#!/usr/bin/env python3
"""
🌊 Money Flow Integration Example
<PERSON><PERSON> dụ tích hợp hệ thống money flow detection nâng cấp
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_sample_market_data():
    """Tạo sample market data với sector rotation pattern"""
    print("📊 Creating sample market data with Layer1 sector rotation...")
    
    market_data = {}
    
    # Simulate Layer1 sector being hot
    layer1_coins = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT']
    defi_coins = ['LINKUSDT', 'UNIUSDT', 'AAVEUSDT']
    gaming_coins = ['AXSUSDT', 'SANDUSDT']
    
    # Create OHLCV data for each coin
    for coin in layer1_coins + defi_coins + gaming_coins:
        # Generate 50 hours of data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=50), periods=50, freq='1H')
        
        # Base price
        base_price = np.random.uniform(20, 100)
        
        # Sector-specific performance
        if coin in layer1_coins:
            # Layer1 is hot - strong uptrend with high volume
            trend = 0.08  # 8% gain over period
            volume_multiplier = 2.5
        elif coin in defi_coins:
            # DeFi is cooling - slight downtrend
            trend = -0.02  # 2% loss
            volume_multiplier = 0.8
        else:
            # Gaming is neutral
            trend = 0.01  # 1% gain
            volume_multiplier = 1.1
        
        prices = []
        volumes = []
        
        for i in range(50):
            # Apply trend in recent hours (last 25 hours)
            if i >= 25:
                price_change = trend * (i - 25) / 25
                price = base_price * (1 + price_change + np.random.normal(0, 0.005))
            else:
                price = base_price * (1 + np.random.normal(0, 0.01))
            
            # Volume surge in recent hours for hot sectors
            base_volume = np.random.uniform(1000, 10000)
            if i >= 40 and coin in layer1_coins:
                volume = base_volume * volume_multiplier
            else:
                volume = base_volume * (volume_multiplier if i >= 25 else 1.0)
            
            prices.append(max(0.01, price))  # Ensure positive price
            volumes.append(max(100, volume))  # Ensure positive volume
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        market_data[coin] = {
            'ohlcv_data': ohlcv_data,
            'current_price': prices[-1]
        }
    
    print(f"✅ Created market data for {len(market_data)} coins")
    return market_data

def demonstrate_money_flow_detection():
    """Demo hệ thống money flow detection"""
    print("\n🌊 === MONEY FLOW DETECTION DEMONSTRATION ===")
    
    try:
        # Import required modules
        from money_flow_analyzer import MoneyFlowAnalyzer
        from telegram_notifier import EnhancedTelegramNotifier
        
        print("✅ Modules imported successfully")
        
        # Initialize money flow analyzer
        analyzer = MoneyFlowAnalyzer(
            tracking_pairs=['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
                          'LINKUSDT', 'UNIUSDT', 'AAVEUSDT', 'AXSUSDT', 'SANDUSDT'],
            flow_threshold=1000000,
            correlation_window=24,
            sector_analysis=True
        )
        
        print(f"✅ MoneyFlowAnalyzer initialized with {len(analyzer.sectors)} sectors")
        
        # Create sample market data with Layer1 rotation
        market_data = create_sample_market_data()
        
        # Run money flow analysis
        print("\n🔍 Running money flow analysis...")
        analysis_result = analyzer.analyze_market_money_flow(market_data)
        
        # Display sector rotation results
        print("\n📊 SECTOR ROTATION ANALYSIS:")
        sector_rotation = analysis_result.get('sector_rotation', {})
        
        print(f"🔄 Rotation detected: {sector_rotation.get('rotation_detected', False)}")
        print(f"🎯 Hot sector: {sector_rotation.get('hot_sector', 'None')}")
        print(f"📈 Active sectors: {sector_rotation.get('sector_count', 0)}")
        
        # Display rotation signals
        rotation_signals = sector_rotation.get('rotation_signals', [])
        print(f"\n🚨 ROTATION SIGNALS ({len(rotation_signals)}):")
        
        for signal in rotation_signals:
            print(f"\n🎯 {signal['sector']}:")
            print(f"  📊 Signal: {signal['signal']}")
            print(f"  💪 Strength: {signal['strength']}")
            print(f"  📈 Sector Strength: {signal['sector_strength']:.3f}")
            print(f"  🌊 Money Flow: {signal['money_flow_score']:.3f}")
            print(f"  📝 Reason: {signal['reason']}")
        
        # Generate money flow signals
        print("\n🌊 GENERATING MONEY FLOW SIGNALS...")
        money_flow_signals = analyzer.get_money_flow_signals(analysis_result)
        
        print(f"\n📤 MONEY FLOW SIGNALS ({len(money_flow_signals)}):")
        
        for i, signal in enumerate(money_flow_signals, 1):
            print(f"\n📊 Signal {i}:")
            print(f"  Type: {signal['type']}")
            print(f"  Subtype: {signal.get('subtype', 'N/A')}")
            
            if 'hot_sector' in signal:
                print(f"  🏢 Hot Sector: {signal['hot_sector']}")
                print(f"  💪 Strength: {signal['strength']}")
                print(f"  📝 Reason: {signal['reason']}")
                
                # Display formatted message
                if 'formatted_message' in signal:
                    print(f"\n📝 FORMATTED MESSAGE:")
                    print("=" * 50)
                    print(signal['formatted_message'])
                    print("=" * 50)
        
        # Demonstrate Telegram integration (without actually sending)
        print("\n📱 TELEGRAM INTEGRATION DEMO:")
        
        # Initialize notifier (with dummy credentials for demo)
        notifier = EnhancedTelegramNotifier(
            bot_token="dummy_token_for_demo",
            chat_id="-1002301937119"
        )
        
        print("✅ TelegramNotifier initialized")
        
        # Check if send_money_flow_signal method exists
        if hasattr(notifier, 'send_money_flow_signal'):
            print("✅ send_money_flow_signal method available")
            
            # Demo how to send signals (without actually sending)
            print("\n📤 DEMO: How to send money flow signals:")
            for signal in money_flow_signals:
                print(f"  📊 Would send: {signal.get('subtype', 'Unknown')} signal")
                # notifier.send_money_flow_signal(signal)  # Uncomment to actually send
        else:
            print("❌ send_money_flow_signal method not found")
        
        # Summary
        print(f"\n🎉 DEMONSTRATION COMPLETE!")
        print(f"✅ Detected {len(rotation_signals)} sector rotation signals")
        print(f"✅ Generated {len(money_flow_signals)} money flow signals")
        print(f"✅ Ready for Telegram integration")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_integration_code():
    """Show integration code examples"""
    print("\n💻 === INTEGRATION CODE EXAMPLES ===")
    
    integration_code = '''
# 1. Basic Integration
from money_flow_analyzer import MoneyFlowAnalyzer
from telegram_notifier import EnhancedTelegramNotifier

# Initialize components
money_flow_analyzer = MoneyFlowAnalyzer(sector_analysis=True)
telegram_notifier = EnhancedTelegramNotifier(bot_token, chat_id)

# In your main trading loop:
def check_money_flow_signals(market_data):
    # Analyze money flow
    analysis_result = money_flow_analyzer.analyze_market_money_flow(market_data)
    
    # Generate signals
    money_flow_signals = money_flow_analyzer.get_money_flow_signals(analysis_result)
    
    # Send notifications
    for signal in money_flow_signals:
        if signal['type'] == 'MONEY_FLOW_SIGNAL':
            telegram_notifier.send_money_flow_signal(signal)

# 2. Advanced Configuration
analyzer = MoneyFlowAnalyzer(
    tracking_pairs=['BTCUSDT', 'ETHUSDT', ...],
    flow_threshold=1000000,  # $1M threshold
    correlation_window=24,   # 24 hour window
    sector_analysis=True     # Enable sector analysis
)

# 3. Custom Signal Filtering
def filter_high_confidence_signals(signals):
    return [s for s in signals if s.get('sector_strength', 0) >= 0.7]

high_confidence_signals = filter_high_confidence_signals(money_flow_signals)
'''
    
    print(integration_code)

def main():
    """Main demonstration function"""
    print("🌊 === ENHANCED MONEY FLOW SYSTEM DEMONSTRATION ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run demonstration
    success = demonstrate_money_flow_detection()
    
    if success:
        # Show integration examples
        show_integration_code()
        
        print(f"\n🎉 === DEMONSTRATION SUCCESSFUL ===")
        print("\n📋 ENHANCED FEATURES DEMONSTRATED:")
        print("✅ 12 comprehensive sector categories")
        print("✅ Multi-timeframe price analysis")
        print("✅ Enhanced volume flow analysis")
        print("✅ Money flow score calculation")
        print("✅ Sector strength calculation")
        print("✅ Rotation signal generation")
        print("✅ Formatted message output")
        print("✅ Telegram integration ready")
        
        print("\n🌊 SAMPLE OUTPUT:")
        print("🔄 SECTOR ROTATION DETECTED")
        print("🎯 Hot Sector: Layer1")
        print("📊 Signal: BUY_SECTOR")
        print("💪 Strength: MODERATE")
        print("📝 Reason: Money rotating into Layer1 sector")
        print("📊 Market Flow Score: 0.075 đang được chú ý")
        
        print(f"\n🚀 Ready for production use!")
    else:
        print(f"\n❌ Demonstration failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
