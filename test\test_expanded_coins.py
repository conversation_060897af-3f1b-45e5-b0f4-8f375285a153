#!/usr/bin/env python3
"""
🧪 TEST: Expanded Coins Coverage
Test để kiểm tra số lượng coins sau khi fix
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_money_flow_analyzer():
    """Test MoneyFlowAnalyzer với expanded coins"""
    print("🧪 === TESTING EXPANDED COINS COVERAGE ===")
    
    try:
        print("🌊 Testing MoneyFlowAnalyzer...")
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        print("🔧 Creating MoneyFlowAnalyzer instance...")
        analyzer = MoneyFlowAnalyzer()
        
        print("📊 Getting sector info...")
        sector_info = analyzer.get_sector_info()
        
        total_coins = sector_info['total_coins']
        total_sectors = sector_info['total_sectors']
        sectors = sector_info['sectors']
        
        print(f"✅ Total coins: {total_coins}")
        print(f"✅ Total sectors: {total_sectors}")
        
        print("\n📋 Sector breakdown:")
        for sector, coins in sectors.items():
            if sector != 'all_coins':
                print(f"  {sector}: {len(coins)} coins")
                if len(coins) <= 10:  # Show coins if not too many
                    coin_names = [coin.replace('USDT', '') for coin in coins[:10]]
                    print(f"    Examples: {', '.join(coin_names)}")
                else:
                    coin_names = [coin.replace('USDT', '') for coin in coins[:5]]
                    print(f"    Examples: {', '.join(coin_names)}... (+{len(coins)-5} more)")
        
        # Check if we have significantly more coins now
        if total_coins > 200:
            print(f"\n🎉 SUCCESS: Expanded coverage achieved!")
            print(f"✅ {total_coins} coins (much better than previous 50)")
        elif total_coins > 100:
            print(f"\n✅ GOOD: Improved coverage!")
            print(f"✅ {total_coins} coins (better than previous 50)")
        else:
            print(f"\n⚠️ LIMITED: Still limited coverage")
            print(f"⚠️ {total_coins} coins (not much improvement)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coin_categorizer():
    """Test CoinCategorizer với expanded coins"""
    print("\n🏷️ Testing CoinCategorizer with expanded coins...")
    
    try:
        import coin_categorizer
        
        categorizer = coin_categorizer.CoinCategorizer(auto_update=False)
        
        if hasattr(categorizer, 'use_dynamic_sectors') and categorizer.use_dynamic_sectors:
            total_coins = len(categorizer.dynamic_sectors.get('all_coins', []))
            mode = "Dynamic"
        elif hasattr(categorizer, 'known_coins'):
            total_coins = len(categorizer.known_coins)
            mode = "Static"
        else:
            total_coins = 0
            mode = "Unknown"
        
        print(f"✅ CoinCategorizer: {total_coins} coins ({mode} mode)")
        
        # Test some categorizations
        test_coins = ['BTC', 'ETH', 'DOGE', 'UNI', 'LINK', 'MATIC', 'SAND', 'FET']
        print("\n🔍 Testing categorizations:")
        for coin in test_coins:
            category = categorizer.get_coin_category(coin)
            print(f"  {coin}: {category}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run tests"""
    print("🧪 === EXPANDED COINS COVERAGE TEST ===")
    
    success1 = test_money_flow_analyzer()
    success2 = test_coin_categorizer()
    
    if success1 and success2:
        print("\n🎉 SUCCESS: Expanded coins coverage working!")
    else:
        print("\n❌ FAILED: Some tests failed")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
