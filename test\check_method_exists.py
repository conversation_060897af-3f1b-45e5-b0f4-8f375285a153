#!/usr/bin/env python3
"""
🧪 Check if send_money_flow_signal method exists
"""

import sys
import os
import importlib

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🧪 === CHECKING METHOD EXISTENCE ===")
    
    try:
        # Force reload module to clear cache
        if 'telegram_notifier' in sys.modules:
            importlib.reload(sys.modules['telegram_notifier'])
        
        from telegram_notifier import EnhancedTelegramNotifier
        print("✅ EnhancedTelegramNotifier imported")
        
        # Check if method exists
        if hasattr(EnhancedTelegramNotifier, 'send_money_flow_signal'):
            print("✅ send_money_flow_signal method EXISTS")
            
            # Get method info
            method = getattr(EnhancedTelegramNotifier, 'send_money_flow_signal')
            print(f"📋 Method type: {type(method)}")
            print(f"📋 Method doc: {method.__doc__[:100] if method.__doc__ else 'No doc'}...")
            
        else:
            print("❌ send_money_flow_signal method MISSING")
            
            # List all methods containing 'money' or 'flow'
            all_methods = dir(EnhancedTelegramNotifier)
            money_methods = [m for m in all_methods if 'money' in m.lower() or 'flow' in m.lower()]
            print(f"📋 Methods with 'money' or 'flow': {money_methods}")
            
            # List all public methods
            public_methods = [m for m in all_methods if not m.startswith('_')]
            print(f"📊 Total public methods: {len(public_methods)}")
            print(f"📋 Last 10 public methods: {public_methods[-10:]}")
        
        # Check helper method too
        if hasattr(EnhancedTelegramNotifier, '_send_enhanced_sector_rotation_signal'):
            print("✅ _send_enhanced_sector_rotation_signal method EXISTS")
        else:
            print("❌ _send_enhanced_sector_rotation_signal method MISSING")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
