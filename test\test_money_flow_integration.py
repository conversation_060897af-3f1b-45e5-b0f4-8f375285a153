#!/usr/bin/env python3
"""
🧪 TEST: Money Flow Integration Test
Test để kiểm tra toàn bộ hệ thống money flow nâng cấp với sector rotation detection
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_money_flow_analyzer_import():
    """Test import money flow analyzer"""
    print("\n🧪 === TESTING MONEY FLOW ANALYZER IMPORT ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        print("✅ MoneyFlowAnalyzer imported successfully")
        
        # Initialize analyzer
        analyzer = MoneyFlowAnalyzer()
        print("✅ MoneyFlowAnalyzer initialized successfully")
        
        # Check sectors
        print(f"📊 Sectors available: {len(analyzer.sectors)}")
        for sector, coins in list(analyzer.sectors.items())[:5]:
            print(f"  {sector}: {len(coins)} coins")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing MoneyFlowAnalyzer: {e}")
        return False

def test_telegram_notifier_money_flow():
    """Test telegram notifier money flow methods"""
    print("\n🧪 === TESTING TELEGRAM NOTIFIER MONEY FLOW METHODS ===")
    
    try:
        from telegram_notifier import EnhancedTelegramNotifier
        print("✅ EnhancedTelegramNotifier imported successfully")
        
        # Check if money flow methods exist
        methods_to_check = [
            'send_money_flow_signal',
            '_send_enhanced_sector_rotation_signal'
        ]
        
        for method_name in methods_to_check:
            if hasattr(EnhancedTelegramNotifier, method_name):
                print(f"✅ Method exists: {method_name}")
            else:
                print(f"❌ Method missing: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing telegram notifier: {e}")
        return False

def test_money_flow_signal_generation():
    """Test money flow signal generation"""
    print("\n🧪 === TESTING MONEY FLOW SIGNAL GENERATION ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        # Initialize analyzer
        analyzer = MoneyFlowAnalyzer()
        
        # Create test signal data
        test_signal = {
            'sector': 'Layer1',
            'signal': 'BUY_SECTOR',
            'strength': 'MODERATE',
            'sector_strength': 0.75,
            'money_flow_score': 0.65,
            'reason': 'Money rotating into Layer1 sector'
        }
        
        test_analysis_result = {'total_flow_score': 0.075}
        
        # Test signal formatting
        formatted_message = analyzer._format_sector_rotation_signal(test_signal, test_analysis_result)
        
        print("📝 FORMATTED SIGNAL:")
        print(formatted_message)
        
        # Check if formatted message contains expected elements
        expected_elements = [
            "🌊 **MONEY FLOW SIGNAL**",
            "🔄 **SECTOR ROTATION DETECTED**",
            "🎯 Hot Sector: Layer1",
            "📊 Signal: BUY_SECTOR",
            "💪 Strength: MODERATE"
        ]
        
        missing_elements = []
        for element in expected_elements:
            if element not in formatted_message:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"\n❌ Missing elements in formatted message:")
            for element in missing_elements:
                print(f"  - {element}")
            return False
        else:
            print(f"\n✅ All expected elements found in formatted message!")
            return True
        
    except Exception as e:
        print(f"❌ Error testing signal generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sector_categorization():
    """Test enhanced sector categorization"""
    print("\n🧪 === TESTING ENHANCED SECTOR CATEGORIZATION ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        analyzer = MoneyFlowAnalyzer()
        
        # Check enhanced sectors
        expected_sectors = [
            'Layer1', 'DeFi', 'Layer2', 'Gaming', 'AI', 'Meme', 
            'Infrastructure', 'Exchange', 'Privacy', 'Oracle', 'Storage', 'Metaverse'
        ]
        
        print(f"📊 ENHANCED SECTOR CATEGORIZATION:")
        for sector in expected_sectors:
            if sector in analyzer.sectors:
                coin_count = len(analyzer.sectors[sector])
                print(f"  ✅ {sector}: {coin_count} coins")
            else:
                print(f"  ❌ Missing sector: {sector}")
                return False
        
        # Check total coins
        total_coins = sum(len(coins) for coins in analyzer.sectors.values())
        print(f"\n📊 Total coins categorized: {total_coins}")
        
        if total_coins >= 50:  # Should have at least 50 coins
            print("✅ Sufficient coin coverage")
            return True
        else:
            print("❌ Insufficient coin coverage")
            return False
        
    except Exception as e:
        print(f"❌ Error testing sector categorization: {e}")
        return False

def test_money_flow_signal_format():
    """Test money flow signal format matches example"""
    print("\n🧪 === TESTING MONEY FLOW SIGNAL FORMAT ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        analyzer = MoneyFlowAnalyzer()
        
        # Create test data that should generate the example format
        test_signal = {
            'sector': 'Layer1',
            'signal': 'BUY_SECTOR',
            'strength': 'MODERATE',
            'sector_strength': 0.75,
            'money_flow_score': 0.65,
            'reason': 'Money rotating into Layer1 sector'
        }
        
        test_analysis_result = {'total_flow_score': 0.075}
        
        # Generate formatted message
        formatted_message = analyzer._format_sector_rotation_signal(test_signal, test_analysis_result)
        
        # Expected format from user's example:
        expected_format = """🌊 **MONEY FLOW SIGNAL**

🔄 **SECTOR ROTATION DETECTED**
🎯 Hot Sector: Layer1
📊 Signal: BUY_SECTOR
💪 Strength: MODERATE
📝 Reason: Money rotating into Layer1 sector

📊 **Market Flow Score: 0.075** đang được chú ý"""
        
        print("📝 GENERATED FORMAT:")
        print(formatted_message)
        
        print("\n📝 EXPECTED FORMAT:")
        print(expected_format)
        
        # Check key components
        format_checks = [
            ("🌊 **MONEY FLOW SIGNAL**" in formatted_message, "Header"),
            ("🔄 **SECTOR ROTATION DETECTED**" in formatted_message, "Rotation detected"),
            ("🎯 Hot Sector: Layer1" in formatted_message, "Hot sector"),
            ("📊 Signal: BUY_SECTOR" in formatted_message, "Signal type"),
            ("💪 Strength: MODERATE" in formatted_message, "Strength"),
            ("📊 **Market Flow Score: 0.075**" in formatted_message, "Flow score"),
            ("đang được chú ý" in formatted_message, "Vietnamese text")
        ]
        
        all_passed = True
        for check, description in format_checks:
            if check:
                print(f"✅ {description}: PASS")
            else:
                print(f"❌ {description}: FAIL")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing signal format: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 === MONEY FLOW INTEGRATION TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Money Flow Analyzer Import", test_money_flow_analyzer_import),
        ("Telegram Notifier Money Flow Methods", test_telegram_notifier_money_flow),
        ("Enhanced Sector Categorization", test_sector_categorization),
        ("Money Flow Signal Generation", test_money_flow_signal_generation),
        ("Money Flow Signal Format", test_money_flow_signal_format)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append({
            'name': test_name,
            'success': success,
            'duration': end_time - start_time
        })
        
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"\n{test_name}: {status} ({end_time - start_time:.2f}s)")
    
    # Summary
    print(f"\n{'='*60}")
    print("🧪 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['name']}: {result['duration']:.2f}s")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Money flow system is ready!")
        print("\n📋 ENHANCED MONEY FLOW FEATURES:")
        print("✅ 12 comprehensive sector categories")
        print("✅ Enhanced sector rotation detection")
        print("✅ Multi-timeframe price analysis")
        print("✅ Money flow score calculation")
        print("✅ Sector strength calculation")
        print("✅ Formatted signal generation")
        print("✅ Telegram integration")
        print("✅ Signal format matches user example")
        
        print("\n🌊 MONEY FLOW SIGNAL EXAMPLE:")
        print("🔄 SECTOR ROTATION DETECTED")
        print("🎯 Hot Sector: Layer1")
        print("📊 Signal: BUY_SECTOR")
        print("💪 Strength: MODERATE")
        print("📝 Reason: Money rotating into Layer1 sector")
        print("📊 Market Flow Score: 0.075 đang được chú ý")
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
