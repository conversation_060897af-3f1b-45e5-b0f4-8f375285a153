#!/usr/bin/env python3
"""
📈 ENHANCED ZIGZAG PERCENTAGE ANALYZER V2.0 - PRODUCTION READY
=============================================================

Advanced ZigZag Pattern Analysis System with Machine Learning Integration:
- 📈 Intelligent ZigZag pattern detection with adaptive algorithms
- 🎯 Advanced Fibonacci analysis with multi-timeframe confirmation
- 📊 Professional pattern recognition with ML-enhanced accuracy
- 🚀 Performance optimized for real-time trading applications
- 🛡️ Comprehensive error handling and fallback systems
- 🔄 Multi-confirmation signal generation with risk management

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

from typing import List, Optional, Tuple, Dict, Any, Union
from enum import Enum
import pandas as pd
import numpy as np
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from scipy import signal as scipy_signal
    from scipy.stats import linregress
    AVAILABLE_MODULES['scipy'] = True
    print("✅ scipy imported successfully - Advanced signal processing available")
except ImportError:
    AVAILABLE_MODULES['scipy'] = False
    print("⚠️ scipy not available - Using basic signal processing")

try:
    from sklearn.cluster import DBSCAN
    from sklearn.preprocessing import StandardScaler
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - ML pattern recognition available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic pattern recognition")

try:
    import talib
    AVAILABLE_MODULES['talib'] = True
    print("✅ TA-Lib imported successfully - Advanced technical analysis available")
except ImportError:
    AVAILABLE_MODULES['talib'] = False
    print("⚠️ TA-Lib not available - Using basic technical analysis")

print(f"📈 ZigZag Analyzer V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class PriceChangeMode(Enum):
    PERCENTAGE = 0
    PRICE = 1

class ChartPoint:
    def __init__(self, time: pd.Timestamp, price: float):
        self.time: pd.Timestamp = time
        self.price: float = price

class Settings:
    """
    📈 ENHANCED ZIGZAG SETTINGS V2.0 - PRODUCTION READY
    ===================================================

    Advanced configuration system for ZigZag analysis with comprehensive features:
    - 📈 Intelligent parameter optimization with market adaptation
    - 🎯 Multi-confirmation settings with ML integration
    - 📊 Performance tuning for different market conditions
    """

    def __init__(self, deviation: float = 5.0,
                 change_mode: PriceChangeMode = PriceChangeMode.PERCENTAGE,
                 use_fourier: bool = False, fourier_harmonics: int = 10,
                 enhanced_mode: bool = True, multi_confirmation: bool = True,
                 enable_ml_patterns: bool = True, enable_adaptive_deviation: bool = True,
                 min_pivot_distance: int = 3, max_lookback_periods: int = 500):
        """
        Initialize Enhanced ZigZag Settings V2.0.

        Args:
            deviation: Minimum price change percentage (5.0)
            change_mode: Price change calculation mode
            use_fourier: Enable Fourier analysis integration
            fourier_harmonics: Number of Fourier harmonics (10)
            enhanced_mode: Enable enhanced pattern detection
            multi_confirmation: Enable multi-confirmation signals
            enable_ml_patterns: Enable ML-based pattern recognition
            enable_adaptive_deviation: Enable adaptive deviation adjustment
            min_pivot_distance: Minimum distance between pivots (3)
            max_lookback_periods: Maximum lookback periods (500)
        """
        # Core settings with validation
        self.deviation: float = max(0.5, min(20.0, deviation))  # 0.5-20%
        self.change_mode: PriceChangeMode = change_mode
        self.use_fourier: bool = use_fourier and AVAILABLE_MODULES.get('scipy', False)
        self.fourier_harmonics: int = max(5, min(50, fourier_harmonics))  # 5-50 harmonics

        # Enhanced features
        self.enhanced_mode: bool = enhanced_mode
        self.multi_confirmation: bool = multi_confirmation
        self.enable_ml_patterns: bool = enable_ml_patterns and AVAILABLE_MODULES.get('sklearn', False)
        self.enable_adaptive_deviation: bool = enable_adaptive_deviation

        # Performance settings
        self.min_pivot_distance: int = max(1, min(10, min_pivot_distance))  # 1-10 bars
        self.max_lookback_periods: int = max(100, min(2000, max_lookback_periods))  # 100-2000 bars

        # Advanced settings
        self.noise_filter_enabled: bool = True
        self.volume_confirmation_enabled: bool = True
        self.fibonacci_levels_enabled: bool = True
        self.pattern_strength_threshold: float = 0.6  # 60% minimum strength

        print(f"📈 ZigZag Settings V2.0 initialized:")
        print(f"    📊 Deviation: {self.deviation}%")
        print(f"    🔄 Enhanced mode: {'Enabled' if self.enhanced_mode else 'Disabled'}")
        print(f"    🧠 ML patterns: {'Enabled' if self.enable_ml_patterns else 'Disabled'}")
        print(f"    📈 Fourier analysis: {'Enabled' if self.use_fourier else 'Disabled'}")
        print(f"    🎯 Multi-confirmation: {'Enabled' if self.multi_confirmation else 'Disabled'}")

class ZigZag:
    def __init__(self, settings: Settings):
        self.settings: Settings = settings
        self.pivots: List[ChartPoint] = []
        self.last_pivot_price: float = 0.0
        self.last_pivot_time: Optional[pd.Timestamp] = None
        self.current_trend: int = 0
        
        # Enhanced Fibonacci ratios including advanced extensions
        self.enhanced_fib_ratios = {
            # Classical retracements
            "0.236": 0.236, "0.382": 0.382, "0.5": 0.5, "0.618": 0.618, "0.786": 0.786,
            # Advanced retracements
            "0.146": 0.146, "0.854": 0.854, "0.886": 0.886,
            # Extensions
            "1.272": 1.272, "1.414": 1.414, "1.618": 1.618, "2.0": 2.0, "2.618": 2.618,
            # Advanced extensions
            "3.618": 3.618, "4.236": 4.236,
            # Time-based ratios
            "1.0": 1.0, "1.382": 1.382,
            # Harmonic pattern ratios
            "0.09": 0.09, "2.272": 2.272, "2.414": 2.414, "3.0": 3.0, "3.272": 3.272,
            "3.414": 3.414, "4.272": 4.272, "4.414": 4.414, "4.618": 4.618, "4.764": 4.764
        }
        
        # Initialize analysis results storage
        self.last_volume_analysis = {}
        self.last_momentum_analysis = {}

    def calculate(self, df_ohlc: pd.DataFrame) -> List[Dict[str, Any]]:
        """Main calculation method with improved volume confirmation handling."""
        try:
            print(f"    🔍 Running ZigZag calculation with {len(df_ohlc)} data points...")
            
            if df_ohlc is None or df_ohlc.empty:
                print(f"    ❌ No OHLCV data provided")
                return []
            
            if len(df_ohlc) < 10:
                print(f"    ❌ Insufficient data: {len(df_ohlc)} bars (minimum 10 required)")
                return []
            
            # Extract price arrays
            highs = df_ohlc['high'].values
            lows = df_ohlc['low'].values
            closes = df_ohlc['close'].values
            timestamps = df_ohlc.index
            
            # Find pivots using core algorithm
            raw_pivots = self._find_zigzag_pivots(highs, lows, closes, timestamps)
            
            if not raw_pivots:
                print(f"    ⚠️ Insufficient pivots found")
                return []
            
            print(f"    ✅ Found {len(raw_pivots)} raw pivots")
            
            # Enhanced pivot processing with volume confirmation (single pass)
            enhanced_pivots = self._enhance_pivots_with_confirmations(raw_pivots, df_ohlc)
            
            # Calculate comprehensive analysis
            analysis_results = self._calculate_comprehensive_analysis(enhanced_pivots, df_ohlc)
            
            # Single volume confirmation calculation here
            if len(enhanced_pivots) > 0:
                volume_confirmed_count = sum(1 for p in enhanced_pivots if p.get('volume_confirmed', False))
                total_pivots = len(enhanced_pivots)
                volume_confirmation_rate = (volume_confirmed_count / total_pivots) * 100
                
                print(f"      🔍 Enhanced Volume Analysis: {volume_confirmed_count}/{total_pivots} confirmed ({volume_confirmation_rate:.1f}%)")
                
                # Store volume analysis results
                self.last_volume_analysis = {
                    "volume_confirmed_count": volume_confirmed_count,
                    "total_pivots": total_pivots,
                    "confirmation_rate": volume_confirmation_rate
                }
            
            # Build comprehensive result structure
            result = [{
                "status": "success",
                "pivots": enhanced_pivots,
                "pivot_count": len(enhanced_pivots),
                "analysis": analysis_results,
                "zigzag_config": {
                    "deviation": self.settings.deviation,
                    "change_mode": self.settings.change_mode.name,
                    "enhanced_mode": self.settings.enhanced_mode
                },
                "data_quality": {
                    "bars_analyzed": len(df_ohlc),
                    "pivot_density": len(enhanced_pivots) / len(df_ohlc) if len(df_ohlc) > 0 else 0
                }
            }]
            
            print(f"    ✅ ZigZag calculation completed: {len(enhanced_pivots)} pivots")
            return result
            
        except Exception as e:
            print(f"    ❌ Error in ZigZag calculation: {e}")
            return [{
                "status": "error",
                "message": str(e),
                "pivots": [],
                "pivot_count": 0
            }]
    
    def _calculate_comprehensive_analysis(self, pivots: List[Dict[str, Any]], 
                                        df_ohlc: pd.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive analysis of ZigZag patterns."""
        try:
            analysis = {}
            
            # Basic statistics
            analysis["statistics"] = self._calculate_pivot_statistics(pivots)
            
            # Trend analysis
            analysis["trend_analysis"] = self._analyze_zigzag_trend(pivots)
            
            # Pattern detection
            analysis["patterns"] = self._analyze_zigzag_patterns(pivots)
            
            # Enhanced pattern detection
            analysis["enhanced_patterns"] = self._detect_enhanced_patterns(pivots, df_ohlc)
            
            # Support/Resistance levels
            analysis["support_resistance"] = self._calculate_support_resistance_levels(pivots)
            
            # Fibonacci analysis
            analysis["fibonacci_analysis"] = self._calculate_fibonacci_analysis(pivots)
            
            # Volume confirmation summary
            analysis["volume_confirmation"] = self.last_volume_analysis
            
            # Price targets
            analysis["price_targets"] = self._calculate_price_targets(pivots, df_ohlc)
            
            return analysis
            
        except Exception as e:
            print(f"    ❌ Error in comprehensive analysis: {e}")
            return {"error": str(e)}
    
    def _calculate_pivot_statistics(self, pivots: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate statistical metrics for pivots."""
        try:
            if not pivots:
                return {}
            
            # Extract prices and calculate moves
            prices = [float(p.get('price', 0)) for p in pivots]
            price_moves = []
            durations = []
            
            for i in range(1, len(pivots)):
                prev_price = float(pivots[i-1].get('price', 0))
                curr_price = float(pivots[i].get('price', 0))
                
                if prev_price > 0:
                    move_pct = ((curr_price - prev_price) / prev_price) * 100
                    price_moves.append(abs(move_pct))
                
                prev_idx = pivots[i-1].get('index', 0)
                curr_idx = pivots[i].get('index', 0)
                duration = curr_idx - prev_idx
                durations.append(duration)
            
            # Calculate statistics
            stats = {
                "total_pivots": len(pivots),
                "high_count": len([p for p in pivots if p.get('type') == 'high']),
                "low_count": len([p for p in pivots if p.get('type') == 'low']),
                "price_range": {
                    "min": min(prices) if prices else 0,
                    "max": max(prices) if prices else 0,
                    "range_pct": ((max(prices) - min(prices)) / min(prices) * 100) if prices and min(prices) > 0 else 0
                }
            }
            
            if price_moves:
                stats["price_moves"] = {
                    "average_move_pct": sum(price_moves) / len(price_moves),
                    "max_move_pct": max(price_moves),
                    "min_move_pct": min(price_moves),
                    "move_volatility": np.std(price_moves)
                }
            
            if durations:
                stats["durations"] = {
                    "average_duration": sum(durations) / len(durations),
                    "max_duration": max(durations),
                    "min_duration": min(durations),
                    "duration_consistency": 1.0 - (np.std(durations) / np.mean(durations)) if np.mean(durations) > 0 else 0
                }
            
            return stats
            
        except Exception as e:
            print(f"    Error calculating pivot statistics: {e}")
            return {"error": str(e)}
    
    def _detect_enhanced_patterns(self, pivots: List[Dict[str, Any]], 
                                df_ohlc: pd.DataFrame) -> Dict[str, Any]:
        """Detect enhanced chart patterns."""
        try:
            patterns = {
                "head_and_shoulders": self._detect_head_and_shoulders(pivots),
                "inverse_head_and_shoulders": self._detect_inverse_head_and_shoulders(pivots),
                "flag_patterns": self._detect_flag_patterns(pivots, df_ohlc),
                "pennant_patterns": self._detect_pennant_patterns(pivots),
                "wedge_patterns": self._detect_wedge_patterns(pivots),
                "channel_patterns": self._detect_channel_patterns(pivots),
                "elliott_wave_basic": self._detect_elliott_wave_basic(pivots)
            }
            
            # Count detected patterns
            detected_count = sum(1 for pattern_list in patterns.values() 
                               if isinstance(pattern_list, list) and len(pattern_list) > 0)
            
            patterns["summary"] = {
                "total_patterns_detected": detected_count,
                "pattern_strength": self._calculate_overall_pattern_strength(patterns)
            }
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting enhanced patterns: {e}")
            return {"error": str(e)}
    
    def _detect_head_and_shoulders(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect Head and Shoulders patterns."""
        try:
            patterns = []
            
            if len(pivots) < 7:  # Need at least 7 pivots for H&S
                return patterns
            
            # Look for pattern: Low-High-Low-High-Low-High-Low
            for i in range(len(pivots) - 6):
                sequence = pivots[i:i+7]
                types = [p.get('type') for p in sequence]
                
                if types == ['low', 'high', 'low', 'high', 'low', 'high', 'low']:
                    left_shoulder = float(sequence[1]['price'])
                    head = float(sequence[3]['price'])
                    right_shoulder = float(sequence[5]['price'])
                    
                    # Check H&S criteria
                    head_higher = head > left_shoulder and head > right_shoulder
                    shoulders_similar = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder) < 0.05
                    
                    if head_higher and shoulders_similar:
                        # Calculate neckline
                        left_low = float(sequence[2]['price'])
                        right_low = float(sequence[4]['price'])
                        neckline = (left_low + right_low) / 2
                        
                        # Calculate target
                        head_to_neckline = head - neckline
                        target = neckline - head_to_neckline
                        
                        pattern_strength = self._calculate_hs_strength(sequence)
                        
                        patterns.append({
                            "type": "head_and_shoulders",
                            "start_index": sequence[0]['index'],
                            "end_index": sequence[6]['index'],
                            "left_shoulder_price": left_shoulder,
                            "head_price": head,
                            "right_shoulder_price": right_shoulder,
                            "neckline_price": neckline,
                            "target_price": target,
                            "strength": pattern_strength,
                            "confirmation_pending": sequence[6]['price'] > neckline
                        })
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting H&S patterns: {e}")
            return []
    
    def _detect_inverse_head_and_shoulders(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect Inverse Head and Shoulders patterns."""
        try:
            patterns = []
            
            if len(pivots) < 7:
                return patterns
            
            # Look for pattern: High-Low-High-Low-High-Low-High
            for i in range(len(pivots) - 6):
                sequence = pivots[i:i+7]
                types = [p.get('type') for p in sequence]
                
                if types == ['high', 'low', 'high', 'low', 'high', 'low', 'high']:
                    left_shoulder = float(sequence[1]['price'])
                    head = float(sequence[3]['price'])
                    right_shoulder = float(sequence[5]['price'])
                    
                    # Check inverse H&S criteria
                    head_lower = head < left_shoulder and head < right_shoulder
                    shoulders_similar = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder) < 0.05
                    
                    if head_lower and shoulders_similar:
                        # Calculate neckline
                        left_high = float(sequence[2]['price'])
                        right_high = float(sequence[4]['price'])
                        neckline = (left_high + right_high) / 2
                        
                        # Calculate target
                        neckline_to_head = neckline - head
                        target = neckline + neckline_to_head
                        
                        pattern_strength = self._calculate_hs_strength(sequence)
                        
                        patterns.append({
                            "type": "inverse_head_and_shoulders",
                            "start_index": sequence[0]['index'],
                            "end_index": sequence[6]['index'],
                            "left_shoulder_price": left_shoulder,
                            "head_price": head,
                            "right_shoulder_price": right_shoulder,
                            "neckline_price": neckline,
                            "target_price": target,
                            "strength": pattern_strength,
                            "confirmation_pending": sequence[6]['price'] < neckline
                        })
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting inverse H&S patterns: {e}")
            return []
    
    def _detect_flag_patterns(self, pivots: List[Dict[str, Any]], 
                            df_ohlc: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect Flag patterns."""
        try:
            patterns = []
            
            if len(pivots) < 5:
                return patterns
            
            # Look for strong move followed by consolidation
            for i in range(len(pivots) - 4):
                sequence = pivots[i:i+5]
                
                # Check for initial strong move
                initial_move = abs(float(sequence[1]['price']) - float(sequence[0]['price']))
                initial_move_pct = initial_move / float(sequence[0]['price']) * 100
                
                if initial_move_pct > 5:  # Significant initial move
                    # Check for consolidation (flag pole)
                    consolidation_range = []
                    for j in range(1, len(sequence)):
                        consolidation_range.append(float(sequence[j]['price']))
                    
                    range_high = max(consolidation_range)
                    range_low = min(consolidation_range)
                    consolidation_pct = (range_high - range_low) / range_low * 100
                    
                    if consolidation_pct < initial_move_pct * 0.5:  # Consolidation < 50% of initial move
                        direction = "bullish" if float(sequence[1]['price']) > float(sequence[0]['price']) else "bearish"
                        
                        patterns.append({
                            "type": f"{direction}_flag",
                            "start_index": sequence[0]['index'],
                            "end_index": sequence[-1]['index'],
                            "flagpole_move_pct": initial_move_pct,
                            "consolidation_range_pct": consolidation_pct,
                            "direction": direction,
                            "strength": min(1.0, initial_move_pct / 10),
                            "target_move_pct": initial_move_pct
                        })
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting flag patterns: {e}")
            return []
    
    def _detect_pennant_patterns(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect Pennant patterns."""
        try:
            patterns = []
            
            if len(pivots) < 6:
                return patterns
            
            # Look for converging triangle after strong move
            for i in range(len(pivots) - 5):
                sequence = pivots[i:i+6]
                
                # Check for initial strong move
                initial_move_pct = abs(float(sequence[1]['price']) - float(sequence[0]['price'])) / float(sequence[0]['price']) * 100
                
                if initial_move_pct > 3:  # Minimum move for pennant
                    # Analyze convergence in remaining pivots
                    remaining_pivots = sequence[1:]
                    highs = [p for p in remaining_pivots if p.get('type') == 'high']
                    lows = [p for p in remaining_pivots if p.get('type') == 'low']
                    
                    if len(highs) >= 2 and len(lows) >= 2:
                        # Check for converging lines
                        high_prices = [float(h['price']) for h in highs]
                        low_prices = [float(l['price']) for l in lows]
                        
                        high_trend = np.polyfit(range(len(high_prices)), high_prices, 1)[0]
                        low_trend = np.polyfit(range(len(low_prices)), low_prices, 1)[0]
                        
                        # Pennant: converging lines (opposite slopes)
                        if high_trend * low_trend < 0 and abs(high_trend) > 0.1 and abs(low_trend) > 0.1:
                            direction = "bullish" if float(sequence[1]['price']) > float(sequence[0]['price']) else "bearish"
                            
                            patterns.append({
                                "type": f"{direction}_pennant",
                                "start_index": sequence[0]['index'],
                                "end_index": sequence[-1]['index'],
                                "initial_move_pct": initial_move_pct,
                                "high_trend_slope": high_trend,
                                "low_trend_slope": low_trend,
                                "convergence_rate": abs(high_trend - low_trend),
                                "direction": direction,
                                "strength": min(1.0, initial_move_pct / 8)
                            })
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting pennant patterns: {e}")
            return []
    
    def _detect_wedge_patterns(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect Rising/Falling Wedge patterns."""
        try:
            patterns = []
            
            if len(pivots) < 6:
                return patterns
            
            # Look for wedge patterns (converging trend lines with same direction)
            for i in range(len(pivots) - 5):
                sequence = pivots[i:i+6]
                
                highs = [p for p in sequence if p.get('type') == 'high']
                lows = [p for p in sequence if p.get('type') == 'low']
                
                if len(highs) >= 3 and len(lows) >= 3:
                    high_prices = [float(h['price']) for h in highs]
                    low_prices = [float(l['price']) for l in lows]
                    
                    high_trend = np.polyfit(range(len(high_prices)), high_prices, 1)[0]
                    low_trend = np.polyfit(range(len(low_prices)), low_prices, 1)[0]
                    
                    # Rising wedge: both lines rising, converging
                    if high_trend > 0 and low_trend > 0 and high_trend < low_trend:
                        patterns.append({
                            "type": "rising_wedge",
                            "start_index": sequence[0]['index'],
                            "end_index": sequence[-1]['index'],
                            "high_slope": high_trend,
                            "low_slope": low_trend,
                            "direction": "bearish",
                            "strength": min(1.0, abs(high_trend - low_trend)),
                            "pattern_type": "reversal"
                        })
                    
                    # Falling wedge: both lines falling, converging
                    elif high_trend < 0 and low_trend < 0 and high_trend > low_trend:
                        patterns.append({
                            "type": "falling_wedge",
                            "start_index": sequence[0]['index'],
                            "end_index": sequence[-1]['index'],
                            "high_slope": high_trend,
                            "low_slope": low_trend,
                            "direction": "bullish",
                            "strength": min(1.0, abs(high_trend - low_trend)),
                            "pattern_type": "reversal"
                        })
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting wedge patterns: {e}")
            return []
    
    def _detect_channel_patterns(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect Channel patterns."""
        try:
            patterns = []
            
            if len(pivots) < 6:
                return patterns
            
            # Look for parallel trend lines
            for i in range(len(pivots) - 5):
                sequence = pivots[i:i+6]
                
                highs = [p for p in sequence if p.get('type') == 'high']
                lows = [p for p in sequence if p.get('type') == 'low']
                
                if len(highs) >= 3 and len(lows) >= 3:
                    high_prices = [float(h['price']) for h in highs]
                    low_prices = [float(l['price']) for l in lows]
                    
                    high_trend = np.polyfit(range(len(high_prices)), high_prices, 1)[0]
                    low_trend = np.polyfit(range(len(low_prices)), low_prices, 1)[0]
                    
                    # Channel: parallel lines (similar slopes)
                    slope_diff = abs(high_trend - low_trend)
                    avg_slope = (abs(high_trend) + abs(low_trend)) / 2
                    
                    if slope_diff < avg_slope * 0.3:  # Lines are relatively parallel
                        if high_trend > 0.1 and low_trend > 0.1:
                            channel_type = "ascending_channel"
                            direction = "bullish"
                        elif high_trend < -0.1 and low_trend < -0.1:
                            channel_type = "descending_channel"
                            direction = "bearish"
                        else:
                            channel_type = "horizontal_channel"
                            direction = "neutral"
                        
                        patterns.append({
                            "type": channel_type,
                            "start_index": sequence[0]['index'],
                            "end_index": sequence[-1]['index'],
                            "high_slope": high_trend,
                            "low_slope": low_trend,
                            "direction": direction,
                            "strength": 1.0 - (slope_diff / avg_slope) if avg_slope > 0 else 0.5,
                            "parallel_quality": 1.0 - (slope_diff / avg_slope) if avg_slope > 0 else 0.5
                        })
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting channel patterns: {e}")
            return []
    
    def _detect_elliott_wave_basic(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect basic Elliott Wave patterns (5-wave structure)."""
        try:
            patterns = []
            
            if len(pivots) < 9:  # Need 9 pivots for 5-wave structure
                return patterns
            
            # Look for 5-wave impulse patterns
            for i in range(len(pivots) - 8):
                sequence = pivots[i:i+9]
                
                # Check if we have alternating types starting with the right type
                types = [p.get('type') for p in sequence]
                
                # Bullish 5-wave: low-high-low-high-low-high-low-high-low
                if types == ['low', 'high', 'low', 'high', 'low', 'high', 'low', 'high', 'low']:
                    wave_pattern = self._analyze_elliott_wave_structure(sequence, 'bullish')
                    if wave_pattern['is_valid']:
                        patterns.append(wave_pattern)
                
                # Bearish 5-wave: high-low-high-low-high-low-high-low-high
                elif types == ['high', 'low', 'high', 'low', 'high', 'low', 'high', 'low', 'high']:
                    wave_pattern = self._analyze_elliott_wave_structure(sequence, 'bearish')
                    if wave_pattern['is_valid']:
                        patterns.append(wave_pattern)
            
            return patterns
            
        except Exception as e:
            print(f"    Error detecting Elliott Wave patterns: {e}")
            return []
    
    def _analyze_elliott_wave_structure(self, sequence: List[Dict[str, Any]], direction: str) -> Dict[str, Any]:
        """Analyze if sequence follows Elliott Wave rules."""
        try:
            prices = [float(p['price']) for p in sequence]
            
            if direction == 'bullish':
                # Wave structure: 0-1-2-3-4-5-A-B-C
                wave_0 = prices[0]  # Start
                wave_1 = prices[1]  # First high
                wave_2 = prices[2]  # Correction low
                wave_3 = prices[3]  # Extension high
                wave_4 = prices[4]  # Final correction low
                wave_5 = prices[5]  # Final high
                
                # Elliott Wave rules for bullish pattern
                rules_passed = 0
                total_rules = 5
                
                # Rule 1: Wave 2 does not retrace more than 100% of Wave 1
                if wave_2 > wave_0:
                    rules_passed += 1
                
                # Rule 2: Wave 3 is not the shortest of waves 1, 3, and 5
                wave1_len = wave_1 - wave_0
                wave3_len = wave_3 - wave_2
                wave5_len = wave_5 - wave_4
                
                if wave3_len >= wave1_len or wave3_len >= wave5_len:
                    rules_passed += 1
                
                # Rule 3: Wave 4 does not overlap with Wave 1
                if wave_4 > wave_1:
                    rules_passed += 1
                
                # Rule 4: Wave 3 extends beyond Wave 1
                if wave_3 > wave_1:
                    rules_passed += 1
                
                # Rule 5: Wave 5 should reach at least Wave 1 level
                if wave_5 >= wave_1:
                    rules_passed += 1
                
            else:  # bearish
                # Similar logic but inverted for bearish pattern
                wave_0 = prices[0]  # Start
                wave_1 = prices[1]  # First low
                wave_2 = prices[2]  # Correction high
                wave_3 = prices[3]  # Extension low
                wave_4 = prices[4]  # Final correction high
                wave_5 = prices[5]  # Final low
                
                rules_passed = 0
                total_rules = 5
                
                # Inverted rules for bearish pattern
                if wave_2 < wave_0:
                    rules_passed += 1
                
                wave1_len = wave_0 - wave_1
                wave3_len = wave_2 - wave_3
                wave5_len = wave_4 - wave_5
                
                if wave3_len >= wave1_len or wave3_len >= wave5_len:
                    rules_passed += 1
                
                if wave_4 < wave_1:
                    rules_passed += 1
                
                if wave_3 < wave_1:
                    rules_passed += 1
                
                if wave_5 <= wave_1:
                    rules_passed += 1
            
            # Pattern is valid if it passes at least 60% of rules
            is_valid = rules_passed >= (total_rules * 0.6)
            confidence = rules_passed / total_rules
            
            return {
                "type": f"elliott_wave_5_{direction}",
                "start_index": sequence[0]['index'],
                "end_index": sequence[-1]['index'],
                "direction": direction,
                "is_valid": is_valid,
                "confidence": confidence,
                "rules_passed": rules_passed,
                "total_rules": total_rules,
                "strength": confidence,
                "wave_structure": {f"wave_{i}": prices[i] for i in range(min(6, len(prices)))}
            }
            
        except Exception as e:
            print(f"    Error analyzing Elliott Wave structure: {e}")
            return {"is_valid": False, "error": str(e)}
    
    def _calculate_hs_strength(self, sequence: List[Dict[str, Any]]) -> float:
        """Calculate Head and Shoulders pattern strength."""
        try:
            if len(sequence) < 7:
                return 0.0
            
            left_shoulder = float(sequence[1]['price'])
            head = float(sequence[3]['price'])
            right_shoulder = float(sequence[5]['price'])
            
            # Symmetry score
            shoulder_diff = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder)
            symmetry_score = 1.0 - min(1.0, shoulder_diff / 0.05)  # Perfect symmetry = 1.0
            
            # Head prominence score
            head_prominence = min(
                (head - left_shoulder) / left_shoulder,
                (head - right_shoulder) / right_shoulder
            )
            prominence_score = min(1.0, head_prominence / 0.1)  # 10% prominence = 1.0
            
            # Volume confirmation (if available)
            volume_score = 0.5  # Default neutral score
            
            # Time proportion score
            total_duration = sequence[6]['index'] - sequence[0]['index']
            if total_duration > 0:
                # Good H&S should have relatively equal time segments
                time_score = 0.8  # Default good score
            else:
                time_score = 0.5
            
            # Weighted average
            strength = (symmetry_score * 0.3 + prominence_score * 0.4 + 
                       volume_score * 0.2 + time_score * 0.1)
            
            return min(1.0, max(0.0, strength))
            
        except Exception as e:
            print(f"    Error calculating H&S strength: {e}")
            return 0.5
    
    def _calculate_overall_pattern_strength(self, patterns: Dict[str, Any]) -> float:
        """Calculate overall pattern strength score."""
        try:
            total_strength = 0.0
            pattern_count = 0
            
            for pattern_type, pattern_list in patterns.items():
                if pattern_type == "summary":
                    continue
                
                if isinstance(pattern_list, list):
                    for pattern in pattern_list:
                        if isinstance(pattern, dict) and "strength" in pattern:
                            total_strength += pattern["strength"]
                            pattern_count += 1
            
            return total_strength / pattern_count if pattern_count > 0 else 0.0
            
        except Exception as e:
            print(f"    Error calculating overall pattern strength: {e}")
            return 0.0
    
    def _calculate_support_resistance_levels(self, pivots: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate support and resistance levels from pivots."""
        try:
            if not pivots:
                return {"support_levels": [], "resistance_levels": []}
            
            # Separate highs and lows
            highs = [p for p in pivots if p.get('type') == 'high']
            lows = [p for p in pivots if p.get('type') == 'low']
            
            # Calculate resistance levels (from highs)
            resistance_levels = []
            if highs:
                high_prices = [float(h['price']) for h in highs]
                
                # Group similar price levels
                resistance_groups = self._group_similar_prices(high_prices, tolerance=0.02)
                
                for group in resistance_groups:
                    avg_price = sum(group['prices']) / len(group['prices'])
                    resistance_levels.append({
                        "price": avg_price,
                        "strength": len(group['prices']),
                        "touches": len(group['prices']),
                        "type": "resistance"
                    })
            
            # Calculate support levels (from lows)
            support_levels = []
            if lows:
                low_prices = [float(l['price']) for l in lows]
                
                # Group similar price levels
                support_groups = self._group_similar_prices(low_prices, tolerance=0.02)
                
                for group in support_groups:
                    avg_price = sum(group['prices']) / len(group['prices'])
                    support_levels.append({
                        "price": avg_price,
                        "strength": len(group['prices']),
                        "touches": len(group['prices']),
                        "type": "support"
                    })
            
            # Sort by strength
            resistance_levels.sort(key=lambda x: x['strength'], reverse=True)
            support_levels.sort(key=lambda x: x['strength'], reverse=True)
            
            return {
                "support_levels": support_levels[:5],  # Top 5 support levels
                "resistance_levels": resistance_levels[:5],  # Top 5 resistance levels
                "total_support_levels": len(support_levels),
                "total_resistance_levels": len(resistance_levels)
            }
            
        except Exception as e:
            print(f"    Error calculating support/resistance levels: {e}")
            return {"support_levels": [], "resistance_levels": [], "error": str(e)}
    
    def _group_similar_prices(self, prices: List[float], tolerance: float = 0.02) -> List[Dict[str, Any]]:
        """Group similar prices within tolerance."""
        try:
            if not prices:
                return []
            
            groups = []
            sorted_prices = sorted(prices)
            
            current_group = [sorted_prices[0]]
            
            for i in range(1, len(sorted_prices)):
                current_price = sorted_prices[i]
                group_avg = sum(current_group) / len(current_group)
                
                # Check if price is within tolerance of group average
                if abs(current_price - group_avg) / group_avg <= tolerance:
                    current_group.append(current_price)
                else:
                    # Save current group and start new one
                    groups.append({"prices": current_group.copy()})
                    current_group = [current_price]
            
            # Don't forget the last group
            if current_group:
                groups.append({"prices": current_group})
            
            return groups
            
        except Exception as e:
            print(f"    Error grouping similar prices: {e}")
            return []
    
    def _calculate_fibonacci_analysis(self, pivots: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate Fibonacci retracements and extensions."""
        try:
            if len(pivots) < 3:
                return {"retracements": [], "extensions": [], "projections": []}
            
            fibonacci_analysis = {
                "retracements": [],
                "extensions": [],
                "projections": []
            }
            
            # Calculate retracements for recent swings
            for i in range(len(pivots) - 2):
                swing_start = pivots[i]
                swing_end = pivots[i + 1]
                
                retracements = self._calculate_fibonacci_retracements(
                    float(swing_start['price']), 
                    float(swing_end['price'])
                )
                
                if retracements:
                    fibonacci_analysis["retracements"].append({
                        "swing_start": swing_start,
                        "swing_end": swing_end,
                        "levels": retracements
                    })
            
            # Calculate extensions for ABC patterns
            if len(pivots) >= 3:
                for i in range(len(pivots) - 2):
                    a_point = float(pivots[i]['price'])
                    b_point = float(pivots[i + 1]['price'])
                    c_point = float(pivots[i + 2]['price'])
                    
                    extensions = self._calculate_fibonacci_extensions(a_point, b_point, c_point)
                    
                    if extensions:
                        fibonacci_analysis["extensions"].append({
                            "a_point": pivots[i],
                            "b_point": pivots[i + 1],
                            "c_point": pivots[i + 2],
                            "levels": extensions
                        })
            
            return fibonacci_analysis
            
        except Exception as e:
            print(f"    Error calculating Fibonacci analysis: {e}")
            return {"retracements": [], "extensions": [], "projections": [], "error": str(e)}
    
    def _calculate_fibonacci_retracements(self, start_price: float, end_price: float) -> List[Dict[str, Any]]:
        """Calculate Fibonacci retracement levels."""
        try:
            if start_price <= 0 or end_price <= 0:
                return []
            
            fib_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
            retracements = []
            
            price_diff = end_price - start_price
            
            for level in fib_levels:
                retracement_price = end_price - (price_diff * level)
                
                retracements.append({
                    "level": level,
                    "price": retracement_price,
                    "percentage": level * 100,
                    "description": f"{level:.3f} retracement"
                })
            
            return retracements
            
        except Exception as e:
            print(f"    Error calculating Fibonacci retracements: {e}")
            return []
    
    def _calculate_fibonacci_extensions(self, a_price: float, b_price: float, c_price: float) -> List[Dict[str, Any]]:
        """Calculate Fibonacci extension levels."""
        try:
            if a_price <= 0 or b_price <= 0 or c_price <= 0:
                return []
            
            fib_extension_levels = [1.272, 1.414, 1.618, 2.0, 2.618]
            extensions = []
            
            # Calculate AB move
            ab_move = b_price - a_price
            
            # Extensions are calculated from C point
            for level in fib_extension_levels:
                if ab_move != 0:
                    extension_price = c_price + (ab_move * level)
                    
                    extensions.append({
                        "level": level,
                        "price": extension_price,
                        "percentage": level * 100,
                        "description": f"{level:.3f} extension"
                    })
            
            return extensions
            
        except Exception as e:
            print(f"    Error calculating Fibonacci extensions: {e}")
            return []
    
    def _calculate_price_targets(self, pivots: List[Dict[str, Any]], 
                               df_ohlc: pd.DataFrame) -> Dict[str, Any]:
        """Calculate price targets based on pivot analysis."""
        try:
            if not pivots or len(pivots) < 2:
                return {"upside_targets": [], "downside_targets": []}
            
            current_price = float(df_ohlc['close'].iloc[-1])
            
            # Get recent pivots
            recent_pivots = pivots[-6:] if len(pivots) >= 6 else pivots
            
            upside_targets = []
            downside_targets = []
            
            # Resistance levels as upside targets
            resistance_levels = self._calculate_support_resistance_levels(pivots)["resistance_levels"]
            for level in resistance_levels:
                if level["price"] > current_price:
                    upside_targets.append({
                        "price": level["price"],
                        "type": "resistance_level",
                        "strength": level["strength"],
                        "distance_pct": ((level["price"] - current_price) / current_price) * 100
                    })
            
            # Support levels as downside targets
            support_levels = self._calculate_support_resistance_levels(pivots)["support_levels"]
            for level in support_levels:
                if level["price"] < current_price:
                    downside_targets.append({
                        "price": level["price"],
                        "type": "support_level",
                        "strength": level["strength"],
                        "distance_pct": ((current_price - level["price"]) / current_price) * 100
                    })
            
            # Pattern-based targets
            pattern_targets = self._calculate_pattern_based_targets(recent_pivots, current_price)
            upside_targets.extend(pattern_targets["upside"])
            downside_targets.extend(pattern_targets["downside"])
            
            # Sort targets by distance
            upside_targets.sort(key=lambda x: x["distance_pct"])
            downside_targets.sort(key=lambda x: x["distance_pct"])
            
            return {
                "current_price": current_price,
                "upside_targets": upside_targets[:5],  # Top 5 targets
                "downside_targets": downside_targets[:5],  # Top 5 targets
                "nearest_upside": upside_targets[0] if upside_targets else None,
                "nearest_downside": downside_targets[0] if downside_targets else None
            }
            
        except Exception as e:
            print(f"    Error calculating price targets: {e}")
            return {"upside_targets": [], "downside_targets": [], "error": str(e)}
    
    def _calculate_pattern_based_targets(self, pivots: List[Dict[str, Any]], 
                                       current_price: float) -> Dict[str, Any]:
        """Calculate targets based on chart patterns."""
        try:
            upside_targets = []
            downside_targets = []
            
            if len(pivots) < 3:
                return {"upside": upside_targets, "downside": downside_targets}
            
            # Calculate measured move targets
            for i in range(len(pivots) - 2):
                move1_start = float(pivots[i]['price'])
                move1_end = float(pivots[i + 1]['price'])
                move2_start = float(pivots[i + 1]['price'])
                
                move1_size = abs(move1_end - move1_start)
                
                # Measured move up
                if move1_end > move1_start:  # Upward move
                    target_price = move2_start + move1_size
                    if target_price > current_price:
                        upside_targets.append({
                            "price": target_price,
                            "type": "measured_move_up",
                            "strength": 0.7,
                            "distance_pct": ((target_price - current_price) / current_price) * 100
                        })
                
                # Measured move down
                else:  # Downward move
                    target_price = move2_start - move1_size
                    if target_price < current_price:
                        downside_targets.append({
                            "price": target_price,
                            "type": "measured_move_down",
                            "strength": 0.7,
                            "distance_pct": ((current_price - target_price) / current_price) * 100
                        })
            
            return {"upside": upside_targets, "downside": downside_targets}
            
        except Exception as e:
            print(f"    Error calculating pattern-based targets: {e}")
            return {"upside": [], "downside": []}

    # 🔧 FIX: Add missing helper functions
    def calculate_fibo_blocks(self, df_ohlcv: pd.DataFrame, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhanced Fibonacci block calculation for support/resistance zones."""
        try:
            if not pivots or len(pivots) < 2:
                return []
            
            fibo_blocks = []
            
            # Calculate Fibonacci blocks for each significant swing
            for i in range(len(pivots) - 1):
                start_price = float(pivots[i]['price'])
                end_price = float(pivots[i + 1]['price'])
                
                if start_price <= 0 or end_price <= 0:
                    continue
                
                # Calculate retracement levels
                price_diff = end_price - start_price
                swing_type = "bullish" if end_price > start_price else "bearish"
                
                # Create Fibonacci blocks
                for ratio_name, ratio_value in self.enhanced_fib_ratios.items():
                    if ratio_value <= 1.0:  # Only retracement levels for blocks
                        fib_price = end_price - (price_diff * ratio_value)
                        
                        fibo_blocks.append({
                            "price": fib_price,
                            "ratio": ratio_value,
                            "ratio_name": ratio_name,
                            "swing_type": swing_type,
                            "swing_start": start_price,
                            "swing_end": end_price,
                            "strength": self._calculate_fib_level_strength(ratio_value),
                            "block_type": "retracement"
                        })
            
            # Sort by strength and remove duplicates
            fibo_blocks.sort(key=lambda x: x["strength"], reverse=True)
            
            return fibo_blocks[:10]  # Return top 10 strongest levels
            
        except Exception as e:
            print(f"    Error calculating Fibonacci blocks: {e}")
            return []
    
    def calculate_fibonacci_extension(self, p1_price: float, p2_price: float, p3_price: float) -> List[Dict[str, Any]]:
        """Enhanced ABC Fibonacci extension calculation."""
        try:
            if p1_price <= 0 or p2_price <= 0 or p3_price <= 0:
                return []
            
            extensions = []
            
            # Calculate AB and BC moves
            ab_move = p2_price - p1_price
            bc_move = p3_price - p2_price
            
            # Extension ratios for ABC patterns
            extension_ratios = [1.272, 1.414, 1.618, 2.0, 2.618, 3.618, 4.236]
            
            for ratio in extension_ratios:
                # Calculate extension from C point using AB move
                extension_price = p3_price + (ab_move * ratio)
                
                # Calculate retracement ratio (BC/AB)
                bc_ab_ratio = abs(bc_move / ab_move) if ab_move != 0 else 0
                
                extensions.append({
                    "extension_level": ratio,
                    "target_price": extension_price,
                    "ab_move": ab_move,
                    "bc_move": bc_move,
                    "bc_ab_ratio": bc_ab_ratio,
                    "pattern_type": self._classify_abc_pattern(bc_ab_ratio),
                    "strength": self._calculate_extension_strength(ratio, bc_ab_ratio)
                })
            
            # Sort by strength
            extensions.sort(key=lambda x: x["strength"], reverse=True)
            
            return extensions
            
        except Exception as e:
            print(f"    Error calculating Fibonacci extensions: {e}")
            return []
    
    def _calculate_fib_level_strength(self, ratio: float) -> float:
        """Calculate strength of Fibonacci level based on market significance."""
        # Key Fibonacci levels have higher strength
        key_levels = {
            0.382: 0.8,
            0.5: 0.9,
            0.618: 1.0,  # Golden ratio - strongest
            0.786: 0.7,
            0.236: 0.6
        }
        
        return key_levels.get(ratio, 0.5)  # Default strength for other levels
    
    def _classify_abc_pattern(self, bc_ab_ratio: float) -> str:
        """Classify ABC pattern based on BC/AB ratio."""
        if 0.5 <= bc_ab_ratio <= 0.7:
            return "strong_abc"
        elif 0.3 <= bc_ab_ratio <= 0.9:
            return "moderate_abc"
        else:
            return "weak_abc"
    
    def _calculate_extension_strength(self, ratio: float, bc_ab_ratio: float) -> float:
        """Calculate extension target strength."""
        # Key extension levels
        key_extensions = {1.272: 0.7, 1.618: 1.0, 2.0: 0.8, 2.618: 0.9}
        ratio_strength = key_extensions.get(ratio, 0.5)
        
        # Pattern strength based on BC/AB ratio
        if 0.5 <= bc_ab_ratio <= 0.7:
            pattern_strength = 1.0
        elif 0.3 <= bc_ab_ratio <= 0.9:
            pattern_strength = 0.7
        else:
            pattern_strength = 0.4
        
        return (ratio_strength + pattern_strength) / 2

    def _enhance_pivots_with_confirmations(self, raw_pivots: List[Dict[str, Any]], 
                                         df_ohlc: pd.DataFrame) -> List[Dict[str, Any]]:
        """Enhance pivots with volume and momentum confirmations in a single pass."""
        try:
            enhanced_pivots = []
            
            for i, pivot in enumerate(raw_pivots):
                enhanced_pivot = pivot.copy()
                
                pivot_idx = pivot.get('index', 0)
                pivot_type = pivot.get('type', 'unknown')
                
                # Add volume confirmation
                volume_confirmation = self._calculate_single_pivot_volume_confirmation(
                    df_ohlc, pivot_idx, pivot_type
                )
                
                enhanced_pivot.update({
                    'volume_confirmed': volume_confirmation.get('confirmed', False),
                    'volume_strength': volume_confirmation.get('strength', 0.0),
                    'volume_ratio': volume_confirmation.get('ratio', 1.0),
                    'volume_analysis': volume_confirmation.get('analysis', 'unknown')
                })
                
                # Add momentum confirmation
                momentum_confirmation = self._calculate_single_pivot_momentum_confirmation(
                    df_ohlc, pivot_idx, pivot_type, pivot.get('price', 0)
                )
                
                enhanced_pivot.update({
                    'momentum_confirmed': momentum_confirmation.get('confirmed', False),
                    'momentum_strength': momentum_confirmation.get('strength', 0.0),
                    'momentum_score': momentum_confirmation.get('momentum_score', 0.0)
                })
                
                # Calculate price move percentage if not already present
                if i > 0 and 'price_move_pct' not in enhanced_pivot:
                    prev_price = raw_pivots[i-1].get('price', 0)
                    current_price = pivot.get('price', 0)
                    
                    if prev_price > 0:
                        price_move_pct = ((current_price - prev_price) / prev_price) * 100
                        enhanced_pivot['price_move_pct'] = price_move_pct
                
                # Determine significance
                price_move = abs(enhanced_pivot.get('price_move_pct', 0))
                enhanced_pivot['is_significant'] = (
                    price_move > 2.0 or  # 2% price move
                    enhanced_pivot.get('volume_confirmed', False) or
                    enhanced_pivot.get('momentum_confirmed', False)
                )
                
                enhanced_pivots.append(enhanced_pivot)
            
            return enhanced_pivots
            
        except Exception as e:
            print(f"    ❌ Error enhancing pivots: {e}")
            return raw_pivots
    
    def _calculate_single_pivot_volume_confirmation(self, df: pd.DataFrame, 
                                                  pivot_idx: int, pivot_type: str) -> Dict[str, Any]:
        """Calculate volume confirmation for a single pivot efficiently."""
        try:
            confirmation_data = {
                'confirmed': False,
                'strength': 0.0,
                'ratio': 1.0,
                'analysis': 'no_data'
            }
            
            if pivot_idx < 3 or pivot_idx >= len(df) - 1 or 'volume' not in df.columns:
                return confirmation_data
            
            pivot_volume = df['volume'].iloc[pivot_idx]
            
            if pivot_volume <= 0:
                confirmation_data['analysis'] = 'zero_volume'
                return confirmation_data
            
            # Calculate baseline volume efficiently
            baseline_start = max(0, pivot_idx - 10)
            baseline_end = max(0, pivot_idx - 2)
            
            if baseline_end <= baseline_start:
                confirmation_data['analysis'] = 'insufficient_baseline'
                return confirmation_data
            
            baseline_volumes = df['volume'].iloc[baseline_start:baseline_end]
            valid_volumes = baseline_volumes[baseline_volumes > 0]
            
            if len(valid_volumes) < 2:
                confirmation_data['analysis'] = 'insufficient_valid_baseline'
                return confirmation_data
            
            baseline_volume = valid_volumes.mean()
            volume_ratio = pivot_volume / baseline_volume
            confirmation_data['ratio'] = volume_ratio
            
            # Efficient confirmation logic
            if pivot_type == 'high':
                if volume_ratio > 1.4:
                    confirmation_data.update({
                        'confirmed': True,
                        'strength': min(1.0, (volume_ratio - 1.0) / 2.0),
                        'analysis': f'high_vol_{volume_ratio:.1f}x'
                    })
                elif volume_ratio > 1.1:
                    confirmation_data.update({
                        'confirmed': True,
                        'strength': min(0.6, (volume_ratio - 1.0) / 1.5),
                        'analysis': f'mod_vol_{volume_ratio:.1f}x'
                    })
                elif volume_ratio > 0.8:
                    confirmation_data.update({
                        'confirmed': True,
                        'strength': 0.3,
                        'analysis': f'low_vol_{volume_ratio:.1f}x'
                    })
            
            elif pivot_type == 'low':
                if volume_ratio > 1.3:
                    confirmation_data.update({
                        'confirmed': True,
                        'strength': min(1.0, (volume_ratio - 1.0) / 1.8),
                        'analysis': f'buy_vol_{volume_ratio:.1f}x'
                    })
                elif volume_ratio < 0.8:
                    confirmation_data.update({
                        'confirmed': True,
                        'strength': 0.7,
                        'analysis': f'exhaust_{volume_ratio:.1f}x'
                    })
                elif volume_ratio > 1.0:
                    confirmation_data.update({
                        'confirmed': True,
                        'strength': 0.5,
                        'analysis': f'support_{volume_ratio:.1f}x'
                    })
            
            return confirmation_data
            
        except Exception as e:
            return {
                'confirmed': False,
                'strength': 0.0,
                'ratio': 1.0,
                'analysis': f'error_{str(e)[:20]}'
            }
    
    def _calculate_single_pivot_momentum_confirmation(self, df: pd.DataFrame, 
                                                    pivot_idx: int, pivot_type: str, 
                                                    pivot_price: float) -> Dict[str, Any]:
        """Calculate momentum confirmation for a single pivot efficiently."""
        try:
            confirmation_data = {
                'confirmed': False,
                'strength': 0.0,
                'momentum_score': 0.0
            }
            
            if pivot_idx < 5 or pivot_idx >= len(df) - 1:
                return confirmation_data
            
            # Calculate momentum indicators efficiently
            pre_period = 3
            post_period = 2
            
            # Get price ranges
            pre_start = max(0, pivot_idx - pre_period)
            pre_end = pivot_idx
            post_start = pivot_idx
            post_end = min(len(df), pivot_idx + post_period + 1)
            
            if pre_end <= pre_start or post_end <= post_start:
                return confirmation_data
            
            # Pre-pivot momentum
            pre_prices = df['close'].iloc[pre_start:pre_end]
            if len(pre_prices) < 2:
                return confirmation_data
            
            pre_momentum = (pre_prices.iloc[-1] - pre_prices.iloc[0]) / pre_prices.iloc[0] if pre_prices.iloc[0] > 0 else 0
            
            # Post-pivot momentum (if available)
            post_prices = df['close'].iloc[post_start:post_end]
            post_momentum = 0
            if len(post_prices) >= 2:
                post_momentum = (post_prices.iloc[-1] - post_prices.iloc[0]) / post_prices.iloc[0] if post_prices.iloc[0] > 0 else 0
            
            # Price velocity around pivot
            velocity_score = 0
            if len(pre_prices) >= 3:
                price_changes = pre_prices.pct_change().dropna()
                if len(price_changes) > 0:
                    velocity_score = abs(price_changes.mean())
            
            # Momentum confirmation logic
            momentum_score = 0
            
            if pivot_type == 'high':
                # For highs, we want to see upward momentum before and reversal after
                if pre_momentum > 0.01:  # 1% upward momentum before
                    momentum_score += 0.4
                if post_momentum < -0.005:  # Reversal confirmation
                    momentum_score += 0.4
                if velocity_score > 0.01:  # High price velocity
                    momentum_score += 0.2
                
                # Additional confirmation for divergence
                if len(pre_prices) >= 3:
                    recent_high = pre_prices.max()
                    if pivot_price >= recent_high * 0.998:  # Within 0.2% of recent high
                        momentum_score += 0.3
            
            elif pivot_type == 'low':
                # For lows, we want to see downward momentum before and reversal after
                if pre_momentum < -0.01:  # 1% downward momentum before
                    momentum_score += 0.4
                if post_momentum > 0.005:  # Reversal confirmation
                    momentum_score += 0.4
                if velocity_score > 0.01:  # High price velocity
                    momentum_score += 0.2
                
                # Additional confirmation for divergence
                if len(pre_prices) >= 3:
                    recent_low = pre_prices.min()
                    if pivot_price <= recent_low * 1.002:  # Within 0.2% of recent low
                        momentum_score += 0.3
            
            # Calculate RSI-like momentum (simplified)
            if len(pre_prices) >= 5:
                gains = []
                losses = []
                for i in range(1, len(pre_prices)):
                    change = pre_prices.iloc[i] - pre_prices.iloc[i-1]
                    if change > 0:
                        gains.append(change)
                        losses.append(0)
                    else:
                        gains.append(0)
                        losses.append(abs(change))
                
                if len(gains) > 0:
                    avg_gain = sum(gains) / len(gains)
                    avg_loss = sum(losses) / len(losses) if sum(losses) > 0 else 0.00001
                    
                    rs = avg_gain / avg_loss
                    rsi_like = 100 - (100 / (1 + rs))
                    
                    # Momentum confirmation based on RSI-like indicator
                    if pivot_type == 'high' and rsi_like > 70:
                        momentum_score += 0.2
                    elif pivot_type == 'low' and rsi_like < 30:
                        momentum_score += 0.2
            
            # Price action confirmation
            if pivot_idx >= 2 and pivot_idx < len(df) - 2:
                prev_price = df['close'].iloc[pivot_idx - 1]
                next_price = df['close'].iloc[pivot_idx + 1] if pivot_idx + 1 < len(df) else pivot_price
                
                if pivot_type == 'high':
                    # Confirm high with lower close after
                    if next_price < pivot_price * 0.995:  # 0.5% lower
                        momentum_score += 0.2
                elif pivot_type == 'low':
                    # Confirm low with higher close after
                    if next_price > pivot_price * 1.005:  # 0.5% higher
                        momentum_score += 0.2
            
            # Final momentum assessment
            momentum_score = min(1.0, momentum_score)  # Cap at 1.0
            
            confirmation_data.update({
                'momentum_score': momentum_score,
                'pre_momentum': pre_momentum,
                'post_momentum': post_momentum,
                'velocity_score': velocity_score
            })
            
            # Confirmation thresholds
            if momentum_score >= 0.7:
                confirmation_data.update({
                    'confirmed': True,
                    'strength': momentum_score,
                    'quality': 'strong'
                })
            elif momentum_score >= 0.5:
                confirmation_data.update({
                    'confirmed': True,
                    'strength': momentum_score,
                    'quality': 'moderate'
                })
            elif momentum_score >= 0.3:
                confirmation_data.update({
                    'confirmed': True,
                    'strength': momentum_score,
                    'quality': 'weak'
                })
            else:
                confirmation_data.update({
                    'confirmed': False,
                    'strength': momentum_score,
                    'quality': 'insufficient'
                })
            
            return confirmation_data
            
        except Exception as e:
            print(f"    Error calculating momentum confirmation: {e}")
            return {
                'confirmed': False,
                'strength': 0.0,
                'momentum_score': 0.0,
                'error': str(e)
            }
    
    def _find_zigzag_pivots(self, highs: np.ndarray, lows: np.ndarray, 
                           closes: np.ndarray, timestamps: pd.Index) -> List[Dict[str, Any]]:
        """Core ZigZag pivot detection algorithm."""
        try:
            if len(highs) < 10 or len(lows) < 10 or len(closes) < 10:
                return []
            
            pivots = []
            deviation = self.settings.deviation / 100.0  # Convert percentage to decimal
            
            # Initialize with first point
            current_pivot_idx = 0
            current_pivot_price = closes[0]
            current_trend = 0  # 0 = unknown, 1 = up, -1 = down
            
            for i in range(1, len(closes)):
                current_price = closes[i]
                high_price = highs[i]
                low_price = lows[i]
                
                # Calculate percentage change from current pivot
                if current_pivot_price > 0:
                    price_change_pct = abs(current_price - current_pivot_price) / current_pivot_price
                else:
                    continue
                
                # Check for significant move
                if price_change_pct >= deviation:
                    # Determine if this is a new pivot
                    if current_price > current_pivot_price:
                        # Potential upward move
                        if current_trend != 1:  # Trend change or initialization
                            # Add previous pivot if it was a low
                            if current_trend == -1:
                                pivots.append({
                                    'index': current_pivot_idx,
                                    'price': current_pivot_price,
                                    'timestamp': timestamps[current_pivot_idx],
                                    'type': 'low',
                                    'high_price': highs[current_pivot_idx],
                                    'low_price': lows[current_pivot_idx],
                                    'close_price': closes[current_pivot_idx]
                                })
                            
                            current_trend = 1
                        
                        # Update current pivot to highest point in uptrend
                        if high_price > current_pivot_price:
                            current_pivot_idx = i
                            current_pivot_price = high_price
                    
                    else:
                        # Potential downward move
                        if current_trend != -1:  # Trend change or initialization
                            # Add previous pivot if it was a high
                            if current_trend == 1:
                                pivots.append({
                                    'index': current_pivot_idx,
                                    'price': current_pivot_price,
                                    'timestamp': timestamps[current_pivot_idx],
                                    'type': 'high',
                                    'high_price': highs[current_pivot_idx],
                                    'low_price': lows[current_pivot_idx],
                                    'close_price': closes[current_pivot_idx]
                                })
                            
                            current_trend = -1
                        
                        # Update current pivot to lowest point in downtrend
                        if low_price < current_pivot_price:
                            current_pivot_idx = i
                            current_pivot_price = low_price
                
                # Update pivot if we're continuing in the same direction
                else:
                    if current_trend == 1 and high_price > current_pivot_price:
                        current_pivot_idx = i
                        current_pivot_price = high_price
                    elif current_trend == -1 and low_price < current_pivot_price:
                        current_pivot_idx = i
                        current_pivot_price = low_price
            
            # Add the final pivot
            if current_trend != 0:
                final_type = 'high' if current_trend == 1 else 'low'
                pivots.append({
                    'index': current_pivot_idx,
                    'price': current_pivot_price,
                    'timestamp': timestamps[current_pivot_idx],
                    'type': final_type,
                    'high_price': highs[current_pivot_idx],
                    'low_price': lows[current_pivot_idx],
                    'close_price': closes[current_pivot_idx]
                })
            
            # Clean up pivots - remove consecutive same types
            cleaned_pivots = []
            for i, pivot in enumerate(pivots):
                if i == 0 or pivot['type'] != pivots[i-1]['type']:
                    cleaned_pivots.append(pivot)
                else:
                    # Keep the more extreme pivot
                    prev_pivot = cleaned_pivots[-1]
                    if pivot['type'] == 'high':
                        if pivot['price'] > prev_pivot['price']:
                            cleaned_pivots[-1] = pivot
                    else:
                        if pivot['price'] < prev_pivot['price']:
                            cleaned_pivots[-1] = pivot
            
            return cleaned_pivots
            
        except Exception as e:
            print(f"    Error finding ZigZag pivots: {e}")
            return []
    
    def _analyze_zigzag_trend(self, pivots: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze overall trend from ZigZag pivots."""
        try:
            if len(pivots) < 3:
                return {
                    "overall_trend": "UNKNOWN",
                    "trend_strength": 0.0,
                    "trend_consistency": 0.0,
                    "recent_trend": "UNKNOWN"
                }
            
            # Get recent pivots for trend analysis
            recent_pivots = pivots[-6:] if len(pivots) >= 6 else pivots
            
            # Analyze highs and lows progression
            highs = [p for p in recent_pivots if p['type'] == 'high']
            lows = [p for p in recent_pivots if p['type'] == 'low']
            
            trend_signals = []
            
            # Higher highs and higher lows = uptrend
            if len(highs) >= 2:
                high_prices = [h['price'] for h in highs]
                if all(high_prices[i] >= high_prices[i-1] for i in range(1, len(high_prices))):
                    trend_signals.append("higher_highs")
            
            if len(lows) >= 2:
                low_prices = [l['price'] for l in lows]
                if all(low_prices[i] >= low_prices[i-1] for i in range(1, len(low_prices))):
                    trend_signals.append("higher_lows")
            
            # Lower highs and lower lows = downtrend
            if len(highs) >= 2:
                high_prices = [h['price'] for h in highs]
                if all(high_prices[i] <= high_prices[i-1] for i in range(1, len(high_prices))):
                    trend_signals.append("lower_highs")
            
            if len(lows) >= 2:
                low_prices = [l['price'] for l in lows]
                if all(low_prices[i] <= low_prices[i-1] for i in range(1, len(low_prices))):
                    trend_signals.append("lower_lows")
            
            # Determine overall trend
            uptrend_signals = sum(1 for signal in trend_signals if signal in ["higher_highs", "higher_lows"])
            downtrend_signals = sum(1 for signal in trend_signals if signal in ["lower_highs", "lower_lows"])
            
            if uptrend_signals >= 2:
                overall_trend = "UPTREND"
                trend_strength = uptrend_signals / 2.0
            elif downtrend_signals >= 2:
                overall_trend = "DOWNTREND"
                trend_strength = downtrend_signals / 2.0
            elif uptrend_signals == 1 and downtrend_signals == 0:
                overall_trend = "WEAK_UPTREND"
                trend_strength = 0.5
            elif downtrend_signals == 1 and uptrend_signals == 0:
                overall_trend = "WEAK_DOWNTREND"
                trend_strength = 0.5
            else:
                overall_trend = "SIDEWAYS"
                trend_strength = 0.1
            
            # Calculate trend consistency
            total_signals = len(trend_signals)
            consistent_signals = max(uptrend_signals, downtrend_signals)
            trend_consistency = consistent_signals / total_signals if total_signals > 0 else 0.0
            
            # Recent trend (last 3 pivots)
            recent_trend = "UNKNOWN"
            if len(recent_pivots) >= 3:
                last_three = recent_pivots[-3:]
                prices = [p['price'] for p in last_three]
                
                if prices[-1] > prices[0]:
                    recent_trend = "BULLISH"
                elif prices[-1] < prices[0]:
                    recent_trend = "BEARISH"
                else:
                    recent_trend = "NEUTRAL"
            
            return {
                "overall_trend": overall_trend,
                "trend_strength": min(1.0, trend_strength),
                "trend_consistency": trend_consistency,
                "recent_trend": recent_trend,
                "trend_signals": trend_signals,
                "uptrend_signals": uptrend_signals,
                "downtrend_signals": downtrend_signals
            }
            
        except Exception as e:
            print(f"    Error analyzing ZigZag trend: {e}")
            return {
                "overall_trend": "ERROR",
                "trend_strength": 0.0,
                "trend_consistency": 0.0,
                "recent_trend": "ERROR",
                "error": str(e)
            }
    
    def _analyze_zigzag_patterns(self, pivots: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze ZigZag patterns for trading signals."""
        try:
            patterns = {
                "double_top": [],
                "double_bottom": [],
                "triple_top": [],
                "triple_bottom": [],
                "ascending_triangle": [],
                "descending_triangle": [],
                "symmetrical_triangle": []
            }
            
            if len(pivots) < 4:
                return patterns
            
            # Look for double top/bottom patterns
            patterns["double_top"] = self._find_double_tops(pivots)
            patterns["double_bottom"] = self._find_double_bottoms(pivots)
            
            # Look for triple patterns
            if len(pivots) >= 6:
                patterns["triple_top"] = self._find_triple_tops(pivots)
                patterns["triple_bottom"] = self._find_triple_bottoms(pivots)
            
            # Look for triangle patterns
            if len(pivots) >= 5:
                patterns["ascending_triangle"] = self._find_ascending_triangles(pivots)
                patterns["descending_triangle"] = self._find_descending_triangles(pivots)
                patterns["symmetrical_triangle"] = self._find_symmetrical_triangles(pivots)
            
            return patterns
            
        except Exception as e:
            print(f"    Error analyzing ZigZag patterns: {e}")
            return {"error": str(e)}
    
    def _find_double_tops(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find double top patterns."""
        try:
            double_tops = []
            highs = [p for p in pivots if p['type'] == 'high']
            
            if len(highs) < 2:
                return double_tops
            
            for i in range(len(highs) - 1):
                for j in range(i + 1, len(highs)):
                    high1 = highs[i]
                    high2 = highs[j]
                    
                    # Check if prices are similar (within 2%)
                    price_diff = abs(high1['price'] - high2['price']) / max(high1['price'], high2['price'])
                    
                    if price_diff <= 0.02:  # Within 2%
                        # Find the low between the two highs
                        lows_between = [p for p in pivots 
                                      if p['type'] == 'low' and 
                                      high1['index'] < p['index'] < high2['index']]
                        
                        if lows_between:
                            valley = min(lows_between, key=lambda x: x['price'])
                            
                            # Check if valley is significantly lower (at least 3%)
                            valley_depth = (min(high1['price'], high2['price']) - valley['price']) / valley['price']
                            
                            if valley_depth >= 0.03:  # At least 3% pullback
                                double_tops.append({
                                    "pattern_type": "double_top",
                                    "left_high": high1,
                                    "right_high": high2,
                                    "valley": valley,
                                    "resistance_level": (high1['price'] + high2['price']) / 2,
                                    "support_level": valley['price'],
                                    "target_price": valley['price'] - valley_depth * valley['price'],
                                    "strength": 1.0 - price_diff,
                                    "valley_depth_pct": valley_depth * 100
                                })
            
            return double_tops
            
        except Exception as e:
            print(f"    Error finding double tops: {e}")
            return []
    
    def _find_double_bottoms(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find double bottom patterns."""
        try:
            double_bottoms = []
            lows = [p for p in pivots if p['type'] == 'low']
            
            if len(lows) < 2:
                return double_bottoms
            
            for i in range(len(lows) - 1):
                for j in range(i + 1, len(lows)):
                    low1 = lows[i]
                    low2 = lows[j]
                    
                    # Check if prices are similar (within 2%)
                    price_diff = abs(low1['price'] - low2['price']) / max(low1['price'], low2['price'])
                    
                    if price_diff <= 0.02:  # Within 2%
                        # Find the high between the two lows
                        highs_between = [p for p in pivots 
                                       if p['type'] == 'high' and 
                                       low1['index'] < p['index'] < low2['index']]
                        
                        if highs_between:
                            peak = max(highs_between, key=lambda x: x['price'])
                            
                            # Check if peak is significantly higher (at least 3%)
                            peak_height = (peak['price'] - max(low1['price'], low2['price'])) / max(low1['price'], low2['price'])
                            
                            if peak_height >= 0.03:  # At least 3% bounce
                                double_bottoms.append({
                                    "pattern_type": "double_bottom",
                                    "left_low": low1,
                                    "right_low": low2,
                                    "peak": peak,
                                    "support_level": (low1['price'] + low2['price']) / 2,
                                    "resistance_level": peak['price'],
                                    "target_price": peak['price'] + peak_height * peak['price'],
                                    "strength": 1.0 - price_diff,
                                    "peak_height_pct": peak_height * 100
                                })
            
            return double_bottoms
            
        except Exception as e:
            print(f"    Error finding double bottoms: {e}")
            return []
    
    def _find_triple_tops(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find triple top patterns."""
        try:
            triple_tops = []
            highs = [p for p in pivots if p['type'] == 'high']
            
            if len(highs) < 3:
                return triple_tops
            
            for i in range(len(highs) - 2):
                for j in range(i + 1, len(highs) - 1):
                    for k in range(j + 1, len(highs)):
                        high1, high2, high3 = highs[i], highs[j], highs[k]
                        
                        # Check if all three highs are similar (within 2%)
                        prices = [high1['price'], high2['price'], high3['price']]
                        avg_price = sum(prices) / 3
                        max_diff = max(abs(p - avg_price) / avg_price for p in prices)
                        
                        if max_diff <= 0.02:  # Within 2%
                            triple_tops.append({
                                "pattern_type": "triple_top",
                                "highs": [high1, high2, high3],
                                "resistance_level": avg_price,
                                "strength": 1.0 - max_diff,
                                "target_calculation": "neckline_break"
                            })
            
            return triple_tops
            
        except Exception as e:
            print(f"    Error finding triple tops: {e}")
            return []
    
    def _find_triple_bottoms(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find triple bottom patterns."""
        try:
            triple_bottoms = []
            lows = [p for p in pivots if p['type'] == 'low']
            
            if len(lows) < 3:
                return triple_bottoms
            
            for i in range(len(lows) - 2):
                for j in range(i + 1, len(lows) - 1):
                    for k in range(j + 1, len(lows)):
                        low1, low2, low3 = lows[i], lows[j], lows[k]
                        
                        # Check if all three lows are similar (within 2%)
                        prices = [low1['price'], low2['price'], low3['price']]
                        avg_price = sum(prices) / 3
                        max_diff = max(abs(p - avg_price) / avg_price for p in prices)
                        
                        if max_diff <= 0.02:  # Within 2%
                            triple_bottoms.append({
                                "pattern_type": "triple_bottom",
                                "lows": [low1, low2, low3],
                                "support_level": avg_price,
                                "strength": 1.0 - max_diff,
                                "target_calculation": "neckline_break"
                            })
            
            return triple_bottoms
            
        except Exception as e:
            print(f"    Error finding triple bottoms: {e}")
            return []
    
    def _find_ascending_triangles(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find ascending triangle patterns."""
        try:
            ascending_triangles = []
            
            if len(pivots) < 5:
                return ascending_triangles
            
            # Look for ascending triangle pattern: horizontal resistance, rising support
            for i in range(len(pivots) - 4):
                sequence = pivots[i:i+5]
                
                # Separate highs and lows
                highs = [p for p in sequence if p.get('type') == 'high']
                lows = [p for p in sequence if p.get('type') == 'low']
                
                if len(highs) >= 2 and len(lows) >= 2:
                    # Sort by index to get chronological order
                    highs.sort(key=lambda x: x['index'])
                    lows.sort(key=lambda x: x['index'])
                    
                    # Analyze resistance line (should be horizontal)
                    high_prices = [float(h['price']) for h in highs]
                    high_indices = [h['index'] for h in highs]
                    
                    if len(high_prices) >= 2:
                        # Calculate resistance line slope
                        resistance_slope = np.polyfit(range(len(high_prices)), high_prices, 1)[0]
                        resistance_level = np.mean(high_prices)
                        
                        # Check if resistance is relatively horizontal (slope close to 0)
                        max_price_range = max(high_prices) - min(high_prices)
                        horizontal_tolerance = resistance_level * 0.015  # 1.5% tolerance
                        
                        if max_price_range <= horizontal_tolerance:
                            # Analyze support line (should be rising)
                            low_prices = [float(l['price']) for l in lows]
                            low_indices = [l['index'] for l in lows]
                            
                            if len(low_prices) >= 2:
                                # Calculate support line slope
                                support_slope = np.polyfit(range(len(low_prices)), low_prices, 1)[0]
                                
                                # Support should be rising (positive slope)
                                if support_slope > 0:
                                    # Calculate convergence
                                    first_low = low_prices[0]
                                    last_low = low_prices[-1]
                                    convergence_rate = (last_low - first_low) / first_low
                                    
                                    # Ensure meaningful convergence towards resistance
                                    resistance_distance_start = resistance_level - first_low
                                    resistance_distance_end = resistance_level - last_low
                                    
                                    if (resistance_distance_end < resistance_distance_start and 
                                        convergence_rate > 0.01):  # At least 1% rise in support
                                        
                                        # Calculate triangle quality metrics
                                        triangle_height = resistance_level - min(low_prices)
                                        triangle_width = sequence[-1]['index'] - sequence[0]['index']
                                        
                                        # Calculate breakout target
                                        target_move = triangle_height
                                        upside_target = resistance_level + target_move
                                        downside_target = min(low_prices) - target_move * 0.5
                                        
                                        # Calculate pattern strength
                                        slope_quality = min(1.0, support_slope / (resistance_level * 0.001))
                                        horizontal_quality = 1.0 - (max_price_range / horizontal_tolerance)
                                        convergence_quality = min(1.0, convergence_rate / 0.05)
                                        
                                        pattern_strength = (slope_quality + horizontal_quality + convergence_quality) / 3
                                        
                                        ascending_triangles.append({
                                            "pattern_type": "ascending_triangle",
                                            "start_index": sequence[0]['index'],
                                            "end_index": sequence[-1]['index'],
                                            "resistance_level": resistance_level,
                                            "resistance_slope": resistance_slope,
                                            "support_slope": support_slope,
                                            "support_start": first_low,
                                            "support_end": last_low,
                                            "triangle_height": triangle_height,
                                            "triangle_width": triangle_width,
                                            "convergence_rate": convergence_rate,
                                            "upside_target": upside_target,
                                            "downside_target": downside_target,
                                            "pattern_strength": pattern_strength,
                                            "direction_bias": "bullish",
                                            "breakout_probability": min(0.9, pattern_strength + 0.2),
                                            "highs": highs,
                                            "lows": lows
                                        })
            
            # Sort by pattern strength
            ascending_triangles.sort(key=lambda x: x["pattern_strength"], reverse=True)
            return ascending_triangles[:3]  # Return top 3 patterns
            
        except Exception as e:
            print(f"    Error finding ascending triangles: {e}")
            return []
    
    def _find_descending_triangles(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find descending triangle patterns."""
        try:
            descending_triangles = []
            
            if len(pivots) < 5:
                return descending_triangles
            
            # Look for descending triangle pattern: falling resistance, horizontal support
            for i in range(len(pivots) - 4):
                sequence = pivots[i:i+5]
                
                # Separate highs and lows
                highs = [p for p in sequence if p.get('type') == 'high']
                lows = [p for p in sequence if p.get('type') == 'low']
                
                if len(highs) >= 2 and len(lows) >= 2:
                    # Sort by index to get chronological order
                    highs.sort(key=lambda x: x['index'])
                    lows.sort(key=lambda x: x['index'])
                    
                    # Analyze support line (should be horizontal)
                    low_prices = [float(l['price']) for l in lows]
                    low_indices = [l['index'] for l in lows]
                    
                    if len(low_prices) >= 2:
                        # Calculate support line slope
                        support_slope = np.polyfit(range(len(low_prices)), low_prices, 1)[0]
                        support_level = np.mean(low_prices)
                        
                        # Check if support is relatively horizontal (slope close to 0)
                        max_price_range = max(low_prices) - min(low_prices)
                        horizontal_tolerance = support_level * 0.015  # 1.5% tolerance
                        
                        if max_price_range <= horizontal_tolerance:
                            # Analyze resistance line (should be falling)
                            high_prices = [float(h['price']) for h in highs]
                            high_indices = [h['index'] for h in highs]
                            
                            if len(high_prices) >= 2:
                                # Calculate resistance line slope
                                resistance_slope = np.polyfit(range(len(high_prices)), high_prices, 1)[0]
                                
                                # Resistance should be falling (negative slope)
                                if resistance_slope < 0:
                                    # Calculate convergence
                                    first_high = high_prices[0]
                                    last_high = high_prices[-1]
                                    convergence_rate = (first_high - last_high) / first_high
                                    
                                    # Ensure meaningful convergence towards support
                                    support_distance_start = first_high - support_level
                                    support_distance_end = last_high - support_level
                                    
                                    if (support_distance_end < support_distance_start and 
                                        convergence_rate > 0.01):  # At least 1% fall in resistance
                                        
                                        # Calculate triangle quality metrics
                                        triangle_height = max(high_prices) - support_level
                                        triangle_width = sequence[-1]['index'] - sequence[0]['index']
                                        
                                        # Calculate breakout target
                                        target_move = triangle_height
                                        downside_target = support_level - target_move
                                        upside_target = max(high_prices) + target_move * 0.5
                                        
                                        # Calculate pattern strength
                                        slope_quality = min(1.0, abs(resistance_slope) / (support_level * 0.001))
                                        horizontal_quality = 1.0 - (max_price_range / horizontal_tolerance)
                                        convergence_quality = min(1.0, convergence_rate / 0.05)
                                        
                                        pattern_strength = (slope_quality + horizontal_quality + convergence_quality) / 3
                                        
                                        descending_triangles.append({
                                            "pattern_type": "descending_triangle",
                                            "start_index": sequence[0]['index'],
                                            "end_index": sequence[-1]['index'],
                                            "support_level": support_level,
                                            "support_slope": support_slope,
                                            "resistance_slope": resistance_slope,
                                            "resistance_start": first_high,
                                            "resistance_end": last_high,
                                            "triangle_height": triangle_height,
                                            "triangle_width": triangle_width,
                                            "convergence_rate": convergence_rate,
                                            "downside_target": downside_target,
                                            "upside_target": upside_target,
                                            "pattern_strength": pattern_strength,
                                            "direction_bias": "bearish",
                                            "breakout_probability": min(0.9, pattern_strength + 0.2),
                                            "highs": highs,
                                            "lows": lows
                                        })
            
            # Sort by pattern strength
            descending_triangles.sort(key=lambda x: x["pattern_strength"], reverse=True)
            return descending_triangles[:3]  # Return top 3 patterns
            
        except Exception as e:
            print(f"    Error finding descending triangles: {e}")
            return []
    
    def _find_symmetrical_triangles(self, pivots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find symmetrical triangle patterns."""
        try:
            symmetrical_triangles = []
            
            if len(pivots) < 6:
                return symmetrical_triangles
            
            # Look for symmetrical triangle pattern: converging trend lines with opposite slopes
            for i in range(len(pivots) - 5):
                sequence = pivots[i:i+6]
                
                # Separate highs and lows
                highs = [p for p in sequence if p.get('type') == 'high']
                lows = [p for p in sequence if p.get('type') == 'low']
                
                if len(highs) >= 3 and len(lows) >= 3:
                    # Sort by index to get chronological order
                    highs.sort(key=lambda x: x['index'])
                    lows.sort(key=lambda x: x['index'])
                    
                    # Analyze resistance line (should be falling)
                    high_prices = [float(h['price']) for h in highs]
                    high_indices = [h['index'] for h in highs]
                    
                    # Analyze support line (should be rising)
                    low_prices = [float(l['price']) for l in lows]
                    low_indices = [l['index'] for l in lows]
                    
                    if len(high_prices) >= 2 and len(low_prices) >= 2:
                        # Calculate trend lines
                        resistance_slope = np.polyfit(range(len(high_prices)), high_prices, 1)[0]
                        support_slope = np.polyfit(range(len(low_prices)), low_prices, 1)[0]
                        
                        # For symmetrical triangle:
                        # - Resistance should be falling (negative slope)
                        # - Support should be rising (positive slope)
                        # - Lines should be converging
                        if resistance_slope < 0 and support_slope > 0:
                            # Calculate convergence metrics
                            initial_spread = max(high_prices) - min(low_prices)
                            final_spread = high_prices[-1] - low_prices[-1]
                            convergence_ratio = (initial_spread - final_spread) / initial_spread
                            
                            # Ensure meaningful convergence (at least 20% reduction in spread)
                            if convergence_ratio > 0.2 and final_spread > 0:
                                # Calculate symmetry (how similar the slopes are in magnitude)
                                slope_ratio = abs(resistance_slope / support_slope) if support_slope != 0 else 0
                                symmetry_score = 1.0 - abs(1.0 - slope_ratio) if slope_ratio > 0 else 0
                                
                                # Good symmetrical triangle should have slope ratio close to 1
                                if 0.3 <= slope_ratio <= 3.0:  # Reasonable symmetry range
                                    # Calculate triangle apex (convergence point)
                                    resistance_line_start = high_prices[0]
                                    support_line_start = low_prices[0]
                                    
                                    # Estimate apex price and time
                                    if abs(resistance_slope - support_slope) > 0.0001:
                                        # Linear interpolation to find convergence
                                        apex_time_steps = (resistance_line_start - support_line_start) / (support_slope - resistance_slope)
                                        apex_price = support_line_start + (support_slope * apex_time_steps)
                                        apex_index = sequence[0]['index'] + apex_time_steps
                                    else:
                                        apex_price = (high_prices[-1] + low_prices[-1]) / 2
                                        apex_index = sequence[-1]['index']
                                    
                                    # Calculate triangle quality metrics
                                    triangle_height = initial_spread
                                    triangle_width = sequence[-1]['index'] - sequence[0]['index']
                                    
                                    # Calculate breakout targets (can go either direction)
                                    target_move = triangle_height * 0.75  # Conservative target
                                    current_price = (high_prices[-1] + low_prices[-1]) / 2
                                    upside_target = current_price + target_move
                                    downside_target = current_price - target_move
                                    
                                    # Calculate pattern strength
                                    convergence_quality = min(1.0, convergence_ratio / 0.6)
                                    symmetry_quality = symmetry_score
                                    slope_quality = min(1.0, (abs(resistance_slope) + abs(support_slope)) / (current_price * 0.002))
                                    
                                    pattern_strength = (convergence_quality + symmetry_quality + slope_quality) / 3
                                    
                                    # Determine breakout direction bias based on position within triangle
                                    triangle_position = (current_price - min(low_prices)) / triangle_height
                                    
                                    if triangle_position > 0.6:
                                        direction_bias = "bullish"
                                        breakout_probability_up = 0.6
                                        breakout_probability_down = 0.4
                                    elif triangle_position < 0.4:
                                        direction_bias = "bearish"
                                        breakout_probability_up = 0.4
                                        breakout_probability_down = 0.6
                                    else:
                                        direction_bias = "neutral"
                                        breakout_probability_up = 0.5
                                        breakout_probability_down = 0.5
                                    
                                    symmetrical_triangles.append({
                                        "pattern_type": "symmetrical_triangle",
                                        "start_index": sequence[0]['index'],
                                        "end_index": sequence[-1]['index'],
                                        "apex_price": apex_price,
                                        "apex_index": apex_index,
                                        "resistance_slope": resistance_slope,
                                        "support_slope": support_slope,
                                        "initial_spread": initial_spread,
                                        "final_spread": final_spread,
                                        "convergence_ratio": convergence_ratio,
                                        "symmetry_score": symmetry_score,
                                        "triangle_height": triangle_height,
                                        "triangle_width": triangle_width,
                                        "triangle_position": triangle_position,
                                        "upside_target": upside_target,
                                        "downside_target": downside_target,
                                        "pattern_strength": pattern_strength,
                                        "direction_bias": direction_bias,
                                        "breakout_probability_up": breakout_probability_up,
                                        "breakout_probability_down": breakout_probability_down,
                                        "volatility_compression": convergence_ratio,
                                        "highs": highs,
                                        "lows": lows
                                    })
            
            # Sort by pattern strength
            symmetrical_triangles.sort(key=lambda x: x["pattern_strength"], reverse=True)
            return symmetrical_triangles[:3]  # Return top 3 patterns
            
        except Exception as e:
            print(f"    Error finding symmetrical triangles: {e}")
            return []

    def _calculate_triangle_breakout_probability(self, triangle_data: Dict[str, Any], 
                                               current_price: float, volume_data: Optional[np.ndarray] = None) -> Dict[str, float]:
        """Calculate breakout probability for triangle patterns."""
        try:
            pattern_type = triangle_data.get("pattern_type", "")
            pattern_strength = triangle_data.get("pattern_strength", 0.5)
            
            base_probability = {
                "upside": 0.5,
                "downside": 0.5
            }
            
            # Adjust based on pattern type
            if pattern_type == "ascending_triangle":
                base_probability["upside"] = 0.7
                base_probability["downside"] = 0.3
            elif pattern_type == "descending_triangle":
                base_probability["upside"] = 0.3
                base_probability["downside"] = 0.7
            elif pattern_type == "symmetrical_triangle":
                direction_bias = triangle_data.get("direction_bias", "neutral")
                if direction_bias == "bullish":
                    base_probability["upside"] = 0.6
                    base_probability["downside"] = 0.4
                elif direction_bias == "bearish":
                    base_probability["upside"] = 0.4
                    base_probability["downside"] = 0.6
            
            # Adjust based on pattern strength
            strength_multiplier = 0.5 + (pattern_strength * 0.5)
            
            # Adjust based on current price position
            if "triangle_position" in triangle_data:
                position = triangle_data["triangle_position"]
                if position > 0.7:
                    base_probability["upside"] *= 1.2
                    base_probability["downside"] *= 0.8
                elif position < 0.3:
                    base_probability["upside"] *= 0.8
                    base_probability["downside"] *= 1.2
            
            # Volume confirmation adjustment
            if volume_data is not None and len(volume_data) > 5:
                recent_volume = np.mean(volume_data[-3:])
                baseline_volume = np.mean(volume_data[-10:-3])
                
                if recent_volume > baseline_volume * 1.5:
                    # High volume increases breakout probability
                    base_probability["upside"] *= 1.15
                    base_probability["downside"] *= 1.15
            
            # Normalize to ensure probabilities sum to 1
            total_prob = base_probability["upside"] + base_probability["downside"]
            if total_prob > 0:
                base_probability["upside"] /= total_prob
                base_probability["downside"] /= total_prob
            
            # Apply strength multiplier
            final_probability = {
                "upside": min(0.95, base_probability["upside"] * strength_multiplier),
                "downside": min(0.95, base_probability["downside"] * strength_multiplier),
                "pattern_strength": pattern_strength,
                "volume_confirmed": volume_data is not None and len(volume_data) > 5
            }
            
            return final_probability
            
        except Exception as e:
            print(f"    Error calculating triangle breakout probability: {e}")
            return {"upside": 0.5, "downside": 0.5, "pattern_strength": 0.5, "volume_confirmed": False}

    def get_triangle_trading_signals(self, pivots: List[Dict[str, Any]], 
                                   current_price: float, volume_data: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """Get trading signals from triangle patterns."""
        try:
            # Find all triangle patterns
            ascending = self._find_ascending_triangles(pivots)
            descending = self._find_descending_triangles(pivots)
            symmetrical = self._find_symmetrical_triangles(pivots)
            
            all_triangles = ascending + descending + symmetrical
            
            if not all_triangles:
                return {
                    "signal": "NONE",
                    "confidence": 0.0,
                    "patterns_found": 0,
                    "message": "No triangle patterns detected"
                }
            
            # Find the strongest pattern
            best_pattern = max(all_triangles, key=lambda x: x.get("pattern_strength", 0))
            
            # Calculate breakout probability
            breakout_prob = self._calculate_triangle_breakout_probability(
                best_pattern, current_price, volume_data
            )
            
            # Determine signal
            signal = "NONE"
            confidence = 0.0
            
            pattern_type = best_pattern.get("pattern_type", "")
            upside_prob = breakout_prob.get("upside", 0.5)
            downside_prob = breakout_prob.get("downside", 0.5)
            pattern_strength = best_pattern.get("pattern_strength", 0.5)
            
            # Signal generation logic
            if pattern_type == "ascending_triangle" and upside_prob > 0.65:
                signal = "BUY"
                confidence = min(0.95, upside_prob * pattern_strength)
            elif pattern_type == "descending_triangle" and downside_prob > 0.65:
                signal = "SELL"
                confidence = min(0.95, downside_prob * pattern_strength)
            elif pattern_type == "symmetrical_triangle":
                if upside_prob > 0.6:
                    signal = "BUY"
                    confidence = min(0.85, upside_prob * pattern_strength)
                elif downside_prob > 0.6:
                    signal = "SELL"
                    confidence = min(0.85, downside_prob * pattern_strength)
            
            return {
                "signal": signal,
                "confidence": confidence,
                "patterns_found": len(all_triangles),
                "best_pattern": best_pattern,
                "breakout_probability": breakout_prob,
                "ascending_triangles": len(ascending),
                "descending_triangles": len(descending),
                "symmetrical_triangles": len(symmetrical),
                "all_patterns": all_triangles
            }
            
        except Exception as e:
            print(f"    Error getting triangle trading signals: {e}")
            return {
                "signal": "NONE",
                "confidence": 0.0,
                "patterns_found": 0,
                "error": str(e)
            }
    def calculate_fibonacci_trading_levels(self, fibonacci_data: Dict[str, Any], 
                                     current_price: float, trend_direction: str,
                                     ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """🎯 Calculate CORRECTED Entry, TP, SL từ Fibonacci analysis với TP targets chính xác"""
        try:
            print(f"        🎯 Calculating CORRECTED Fibonacci Trading Levels for {trend_direction} trend...")
            
            retracement_levels = fibonacci_data.get("retracement_levels", [])
            extension_levels = fibonacci_data.get("extension_levels", [])
            confluence_zones = fibonacci_data.get("confluence_zones", [])
            pivot_high = fibonacci_data.get("pivot_high", current_price * 1.1)
            pivot_low = fibonacci_data.get("pivot_low", current_price * 0.9)
            
            print(f"        📊 Input validation:")
            print(f"          Current price: {current_price:.8f}")
            print(f"          Pivot high: {pivot_high:.8f}")
            print(f"          Pivot low: {pivot_low:.8f}")
            print(f"          Trend: {trend_direction}")
            
            # ✅ ENHANCED SIGNAL DETERMINATION - More intelligent logic
            range_size = abs(pivot_high - pivot_low)
            range_position = (current_price - pivot_low) / range_size if range_size > 0 else 0.5
            
            # Smart signal determination based on trend and position
            if trend_direction == "UPTREND":
                # In uptrend, prefer BUY signals, especially if not at top
                if range_position < 0.8:  # Not too close to top
                    signal_type = "BUY"
                elif range_position < 0.9:  # Near top but still in uptrend
                    signal_type = "BUY"  # Still BUY but more conservative
                else:  # Very close to top
                    signal_type = "HOLD"  # Wait for pullback
            elif trend_direction == "DOWNTREND":
                # In downtrend, prefer SELL signals, especially if not at bottom
                if range_position > 0.2:  # Not too close to bottom
                    signal_type = "SELL"
                elif range_position > 0.1:  # Near bottom but still in downtrend
                    signal_type = "SELL"  # Still SELL but more conservative
                else:  # Very close to bottom
                    signal_type = "HOLD"  # Wait for bounce
            else:  # SIDEWAYS/UNKNOWN
                # Range-based signals
                if range_position > 0.65:
                    signal_type = "SELL"  # Near top of range
                elif range_position < 0.35:
                    signal_type = "BUY"   # Near bottom of range
                else:
                    signal_type = "HOLD"  # Middle of range
            
            print(f"        🎯 Signal determination:")
            print(f"          Range position: {range_position:.1%}")
            print(f"          Signal type: {signal_type}")
            
            # ✅ SKIP TRADING SETUP FOR HOLD SIGNALS
            if signal_type == "HOLD":
                return {
                    "has_trading_levels": False,
                    "signal_type": signal_type,
                    "reason": f"Position too extreme for {trend_direction} trend",
                    "range_position": range_position,
                    "recommendation": "Wait for better entry"
                }
            
            # ✅ SMART ENTRY CALCULATION
            entry_price = self._calculate_smart_fibonacci_entry(
                current_price, signal_type, retracement_levels, pivot_high, pivot_low, range_position
            )
            
            # ✅ CORRECTED STOP LOSS CALCULATION
            stop_loss = self._calculate_smart_fibonacci_stop_loss(
                entry_price, signal_type, retracement_levels, pivot_high, pivot_low, ohlcv_data
            )
            
            # ✅ CORRECTED TAKE PROFIT CALCULATION - LARGE PROGRESSIVE TARGETS
            take_profit_levels = self._calculate_corrected_fibonacci_take_profit_levels(
                entry_price, signal_type, extension_levels, retracement_levels, 
                pivot_high, pivot_low, confluence_zones
            )
            
            # Primary TP (largest target)
            primary_tp = take_profit_levels["primary_tp"]
            
            # ✅ VALIDATE LOGIC FOR BUY/SELL
            if signal_type == "BUY":
                # For BUY: entry < tp1 < tp2 < tp3 and stop_loss < entry
                if not (stop_loss < entry_price < take_profit_levels["tp1"] < take_profit_levels["tp2"] < take_profit_levels["tp3"]):
                    print(f"        🔧 Fixing BUY signal TP logic...")
                    # Fix the order
                    tp_base = entry_price * 1.02  # Start 2% above entry
                    take_profit_levels = {
                        "tp1": tp_base,
                        "tp2": tp_base * 1.05,  # 5% more than tp1
                        "tp3": tp_base * 1.12,  # 12% more than tp1
                        "primary_tp": tp_base * 1.12
                    }
                    primary_tp = take_profit_levels["primary_tp"]
            
            elif signal_type == "SELL":
                # For SELL: stop_loss > entry > tp1 > tp2 > tp3
                if not (take_profit_levels["tp3"] < take_profit_levels["tp2"] < take_profit_levels["tp1"] < entry_price < stop_loss):
                    print(f"        🔧 Fixing SELL signal TP logic...")
                    # Fix the order
                    tp_base = entry_price * 0.98  # Start 2% below entry
                    take_profit_levels = {
                        "tp1": tp_base,
                        "tp2": tp_base * 0.95,  # 5% below tp1
                        "tp3": tp_base * 0.88,  # 12% below tp1
                        "primary_tp": tp_base * 0.88
                    }
                    primary_tp = take_profit_levels["primary_tp"]
            
            # ✅ FINAL VALIDATION AND RISK/REWARD CALCULATION
            risk = abs(entry_price - stop_loss)
            reward = abs(primary_tp - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # ✅ ENSURE MINIMUM RISK/REWARD RATIO (2:1)
            if risk_reward_ratio < 2.0:
                print(f"        📊 Adjusting for minimum 2:1 R/R (current: {risk_reward_ratio:.2f})")
                if signal_type == "BUY":
                    # Extend TP3 to achieve 2.5:1 R/R
                    new_tp3 = entry_price + (risk * 2.5)
                    take_profit_levels["tp3"] = new_tp3
                    take_profit_levels["primary_tp"] = new_tp3
                    primary_tp = new_tp3
                else:
                    # Extend TP3 to achieve 2.5:1 R/R
                    new_tp3 = entry_price - (risk * 2.5)
                    take_profit_levels["tp3"] = new_tp3
                    take_profit_levels["primary_tp"] = new_tp3
                    primary_tp = new_tp3
                
                # Recalculate
                reward = abs(primary_tp - entry_price)
                risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # ✅ BUILD COMPREHENSIVE TRADING LEVELS
            trading_levels = {
                "has_trading_levels": True,
                "signal_type": signal_type,
                "entry_price": float(entry_price),
                "take_profit": float(primary_tp),
                "stop_loss": float(stop_loss),
                "risk_reward_ratio": float(risk_reward_ratio),
                
                # ✅ CORRECTED TP LEVELS with proper ordering
                "tp_levels": {
                    "tp1": float(take_profit_levels["tp1"]),
                    "tp2": float(take_profit_levels["tp2"]),
                    "tp3": float(take_profit_levels["tp3"]),
                    "tp4": float(take_profit_levels.get("tp4", primary_tp)),
                    "primary_tp": float(primary_tp)
                },
                
                # 📊 FIBONACCI ANALYSIS DETAILS
                "fibonacci_analysis": {
                    "pivot_high": pivot_high,
                    "pivot_low": pivot_low,
                    "trend_direction": trend_direction,
                    "range_size": range_size,
                    "range_position_pct": range_position * 100,
                    "fibonacci_levels_used": len(retracement_levels) + len(extension_levels),
                    "calculation_method": "corrected_progressive_fibonacci_targets"
                },
                
                # 🔧 CALCULATION METHODS
                "calculation_methods": {
                    "entry_method": "smart_fibonacci_trend_entry",
                    "tp_method": "corrected_progressive_fibonacci_targets",
                    "sl_method": "smart_fibonacci_invalidation",
                    "target_method": "multi_fibonacci_projection_corrected"
                },
                
                # 💡 RATIONALE
                "trading_rationale": {
                    "entry_reason": f"Smart Fibonacci {signal_type} entry for {trend_direction} (pos: {range_position:.1%})",
                    "tp_reason": f"Progressive Fibonacci targets with {risk_reward_ratio:.1f}:1 R/R",
                    "sl_reason": f"Smart invalidation stop loss",
                    "confidence_reason": f"High-quality structure: {len(retracement_levels)} ret + {len(extension_levels)} ext levels"
                }
            }
            
            print(f"        ✅ CORRECTED Fibonacci Trading Levels:")
            print(f"          Signal: {signal_type}")
            print(f"          Entry: {entry_price:.8f}")
            if signal_type == "BUY":
                print(f"          TP1: {take_profit_levels['tp1']:.8f} (+{((take_profit_levels['tp1']/entry_price-1)*100):.1f}%)")
                print(f"          TP2: {take_profit_levels['tp2']:.8f} (+{((take_profit_levels['tp2']/entry_price-1)*100):.1f}%)")
                print(f"          TP3: {take_profit_levels['tp3']:.8f} (+{((take_profit_levels['tp3']/entry_price-1)*100):.1f}%)")
                print(f"          SL:  {stop_loss:.8f} ({((stop_loss/entry_price-1)*100):.1f}%)")
            else:
                print(f"          TP1: {take_profit_levels['tp1']:.8f} (+{((entry_price-take_profit_levels['tp1'])/entry_price*100):.1f}%)")
                print(f"          TP2: {take_profit_levels['tp2']:.8f} (+{((entry_price-take_profit_levels['tp2'])/entry_price*100):.1f}%)")
                print(f"          TP3: {take_profit_levels['tp3']:.8f} (+{((entry_price-take_profit_levels['tp3'])/entry_price*100):.1f}%)")
                print(f"          SL:  {stop_loss:.8f} (-{((stop_loss-entry_price)/entry_price*100):.1f}%)")
            print(f"          Risk/Reward: {risk_reward_ratio:.2f}")
            
            return trading_levels
            
        except Exception as e:
            print(f"        ❌ Error calculating CORRECTED Fibonacci trading levels: {e}")
            return self._create_emergency_corrected_trading_levels(current_price, trend_direction)


    def _calculate_smart_fibonacci_entry(self, current_price: float, signal_type: str,
                                   retracement_levels: List[Dict], pivot_high: float, 
                                   pivot_low: float, range_position: float) -> float:
        """🎯 Calculate smart Fibonacci entry price"""
        try:
            # For BUY signals in UPTREND
            if signal_type == "BUY":
                # Look for good retracement entry points
                suitable_retracements = [
                    level for level in retracement_levels 
                    if level.get("price", 0) < current_price * 1.01  # Within 1% above current
                    and level.get("price", 0) > current_price * 0.97  # Not too far below
                ]
                
                if suitable_retracements:
                    # Use the strongest retracement level as entry
                    best_level = max(suitable_retracements, key=lambda x: x.get("strength", 0))
                    return float(best_level["price"]) * 0.999  # Slightly below for better fill
                else:
                    # Use current price with small discount
                    return current_price * 0.998  # 0.2% discount
            
            # For SELL signals in DOWNTREND
            elif signal_type == "SELL":
                # Look for good bounce entry points
                suitable_bounces = [
                    level for level in retracement_levels 
                    if level.get("price", 0) > current_price * 0.99  # Within 1% below current
                    and level.get("price", 0) < current_price * 1.03  # Not too far above
                ]
                
                if suitable_bounces:
                    # Use the strongest bounce level as entry
                    best_level = max(suitable_bounces, key=lambda x: x.get("strength", 0))
                    return float(best_level["price"]) * 1.001  # Slightly above for better fill
                else:
                    # Use current price with small premium
                    return current_price * 1.002  # 0.2% premium
            
            return current_price
            
        except Exception as e:
            print(f"          ❌ Error calculating smart entry: {e}")
            return current_price * (0.998 if signal_type == "BUY" else 1.002)

    def _calculate_smart_fibonacci_stop_loss(self, entry_price: float, signal_type: str,
                                        retracement_levels: List[Dict], pivot_high: float,
                                        pivot_low: float, ohlcv_data: pd.DataFrame) -> float:
        """🛡️ Calculate smart Fibonacci stop loss"""
        try:
            # Get recent ATR for dynamic SL
            recent_atr = self._calculate_simple_atr(ohlcv_data, period=14)
            
            if signal_type == "BUY":
                # For BUY: SL below key support
                
                # Method 1: Use strong retracement level below entry
                strong_retracements_below = [
                    level for level in retracement_levels 
                    if level.get("price", 0) < entry_price * 0.95  # At least 5% below entry
                    and level.get("strength", 0) > 0.6  # Strong level
                ]
                
                if strong_retracements_below:
                    # Use closest strong level below entry
                    closest_level = max(strong_retracements_below, key=lambda x: x.get("price", 0))
                    return float(closest_level["price"]) * 0.995  # 0.5% below level
                
                # Method 2: Use pivot low with buffer
                elif pivot_low > 0 and pivot_low < entry_price:
                    return max(
                        pivot_low * 0.98,  # 2% below pivot low
                        entry_price * 0.94  # Or 6% below entry, whichever is higher
                    )
                
                # Method 3: ATR-based SL
                else:
                    atr_stop = entry_price - (recent_atr * 2.5)
                    percentage_stop = entry_price * 0.95  # 5% stop
                    return max(atr_stop, percentage_stop)  # Use the closer one
            
            else:  # SELL signal
                # For SELL: SL above key resistance
                
                # Method 1: Use strong retracement level above entry
                strong_retracements_above = [
                    level for level in retracement_levels 
                    if level.get("price", 0) > entry_price * 1.05  # At least 5% above entry
                    and level.get("strength", 0) > 0.6  # Strong level
                ]
                
                if strong_retracements_above:
                    # Use closest strong level above entry
                    closest_level = min(strong_retracements_above, key=lambda x: x.get("price", 0))
                    return float(closest_level["price"]) * 1.005  # 0.5% above level
                
                # Method 2: Use pivot high with buffer
                elif pivot_high > 0 and pivot_high > entry_price:
                    return min(
                        pivot_high * 1.02,  # 2% above pivot high
                        entry_price * 1.06  # Or 6% above entry, whichever is lower
                    )
                
                # Method 3: ATR-based SL
                else:
                    atr_stop = entry_price + (recent_atr * 2.5)
                    percentage_stop = entry_price * 1.05  # 5% stop
                    return min(atr_stop, percentage_stop)  # Use the closer one
            
        except Exception as e:
            print(f"          ❌ Error calculating smart SL: {e}")
            # Emergency fallback
            if signal_type == "BUY":
                return entry_price * 0.96  # 4% stop loss
            else:
                return entry_price * 1.04  # 4% stop loss

    def _calculate_corrected_fibonacci_take_profit_levels(self, entry_price: float, signal_type: str,
                                                    extension_levels: List[Dict], retracement_levels: List[Dict],
                                                    pivot_high: float, pivot_low: float,
                                                    confluence_zones: List[Dict]) -> Dict[str, float]:
        """🎯 Calculate CORRECTED take profit targets với proper ordering"""
        try:
            print(f"        🎯 Calculating CORRECTED Take Profit targets for {signal_type}...")
            
            # 📊 METHOD 1: Fibonacci Extension Targets
            extension_targets = []
            if extension_levels:
                for ext in extension_levels:
                    ext_price = ext.get("price", 0)
                    if ext_price > 0:
                        if signal_type == "BUY" and ext_price > entry_price:
                            extension_targets.append(ext_price)
                        elif signal_type == "SELL" and ext_price < entry_price:
                            extension_targets.append(ext_price)
            
            # 📊 METHOD 2: Projected Targets from Range
            range_size = abs(pivot_high - pivot_low)
            if range_size > 0:
                range_targets = []
                projection_ratios = [1.0, 1.272, 1.618, 2.0, 2.618]
                
                for ratio in projection_ratios:
                    if signal_type == "BUY":
                        projected_target = entry_price + (range_size * ratio)
                        range_targets.append(projected_target)
                    else:
                        projected_target = entry_price - (range_size * ratio)
                        range_targets.append(projected_target)
                
                extension_targets.extend(range_targets)
            
            # 📊 METHOD 3: Confluence Zone Targets
            if confluence_zones:
                for zone in confluence_zones:
                    zone_price = zone.get("price", 0)
                    if zone_price > 0:
                        if signal_type == "BUY" and zone_price > entry_price:
                            extension_targets.append(zone_price)
                        elif signal_type == "SELL" and zone_price < entry_price:
                            extension_targets.append(zone_price)
            
            # ✅ SORT TARGETS CORRECTLY
            if signal_type == "BUY":
                # For BUY: Sort ascending (closest to farthest)
                valid_targets = sorted([t for t in extension_targets if t > entry_price])
            else:
                # For SELL: Sort descending (closest to farthest)
                valid_targets = sorted([t for t in extension_targets if t < entry_price], reverse=True)
            
            print(f"          Found {len(valid_targets)} valid targets")
            
            # ✅ ASSIGN TP LEVELS PROGRESSIVELY
            if len(valid_targets) >= 3:
                tp1 = valid_targets[0]  # Closest target
                tp2 = valid_targets[1]  # Medium target
                tp3 = valid_targets[2]  # Farthest target
            elif len(valid_targets) >= 2:
                tp1 = valid_targets[0]
                tp2 = valid_targets[1]
                # Create third target by extending
                if signal_type == "BUY":
                    tp3 = valid_targets[1] * 1.1  # 10% further
                else:
                    tp3 = valid_targets[1] * 0.9  # 10% further down
            else:
                # ✅ CREATE PROGRESSIVE TARGETS
                if signal_type == "BUY":
                    tp1 = entry_price * 1.03   # 3% profit
                    tp2 = entry_price * 1.06   # 6% profit
                    tp3 = entry_price * 1.12   # 12% profit
                else:
                    tp1 = entry_price * 0.97   # 3% profit
                    tp2 = entry_price * 0.94   # 6% profit
                    tp3 = entry_price * 0.88   # 12% profit
            
            # ✅ FINAL VALIDATION: Ensure correct order
            if signal_type == "BUY":
                # For BUY: tp1 < tp2 < tp3 (all above entry)
                if not (entry_price < tp1 < tp2 < tp3):
                    print(f"          🔧 Correcting BUY TP order...")
                    tp1 = entry_price * 1.025  # 2.5%
                    tp2 = entry_price * 1.055  # 5.5%
                    tp3 = entry_price * 1.095  # 9.5%
            else:
                # For SELL: tp1 > tp2 > tp3 (all below entry)
                if not (tp3 < tp2 < tp1 < entry_price):
                    print(f"          🔧 Correcting SELL TP order...")
                    tp1 = entry_price * 0.975  # 2.5%
                    tp2 = entry_price * 0.945  # 5.5%
                    tp3 = entry_price * 0.905  # 9.5%
            
            tp_levels = {
                "tp1": float(tp1),
                "tp2": float(tp2),
                "tp3": float(tp3),
                "tp4": float(tp3 * (1.05 if signal_type == "BUY" else 0.95)),  # Extended target
                "primary_tp": float(tp3)
            }
            
            print(f"        ✅ CORRECTED TP Levels:")
            print(f"          TP1: {tp1:.8f}")
            print(f"          TP2: {tp2:.8f}")
            print(f"          TP3: {tp3:.8f}")
            
            return tp_levels
            
        except Exception as e:
            print(f"        ❌ Error calculating corrected TP levels: {e}")
            # Emergency corrected targets
            if signal_type == "BUY":
                return {
                    "tp1": float(entry_price * 1.025),
                    "tp2": float(entry_price * 1.055),
                    "tp3": float(entry_price * 1.095),
                    "primary_tp": float(entry_price * 1.095)
                }
            else:
                return {
                    "tp1": float(entry_price * 0.975),
                    "tp2": float(entry_price * 0.945),
                    "tp3": float(entry_price * 0.905),
                    "primary_tp": float(entry_price * 0.905)
                }
    
    def _create_emergency_corrected_trading_levels(self, current_price: float, trend_direction: str) -> Dict[str, Any]:
        """🚨 Emergency corrected trading levels - cannot fail"""
        try:
            print(f"        🚨 Creating EMERGENCY corrected trading levels...")
            
            # Determine signal based on trend
            if trend_direction == "UPTREND":
                signal_type = "BUY"
                entry_price = current_price * 0.999
                tp1 = entry_price * 1.02   # 2%
                tp2 = entry_price * 1.04   # 4%
                tp3 = entry_price * 1.07   # 7%
                stop_loss = entry_price * 0.97  # -3%
            else:
                signal_type = "SELL"
                entry_price = current_price * 1.001
                tp1 = entry_price * 0.98   # -2%
                tp2 = entry_price * 0.96   # -4%
                tp3 = entry_price * 0.93   # -7%
                stop_loss = entry_price * 1.03  # +3%
            
            risk = abs(entry_price - stop_loss)
            reward = abs(tp3 - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 2.0
            
            return {
                "has_trading_levels": True,
                "signal_type": signal_type,
                "entry_price": float(entry_price),
                "take_profit": float(tp3),
                "stop_loss": float(stop_loss),
                "risk_reward_ratio": float(risk_reward_ratio),
                "tp_levels": {
                    "tp1": float(tp1),
                    "tp2": float(tp2),
                    "tp3": float(tp3),
                    "primary_tp": float(tp3)
                },
                "emergency_setup": True,
                "calculation_method": "emergency_corrected_fibonacci"
            }
            
        except Exception as e:
            print(f"        💀 Even emergency setup failed: {e}")
            return {"has_trading_levels": False, "error": str(e)}

    def _calculate_fibonacci_extension_targets(self, entry_price: float, trend_direction: str,
                                            extension_levels: List[Dict], pivot_high: float, 
                                            pivot_low: float) -> List[float]:
        """📊 Calculate Fibonacci extension targets"""
        try:
            targets = []
            
            # Get extension levels that make sense for trend direction
            if trend_direction == "DOWNTREND":
                # Look for extension levels below entry (bearish targets)
                relevant_extensions = [ext for ext in extension_levels 
                                    if ext.get("price", 0) < entry_price]
            else:
                # Look for extension levels above entry (bullish targets)
                relevant_extensions = [ext for ext in extension_levels 
                                    if ext.get("price", 0) > entry_price]
            
            # Add extension targets
            for ext in relevant_extensions:
                price = ext.get("price", 0)
                strength = ext.get("strength", 0)
                ratio = ext.get("ratio", 0)
                
                # Prioritize key Fibonacci extension ratios
                if ratio in [1.272, 1.414, 1.618, 2.0, 2.618]:
                    targets.append(price)
            
            # Calculate additional harmonic extensions
            range_size = abs(pivot_high - pivot_low)
            if range_size > 0:
                harmonic_ratios = [1.272, 1.414, 1.618, 2.0, 2.618, 3.618]
                
                for ratio in harmonic_ratios:
                    if trend_direction == "DOWNTREND":
                        harmonic_target = pivot_low - (range_size * (ratio - 1))
                        if harmonic_target < entry_price:
                            targets.append(harmonic_target)
                    else:
                        harmonic_target = pivot_high + (range_size * (ratio - 1))
                        if harmonic_target > entry_price:
                            targets.append(harmonic_target)
            
            return targets[:8]  # Return top 8 targets
            
        except Exception as e:
            print(f"    Error calculating extension targets: {e}")
            return []

    def _calculate_fibonacci_projection_targets(self, entry_price: float, trend_direction: str,
                                            retracement_levels: List[Dict], pivot_high: float,
                                            pivot_low: float) -> List[float]:
        """📊 Calculate Fibonacci projection targets"""
        try:
            targets = []
            range_size = abs(pivot_high - pivot_low)
            
            if range_size <= 0:
                return []
            
            # Project based on measured moves
            projection_ratios = [1.0, 1.272, 1.414, 1.618, 2.0, 2.618]
            
            for ratio in projection_ratios:
                if trend_direction == "DOWNTREND":
                    # Project downward from entry
                    projected_target = entry_price - (range_size * ratio)
                    targets.append(projected_target)
                    
                    # Also project from pivot high
                    alt_target = pivot_high - (range_size * ratio * 1.5)
                    targets.append(alt_target)
                else:
                    # Project upward from entry
                    projected_target = entry_price + (range_size * ratio)
                    targets.append(projected_target)
                    
                    # Also project from pivot low
                    alt_target = pivot_low + (range_size * ratio * 1.5)
                    targets.append(alt_target)
            
            return targets[:10]  # Return top 10 targets
            
        except Exception as e:
            print(f"    Error calculating projection targets: {e}")
            return []

    def _calculate_confluence_zone_targets(self, entry_price: float, trend_direction: str,
                                        confluence_zones: List[Dict]) -> List[float]:
        """📊 Calculate confluence zone targets"""
        try:
            targets = []
            
            if not confluence_zones:
                return []
            
            for zone in confluence_zones:
                zone_price = zone.get("price", 0)
                zone_strength = zone.get("strength", 0)
                
                # Only use strong confluence zones as targets
                if zone_strength >= 2:
                    if trend_direction == "DOWNTREND" and zone_price < entry_price:
                        targets.append(zone_price)
                    elif trend_direction != "DOWNTREND" and zone_price > entry_price:
                        targets.append(zone_price)
            
            return targets[:5]  # Return top 5 targets
            
        except Exception as e:
            print(f"    Error calculating confluence targets: {e}")
            return []

    def _calculate_harmonic_pattern_targets(self, entry_price: float, trend_direction: str,
                                        pivot_high: float, pivot_low: float) -> List[float]:
        """📊 Calculate harmonic pattern targets"""
        try:
            targets = []
            range_size = abs(pivot_high - pivot_low)
            
            if range_size <= 0:
                return []
            
            # Harmonic ratios from various patterns (Gartley, Butterfly, Bat, etc.)
            harmonic_ratios = [0.786, 0.886, 1.13, 1.27, 1.414, 1.618, 2.0, 2.618, 3.618]
            
            for ratio in harmonic_ratios:
                if trend_direction == "DOWNTREND":
                    # Calculate bearish harmonic targets
                    harmonic_target = pivot_low - (range_size * ratio * 0.618)
                    if harmonic_target < entry_price:
                        targets.append(harmonic_target)
                else:
                    # Calculate bullish harmonic targets
                    harmonic_target = pivot_high + (range_size * ratio * 0.618)
                    if harmonic_target > entry_price:
                        targets.append(harmonic_target)
            
            return targets[:6]  # Return top 6 targets
            
        except Exception as e:
            print(f"    Error calculating harmonic targets: {e}")
            return []

    def _get_key_retracement_level(self, retracement_levels: List[Dict[str, Any]], current_price: float) -> Dict[str, float]:
        """🎯 Get key retracement level near current price"""
        try:
            if not retracement_levels:
                return {}
            
            # Find the retracement level closest to current price
            nearest_level = min(retracement_levels, 
                            key=lambda x: abs(float(x.get("price", 0)) - current_price))
            
            return {
                "ratio": float(nearest_level.get("ratio", 0)),
                "price": float(nearest_level.get("price", 0)),
                "strength": float(nearest_level.get("strength", 0)),
                "distance_pct": abs(float(nearest_level.get("price", 0)) - current_price) / current_price * 100
            }
            
        except Exception:
            return {}

    def _get_key_extension_level(self, extension_levels: List[Dict[str, Any]], current_price: float) -> Dict[str, float]:
        """🎯 Get key extension level for targets"""
        try:
            if not extension_levels:
                return {}
            
            # Find the strongest extension level
            strongest_level = max(extension_levels, 
                                key=lambda x: float(x.get("strength", 0)))
            
            return {
                "ratio": float(strongest_level.get("ratio", 0)),
                "price": float(strongest_level.get("price", 0)),
                "strength": float(strongest_level.get("strength", 0)),
                "distance_pct": abs(float(strongest_level.get("price", 0)) - current_price) / current_price * 100
            }
            
        except Exception:
            return {}

    def _calculate_simple_atr(self, ohlcv_data: pd.DataFrame, period: int = 14) -> float:
        """📊 Calculate simple ATR for stop loss calculation"""
        try:
            if len(ohlcv_data) < period:
                return float(ohlcv_data['close'].iloc[-1]) * 0.02  # 2% fallback
            
            high_low = ohlcv_data['high'] - ohlcv_data['low']
            high_close = np.abs(ohlcv_data['high'] - ohlcv_data['close'].shift(1))
            low_close = np.abs(ohlcv_data['low'] - ohlcv_data['close'].shift(1))
            
            true_range_series = pd.Series(np.maximum(high_low, np.maximum(high_close, low_close)))
            atr_value = true_range_series.rolling(window=period).mean().iloc[-1]
            
            return float(atr_value) if not pd.isna(atr_value) else float(ohlcv_data['close'].iloc[-1]) * 0.02
            
        except Exception:
            return float(ohlcv_data['close'].iloc[-1]) * 0.02