#!/usr/bin/env python3
"""
🧪 Test Main Bot - Test main bot with chart generation debug
"""

import os
import sys
import time
from datetime import datetime

def test_main_bot():
    """🧪 Test main bot with chart generation."""
    print(f"🧪 MAIN BOT TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import main bot
        from main_bot import TradingBot
        
        print(f"📦 Importing TradingBot...")
        
        # Initialize bot
        print(f"🚀 Initializing TradingBot...")
        bot = TradingBot()
        
        print(f"✅ Bot initialized successfully")
        
        # Force enable chart system
        print(f"🔧 Force enabling chart system...")
        bot.force_enable_chart_system()
        
        # Debug chart system status
        print(f"🔍 Debugging chart system status...")
        bot.debug_chart_system_status()
        
        # Run one cycle of analysis
        print(f"🔄 Running one cycle of enhanced analysis...")
        print(f"⏰ Starting at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
        
        # Run the main analysis loop once
        try:
            # This will run one cycle and should generate charts
            bot.run_enhanced_analysis()
            print(f"✅ Enhanced analysis cycle completed")
            return True
            
        except KeyboardInterrupt:
            print(f"⚠️ Analysis interrupted by user")
            return True
            
        except Exception as analysis_error:
            print(f"❌ Analysis error: {analysis_error}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"🚀 MAIN BOT TESTING UTILITY")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    success = test_main_bot()
    
    if success:
        print(f"\n🎉 MAIN BOT TEST COMPLETED!")
        print(f"📱 Check your Telegram for charts with detailed reports")
        print(f"🔍 Look for debug output above to see chart generation status")
    else:
        print(f"\n💥 MAIN BOT TEST FAILED!")
        print(f"🔍 Check the logs above for error details")
