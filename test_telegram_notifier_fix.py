#!/usr/bin/env python3
"""
🔧 TELEGRAM NOTIFIER FIX TEST
Test that telegram notifier initialization and env reading are fixed
"""

import sys
import os
from unittest.mock import Mock, patch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_env_file_reading():
    """Test .env file reading."""
    print("🔧 TESTING .ENV FILE READING")
    print("=" * 50)
    
    try:
        # Test 1: Check .env file exists
        print("\n🔍 TEST 1: Check .env file exists")
        
        env_file_exists = os.path.exists('.env')
        print(f"   📊 .env file exists: {env_file_exists}")
        
        if not env_file_exists:
            print("❌ .env file not found")
            return False
        
        # Test 2: Check required variables
        print("\n🔍 TEST 2: Check required variables in .env")
        
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHAT_ID',
            'TELEGRAM_FIBONACCI_ZIGZAG_FOURIER',
            'TELEGRAM_VOLUME_PROFILE_POINT_FIGURE',
            'TELEGRAM_AI_ANALYSIS'
        ]
        
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        missing_vars = []
        for var in required_vars:
            if var not in env_content:
                missing_vars.append(var)
            else:
                print(f"   ✅ {var}: Found")
        
        if missing_vars:
            print(f"   ❌ Missing variables: {missing_vars}")
            return False
        else:
            print("   ✅ All required variables found")
        
        # Test 3: Check variable values
        print("\n🔍 TEST 3: Check variable values")
        
        # Extract values
        lines = env_content.split('\n')
        env_vars = {}
        
        for line in lines:
            if '=' in line and not line.strip().startswith('#'):
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
        
        # Check bot token format
        bot_token = env_vars.get('TELEGRAM_BOT_TOKEN', '')
        if ':' in bot_token and len(bot_token) > 20:
            print("   ✅ TELEGRAM_BOT_TOKEN: Valid format")
        else:
            print("   ❌ TELEGRAM_BOT_TOKEN: Invalid format")
            return False
        
        # Check chat IDs format
        chat_id = env_vars.get('TELEGRAM_CHAT_ID', '')
        if chat_id.startswith('-') and len(chat_id) > 10:
            print("   ✅ TELEGRAM_CHAT_ID: Valid format")
        else:
            print("   ❌ TELEGRAM_CHAT_ID: Invalid format")
            return False
        
        print("\n✅ .env file reading test passed")
        return True
        
    except Exception as e:
        print(f"\n❌ .env file reading test failed: {e}")
        return False

def test_telegram_notifier_import():
    """Test telegram notifier import."""
    print("\n🔧 TESTING TELEGRAM NOTIFIER IMPORT")
    print("=" * 50)
    
    try:
        # Test 1: Import telegram_notifier module
        print("\n🔍 TEST 1: Import telegram_notifier module")
        
        import telegram_notifier
        print("   ✅ telegram_notifier module imported successfully")
        
        # Test 2: Check EnhancedTelegramNotifier class
        print("\n🔍 TEST 2: Check EnhancedTelegramNotifier class")
        
        has_enhanced = hasattr(telegram_notifier, 'EnhancedTelegramNotifier')
        print(f"   📊 Has EnhancedTelegramNotifier: {has_enhanced}")
        
        if has_enhanced:
            print("   ✅ EnhancedTelegramNotifier class found")
        else:
            print("   ❌ EnhancedTelegramNotifier class not found")
            return False
        
        # Test 3: Check TelegramNotifier fallback class
        print("\n🔍 TEST 3: Check TelegramNotifier fallback class")
        
        has_basic = hasattr(telegram_notifier, 'TelegramNotifier')
        print(f"   📊 Has TelegramNotifier: {has_basic}")
        
        if has_basic:
            print("   ✅ TelegramNotifier fallback class found")
        else:
            print("   ⚠️ TelegramNotifier fallback class not found")
        
        # Test 4: Test initialization with mock data
        print("\n🔍 TEST 4: Test initialization with mock data")
        
        try:
            # Mock initialization (won't actually connect)
            notifier = telegram_notifier.EnhancedTelegramNotifier(
                bot_token="test_token",
                chat_id="-1001234567890",
                rate_limit_delay=0.4,
                max_retries=3,
                use_queue=False  # Disable queue for testing
            )
            print("   ✅ EnhancedTelegramNotifier initialization successful")
        except Exception as init_error:
            print(f"   ❌ EnhancedTelegramNotifier initialization failed: {init_error}")
            return False
        
        print("\n✅ Telegram notifier import test passed")
        return True
        
    except Exception as e:
        print(f"\n❌ Telegram notifier import test failed: {e}")
        return False

def test_main_bot_integration():
    """Test main bot integration."""
    print("\n🔧 TESTING MAIN BOT INTEGRATION")
    print("=" * 50)
    
    try:
        # Test 1: Check COMMUNICATION_MODULES
        print("\n🔍 TEST 1: Check COMMUNICATION_MODULES")
        
        import main_bot
        
        has_telegram_notifier = 'telegram_notifier' in main_bot.COMMUNICATION_MODULES
        print(f"   📊 telegram_notifier in COMMUNICATION_MODULES: {has_telegram_notifier}")
        
        if has_telegram_notifier:
            print("   ✅ telegram_notifier module available in COMMUNICATION_MODULES")
        else:
            print("   ❌ telegram_notifier module not in COMMUNICATION_MODULES")
            print(f"   📊 Available modules: {list(main_bot.COMMUNICATION_MODULES.keys())}")
            return False
        
        # Test 2: Check environment variables loading
        print("\n🔍 TEST 2: Check environment variables loading")
        
        has_bot_token = hasattr(main_bot, 'TELEGRAM_BOT_TOKEN')
        has_chat_id = hasattr(main_bot, 'TELEGRAM_CHAT_ID')
        
        print(f"   📊 TELEGRAM_BOT_TOKEN loaded: {has_bot_token}")
        print(f"   📊 TELEGRAM_CHAT_ID loaded: {has_chat_id}")
        
        if has_bot_token and has_chat_id:
            print("   ✅ Environment variables loaded successfully")
        else:
            print("   ❌ Environment variables not loaded properly")
            return False
        
        # Test 3: Check specialized chats
        print("\n🔍 TEST 3: Check specialized chats")
        
        has_specialized_chats = hasattr(main_bot, 'TELEGRAM_SPECIALIZED_CHATS')
        print(f"   📊 TELEGRAM_SPECIALIZED_CHATS loaded: {has_specialized_chats}")
        
        if has_specialized_chats:
            specialized_count = len(main_bot.TELEGRAM_SPECIALIZED_CHATS)
            print(f"   ✅ Specialized chats loaded: {specialized_count} chats")
        else:
            print("   ❌ Specialized chats not loaded")
            return False
        
        print("\n✅ Main bot integration test passed")
        return True
        
    except Exception as e:
        print(f"\n❌ Main bot integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 STARTING TELEGRAM NOTIFIER FIX VERIFICATION")
    print("=" * 60)
    
    # Test .env file reading
    env_success = test_env_file_reading()
    
    # Test telegram notifier import
    import_success = test_telegram_notifier_import()
    
    # Test main bot integration
    integration_success = test_main_bot_integration()
    
    overall_success = env_success and import_success and integration_success
    
    print(f"\n🎯 FINAL RESULTS: {'SUCCESS' if overall_success else 'FAILED'}")
    
    if overall_success:
        print("🎉 TELEGRAM NOTIFIER FIX SUCCESSFUL!")
        print("\n✅ Production ready:")
        print("  🔧 .env file reading working")
        print("  📊 telegram_notifier module importing correctly")
        print("  🚀 COMMUNICATION_MODULES populated")
        print("  📱 Environment variables loaded")
        print("  ✅ Specialized chats configured")
        print("\n📊 Expected Production Behavior:")
        print("  - Telegram notifier will initialize successfully")
        print("  - No more 'NoneType' object errors")
        print("  - Photo and message sending will work")
        print("  - Chat ID recognition will work properly")
    else:
        print("❌ Telegram notifier fix needs attention")
        print("\n🔧 Issues to check:")
        if not env_success:
            print("  ❌ .env file reading issues")
        if not import_success:
            print("  ❌ telegram_notifier import issues")
        if not integration_success:
            print("  ❌ Main bot integration issues")
    
    print(f"\n🎯 Final result: {'SUCCESS' if overall_success else 'FAILED'}")
    sys.exit(0 if overall_success else 1)
