#!/usr/bin/env python3
"""
🎨 Test script để kiểm tra Beautiful Charts functionality
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_sample_ohlcv_data():
    """Tạo dữ liệu OHLCV mẫu để test"""
    try:
        # Create 100 candles of sample data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=100), 
                             end=datetime.now(), freq='1H')
        
        # Generate realistic price data
        base_price = 50000  # Starting price
        prices = []
        
        for i in range(len(dates)):
            # Add some randomness
            change = np.random.normal(0, 0.02)  # 2% volatility
            if i == 0:
                price = base_price
            else:
                price = prices[-1] * (1 + change)
            prices.append(price)
        
        # Create OHLCV data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            high = price * (1 + abs(np.random.normal(0, 0.01)))
            low = price * (1 - abs(np.random.normal(0, 0.01)))
            open_price = price * (1 + np.random.normal(0, 0.005))
            close_price = price * (1 + np.random.normal(0, 0.005))
            volume = np.random.uniform(1000, 10000)
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ Created sample OHLCV data: {len(df)} candles")
        print(f"  📊 Price range: {df['low'].min():.2f} - {df['high'].max():.2f}")
        print(f"  📈 Current price: {df['close'].iloc[-1]:.2f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return None

def create_sample_fibonacci_data():
    """Tạo dữ liệu Fibonacci mẫu"""
    return {
        'retracement_levels': [
            {'ratio': 0.236, 'price': 51200, 'strength': 0.8},
            {'ratio': 0.382, 'price': 50800, 'strength': 0.9},
            {'ratio': 0.618, 'price': 50200, 'strength': 0.7},
            {'ratio': 0.786, 'price': 49800, 'strength': 0.6}
        ],
        'extension_levels': [
            {'ratio': 1.272, 'price': 52500, 'strength': 0.7},
            {'ratio': 1.618, 'price': 53200, 'strength': 0.8}
        ],
        'trend_direction': 'UPTREND',
        'confidence': 0.85
    }

def create_sample_volume_profile_data():
    """Tạo dữ liệu Volume Profile mẫu"""
    return {
        'vpoc': {'price': 50500, 'volume': 15000, 'percentage_of_total': 12.5},
        'value_area': {'high': 51000, 'low': 50000, 'percentage': 70},
        'volume_profile': {'total_volume': 120000},
        'support_resistance_levels': [
            {'price': 50000, 'type': 'support', 'strength': 0.9},
            {'price': 51000, 'type': 'resistance', 'strength': 0.8}
        ]
    }

def test_beautiful_chart_generation():
    """Test beautiful chart generation"""
    print("🎨 TESTING BEAUTIFUL CHART GENERATION")
    print("=" * 60)
    
    try:
        # Import chart generator
        from chart_generator import EnhancedChartGenerator
        
        # Create mock telegram notifier
        class MockTelegramNotifier:
            def send_photo(self, photo_path, caption, chat_id, parse_mode="HTML"):
                print(f"    📤 MOCK SEND: {os.path.basename(photo_path)} → {chat_id}")
                return True
        
        # Initialize chart generator
        mock_notifier = MockTelegramNotifier()
        chart_gen = EnhancedChartGenerator(
            output_dir="test_beautiful_charts", 
            telegram_notifier=mock_notifier,
            auto_send_charts=False  # Don't auto-send for testing
        )
        
        print(f"✅ Chart generator initialized")
        print(f"  🎨 Color schemes available: {len(chart_gen.color_schemes)}")
        
        # Create sample data
        ohlcv_data = create_sample_ohlcv_data()
        if ohlcv_data is None:
            return False
        
        current_price = ohlcv_data['close'].iloc[-1]
        
        # Test different chart types
        test_results = {}
        
        # 1. Test Fibonacci chart
        print(f"\n🌀 Testing Beautiful Fibonacci Chart...")
        fibonacci_data = create_sample_fibonacci_data()
        
        fib_chart = chart_gen.generate_beautiful_minimal_chart(
            coin="BTC/USDT",
            chart_type="fibonacci",
            ohlcv_data=ohlcv_data,
            current_price=current_price,
            data=fibonacci_data
        )
        
        test_results['fibonacci'] = fib_chart is not None and os.path.exists(fib_chart) if fib_chart else False
        print(f"  📊 Fibonacci chart: {'✅ SUCCESS' if test_results['fibonacci'] else '❌ FAILED'}")
        if fib_chart:
            file_size = os.path.getsize(fib_chart) / 1024
            print(f"    📁 File: {os.path.basename(fib_chart)} ({file_size:.1f}KB)")
        
        # 2. Test Volume Profile chart
        print(f"\n📊 Testing Beautiful Volume Profile Chart...")
        volume_data = create_sample_volume_profile_data()
        
        vol_chart = chart_gen.generate_beautiful_minimal_chart(
            coin="ETH/USDT",
            chart_type="volume_profile",
            ohlcv_data=ohlcv_data,
            current_price=current_price,
            data=volume_data
        )
        
        test_results['volume_profile'] = vol_chart is not None and os.path.exists(vol_chart) if vol_chart else False
        print(f"  📊 Volume Profile chart: {'✅ SUCCESS' if test_results['volume_profile'] else '❌ FAILED'}")
        if vol_chart:
            file_size = os.path.getsize(vol_chart) / 1024
            print(f"    📁 File: {os.path.basename(vol_chart)} ({file_size:.1f}KB)")
        
        # 3. Test Pump Alert chart
        print(f"\n🚀 Testing Beautiful Pump Alert Chart...")
        pump_data = {'pump_probability': 0.85, 'intensity': 2.5}
        
        pump_chart = chart_gen.generate_beautiful_minimal_chart(
            coin="DOGE/USDT",
            chart_type="pump_alert",
            ohlcv_data=ohlcv_data,
            current_price=current_price,
            data=pump_data
        )
        
        test_results['pump_alert'] = pump_chart is not None and os.path.exists(pump_chart) if pump_chart else False
        print(f"  📊 Pump Alert chart: {'✅ SUCCESS' if test_results['pump_alert'] else '❌ FAILED'}")
        if pump_chart:
            file_size = os.path.getsize(pump_chart) / 1024
            print(f"    📁 File: {os.path.basename(pump_chart)} ({file_size:.1f}KB)")
        
        # 4. Test minimal chart (no specific data)
        print(f"\n🎨 Testing Beautiful Minimal Chart...")
        minimal_chart = chart_gen.generate_beautiful_minimal_chart(
            coin="ADA/USDT",
            chart_type="minimal",
            ohlcv_data=ohlcv_data,
            current_price=current_price
        )
        
        test_results['minimal'] = minimal_chart is not None and os.path.exists(minimal_chart) if minimal_chart else False
        print(f"  📊 Minimal chart: {'✅ SUCCESS' if test_results['minimal'] else '❌ FAILED'}")
        if minimal_chart:
            file_size = os.path.getsize(minimal_chart) / 1024
            print(f"    📁 File: {os.path.basename(minimal_chart)} ({file_size:.1f}KB)")
        
        return test_results
        
    except ImportError as e:
        print(f"❌ Cannot import EnhancedChartGenerator: {e}")
        return False
    except Exception as e:
        print(f"❌ Error in beautiful chart generation test: {e}")
        return False

def test_minimal_captions():
    """Test minimal caption generation"""
    print("\n📝 TESTING MINIMAL CAPTIONS")
    print("=" * 60)
    
    try:
        from chart_generator import EnhancedChartGenerator
        
        chart_gen = EnhancedChartGenerator(output_dir="test_captions")
        
        # Test data
        current_price = 50000.12345678
        fibonacci_data = create_sample_fibonacci_data()
        volume_data = create_sample_volume_profile_data()
        
        # Test captions
        captions = {}
        
        # Fibonacci caption
        fib_caption = chart_gen._create_minimal_fibonacci_caption("BTC/USDT", fibonacci_data, current_price)
        captions['fibonacci'] = len(fib_caption) < 200  # Should be minimal
        print(f"🌀 Fibonacci caption: {'✅ MINIMAL' if captions['fibonacci'] else '❌ TOO LONG'}")
        print(f"  📏 Length: {len(fib_caption)} chars")
        
        # Volume Profile caption
        vol_caption = chart_gen._create_minimal_volume_profile_caption("ETH/USDT", volume_data, current_price)
        captions['volume_profile'] = len(vol_caption) < 200
        print(f"📊 Volume Profile caption: {'✅ MINIMAL' if captions['volume_profile'] else '❌ TOO LONG'}")
        print(f"  📏 Length: {len(vol_caption)} chars")
        
        # Pump Alert caption
        pump_data = {'pump_probability': 0.85, 'intensity': 2.5}
        pump_caption = chart_gen._create_minimal_pump_alert_caption("DOGE/USDT", pump_data, current_price)
        captions['pump_alert'] = len(pump_caption) < 200
        print(f"🚀 Pump Alert caption: {'✅ MINIMAL' if captions['pump_alert'] else '❌ TOO LONG'}")
        print(f"  📏 Length: {len(pump_caption)} chars")
        
        return all(captions.values())
        
    except Exception as e:
        print(f"❌ Error testing minimal captions: {e}")
        return False

def cleanup_test_files():
    """Cleanup test files"""
    try:
        test_dirs = ["test_beautiful_charts", "test_captions"]
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                import shutil
                shutil.rmtree(test_dir)
                print(f"🧹 Cleaned up: {test_dir}")
    except Exception as e:
        print(f"⚠️ Error cleaning up: {e}")

def main():
    """Main test function"""
    print("🎨 BEAUTIFUL CHARTS FUNCTIONALITY TEST")
    print("=" * 70)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Beautiful chart generation
    chart_results = test_beautiful_chart_generation()
    
    # Test 2: Minimal captions
    caption_result = test_minimal_captions()
    
    # Cleanup
    cleanup_test_files()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 BEAUTIFUL CHARTS TEST RESULTS")
    print("=" * 70)
    
    if isinstance(chart_results, dict):
        print("📊 Chart Generation Results:")
        for chart_type, result in chart_results.items():
            status = "✅ SUCCESS" if result else "❌ FAILED"
            print(f"  {chart_type.replace('_', ' ').title():<20}: {status}")
        
        all_charts_passed = all(chart_results.values())
    else:
        all_charts_passed = False
        print("❌ Chart generation test failed")
    
    print(f"\n📝 Caption Generation: {'✅ SUCCESS' if caption_result else '❌ FAILED'}")
    
    print()
    if all_charts_passed and caption_result:
        print("🎉 ALL BEAUTIFUL CHARTS TESTS PASSED!")
        print("✅ Beautiful minimal charts are working")
        print("✅ Modern color schemes applied")
        print("✅ Minimal captions generated")
        print("✅ Charts optimized for Telegram")
        
        print(f"\n🎨 Key improvements:")
        print(f"  • Dark modern themes with vibrant colors")
        print(f"  • Minimal information on charts")
        print(f"  • Clean candlestick design")
        print(f"  • Optimized file sizes for Telegram")
        print(f"  • Beautiful typography and spacing")
        print(f"  • High contrast for mobile viewing")
    else:
        print("❌ SOME TESTS FAILED")
        print("Beautiful charts functionality may need attention")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
