#!/usr/bin/env python3
"""
🧪 TEST: Consensus Signal Detailed Information
Test để kiểm tra consensus signal có gửi thông tin chi tiết đầy đủ
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from telegram_notifier import EnhancedTelegramNotifier

def create_test_consensus_data():
    """Create test consensus data with contributing algorithms"""
    return {
        'consensus_score': 0.639,
        'confidence': 0.75,
        'signal_quality': {
            'strength': 0.82,
            'overall_quality': 0.78,
            'tp_sl_methods_count': 4,
            'algorithm_diversity': 5
        },
        'contributing_algorithms': [
            {
                'algorithm': 'AI Analysis',
                'signal': 'SELL',
                'confidence': 0.85
            },
            {
                'algorithm': 'Fibonacci',
                'signal': 'SELL',
                'confidence': 0.72
            },
            {
                'algorithm': 'Volume Profile',
                'signal': 'SELL',
                'confidence': 0.68
            },
            {
                'algorithm': 'Point Figure',
                'signal': 'SELL',
                'confidence': 0.71
            },
            {
                'algorithm': 'Fourier Analysis',
                'signal': 'SELL',
                'confidence': 0.63
            }
        ]
    }

def create_test_signal_data():
    """Create test signal data"""
    return {
        'signal_id': 'SIG_BTC/USDT_1749972075',
        'coin': 'BTC/USDT',
        'coin_category': 'MAJOR',
        'signal_type': 'SELL',
        'entry': 105367.43000000,
        'take_profit': 98749.20776578,
        'stop_loss': 110989.82100000,
        'risk_reward_ratio': 1.18,
        'primary_tf': '4h',
        'tp_sl_methods': ['ATR', 'Fibonacci', 'Volume Profile', 'Support/Resistance'],
        'tp_sl_confidence': 0.78,
        'enhancement_features': {
            'volume_analysis': {
                'volume_spike_factor': 2.3
            },
            'market_structure': {
                'trend_strength': 0.72
            },
            'confluence_zones': [
                {'price': 98500, 'strength': 0.8},
                {'price': 99200, 'strength': 0.6}
            ]
        },
        'high_confidence': True,
        'multi_timeframe_confirmed': True
    }

def test_detailed_consensus_caption():
    """Test detailed consensus signal caption generation"""
    print("\n🧪 === TESTING DETAILED CONSENSUS CAPTION ===")
    
    try:
        # Initialize notifier (without actually sending)
        notifier = EnhancedTelegramNotifier(
            bot_token="test_token",
            chat_id="-1002301937119"
        )
        
        # Create test data
        consensus_data = create_test_consensus_data()
        signal_data = create_test_signal_data()
        
        # Generate detailed caption
        detailed_caption = notifier._create_detailed_consensus_signal_caption(
            coin="BTC/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data
        )
        
        print("✅ Detailed caption generated successfully!")
        print("\n📝 GENERATED CAPTION:")
        print("=" * 60)
        print(detailed_caption)
        print("=" * 60)
        
        # Check if caption contains expected elements
        expected_elements = [
            "CONSENSUS SIGNAL - BTC/USDT",
            "SELL",
            "105367.43000000",  # Entry
            "98749.20776578",   # Take Profit
            "110989.82100000",  # Stop Loss
            "1.18",             # Risk/Reward
            "0.639",            # Consensus Score
            "SIG_BTC/USDT_1749972075",  # Signal ID
            "CONTRIBUTING ALGORITHMS (5):",
            "AI Analysis: SELL",
            "Fibonacci: SELL",
            "Volume Profile: SELL",
            "SIGNAL QUALITY:",
            "Strength: 0.82",
            "Overall Quality: 0.78",
            "TP/SL Methods: 4",
            "Algorithm Diversity: 5",
            "ENHANCEMENT FEATURES:",
            "Volume Spike: 2.3x",
            "Trend Strength: 0.72",
            "Confluence Zones: 2"
        ]
        
        missing_elements = []
        for element in expected_elements:
            if element not in detailed_caption:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"\n⚠️ Missing elements in caption:")
            for element in missing_elements:
                print(f"  - {element}")
            return False
        else:
            print(f"\n✅ All expected elements found in caption!")
            return True
            
    except Exception as e:
        print(f"❌ Error testing detailed caption: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consensus_signal_method():
    """Test the full consensus signal sending method (without actually sending)"""
    print("\n🧪 === TESTING CONSENSUS SIGNAL METHOD ===")
    
    try:
        # Initialize notifier
        notifier = EnhancedTelegramNotifier(
            bot_token="test_token",
            chat_id="-1002301937119"
        )
        
        # Override send_message and send_photo to prevent actual sending
        original_send_message = notifier.send_message
        original_send_photo = notifier.send_photo
        
        sent_messages = []
        sent_photos = []
        
        def mock_send_message(message, chat_id=None, parse_mode="HTML"):
            sent_messages.append({
                'message': message,
                'chat_id': chat_id,
                'parse_mode': parse_mode
            })
            print(f"📤 MOCK: Would send message to {chat_id}")
            return True
        
        def mock_send_photo(photo_path, caption="", chat_id=None, parse_mode="HTML"):
            sent_photos.append({
                'photo_path': photo_path,
                'caption': caption,
                'chat_id': chat_id,
                'parse_mode': parse_mode
            })
            print(f"📸 MOCK: Would send photo to {chat_id}")
            return True
        
        notifier.send_message = mock_send_message
        notifier.send_photo = mock_send_photo
        
        # Create test data
        consensus_data = create_test_consensus_data()
        signal_data = create_test_signal_data()
        
        # Test consensus signal method
        result = notifier.send_consensus_signal(
            coin="BTC/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data,
            use_html=True,
            ohlcv_data=None,  # No chart generation
            chart_generator=None
        )
        
        # Restore original methods
        notifier.send_message = original_send_message
        notifier.send_photo = original_send_photo
        
        print(f"✅ Consensus signal method completed: {result}")
        print(f"📤 Messages sent: {len(sent_messages)}")
        print(f"📸 Photos sent: {len(sent_photos)}")
        
        if sent_messages:
            print("\n📝 SENT MESSAGE CONTENT:")
            print("=" * 60)
            print(sent_messages[0]['message'])
            print("=" * 60)
            
            # Check if message contains detailed information
            message_content = sent_messages[0]['message']
            detailed_elements = [
                "CONTRIBUTING ALGORITHMS",
                "AI Analysis",
                "Fibonacci",
                "Volume Profile",
                "SIGNAL QUALITY",
                "ENHANCEMENT FEATURES"
            ]
            
            found_elements = [elem for elem in detailed_elements if elem in message_content]
            print(f"\n✅ Found {len(found_elements)}/{len(detailed_elements)} detailed elements")
            
            if len(found_elements) >= len(detailed_elements) * 0.8:  # 80% threshold
                print("✅ Message contains sufficient detailed information!")
                return True
            else:
                print("⚠️ Message lacks detailed information")
                return False
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing consensus signal method: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 === CONSENSUS SIGNAL DETAILED INFORMATION TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Detailed Caption Generation", test_detailed_consensus_caption),
        ("Consensus Signal Method", test_consensus_signal_method)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append({
            'name': test_name,
            'success': success,
            'duration': end_time - start_time
        })
        
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"\n{test_name}: {status} ({end_time - start_time:.2f}s)")
    
    # Summary
    print(f"\n{'='*60}")
    print("🧪 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['name']}: {result['duration']:.2f}s")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Consensus signals now include detailed information!")
        print("\n📋 EXPECTED IMPROVEMENTS:")
        print("✅ Contributing algorithms breakdown")
        print("✅ Signal quality metrics")
        print("✅ Enhancement features details")
        print("✅ Comprehensive analysis information")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
