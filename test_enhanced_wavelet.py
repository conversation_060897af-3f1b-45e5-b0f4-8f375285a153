#!/usr/bin/env python3
"""
🌊 Test Enhanced Wavelet Analysis
Test the upgraded wavelet analysis functionality in Fourier Analyzer
"""

import sys
import numpy as np
import pandas as pd
import time

# Add current directory to path
sys.path.append('.')

def generate_test_data(length=200):
    """Generate synthetic price data with known cycles for testing"""
    t = np.arange(length)
    
    # Create synthetic price data with multiple cycles
    # Base trend
    trend = 100 + 0.1 * t
    
    # Add multiple cycles
    cycle1 = 5 * np.sin(2 * np.pi * t / 20)  # 20-period cycle
    cycle2 = 3 * np.sin(2 * np.pi * t / 50)  # 50-period cycle
    cycle3 = 2 * np.sin(2 * np.pi * t / 10)  # 10-period cycle
    
    # Add noise
    noise = np.random.normal(0, 1, length)
    
    # Combine all components
    prices = trend + cycle1 + cycle2 + cycle3 + noise
    
    # Create DataFrame
    df = pd.DataFrame({
        'close': prices,
        'high': prices * 1.01,
        'low': prices * 0.99,
        'volume': np.random.uniform(1000, 10000, length)
    })
    
    return df

def test_enhanced_wavelet_analysis():
    """Test the enhanced wavelet analysis functionality"""
    print("🌊 Testing Enhanced Wavelet Analysis")
    print("=" * 60)
    
    try:
        # Import Fourier Analyzer
        from fourier_analyzer import FourierAnalyzer
        
        # Create analyzer instance
        analyzer = FourierAnalyzer(
            min_data_points=50,
            n_components=15,
            enable_wavelet_analysis=True
        )
        
        print(f"✅ Fourier Analyzer created successfully")
        print(f"   🌊 Wavelet Analysis: {'Enabled' if analyzer.enable_wavelet_analysis else 'Disabled'}")
        print(f"   📦 PyWavelets Available: {'Yes' if analyzer.pywt_available else 'No'}")
        
        # Generate test data
        print(f"\n📊 Generating test data...")
        test_df = generate_test_data(200)
        print(f"   📈 Generated {len(test_df)} data points")
        print(f"   💰 Price range: {test_df['close'].min():.2f} - {test_df['close'].max():.2f}")
        
        # Test wavelet cycle detection
        print(f"\n🔍 Testing wavelet cycle detection...")
        start_time = time.time()
        
        # Test the enhanced wavelet detection directly
        prices = test_df['close'].values
        cycles = analyzer._detect_wavelet_cycles(prices)
        
        detection_time = time.time() - start_time
        
        print(f"   ⏱️ Detection time: {detection_time:.3f} seconds")
        print(f"   🎯 Cycles detected: {len(cycles)}")
        
        # Display detected cycles
        if cycles:
            print(f"\n📊 Detected Cycles:")
            for i, cycle in enumerate(cycles[:5]):  # Show top 5
                period = cycle.get('period', 0)
                confidence = cycle.get('enhanced_confidence', cycle.get('confidence', 0))
                method = cycle.get('method', 'unknown')
                wavelet_type = cycle.get('wavelet_type', 'unknown')
                
                print(f"   {i+1}. Period: {period:.1f}, Confidence: {confidence:.3f}")
                print(f"      Method: {method}, Wavelet: {wavelet_type}")
                
                # Show additional metrics if available
                if 'phase' in cycle:
                    print(f"      Phase: {cycle['phase']} ({cycle.get('phase_confidence', 0):.3f})")
                if 'risk_level' in cycle:
                    print(f"      Risk: {cycle['risk_level']}")
                print()
        
        # Test full Fourier analysis
        print(f"🔬 Testing full Fourier analysis...")
        start_time = time.time()
        
        analysis_result = analyzer.analyze(test_df, 'TESTCOIN')
        
        analysis_time = time.time() - start_time
        
        print(f"   ⏱️ Full analysis time: {analysis_time:.3f} seconds")
        
        if analysis_result:
            print(f"   ✅ Analysis completed successfully")
            
            # Check if wavelet results are included
            if 'cycle_analysis' in analysis_result:
                cycle_analysis = analysis_result['cycle_analysis']
                if 'detected_cycles' in cycle_analysis:
                    detected = cycle_analysis['detected_cycles']
                    print(f"   🌊 Wavelet cycles in full analysis: {len(detected)}")
            
            # Check signal generation
            if 'signals' in analysis_result:
                signals = analysis_result['signals']
                print(f"   📡 Signals generated: {len(signals)}")
                
                # Show wavelet-based signals
                wavelet_signals = [s for s in signals if 'wavelet' in s.get('method', '').lower()]
                if wavelet_signals:
                    print(f"   🌊 Wavelet-based signals: {len(wavelet_signals)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced wavelet analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wavelet_fallback():
    """Test wavelet analysis fallback when PyWavelets is not available"""
    print(f"\n🔧 Testing Wavelet Fallback System")
    print("=" * 60)
    
    try:
        from fourier_analyzer import FourierAnalyzer
        
        # Create analyzer and force fallback mode
        analyzer = FourierAnalyzer(enable_wavelet_analysis=True)
        
        # Temporarily disable PyWavelets to test fallback
        original_pywt = analyzer.pywt_available
        analyzer.pywt_available = False
        
        print(f"🔧 Forced fallback mode (PyWavelets disabled)")
        
        # Generate test data
        test_df = generate_test_data(100)
        prices = test_df['close'].values
        
        # Test simple wavelet detection
        print(f"🌊 Testing simple wavelet detection...")
        cycles = analyzer._detect_wavelet_cycles(prices)
        
        print(f"   🎯 Cycles detected in fallback mode: {len(cycles)}")
        
        if cycles:
            print(f"   ✅ Fallback wavelet analysis working")
            for i, cycle in enumerate(cycles[:3]):
                period = cycle.get('period', 0)
                confidence = cycle.get('confidence', 0)
                method = cycle.get('method', 'unknown')
                print(f"   {i+1}. Period: {period:.1f}, Confidence: {confidence:.3f}, Method: {method}")
        else:
            print(f"   ⚠️ No cycles detected in fallback mode")
        
        # Restore original setting
        analyzer.pywt_available = original_pywt
        
        return True
        
    except Exception as e:
        print(f"❌ Wavelet fallback test failed: {e}")
        return False

def test_wavelet_performance():
    """Test wavelet analysis performance with different data sizes"""
    print(f"\n⚡ Testing Wavelet Performance")
    print("=" * 60)
    
    try:
        from fourier_analyzer import FourierAnalyzer

        analyzer = FourierAnalyzer(enable_wavelet_analysis=True)
        
        data_sizes = [50, 100, 200, 500]
        
        for size in data_sizes:
            print(f"📊 Testing with {size} data points...")
            
            # Generate test data
            test_df = generate_test_data(size)
            prices = test_df['close'].values
            
            # Time the analysis
            start_time = time.time()
            cycles = analyzer._detect_wavelet_cycles(prices)
            analysis_time = time.time() - start_time
            
            print(f"   ⏱️ Time: {analysis_time:.3f}s, Cycles: {len(cycles)}")
            
            # Check for reasonable performance
            if analysis_time > 5.0:  # More than 5 seconds is too slow
                print(f"   ⚠️ Performance warning: Analysis took {analysis_time:.3f} seconds")
            else:
                print(f"   ✅ Good performance")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """Run all wavelet analysis tests"""
    print("🚀 Enhanced Wavelet Analysis Test Suite")
    print("=" * 80)
    
    tests = [
        ("Enhanced Wavelet Analysis", test_enhanced_wavelet_analysis),
        ("Wavelet Fallback System", test_wavelet_fallback),
        ("Wavelet Performance", test_wavelet_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 80)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All wavelet analysis tests passed!")
        print("🌊 Enhanced Wavelet Analysis is working correctly!")
        return True
    else:
        print("⚠️ Some tests failed - please check the wavelet implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
