#!/usr/bin/env python3
"""
🔍 SIMPLE CONSENSUS TEST
Test consensus with better signals to reduce zeros
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_consensus():
    """Test consensus with enhanced signals"""
    print("🚀 STARTING SIMPLE CONSENSUS TEST")
    print("=" * 50)
    
    try:
        # Test 1: Import consensus analyzer
        print("\n🔍 Testing Consensus Analyzer...")
        from consensus_analyzer import ConsensusAnalyzer
        print("  ✅ ConsensusAnalyzer imported successfully")
        
        # Initialize with LOWER thresholds
        consensus_analyzer = ConsensusAnalyzer(
            min_consensus_score=0.25,  # Very low for testing
            confidence_threshold=0.25,
            weight_config={
                "ai_models": 0.20,
                "volume_profile": 0.15,
                "point_figure": 0.15,
                "zigzag_fibonacci": 0.15,
                "fourier": 0.05,
                "volume_patterns": 0.05,
                "dump_detector": 0.12,
                "pump_detector": 0.13
            }
        )
        print("  ✅ Consensus analyzer initialized with LOW thresholds")
        
        # Test 2: Create simple test data
        print("\n🔍 Creating Simple Test Data...")
        dates = pd.date_range(start=datetime.now() - timedelta(hours=1), periods=60, freq='1min')
        prices = [50000 + i * 10 for i in range(60)]
        
        ohlcv_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * 1.001 for p in prices],
            'low': [p * 0.999 for p in prices],
            'close': prices,
            'volume': [2000000 for _ in prices]  # Constant volume
        })
        
        print(f"  ✅ Simple data created: {len(ohlcv_data)} rows")
        
        # Test 3: Test with STRONG signals
        print("\n🔍 Testing with STRONG signals...")
        
        strong_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            "ohlcv_data": ohlcv_data,
            "processed_features": {},
            
            # STRONG signals (all above thresholds)
            "volume_profile": {"signal": "BUY", "confidence": 0.60},     # Strong
            "point_figure": {"signal": "BUY", "confidence": 0.65},      # Strong
            "fibonacci": {"signal": "BUY", "confidence": 0.55},         # Strong
            "fourier": {"signal": "BUY", "confidence": 0.50},           # Strong
            "orderbook": {"signals": {"primary_signal": "BUY", "confidence": 0.70}},  # Very strong
            
            # STRONG PUMP signal
            "pump_analysis": {
                "probability": 0.75,  # 75% pump probability
                "stage": "ACTIVE_PUMP",
                "confidence": 0.75,
                "severity": "HIGH"
            },
            
            # Low dump signal
            "dump_analysis": {
                "probability": 0.05,
                "stage": "NONE",
                "confidence": 0.05,
                "severity": "LOW"
            },
            
            "volume_pattern_analysis": {},
            "volume_spike_info": {},
            "pump_detection_results": {}
        }
        
        print(f"  📊 Testing with STRONG signals:")
        print(f"    📊 Volume Profile: BUY 60%")
        print(f"    📊 Point Figure: BUY 65%")
        print(f"    📊 Fibonacci: BUY 55%")
        print(f"    📊 Fourier: BUY 50%")
        print(f"    📊 Orderbook: BUY 70%")
        print(f"    🚀 PUMP: 75% confidence")
        
        # Run consensus analysis
        result = consensus_analyzer.analyze_consensus(strong_input)
        
        if result and result.get('status') == 'success':
            consensus = result.get('consensus', {})
            signal = consensus.get('signal', 'NONE')
            confidence = consensus.get('confidence', 0)
            score = consensus.get('consensus_score', 0)
            signal_count = consensus.get('signal_count', 0)
            
            print(f"  ✅ Consensus completed:")
            print(f"    🎯 Signal: {signal}")
            print(f"    🎯 Confidence: {confidence:.1%}")
            print(f"    🎯 Score: {score:.1%}")
            print(f"    📊 Contributing signals: {signal_count}")
            
            # Check results
            if signal_count > 3:
                print(f"  🎉 SUCCESS: Multiple algorithms contributing! ({signal_count} signals)")
            else:
                print(f"  ⚠️ LIMITED: Only {signal_count} algorithms contributing")
                
            if signal == 'BUY' and confidence >= 0.5:
                print(f"  🎉 SUCCESS: Strong BUY consensus achieved!")
                return True
            else:
                print(f"  ⚠️ WEAK: Consensus not strong enough")
                return False
        else:
            print(f"  ❌ Consensus analysis failed")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_consensus()
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
