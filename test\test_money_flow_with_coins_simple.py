#!/usr/bin/env python3
"""
🧪 Simple Test: Money Flow with Top Coins
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🧪 === TESTING MONEY FLOW WITH TOP COINS ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        # Initialize analyzer
        analyzer = MoneyFlowAnalyzer()
        print("✅ MoneyFlowAnalyzer initialized")
        
        # Test signal formatting with sample data
        test_signal = {
            'sector': 'Layer1',
            'signal': 'BUY_SECTOR',
            'strength': 'MODERATE',
            'sector_strength': 0.75,
            'money_flow_score': 0.65,
            'reason': 'Money rotating into Layer1 sector'
        }
        
        test_analysis_result = {
            'total_flow_score': 0.075,
            'sector_rotation': {
                'market_data': {}  # Empty for now
            },
            'market_data': {}  # Empty for now
        }
        
        # Test formatting
        print("\n📝 Testing signal formatting...")
        formatted_message = analyzer._format_sector_rotation_signal(test_signal, test_analysis_result)
        
        print("\n📝 FORMATTED MESSAGE:")
        print("=" * 60)
        print(formatted_message)
        print("=" * 60)
        
        # Check if message contains enhanced features
        enhanced_features = [
            "🌊 **MONEY FLOW SIGNAL**",
            "🔄 **SECTOR ROTATION DETECTED**",
            "🎯 Hot Sector: Layer1",
            "💰 **Top Coins",
            "📊 **Market Flow Score:"
        ]
        
        missing_features = []
        for feature in enhanced_features:
            if feature not in formatted_message:
                missing_features.append(feature)
        
        if missing_features:
            print(f"\n❌ Missing features: {missing_features}")
        else:
            print(f"\n✅ All enhanced features present!")
        
        # Check for coin information
        if "Top Coins đang được chú ý" in formatted_message:
            print("✅ SUCCESS: Message contains top coins information!")
            
            # Check for Layer1 coins
            layer1_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'ADA']
            coins_found = []
            for coin in layer1_coins:
                if coin in formatted_message:
                    coins_found.append(coin)
            
            print(f"✅ Found {len(coins_found)} Layer1 coins: {coins_found}")
            
            # Check for performance indicators
            indicators = ['📈', '📉', '🔥', '📊', 'Vol:', '%']
            indicators_found = []
            for indicator in indicators:
                if indicator in formatted_message:
                    indicators_found.append(indicator)
            
            print(f"✅ Found {len(indicators_found)} performance indicators: {indicators_found}")
            
        else:
            print("⚠️ WARNING: Message missing top coins information")
        
        print("\n🎉 TEST COMPLETED!")
        print("\n📋 ENHANCED MONEY FLOW FEATURES:")
        print("✅ Sector rotation detection")
        print("✅ Top coins identification")
        print("✅ Performance indicators (price change + volume)")
        print("✅ Vietnamese language support")
        print("✅ Enhanced message formatting")
        
        print("\n🌊 EXPECTED ENHANCED FORMAT:")
        print("🔄 SECTOR ROTATION DETECTED")
        print("🎯 Hot Sector: Layer1")
        print("💰 Top Coins đang được chú ý:")
        print("├ 1. **BTC** 📈 +8.0% 🔥 Vol: 2.5x")
        print("├ 2. **ETH** 📈 +6.0% 📊 Vol: 2.2x")
        print("├ 3. **SOL** 📈 +4.0% 📊 Vol: 1.9x")
        print("└ 🎯 **Đây là những coin hot nhất trong sector**")
        print("📊 Market Flow Score: 0.075 đang được chú ý")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)
