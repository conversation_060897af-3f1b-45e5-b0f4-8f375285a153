#!/usr/bin/env python3
"""
🚀 OPTIMIZATION RESULTS TEST
Test the PUMP/DUMP and CONSENSUS optimization results
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_integration_optimization():
    """Test signal integration optimization."""
    print("🚀 TESTING SIGNAL INTEGRATION OPTIMIZATION")
    print("=" * 60)
    
    try:
        # Test 1: Import signal integration
        print("\n🔍 TEST 1: Signal Integration Import")
        import main_bot_signal_integration
        print("✅ Signal integration imported successfully")
        
        # Test 2: Check cooldown configuration
        print("\n🔍 TEST 2: Cooldown Configuration")
        
        # Create mock main bot instance
        class MockMainBot:
            def __init__(self):
                self.chart_generator = None
                self.notifier = None
                self.data_fetcher = None
        
        mock_bot = MockMainBot()
        
        # Create signal integration with 10-minute cooldown
        signal_integration = main_bot_signal_integration.MainBotSignalIntegration(
            mock_bot, 
            cooldown_minutes=10
        )
        
        print(f"✅ Cooldown configured: {signal_integration.cooldown_minutes} minutes")
        
        if signal_integration.cooldown_minutes == 10:
            print("✅ Cooldown optimization applied: 10 minutes (reduced from 20)")
        else:
            print(f"⚠️ Unexpected cooldown: {signal_integration.cooldown_minutes} minutes")
        
        # Test 3: Check PUMP/DUMP specific configuration
        print("\n🔍 TEST 3: PUMP/DUMP Configuration")
        
        if hasattr(signal_integration, 'pump_dump_cooldown_minutes'):
            print(f"✅ PUMP/DUMP cooldown: {signal_integration.pump_dump_cooldown_minutes} minutes")
        else:
            print("❌ PUMP/DUMP cooldown not configured")
        
        if hasattr(signal_integration, 'pump_dump_threshold'):
            print(f"✅ PUMP/DUMP threshold: {signal_integration.pump_dump_threshold*100:.1f}%")
        else:
            print("❌ PUMP/DUMP threshold not configured")
        
        if hasattr(signal_integration, 'min_confidence_score'):
            print(f"✅ Min confidence score: {signal_integration.min_confidence_score*100:.1f}%")
        else:
            print("❌ Min confidence score not configured")
        
        # Test 4: Test duplicate detection with debug
        print("\n🔍 TEST 4: Enhanced Duplicate Detection")
        
        # Test duplicate detection method
        is_duplicate = signal_integration._is_duplicate_signal(
            "test_analyzer", "BTC/USDT", "BUY", 50000.0
        )
        
        print(f"✅ Duplicate detection working: {not is_duplicate}")
        
        # Test PUMP/DUMP cooldown method
        if hasattr(signal_integration, '_is_pump_dump_cooldown_active'):
            is_cooldown = signal_integration._is_pump_dump_cooldown_active(
                "pump_detection", "BTC/USDT"
            )
            print(f"✅ PUMP/DUMP cooldown detection working: {not is_cooldown}")
        else:
            print("❌ PUMP/DUMP cooldown method not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Signal integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_bot_configuration():
    """Test main bot configuration updates."""
    print("\n🔍 TESTING MAIN BOT CONFIGURATION")
    print("=" * 50)
    
    try:
        # Test configuration values by importing main_bot module
        import main_bot
        
        # Check PUMP/DUMP thresholds
        print(f"📈 PUMP Alert Threshold: {main_bot.PUMP_ALERT_THRESHOLD*100:.1f}%")
        print(f"📉 DUMP Alert Threshold: {main_bot.DUMP_ALERT_THRESHOLD*100:.1f}%")
        
        # Check new PUMP/DUMP configuration
        if hasattr(main_bot, 'PUMP_DUMP_COOLDOWN_MINUTES'):
            print(f"⏰ PUMP/DUMP Cooldown: {main_bot.PUMP_DUMP_COOLDOWN_MINUTES} minutes")
        else:
            print("❌ PUMP/DUMP cooldown not configured in main_bot")
        
        if hasattr(main_bot, 'PUMP_DUMP_MIN_CONFIDENCE'):
            print(f"🎯 PUMP/DUMP Min Confidence: {main_bot.PUMP_DUMP_MIN_CONFIDENCE*100:.1f}%")
        else:
            print("❌ PUMP/DUMP min confidence not configured in main_bot")
        
        if hasattr(main_bot, 'PUMP_DUMP_MIN_SIGNAL_TYPES'):
            print(f"📊 PUMP/DUMP Min Signal Types: {main_bot.PUMP_DUMP_MIN_SIGNAL_TYPES}")
        else:
            print("❌ PUMP/DUMP min signal types not configured in main_bot")
        
        # Check if thresholds are optimized (50% instead of 40%)
        if main_bot.PUMP_ALERT_THRESHOLD == 0.5 and main_bot.DUMP_ALERT_THRESHOLD == 0.5:
            print("✅ PUMP/DUMP thresholds optimized to 50%")
        else:
            print(f"⚠️ PUMP/DUMP thresholds: PUMP={main_bot.PUMP_ALERT_THRESHOLD*100:.1f}%, DUMP={main_bot.DUMP_ALERT_THRESHOLD*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Main bot configuration test failed: {e}")
        return False

def test_analyzer_availability():
    """Test analyzer availability for consensus."""
    print("\n🔍 TESTING ANALYZER AVAILABILITY")
    print("=" * 50)
    
    available_analyzers = []
    
    analyzers_to_test = [
        "volume_profile_analyzer",
        "point_figure_analyzer", 
        "fourier_analyzer",
        "orderbook_analyzer",
        "volume_pattern_analyzer",
        "volume_spike_detector",
        "ai_model_manager"
    ]
    
    for analyzer in analyzers_to_test:
        try:
            __import__(analyzer)
            available_analyzers.append(analyzer)
            print(f"✅ {analyzer}: Available")
        except:
            print(f"❌ {analyzer}: Not available")
    
    print(f"\n📊 Available analyzers: {len(available_analyzers)}/{len(analyzers_to_test)}")
    
    if len(available_analyzers) >= 5:
        print("✅ Sufficient analyzers for consensus")
        return True
    else:
        print("⚠️ Limited analyzers for consensus")
        return False

if __name__ == "__main__":
    print("🚀 STARTING OPTIMIZATION RESULTS TEST")
    print("=" * 70)
    
    # Test signal integration optimization
    signal_success = test_signal_integration_optimization()
    
    # Test main bot configuration
    config_success = test_main_bot_configuration()
    
    # Test analyzer availability
    analyzer_success = test_analyzer_availability()
    
    # Overall results
    print("\n" + "=" * 70)
    print("🎯 OPTIMIZATION TEST RESULTS")
    print("=" * 70)
    
    print(f"🔗 Signal Integration: {'✅ OPTIMIZED' if signal_success else '❌ ISSUES'}")
    print(f"⚙️ Configuration: {'✅ UPDATED' if config_success else '❌ ISSUES'}")
    print(f"📊 Analyzers: {'✅ AVAILABLE' if analyzer_success else '❌ LIMITED'}")
    
    overall_success = signal_success and config_success and analyzer_success
    
    if overall_success:
        print("\n🎉 OPTIMIZATION SUCCESSFUL!")
        print("✅ 10-minute cooldown configured")
        print("✅ PUMP/DUMP thresholds optimized to 50%")
        print("✅ Enhanced duplicate detection with debug logs")
        print("✅ PUMP/DUMP specific cooldown mechanism")
        print("✅ Sufficient analyzers available")
        print("✅ Ready for improved signal flow")
        
        print("\n📊 Expected improvements:")
        print("  🚀 More CONSENSUS signals (10min vs 20min cooldown)")
        print("  🎯 Fewer false PUMP/DUMP alerts (50% vs 40% threshold)")
        print("  🔍 Better duplicate detection with detailed logs")
        print("  ⏰ Separate cooldown for PUMP/DUMP (10 minutes)")
        print("  📈 Better signal quality control")
        
    else:
        print("\n⚠️ OPTIMIZATION ISSUES DETECTED")
        if not signal_success:
            print("🔧 Fix signal integration issues")
        if not config_success:
            print("🔧 Fix configuration issues")
        if not analyzer_success:
            print("🔧 Improve analyzer availability")
    
    print(f"\n🎯 Final Result: {'SUCCESS' if overall_success else 'NEEDS_ATTENTION'}")
    sys.exit(0 if overall_success else 1)
