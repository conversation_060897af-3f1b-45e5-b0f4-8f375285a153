#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 TEST DUMP/PUMP CONSENSUS INTEGRATION
======================================
Test if dump_analysis and pump_analysis are properly passed to consensus analyzer.

Author: Trading Bot System
Version: 1.0.0
"""

import sys
import os
import pandas as pd
from datetime import datetime

def test_dump_pump_consensus():
    """🧪 Test dump/pump analysis integration with consensus analyzer."""
    
    print("🧪 Testing Dump/Pump Analysis Integration with Consensus...")
    print("=" * 60)
    
    try:
        # Import consensus analyzer
        from consensus_analyzer import ConsensusAnalyzer
        print("✅ ConsensusAnalyzer imported successfully")
        
        # Create sample consensus input with dump/pump analysis
        consensus_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            
            # Regular signals
            "volume_profile": {
                "signal": "BUY",
                "confidence": 0.45
            },
            "point_figure": {
                "signal": "BUY", 
                "confidence": 0.65
            },
            "fibonacci": {
                "signal": "SELL",
                "confidence": 0.55
            },
            "fourier": {
                "signal": "BUY",
                "confidence": 0.60
            },
            "orderbook": {
                "signals": {
                    "primary_signal": "BUY",
                    "confidence": 0.50
                }
            },
            
            # ✅ TEST: Add dump analysis
            "dump_analysis": {
                "probability": 0.25,
                "stage": "EARLY_WARNING",
                "confidence": 0.25,
                "severity": "MEDIUM"
            },
            
            # ✅ TEST: Add pump analysis  
            "pump_analysis": {
                "probability": 0.35,
                "stage": "PRE_PUMP",
                "confidence": 0.35,
                "severity": "MEDIUM"
            },
            
            # AI prediction
            "ai_prediction": {
                "ensemble_signal": "BUY",
                "ensemble_confidence": 0.75
            }
        }
        
        print("\n📊 Consensus Input Created:")
        print(f"  - Regular signals: 5")
        print(f"  - Dump analysis: prob={consensus_input['dump_analysis']['probability']:.1%}, stage={consensus_input['dump_analysis']['stage']}")
        print(f"  - Pump analysis: prob={consensus_input['pump_analysis']['probability']:.1%}, stage={consensus_input['pump_analysis']['stage']}")
        print(f"  - AI prediction: {consensus_input['ai_prediction']['ensemble_signal']} ({consensus_input['ai_prediction']['ensemble_confidence']:.1%})")
        
        # Initialize consensus analyzer
        print("\n🎯 Initializing Consensus Analyzer...")
        consensus_analyzer = ConsensusAnalyzer(
            min_consensus_score=0.55,
            confidence_threshold=0.6
        )
        print("✅ Consensus analyzer initialized")
        
        # Run consensus analysis
        print("\n🔍 Running Consensus Analysis...")
        result = consensus_analyzer.analyze_consensus(consensus_input)
        
        print("\n📊 Consensus Results:")
        print(f"  - Status: {result.get('status')}")
        
        if result.get("status") == "success":
            consensus_data = result.get("consensus", {})
            signal = consensus_data.get("signal", "NONE")
            confidence = consensus_data.get("confidence", 0)
            consensus_score = consensus_data.get("consensus_score", 0)
            
            print(f"  - Signal: {signal}")
            print(f"  - Confidence: {confidence:.3f} ({confidence:.1%})")
            print(f"  - Consensus Score: {consensus_score:.3f}")
            
            # Check contributing signals
            contributing = result.get("contributing_algorithms", [])
            print(f"  - Contributing signals: {len(contributing)}")
            for contrib in contributing:
                print(f"    • {contrib}")
            
            # ✅ CHECK: Look for dump/pump in debug output
            debug_info = result.get("debug_info", {})
            if debug_info:
                print(f"\n🔍 Debug Information:")
                for key, value in debug_info.items():
                    if "dump" in key.lower() or "pump" in key.lower():
                        print(f"  - {key}: {value}")
            
            # ✅ CHECK: Look for dump/pump in analysis details
            analysis_details = result.get("analysis_details", {})
            if analysis_details:
                print(f"\n📋 Analysis Details:")
                for key, value in analysis_details.items():
                    if "dump" in key.lower() or "pump" in key.lower():
                        print(f"  - {key}: {value}")
        
        else:
            print(f"  - Error: {result.get('message', 'Unknown error')}")
            return False
        
        print(f"\n🎉 Test completed successfully!")
        print(f"✅ Dump/Pump analysis integration working")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consensus_with_high_dump_probability():
    """🧪 Test consensus with high dump probability."""
    
    print("\n🧪 Testing High Dump Probability Scenario...")
    print("=" * 50)
    
    try:
        from consensus_analyzer import ConsensusAnalyzer
        
        # Create input with high dump probability
        consensus_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            
            # Strong BUY signals
            "volume_profile": {"signal": "BUY", "confidence": 0.80},
            "point_figure": {"signal": "BUY", "confidence": 0.85},
            "fibonacci": {"signal": "BUY", "confidence": 0.75},
            "fourier": {"signal": "BUY", "confidence": 0.70},
            "orderbook": {"signals": {"primary_signal": "BUY", "confidence": 0.65}},
            
            # ✅ HIGH DUMP PROBABILITY - Should override BUY signals
            "dump_analysis": {
                "probability": 0.85,  # Very high dump probability
                "stage": "ACTIVE_DUMP",
                "confidence": 0.85,
                "severity": "HIGH"
            },
            
            # Low pump probability
            "pump_analysis": {
                "probability": 0.05,
                "stage": "NONE",
                "confidence": 0.05,
                "severity": "LOW"
            },
            
            "ai_prediction": {"ensemble_signal": "BUY", "ensemble_confidence": 0.80}
        }
        
        print(f"📊 High Dump Scenario:")
        print(f"  - All regular signals: BUY (high confidence)")
        print(f"  - Dump probability: {consensus_input['dump_analysis']['probability']:.1%} (HIGH)")
        print(f"  - Expected: Dump should override BUY signals")
        
        consensus_analyzer = ConsensusAnalyzer()
        result = consensus_analyzer.analyze_consensus(consensus_input)
        
        if result.get("status") == "success":
            consensus_data = result.get("consensus", {})
            signal = consensus_data.get("signal", "NONE")
            confidence = consensus_data.get("confidence", 0)
            
            print(f"\n📊 Results:")
            print(f"  - Final Signal: {signal}")
            print(f"  - Confidence: {confidence:.1%}")
            
            if signal == "SELL" or signal == "NONE":
                print(f"  ✅ PASS: Dump analysis correctly influenced consensus")
            else:
                print(f"  ❌ FAIL: Dump analysis did not influence consensus")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ High dump test failed: {e}")
        return False

def test_consensus_with_high_pump_probability():
    """🧪 Test consensus with high pump probability."""
    
    print("\n🧪 Testing High Pump Probability Scenario...")
    print("=" * 50)
    
    try:
        from consensus_analyzer import ConsensusAnalyzer
        
        # Create input with high pump probability
        consensus_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            
            # Weak signals
            "volume_profile": {"signal": "BUY", "confidence": 0.35},
            "point_figure": {"signal": "SELL", "confidence": 0.40},
            "fibonacci": {"signal": "BUY", "confidence": 0.30},
            "fourier": {"signal": "NEUTRAL", "confidence": 0.25},
            "orderbook": {"signals": {"primary_signal": "BUY", "confidence": 0.35}},
            
            # Low dump probability
            "dump_analysis": {
                "probability": 0.05,
                "stage": "NONE",
                "confidence": 0.05,
                "severity": "LOW"
            },
            
            # ✅ HIGH PUMP PROBABILITY - Should boost BUY signals
            "pump_analysis": {
                "probability": 0.90,  # Very high pump probability
                "stage": "ACTIVE_PUMP",
                "confidence": 0.90,
                "severity": "HIGH"
            },
            
            "ai_prediction": {"ensemble_signal": "BUY", "ensemble_confidence": 0.60}
        }
        
        print(f"📊 High Pump Scenario:")
        print(f"  - Regular signals: Mixed/weak")
        print(f"  - Pump probability: {consensus_input['pump_analysis']['probability']:.1%} (HIGH)")
        print(f"  - Expected: Pump should boost BUY confidence")
        
        consensus_analyzer = ConsensusAnalyzer()
        result = consensus_analyzer.analyze_consensus(consensus_input)
        
        if result.get("status") == "success":
            consensus_data = result.get("consensus", {})
            signal = consensus_data.get("signal", "NONE")
            confidence = consensus_data.get("confidence", 0)
            
            print(f"\n📊 Results:")
            print(f"  - Final Signal: {signal}")
            print(f"  - Confidence: {confidence:.1%}")
            
            if signal == "BUY" and confidence > 0.6:
                print(f"  ✅ PASS: Pump analysis correctly boosted BUY confidence")
            else:
                print(f"  ❌ FAIL: Pump analysis did not boost consensus")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ High pump test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 DUMP/PUMP CONSENSUS INTEGRATION TESTS")
    print("=" * 60)
    
    # Test 1: Basic integration
    test1_passed = test_dump_pump_consensus()
    
    # Test 2: High dump probability
    test2_passed = test_consensus_with_high_dump_probability()
    
    # Test 3: High pump probability  
    test3_passed = test_consensus_with_high_pump_probability()
    
    print(f"\n🎯 TEST SUMMARY:")
    print(f"=" * 30)
    print(f"✅ Basic Integration: {'PASS' if test1_passed else 'FAIL'}")
    print(f"✅ High Dump Test: {'PASS' if test2_passed else 'FAIL'}")
    print(f"✅ High Pump Test: {'PASS' if test3_passed else 'FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Dump/Pump analysis integration is working correctly")
        print(f"🚀 Ready for production use")
    else:
        print(f"\n❌ Some tests failed")
        print(f"🔧 Check consensus_analyzer.py for dump/pump handling")
