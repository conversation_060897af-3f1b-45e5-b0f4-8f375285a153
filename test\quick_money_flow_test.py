#!/usr/bin/env python3
"""
🧪 Quick Money Flow Test
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🧪 === QUICK MONEY FLOW TEST ===")
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from money_flow_analyzer import MoneyFlowAnalyzer
        print("✅ MoneyFlowAnalyzer imported")
        
        from telegram_notifier import EnhancedTelegramNotifier
        print("✅ EnhancedTelegramNotifier imported")
        
        # Test MoneyFlowAnalyzer
        print("\n📊 Testing MoneyFlowAnalyzer...")
        analyzer = MoneyFlowAnalyzer()
        print(f"✅ Initialized with {len(analyzer.sectors)} sectors")
        
        # Test signal formatting
        print("\n📝 Testing signal formatting...")
        test_signal = {
            'sector': 'Layer1',
            'signal': 'BUY_SECTOR',
            'strength': 'MODERATE',
            'sector_strength': 0.75,
            'money_flow_score': 0.65,
            'reason': 'Money rotating into Layer1 sector'
        }
        test_analysis = {'total_flow_score': 0.075}
        
        formatted = analyzer._format_sector_rotation_signal(test_signal, test_analysis)
        print("✅ Signal formatting works")
        
        # Test TelegramNotifier
        print("\n📱 Testing TelegramNotifier...")
        notifier = EnhancedTelegramNotifier('test', 'test')
        print("✅ TelegramNotifier initialized")
        
        if hasattr(notifier, 'send_money_flow_signal'):
            print("✅ send_money_flow_signal method exists")
        else:
            print("❌ send_money_flow_signal method missing")
            return False
        
        # Test signal creation
        print("\n🌊 Testing signal creation...")
        test_signal_data = {
            'type': 'MONEY_FLOW_SIGNAL',
            'subtype': 'SECTOR_ROTATION_DETECTED',
            'hot_sector': 'Layer1',
            'signal': 'BUY_SECTOR',
            'strength': 'MODERATE',
            'sector_strength': 0.75,
            'money_flow_score': 0.65,
            'reason': 'Money rotating into Layer1 sector',
            'formatted_message': formatted
        }
        print("✅ Test signal data created")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 MONEY FLOW SYSTEM READY:")
        print("✅ Enhanced sector categorization")
        print("✅ Sector rotation detection")
        print("✅ Signal formatting")
        print("✅ Telegram integration")
        
        print("\n🌊 SAMPLE SIGNAL OUTPUT:")
        print(formatted[:300] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)
