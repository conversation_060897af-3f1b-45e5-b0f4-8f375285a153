#!/usr/bin/env python3
"""
👑 ADMIN COMMANDS TEST
=====================

Test script để kiểm tra admin commands hoạt động trên Telegram
"""

import os
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_telegram_connection():
    """Test Telegram API connection"""
    print("📱 === TESTING TELEGRAM CONNECTION ===")
    print("=" * 50)
    
    try:
        from telegram_message_handler import TelegramMessageHandler
        
        # Create mock bot
        class MockBot:
            def __init__(self):
                self.notifier = None
        
        mock_bot = MockBot()
        handler = TelegramMessageHandler(mock_bot)
        
        # Test connection
        print("🔧 Testing Telegram API connection...")
        success = handler.test_connection()
        
        if success:
            print("✅ Telegram API connection successful!")
            return True
        else:
            print("❌ Telegram API connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def test_admin_config():
    """Test admin configuration"""
    print("\n👑 === TESTING ADMIN CONFIGURATION ===")
    print("=" * 50)
    
    try:
        import admin_config
        
        print("🔧 Checking admin configuration...")
        
        # Check admin lists
        admin_users = admin_config.ADMIN_USERS
        super_admin_users = admin_config.SUPER_ADMIN_USERS
        csv_export_users = admin_config.CSV_EXPORT_ADMIN_USERS
        
        print(f"👑 Basic admins: {len(admin_users)} configured")
        print(f"🔒 Super admins: {len(super_admin_users)} configured")
        print(f"📊 CSV export admins: {len(csv_export_users)} configured")
        
        if len(admin_users) == 0:
            print("\n⚠️ WARNING: No admin users configured!")
            print("   Please add your Telegram User ID to admin_config.py")
            print("   Example: ADMIN_USERS = [123456789, 987654321]")
            return False
        
        # Test permission functions
        test_user_id = admin_users[0] if admin_users else 123456789
        print(f"\n🧪 Testing permissions for user {test_user_id}:")
        print(f"  - Is admin: {admin_config.is_admin(test_user_id)}")
        print(f"  - Is super admin: {admin_config.is_super_admin(test_user_id)}")
        print(f"  - Has CSV export: {admin_config.is_csv_export_admin(test_user_id)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing admin config: {e}")
        return False

def test_admin_commands():
    """Test admin commands functionality"""
    print("\n🔧 === TESTING ADMIN COMMANDS ===")
    print("=" * 50)
    
    try:
        from member_admin_commands import MemberAdminCommands
        
        # Create mock bot
        class MockBot:
            def __init__(self):
                self.notifier = None
                self.member_manager = None
        
        mock_bot = MockBot()
        admin_commands = MemberAdminCommands(mock_bot)
        
        print("✅ MemberAdminCommands initialized")
        
        # Test command list
        available_commands = [
            "/stats", "/extend", "/donation", "/members", "/help_admin"
        ]
        
        print(f"📋 Available admin commands: {len(available_commands)}")
        for cmd in available_commands:
            print(f"  - {cmd}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing admin commands: {e}")
        return False

def test_hidden_csv_commands():
    """Test hidden CSV commands"""
    print("\n🔒 === TESTING HIDDEN CSV COMMANDS ===")
    print("=" * 50)
    
    try:
        from hidden_admin_csv_system import HiddenAdminCSVSystem
        
        hidden_csv = HiddenAdminCSVSystem()
        print("✅ HiddenAdminCSVSystem initialized")
        
        # Test hidden command list
        hidden_commands = [
            "/export all", "/export group", "/export new", "/export expiring",
            "/admin_export_all", "/admin_export_group", "/admin_export_new"
        ]
        
        print(f"🔒 Available hidden commands: {len(hidden_commands)}")
        for cmd in hidden_commands:
            print(f"  - {cmd}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing hidden CSV commands: {e}")
        return False

def show_admin_setup_guide():
    """Show admin setup guide"""
    print("\n📋 === ADMIN SETUP GUIDE ===")
    print("=" * 50)
    
    print("🔧 TO ENABLE ADMIN COMMANDS:")
    print()
    print("1️⃣ GET YOUR TELEGRAM USER ID:")
    print("   • Message @userinfobot on Telegram")
    print("   • Send any message to get your User ID")
    print("   • Copy the numeric User ID (e.g., 123456789)")
    print()
    print("2️⃣ CONFIGURE ADMIN_CONFIG.PY:")
    print("   • Open admin_config.py")
    print("   • Add your User ID to ADMIN_USERS list:")
    print("     ADMIN_USERS = [123456789]  # Replace with your ID")
    print("   • For CSV export access, also add to:")
    print("     CSV_EXPORT_ADMIN_USERS = [123456789]")
    print()
    print("3️⃣ START THE BOT:")
    print("   • Run: python main_bot.py")
    print("   • Bot will start with message polling")
    print("   • Admin commands will be active")
    print()
    print("4️⃣ TEST ADMIN COMMANDS:")
    print("   • Send /help_admin in Telegram")
    print("   • Try /stats to see member statistics")
    print("   • Use /export all to test CSV export")
    print()
    print("🔒 HIDDEN COMMANDS:")
    print("   • Only admins can see and use these")
    print("   • Non-admins get no response (silent)")
    print("   • Commands: /export, /admin_export_*")
    print()
    print("👥 MEMBER MANAGEMENT:")
    print("   • Auto welcome new members")
    print("   • 60-day trial system")
    print("   • QR code donation")
    print("   • Expiration warnings")

def show_command_examples():
    """Show command examples"""
    print("\n💡 === COMMAND EXAMPLES ===")
    print("=" * 50)
    
    print("👑 BASIC ADMIN COMMANDS:")
    print("   /help_admin     - Show admin help")
    print("   /stats          - Member statistics")
    print("   /members        - List recent members")
    print("   /donation       - Send donation info with QR")
    print("   /extend 123456789 -1002301937119 30  - Extend trial")
    print()
    print("🔒 HIDDEN CSV EXPORT COMMANDS:")
    print("   /export all     - Export all members")
    print("   /export group -1002301937119  - Export group members")
    print("   /export new     - Export today's new members")
    print("   /export expiring 7  - Export expiring in 7 days")
    print()
    print("📊 DIRECT ADMIN EXPORT:")
    print("   /admin_export_all")
    print("   /admin_export_group -1002301937119")
    print("   /admin_export_new")
    print("   /admin_export_expiring 7")
    print()
    print("🤖 BASIC BOT COMMANDS (All Users):")
    print("   /start          - Welcome message")
    print("   /help           - Bot help")
    print("   /status         - Bot status")
    print("   /donate         - Donation info")

def main():
    """Main test function"""
    print("👑 === ADMIN COMMANDS TEST ===")
    print("🎯 Testing admin commands functionality")
    print()
    
    # Run tests
    test1 = test_telegram_connection()
    test2 = test_admin_config()
    test3 = test_admin_commands()
    test4 = test_hidden_csv_commands()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Telegram Connection", test1),
        ("Admin Configuration", test2),
        ("Admin Commands", test3),
        ("Hidden CSV Commands", test4)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL RESULT: {passed}/{total} tests passed")
    
    # Show setup guide
    show_admin_setup_guide()
    show_command_examples()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ ADMIN COMMANDS STATUS:")
        print("  • Telegram API: CONNECTED")
        print("  • Admin Config: READY")
        print("  • Admin Commands: FUNCTIONAL")
        print("  • Hidden Commands: SECURED")
        print("  • Message Handler: INTEGRATED")
        print("\n💡 NEXT STEPS:")
        print("  1. Configure admin users in admin_config.py")
        print("  2. Start main bot: python main_bot.py")
        print("  3. Test commands in Telegram")
        print("  4. Verify member management")
        print("\n🚀 ADMIN COMMANDS READY FOR USE!")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the configuration and try again.")
        
        if not test2:  # Admin config failed
            print("\n🔧 QUICK FIX:")
            print("  1. Get your Telegram User ID from @userinfobot")
            print("  2. Add it to admin_config.py:")
            print("     ADMIN_USERS = [YOUR_USER_ID_HERE]")
            print("  3. Run this test again")

if __name__ == "__main__":
    main()
