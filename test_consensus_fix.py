#!/usr/bin/env python3
"""
🔍 CONSENSUS FIX TEST
Quick test to verify consensus fix
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_consensus_fix():
    """Test consensus fix for PUMP/DUMP override"""
    print("🚀 STARTING CONSENSUS FIX TEST")
    print("=" * 50)
    
    try:
        # Test 1: Import consensus analyzer
        print("\n🔍 Testing Consensus Analyzer...")
        from consensus_analyzer import ConsensusAnalyzer
        print("  ✅ ConsensusAnalyzer imported successfully")
        
        # Initialize consensus analyzer
        consensus_analyzer = ConsensusAnalyzer(
            min_consensus_score=0.55,
            weight_config={
                "ai_models": 0.20,
                "volume_profile": 0.15,
                "point_figure": 0.15,
                "zigzag_fibonacci": 0.15,
                "fourier": 0.05,
                "volume_patterns": 0.05,
                "dump_detector": 0.12,
                "pump_detector": 0.13
            }
        )
        print("  ✅ Consensus analyzer initialized")
        
        # Test 2: Create minimal test data
        print("\n🔍 Creating Test Data...")
        dates = pd.date_range(start=datetime.now() - timedelta(hours=1), periods=60, freq='1min')
        prices = [50000 + i * 10 for i in range(60)]
        
        ohlcv_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * 1.001 for p in prices],
            'low': [p * 0.999 for p in prices],
            'close': prices,
            'volume': [1000000 for _ in prices]
        })
        
        print(f"  ✅ Test data created: {len(ohlcv_data)} rows")
        
        # Test 3: Test PUMP override
        print("\n🔍 Testing PUMP Override...")
        
        pump_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            "ohlcv_data": ohlcv_data,
            "processed_features": {},
            
            # Minimal signals (insufficient for normal consensus)
            "volume_profile": {"signal": "BUY", "confidence": 0.25},
            "point_figure": {"signal": "NEUTRAL", "confidence": 0.20},
            "fibonacci": {"signal": "SELL", "confidence": 0.15},
            "fourier": {"signal": "NEUTRAL", "confidence": 0.10},
            "orderbook": {"signals": {"primary_signal": "NEUTRAL", "confidence": 0.20}},
            
            # STRONG PUMP signal (should trigger override)
            "pump_analysis": {
                "probability": 0.60,  # 60% pump probability
                "stage": "ACTIVE_PUMP",
                "confidence": 0.60,
                "severity": "HIGH"
            },
            
            # No dump signal
            "dump_analysis": {
                "probability": 0.05,
                "stage": "NONE",
                "confidence": 0.05,
                "severity": "LOW"
            },
            
            "volume_pattern_analysis": {},
            "volume_spike_info": {},
            "pump_detection_results": {}
        }
        
        print(f"  📊 Testing with PUMP signal: 60% confidence")
        
        # Run consensus analysis
        result = consensus_analyzer.analyze_consensus(pump_input)
        
        if result and result.get('status') == 'success':
            consensus = result.get('consensus', {})
            signal = consensus.get('signal', 'NONE')
            confidence = consensus.get('confidence', 0)
            score = consensus.get('consensus_score', 0)
            
            print(f"  ✅ Consensus completed:")
            print(f"    🎯 Signal: {signal}")
            print(f"    🎯 Confidence: {confidence:.1%}")
            print(f"    🎯 Score: {score:.1%}")
            
            # Check if PUMP override worked
            if signal == 'BUY' and confidence >= 0.5:
                print(f"  🎉 SUCCESS: PUMP override working! Signal={signal}, Confidence={confidence:.1%}")
                return True
            else:
                print(f"  ❌ FAILED: PUMP override not working. Signal={signal}, Confidence={confidence:.1%}")
                return False
        else:
            print(f"  ❌ Consensus analysis failed")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_consensus_fix()
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
