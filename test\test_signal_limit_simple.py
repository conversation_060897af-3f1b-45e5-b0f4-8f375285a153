#!/usr/bin/env python3
"""
🧪 TEST: Simple Signal Limit Check
Test đơn giản để kiểm tra signal limit enforcement
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_signal_limit_simple():
    """Simple test for signal limit enforcement"""
    print("🧪 === SIMPLE SIGNAL LIMIT TEST ===")
    
    try:
        print("✅ Testing signal limit enforcement fixes:")
        
        # Test 1: Check that notification methods have limit checks
        print("\n📊 Checking notification methods for limit checks:")
        
        # Read main_bot.py to verify fixes
        with open("main_bot.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for signal limit checks in notification methods
        checks = [
            ("Money Flow", "can_send_signal(\"money_flow\")" in content),
            ("Whale Activity", "can_send_signal(\"whale_activity\")" in content),
            ("Manipulation", "can_send_signal(\"manipulation_detection\")" in content),
            ("Cross Asset", "can_send_signal(\"cross_asset\")" in content),
            ("Fallback Consensus", "can_send_signal(\"consensus\")" in content and "fallback" in content.lower())
        ]
        
        all_checks_passed = True
        for check_name, check_result in checks:
            if check_result:
                print(f"  ✅ {check_name}: Signal limit check found")
            else:
                print(f"  ❌ {check_name}: Signal limit check missing")
                all_checks_passed = False
        
        # Test 2: Check for blocking messages
        print("\n📊 Checking for proper blocking messages:")
        
        blocking_messages = [
            ("Money Flow Block", "Money flow notification blocked - signal limit reached" in content),
            ("Whale Block", "Whale activity notification blocked - signal limit reached" in content),
            ("Manipulation Block", "Manipulation detection notification blocked - signal limit reached" in content),
            ("Cross Asset Block", "Cross-asset notification blocked - signal limit reached" in content),
            ("Fallback Block", "Fallback consensus notification blocked - signal limit reached" in content)
        ]
        
        all_blocks_found = True
        for block_name, block_result in blocking_messages:
            if block_result:
                print(f"  ✅ {block_name}: Blocking message found")
            else:
                print(f"  ❌ {block_name}: Blocking message missing")
                all_blocks_found = False
        
        # Test 3: Check for early returns
        print("\n📊 Checking for early return statements:")
        
        early_returns = [
            ("Money Flow Return", "if not self.signal_integration.can_send_signal(\"money_flow\"):" in content and "return" in content),
            ("Whale Return", "if not self.signal_integration.can_send_signal(\"whale_activity\"):" in content and "return" in content),
            ("Manipulation Return", "if not self.signal_integration.can_send_signal(\"manipulation_detection\"):" in content and "return" in content),
            ("Cross Asset Return", "if not self.signal_integration.can_send_signal(\"cross_asset\"):" in content and "return" in content)
        ]
        
        all_returns_found = True
        for return_name, return_result in early_returns:
            if return_result:
                print(f"  ✅ {return_name}: Early return found")
            else:
                print(f"  ❌ {return_name}: Early return missing")
                all_returns_found = False
        
        # Overall result
        if all_checks_passed and all_blocks_found and all_returns_found:
            print(f"\n🎉 SUCCESS: All signal limit enforcement fixes applied!")
            print(f"✅ 5 notification methods now check signal limits")
            print(f"✅ Proper blocking messages implemented")
            print(f"✅ Early returns prevent unlimited sending")
            return True
        else:
            print(f"\n❌ FAILED: Some fixes missing")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run simple signal limit test"""
    print("🧪 === SIMPLE SIGNAL LIMIT ENFORCEMENT TEST ===")
    
    success = test_signal_limit_simple()
    
    if success:
        print("\n🎯 CONCLUSION:")
        print("✅ Signal limit enforcement fixes successfully applied")
        print("✅ No more unlimited signal sending")
        print("✅ 20-signal limit will be respected")
        print("✅ All notification types now limited")
    else:
        print("\n❌ FAILED: Signal limit enforcement incomplete")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
