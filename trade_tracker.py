#!/usr/bin/env python3
"""
📊 ENHANCED TRADE TRACKER V3.0 - PRODUCTION READY
=================================================

Advanced Trade Tracking System with Machine Learning Integration:
- 📊 Comprehensive trade lifecycle management with real-time monitoring
- 🎯 Advanced TP/SL optimization with dynamic adjustment algorithms
- 📈 Real-time performance analytics with ML-based predictions
- 🔄 Smart position sizing and risk management
- 📱 Intelligent notification system with customizable alerts
- 🚀 Performance optimized for high-frequency trading
- 🛡️ Comprehensive error handling and data protection

Author: AI Trading Bot Team
Version: 3.0 - Production Ready
License: Proprietary
"""

import pandas as pd
import numpy as np
import time
import json
import os
import threading
import traceback
import warnings
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from scipy import stats
    AVAILABLE_MODULES['scipy'] = True
    print("✅ scipy imported successfully - Advanced statistical analysis available")
except ImportError:
    AVAILABLE_MODULES['scipy'] = False
    print("⚠️ scipy not available - Using basic statistical methods")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.ensemble import RandomForestRegressor
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - ML predictions available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic predictions")

# Import Advanced Signal Deduplicator
try:
    from advanced_signal_deduplicator import AdvancedSignalDeduplicator
    DEDUPLICATOR_AVAILABLE = True
    print("✅ Advanced Signal Deduplicator imported successfully")
except ImportError:
    print("⚠️ Advanced Signal Deduplicator not available - using basic duplicate detection")
    DEDUPLICATOR_AVAILABLE = False

print(f"📊 Trade Tracker V3.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class TradeTracker:
    """
    📊 ENHANCED TRADE TRACKER V3.0 - PRODUCTION READY
    =================================================

    Advanced Trade Tracking System with comprehensive features:
    - 📊 Comprehensive trade lifecycle management with real-time monitoring
    - 🎯 Advanced TP/SL optimization with dynamic adjustment algorithms
    - 📈 Real-time performance analytics with ML-based predictions
    - 🔄 Smart position sizing and risk management
    - 📱 Intelligent notification system with customizable alerts
    - 🚀 Performance optimized for high-frequency trading
    """

    def __init__(self, notifier=None, data_fetcher=None, data_logger=None,
                max_active_signals=20, backup_interval=300, backup_dir=None,
                enable_ultra_fast_tracking=True, enable_dynamic_tp_sl=True,
                enable_trailing_stop=True, enable_partial_profits=True,
                volatility_adaptive=True, enable_ml_predictions=True):
        """
        Initialize Enhanced Trade Tracker V3.0.

        Args:
            notifier: Notification service
            data_fetcher: Data fetching service
            data_logger: Data logging service
            max_active_signals: Maximum number of active signals to track (20)
            backup_interval: Interval in seconds for state backups (300)
            backup_dir: Directory for state backups
            enable_ultra_fast_tracking: Enable ultra-fast tracking mode
            enable_dynamic_tp_sl: Enable dynamic TP/SL adjustment
            enable_trailing_stop: Enable trailing stop functionality
            enable_partial_profits: Enable partial profit taking
            volatility_adaptive: Enable volatility-adaptive adjustments
            enable_ml_predictions: Enable ML-based predictions
        """
        print("📊 Initializing Enhanced Trade Tracker V3.0...")

        # Core services
        self.notifier = notifier
        self.fetcher = data_fetcher
        self.logger = data_logger

        # Core configuration with validation
        self.max_active_signals = max(5, min(100, max_active_signals))  # 5-100 signals
        self.backup_interval = max(60, min(3600, backup_interval))  # 1min-1hour
        self.backup_dir = backup_dir or "trade_tracker_backups"

        # Enhanced features
        self.enable_ultra_fast_tracking = enable_ultra_fast_tracking
        self.enable_dynamic_tp_sl = enable_dynamic_tp_sl
        self.enable_trailing_stop = enable_trailing_stop
        self.enable_partial_profits = enable_partial_profits
        self.volatility_adaptive = volatility_adaptive
        self.enable_ml_predictions = enable_ml_predictions and AVAILABLE_MODULES.get('sklearn', False)

        # 🚀 ULTRA TRACKER V3.0: Advanced Signal Management
        self.signal_management = {
            "max_signals": 20,  # Maximum 20 signals
            "completion_threshold": 18,  # Need 18/20 completed before new signals
            "completed_count": 0,  # Track completed signals
            "active_count": 0,  # Track active signals
            "allow_new_signals": True,  # Flag to control new signal acceptance
            "completion_tracking": [],  # Track completion history
            "signal_queue": [],  # Queue for pending signals when limit reached
            "auto_cleanup_enabled": True,  # Auto cleanup completed signals
            "cleanup_threshold": 50  # Keep max 50 completed signals in memory
        }

        # 🔥 NEW: Advanced Signal Deduplicator V2.0
        if DEDUPLICATOR_AVAILABLE:
            self.signal_deduplicator = AdvancedSignalDeduplicator(
                time_window_minutes=30,  # 30 minutes window
                similarity_threshold=0.85,  # 85% similarity threshold
                price_tolerance_percent=0.5,  # 0.5% price tolerance
                max_history_size=500  # Keep 500 signals in history
            )
            print(f"🔥 Advanced Signal Deduplicator V2.0 enabled")
        else:
            self.signal_deduplicator = None
            print(f"⚠️ Using basic duplicate detection")
        
        # 🔧 Enhanced signal tracking with thread safety
        self.active_signals = []
        self.completed_signals = []
        self.tracked_signals_lock = threading.RLock()
        
        # 📊 Enhanced performance metrics
        self.metrics = {
            "win_count": 0,
            "loss_count": 0,
            "win_rate": 0.0,
            "avg_win_pct": 0.0,
            "avg_loss_pct": 0.0,
            "profit_factor": 0.0,
            "avg_holding_time": 0.0,
            "total_signals": 0,
            "open_signals": 0,
            "closed_signals": 0,
            "sharpe_ratio": 0.0,
            "max_drawdown": 0.0,
            "consecutive_wins": 0,
            "consecutive_losses": 0,
            "best_trade": 0.0,
            "worst_trade": 0.0,
            "total_pnl": 0.0,
            "avg_risk_reward": 0.0,
            "success_rate_by_type": {"BUY": 0.0, "SELL": 0.0},
            "performance_trend": "neutral",
            "last_updated": int(time.time())
        }
        
        # 🚀 Advanced tracking features
        self.signal_history = []  # Complete history for analysis
        self.performance_snapshots = []  # Daily performance snapshots
        self.risk_management = {
            "max_risk_per_trade": 2.0,  # 2% max risk per trade
            "max_daily_loss": 5.0,      # 5% max daily loss
            "max_open_positions": max_active_signals,
            "correlation_limit": 0.7     # Max correlation between positions
        }
        
        # 🔍 Signal quality assessment
        self.signal_quality_metrics = {
            "avg_confidence": 0.0,
            "high_confidence_signals": 0,
            "low_confidence_signals": 0,
            "model_performance": {},
            "timeframe_performance": {}
        }
        
        # 📈 Ultra-Fast Real-time tracking
        self.last_check_time = int(time.time())
        self.check_interval = 15  # Check signals every 15 seconds (4x faster)
        self.fast_check_interval = 5  # Ultra-fast check for critical signals
        self.price_check_interval = 3  # Price monitoring every 3 seconds

        # ✅ UPGRADED: Ultra-Fast Real-time TP/SL tracking and notifications
        self.tp_sl_tracking = {
            "enabled": True,
            "update_threshold": 0.3,  # Minimum % change to trigger update (reduced for faster response)
            "notification_cooldown": 120,  # 2 minutes between notifications for same signal (faster)
            "last_notifications": {},  # Track last notification time per signal
            "update_history": [],  # History of all TP/SL updates
            "auto_trailing_enabled": True,
            "trailing_trigger_profit": 1.5,  # Enable trailing after 1.5% profit (faster trigger)
            "max_updates_per_signal": 15,  # Max TP/SL updates per signal (more updates)
            "fast_response_mode": True,  # Enable ultra-fast response
            "immediate_notification": True,  # Send notifications immediately
            "smart_adjustment_enabled": True,  # Enable smart TP/SL adjustments
            "momentum_tracking": True,  # Track price momentum for better adjustments
            "volatility_adaptation": True  # Adapt to market volatility
        }
        
        # Initialize system
        self._initialize_tracker()
        
        print(f"🚀 ULTRA TRACKER V3.0 - Advanced Signal Management:")
        print(f"    📊 Max signals: {self.signal_management['max_signals']} (LIMIT: 20)")
        print(f"    ✅ Completion threshold: {self.signal_management['completion_threshold']}/20 (Need 18 completed)")
        print(f"    🎯 New signals allowed: {self.signal_management['allow_new_signals']}")
        print(f"    🔄 Auto cleanup: {self.signal_management['auto_cleanup_enabled']}")
        print(f"    💾 Backup interval: {backup_interval}s")
        print(f"    📁 Backup directory: {self.backup_dir}")
        print(f"    ⚡ Check interval: {self.check_interval}s (Ultra-Fast)")
        print(f"    🚀 Fast check: {self.fast_check_interval}s")
        print(f"    📊 Price monitoring: {self.price_check_interval}s")
        print(f"    🎯 TP/SL update threshold: {self.tp_sl_tracking['update_threshold']}%")
        print(f"    📱 Notification cooldown: {self.tp_sl_tracking['notification_cooldown']}s")
        print(f"    🔥 Advanced Deduplicator: {'ENABLED' if self.signal_deduplicator else 'DISABLED'}")
    
    def _initialize_tracker(self) -> None:
        """🔧 Initialize the tracker system."""
        try:
            # Load previous state if available
            self._load_state()
            
            # Start background processes
            if self.backup_interval > 0:
                self._start_backup_thread()
            
            self._start_monitoring_thread()
            
            # Validate existing signals
            self._validate_existing_signals()
            
            print(f"✅ TradeTracker initialized successfully")
            
        except Exception as e:
            print(f"❌ Error initializing TradeTracker: {e}")
            print(f"📊 Traceback: {traceback.format_exc()}")
    
    def add_signal(self, signal_data: Dict[str, Any]) -> bool:
        """
        🔥 ENHANCED: Add a new trading signal with comprehensive validation and risk management.
        
        Args:
            signal_data: Signal information including entry, TP, SL, etc.
            
        Returns:
            bool: Whether the signal was added successfully
        """
        with self.tracked_signals_lock:
            try:
                print(f"🔄 Adding new signal for {signal_data.get('coin', 'UNKNOWN')}...")
                
                # 1. Pre-validation checks
                if not self._pre_validate_signal(signal_data):
                    # 🚀 ULTRA TRACKER V3.0: Add to queue if signal limit reached but signal is valid
                    if not self.signal_management['allow_new_signals']:
                        print(f"    📋 Adding signal to queue for later processing...")
                        self.signal_management['signal_queue'].append(signal_data.copy())
                        print(f"    📊 Queue status: {len(self.signal_management['signal_queue'])} signals waiting")
                        return False  # Signal queued, not added yet
                    return False
                
                # 2. Risk management validation
                if not self._validate_risk_management(signal_data):
                    return False
                
                # 3. ✅ ENHANCED: Advanced duplicate detection with multi-layer analysis
                is_duplicate, reason, similar_signal = self._check_advanced_duplicates(signal_data)
                if is_duplicate:
                    print(f"    🚫 DUPLICATE SIGNAL DETECTED for {signal_data.get('coin')}")
                    print(f"      📋 Reason: {reason}")
                    if similar_signal:
                        print(f"      🔍 Similar to: {similar_signal.analyzer_type} signal at {similar_signal.entry_price}")
                    return False
                
                # 4. Signal quality assessment
                quality_score = self._assess_signal_quality(signal_data)
                signal_data['quality_score'] = quality_score
                
                if quality_score < 0.3:
                    print(f"    ⚠️ Signal quality too low ({quality_score:.2f}) for {signal_data.get('coin')}")
                    return False
                
                # 5. Enhanced signal formatting
                formatted_signal = self._format_signal_data_enhanced(signal_data)
                if not formatted_signal:
                    return False
                
                # 6. Position sizing calculation
                position_data = self._calculate_position_sizing(formatted_signal)
                formatted_signal.update(position_data)
                
                # 7. Add to tracking system
                self.active_signals.append(formatted_signal)
                self.signal_history.append(formatted_signal.copy())
                
                # 8. Update metrics and logging
                self._update_metrics_enhanced()
                self._log_signal_enhanced(formatted_signal)
                
                # 9. Backup state
                self._backup_state()
                
                # 10. Success notification
                print(f"✅ Signal added successfully:")
                print(f"    🪙 Coin: {formatted_signal.get('coin')}")
                print(f"    📈 Type: {formatted_signal.get('signal_type')}")
                print(f"    💯 Quality: {quality_score:.2f}")
                print(f"    🎯 Entry: {formatted_signal.get('entry')}")
                print(f"    🏆 TP: {formatted_signal.get('take_profit')}")
                print(f"    🛡️ SL: {formatted_signal.get('stop_loss')}")
                print(f"    🆔 ID: {formatted_signal.get('signal_id')}")
                
                return True
                
            except Exception as e:
                print(f"❌ Critical error adding signal for {signal_data.get('coin', 'UNKNOWN')}: {e}")
                print(f"📊 Traceback: {traceback.format_exc()}")
                return False
    
    def _pre_validate_signal(self, signal_data: Dict[str, Any]) -> bool:
        """🔧 ULTRA TRACKER V3.0: Enhanced pre-validation with 20-signal limit and 18/20 completion rule."""
        try:
            # 🚀 ULTRA TRACKER: Check signal management rules
            if not self._check_signal_management_rules():
                return False
            
            # Required fields validation
            required_fields = ['coin', 'signal_type', 'entry', 'take_profit', 'stop_loss']
            missing_fields = [field for field in required_fields 
                            if field not in signal_data or signal_data[field] is None]
            
            if missing_fields:
                print(f"    ❌ Missing required fields: {missing_fields}")
                return False
            
            # Basic type validation
            try:
                float(signal_data['entry'])
                float(signal_data['take_profit'])
                float(signal_data['stop_loss'])
            except (TypeError, ValueError) as e:
                print(f"    ❌ Invalid price values: {e}")
                return False
            
            # Signal type validation
            if signal_data['signal_type'].upper() not in ['BUY', 'SELL']:
                print(f"    ❌ Invalid signal type: {signal_data['signal_type']}")
                return False
            
            return True
            
        except Exception as e:
            print(f"    ❌ Error in pre-validation: {e}")
            return False

    def _check_signal_management_rules(self) -> bool:
        """🚀 ULTRA TRACKER V3.0: Check signal management rules (20 max, 18/20 completion)."""
        try:
            # Update current counts
            self.signal_management['active_count'] = len(self.active_signals)
            self.signal_management['completed_count'] = len(self.completed_signals)

            total_signals = self.signal_management['active_count'] + self.signal_management['completed_count']

            print(f"    📊 ULTRA TRACKER STATUS:")
            print(f"      🔢 Active signals: {self.signal_management['active_count']}")
            print(f"      ✅ Completed signals: {self.signal_management['completed_count']}")
            print(f"      📈 Total signals: {total_signals}")

            # Rule 1: Maximum 20 signals total
            if total_signals >= self.signal_management['max_signals']:
                completed_ratio = self.signal_management['completed_count'] / total_signals
                completion_threshold_ratio = self.signal_management['completion_threshold'] / self.signal_management['max_signals']

                print(f"      🎯 Completion ratio: {completed_ratio:.1%} (need: {completion_threshold_ratio:.1%})")

                # Rule 2: Need 18/20 (90%) completed before allowing new signals
                if self.signal_management['completed_count'] < self.signal_management['completion_threshold']:
                    needed = self.signal_management['completion_threshold'] - self.signal_management['completed_count']
                    print(f"    ⚠️ SIGNAL LIMIT REACHED: {total_signals}/20 signals")
                    print(f"    🔒 Need {needed} more completions before new signals allowed")
                    print(f"    📊 Current: {self.signal_management['completed_count']}/20 completed (need: 18/20)")

                    # Add to queue for later processing
                    self.signal_management['allow_new_signals'] = False
                    return False
                else:
                    # 18/20 completed - allow new signals and cleanup
                    print(f"    ✅ COMPLETION THRESHOLD MET: {self.signal_management['completed_count']}/20 completed")
                    print(f"    🔄 Auto-cleanup triggered - removing old completed signals")
                    self._auto_cleanup_completed_signals()
                    self.signal_management['allow_new_signals'] = True
                    return True
            else:
                # Under 20 signals - allow new signals
                self.signal_management['allow_new_signals'] = True
                print(f"    ✅ UNDER LIMIT: {total_signals}/20 signals - new signals allowed")
                return True

        except Exception as e:
            print(f"    ❌ Error checking signal management rules: {e}")
            return False

    def _auto_cleanup_completed_signals(self) -> None:
        """🧹 Auto cleanup completed signals when threshold is met."""
        try:
            if not self.signal_management['auto_cleanup_enabled']:
                return

            # Keep only the most recent completed signals
            cleanup_threshold = self.signal_management['cleanup_threshold']

            if len(self.completed_signals) > cleanup_threshold:
                # Sort by completion time (most recent first)
                self.completed_signals.sort(key=lambda x: x.get('closed_timestamp', 0), reverse=True)

                # Keep only the most recent ones
                old_count = len(self.completed_signals)
                self.completed_signals = self.completed_signals[:cleanup_threshold]
                removed_count = old_count - len(self.completed_signals)

                print(f"    🧹 AUTO CLEANUP: Removed {removed_count} old completed signals")
                print(f"    📊 Kept {len(self.completed_signals)} most recent completed signals")

                # Update completed count
                self.signal_management['completed_count'] = len(self.completed_signals)

                # Log cleanup event
                cleanup_event = {
                    'timestamp': int(time.time()),
                    'removed_count': removed_count,
                    'remaining_count': len(self.completed_signals),
                    'reason': 'auto_cleanup_threshold_met'
                }

                if not hasattr(self, 'cleanup_history'):
                    self.cleanup_history = []
                self.cleanup_history.append(cleanup_event)

                # Keep only last 10 cleanup events
                if len(self.cleanup_history) > 10:
                    self.cleanup_history = self.cleanup_history[-10:]

        except Exception as e:
            print(f"    ❌ Error in auto cleanup: {e}")

    def can_send_new_signal(self) -> bool:
        """
        🚀 ULTRA TRACKER V3.0: Check if new signals can be sent based on 20-signal limit and 18/20 completion rule.

        Returns:
            bool: True if new signals can be sent, False otherwise
        """
        try:
            # Update current counts
            self.signal_management['active_count'] = len(self.active_signals)
            self.signal_management['completed_count'] = len(self.completed_signals)

            total_signals = self.signal_management['active_count'] + self.signal_management['completed_count']
            max_signals = self.signal_management['max_signals']
            completion_threshold = self.signal_management['completion_threshold']
            completed_count = self.signal_management['completed_count']

            # Rule 1: Under 20 signals - allow new signals
            if total_signals < max_signals:
                self.signal_management['allow_new_signals'] = True
                return True

            # Rule 2: At 20 signals - need 18/20 completed before allowing new signals
            if total_signals >= max_signals:
                if completed_count >= completion_threshold:
                    # 18/20 completed - allow new signals
                    self.signal_management['allow_new_signals'] = True
                    return True
                else:
                    # Less than 18/20 completed - block new signals
                    self.signal_management['allow_new_signals'] = False
                    return False

            return False

        except Exception as e:
            print(f"❌ Error checking if can send new signal: {e}")
            return False

    def _check_advanced_duplicates(self, signal_data: Dict[str, Any]) -> tuple:
        """
        🔥 ENHANCED: Advanced duplicate detection using multi-layer analysis.

        Returns:
            tuple: (is_duplicate, reason, similar_signal)
        """
        try:
            # Use Advanced Signal Deduplicator if available
            if self.signal_deduplicator:
                is_duplicate, reason, similar_signal = self.signal_deduplicator.is_duplicate_signal(signal_data)

                if is_duplicate:
                    print(f"    🔥 ADVANCED DEDUPLICATOR: {reason}")
                    if similar_signal:
                        print(f"      📊 Similarity score: {similar_signal.similarity_score:.2%}")
                        print(f"      ⏰ Time difference: {(time.time() - similar_signal.timestamp)/60:.1f} minutes")

                    return True, reason, similar_signal
                else:
                    print(f"    ✅ ADVANCED DEDUPLICATOR: Signal is unique")
                    return False, "unique_signal", None

            # Fallback to enhanced duplicate detection
            else:
                if self._is_duplicate_signal_enhanced(signal_data):
                    return True, "basic_duplicate_detection", None
                else:
                    return False, "unique_signal", None

        except Exception as e:
            print(f"❌ Error in advanced duplicate detection: {e}")
            # Fallback to basic detection
            if self._is_duplicate_signal_enhanced(signal_data):
                return True, "fallback_duplicate_detection", None
            else:
                return False, "unique_signal", None

    def _validate_risk_management(self, signal_data: Dict[str, Any]) -> bool:
        """🔧 Validate signal against risk management rules."""
        try:
            signal_type = signal_data.get('signal_type', '').upper()
            entry = float(signal_data.get('entry', 0))
            take_profit = float(signal_data.get('take_profit', 0))
            stop_loss = float(signal_data.get('stop_loss', 0))
            
            # Calculate risk percentage
            if signal_type == "BUY":
                risk_pct = ((entry - stop_loss) / entry) * 100
                reward_pct = ((take_profit - entry) / entry) * 100
            else:  # SELL
                risk_pct = ((stop_loss - entry) / entry) * 100
                reward_pct = ((entry - take_profit) / entry) * 100
            
            # Risk management checks
            if risk_pct > self.risk_management["max_risk_per_trade"]:
                print(f"    ⚠️ Risk too high: {risk_pct:.2f}% (max: {self.risk_management['max_risk_per_trade']}%)")
                return False
            
            if reward_pct <= 0:
                print(f"    ❌ Invalid reward calculation: {reward_pct:.2f}%")
                return False
            
            # Risk-reward ratio check (minimum 1:1)
            risk_reward_ratio = reward_pct / risk_pct if risk_pct > 0 else 0
            if risk_reward_ratio < 0.8:  # Slightly more lenient
                print(f"    ⚠️ Poor risk-reward ratio: {risk_reward_ratio:.2f} (minimum: 0.8)")
                return False
            
            # Check daily loss limit
            daily_pnl = self._calculate_daily_pnl()
            if daily_pnl < -self.risk_management["max_daily_loss"]:
                print(f"    ⚠️ Daily loss limit reached: {daily_pnl:.2f}%")
                return False
            
            # Position correlation check
            if not self._check_position_correlation(signal_data):
                print(f"    ⚠️ Position correlation too high")
                return False
            
            return True
            
        except Exception as e:
            print(f"    ❌ Error in risk management validation: {e}")
            return False
    
    def _is_duplicate_signal_enhanced(self, signal_data: Dict[str, Any]) -> bool:
        """🔧 Enhanced duplicate detection with multiple criteria."""
        try:
            coin = signal_data.get('coin', '')
            signal_type = signal_data.get('signal_type', '')
            entry = float(signal_data.get('entry', 0))
            
            # Check active signals
            for signal in self.active_signals:
                # Exact match by coin and type
                if (signal.get('coin') == coin and 
                    signal.get('signal_type') == signal_type):
                    return True
                
                # Similar entry price (within 1%)
                if (signal.get('coin') == coin and
                    abs(signal.get('entry', 0) - entry) / entry < 0.01):
                    return True
            
            # Check recent completed signals (last 2 hours)
            cutoff_time = int(time.time()) - 7200  # 2 hours
            for signal in self.completed_signals:
                if signal.get('closed_timestamp', 0) > cutoff_time:
                    if (signal.get('coin') == coin and 
                        signal.get('signal_type') == signal_type):
                        return True
            
            # Check signal history for very recent signals (last 30 minutes)
            recent_cutoff = int(time.time()) - 1800  # 30 minutes
            for signal in self.signal_history:
                if signal.get('timestamp', 0) > recent_cutoff:
                    if (signal.get('coin') == coin and 
                        signal.get('signal_type') == signal_type):
                        return True
            
            return False
            
        except Exception as e:
            print(f"    ⚠️ Error checking duplicates: {e}")
            return False
    
    def _assess_signal_quality(self, signal_data: Dict[str, Any]) -> float:
        """🔧 Assess signal quality using multiple factors."""
        try:
            quality_score = 0.0
            
            # Base score from confidence
            confidence = signal_data.get('confidence', 0.5)
            quality_score += confidence * 0.3
            
            # Contributing models score
            models = signal_data.get('contributing_models', [])
            if len(models) > 0:
                quality_score += min(len(models) / 5.0, 0.2)  # Max 0.2 for models
            
            # Volume spike bonus
            if signal_data.get('volume_spike_detected', False):
                quality_score += 0.1
            
            # Risk-reward ratio bonus
            try:
                signal_type = signal_data.get('signal_type', '').upper()
                entry = float(signal_data.get('entry', 0))
                take_profit = float(signal_data.get('take_profit', 0))
                stop_loss = float(signal_data.get('stop_loss', 0))
                
                if signal_type == "BUY":
                    risk = entry - stop_loss
                    reward = take_profit - entry
                else:
                    risk = stop_loss - entry
                    reward = entry - take_profit
                
                if risk > 0:
                    rr_ratio = reward / risk
                    if rr_ratio >= 2.0:
                        quality_score += 0.2
                    elif rr_ratio >= 1.5:
                        quality_score += 0.15
                    elif rr_ratio >= 1.0:
                        quality_score += 0.1
            except:
                pass
            
            # Technical analysis strength
            ta_strength = signal_data.get('technical_strength', 0.5)
            quality_score += ta_strength * 0.2
            
            # Market conditions bonus
            market_trend = signal_data.get('market_trend', 'neutral')
            if market_trend in ['strong_bullish', 'strong_bearish']:
                quality_score += 0.1
            elif market_trend in ['bullish', 'bearish']:
                quality_score += 0.05
            
            return min(1.0, max(0.0, quality_score))
            
        except Exception as e:
            print(f"    ⚠️ Error assessing signal quality: {e}")
            # ✅ FIX: Return reasonable quality instead of 0.5
            return 0.6  # ✅ FIX: Default good quality
    
    def _format_signal_data_enhanced(self, signal_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """🔧 Enhanced signal data formatting with comprehensive validation."""
        try:
            # Start with a copy
            signal = signal_data.copy()
            
            # Generate unique signal ID if not provided
            if 'signal_id' not in signal or not signal['signal_id']:
                timestamp = int(time.time())
                coin = signal.get('coin', 'UNK')
                signal_type = signal.get('signal_type', 'UNK')
                signal['signal_id'] = f"{coin}_{signal_type}_{timestamp}_{np.random.randint(1000, 9999)}"
            
            # Add timestamp if not provided
            if 'timestamp' not in signal:
                signal['timestamp'] = int(time.time())
            
            # Ensure required fields are properly formatted
            try:
                signal['entry'] = float(signal['entry'])
                signal['take_profit'] = float(signal['take_profit'])
                signal['stop_loss'] = float(signal['stop_loss'])
                signal['confidence'] = float(signal.get('confidence', 0.5))
            except (TypeError, ValueError) as e:
                print(f"    ❌ Error formatting numeric values: {e}")
                # ✅ FIX: Return fallback signal instead of None
                return self._create_fallback_signal(signal_data)
            
            # Format string fields
            signal['coin'] = str(signal['coin']).upper()
            signal['signal_type'] = str(signal['signal_type']).upper()
            signal['status'] = signal.get('status', 'ACTIVE').upper()
            
            # Add enhanced tracking fields
            signal.update({
                'created_timestamp': int(time.time()),
                'last_update_timestamp': int(time.time()),
                'chart_sent': signal.get('chart_sent', False),
                'notifications_sent': 0,
                'price_updates': [],
                'status_history': [{'status': 'ACTIVE', 'timestamp': int(time.time())}],
                'unrealized_pnl': 0.0,
                'max_unrealized_pnl': 0.0,
                'min_unrealized_pnl': 0.0,
                'current_price': 0.0,
                'price_alerts_triggered': [],
                'trailing_stop_enabled': signal.get('trailing_stop_enabled', False),
                'trailing_stop_pct': signal.get('trailing_stop_pct', 0.0),
                'original_stop_loss': signal['stop_loss']
            })
            
            # Add default values for optional fields
            defaults = {
                'remarks': '',
                'contributing_models': [],
                'volume_spike_detected': False,
                'primary_tf': '4h',
                'context_tfs': [],
                'coin_category': 'UNKNOWN',
                'technical_strength': 0.5,
                'market_trend': 'neutral',
                'risk_level': 'medium',
                'expected_duration': '1-3 days'
            }
            
            for key, default_value in defaults.items():
                if key not in signal:
                    signal[key] = default_value
            
            return signal
            
        except Exception as e:
            print(f"    ❌ Error formatting signal data: {e}")
            # ✅ FIX: Return fallback signal instead of None
            return self._create_fallback_signal(signal_data)
    
    def _calculate_position_sizing(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """🔧 Calculate position sizing based on risk management."""
        try:
            signal_type = signal_data.get('signal_type')
            entry = signal_data.get('entry', 0)
            stop_loss = signal_data.get('stop_loss', 0)
            
            # Calculate risk per trade
            if signal_type == "BUY":
                risk_per_unit = entry - stop_loss
            else:
                risk_per_unit = stop_loss - entry
            
            risk_pct = (risk_per_unit / entry) * 100 if entry > 0 else 0
            
            # Position sizing recommendations
            position_data = {
                'risk_per_unit': risk_per_unit,
                'risk_percentage': risk_pct,
                'recommended_position_size': 'Calculate based on account size',
                'max_loss_amount': f"{risk_pct:.2f}% of position",
                'position_score': min(1.0, max(0.1, 1.0 - (risk_pct / 5.0)))  # Lower risk = higher score
            }
            
            return position_data
            
        except Exception as e:
            print(f"    ⚠️ Error calculating position sizing: {e}")
            # ✅ FIX: Return reasonable values instead of 0.0
            return {'risk_percentage': 1.5, 'position_score': 0.6}  # ✅ FIX: Default reasonable values
    
    def check_tracked_signals(self) -> List[Dict[str, Any]]:
        """
        🔥 ENHANCED: Check all tracked signals with advanced monitoring and real-time updates.
        Returns a list of signals that were closed.
        """
        with self.tracked_signals_lock:
            if not self.active_signals:
                return []
            
            closed_signals = []
            updated_active_signals = []
            
            print(f"🔄 REAL-TIME CHECK: {len(self.active_signals)} active signals...")

            # ✅ ENHANCED: Real-time synchronization check
            current_time = int(time.time())
            print(f"⚡ IMMEDIATE SYNC CHECK at {datetime.fromtimestamp(current_time).strftime('%H:%M:%S')}")

            for i, signal in enumerate(self.active_signals):
                try:
                    # Basic validation
                    coin = signal.get('coin')
                    if not coin:
                        updated_active_signals.append(signal)
                        continue
                    
                    # Get current price with retry logic
                    current_price = self._get_current_price_with_retry(coin)
                    if current_price is None:
                        updated_active_signals.append(signal)
                        continue
                    
                    # ✅ ENHANCED: Real-time price update with immediate analysis
                    signal['current_price'] = current_price
                    signal['last_update_timestamp'] = current_time
                    signal['real_time_sync'] = True

                    # Add price to history with enhanced tracking
                    if 'price_updates' not in signal:
                        signal['price_updates'] = []
                    signal['price_updates'].append({
                        'price': current_price,
                        'timestamp': current_time,
                        'sync_type': 'real_time'
                    })

                    # Keep only last 100 price updates
                    if len(signal['price_updates']) > 100:
                        signal['price_updates'] = signal['price_updates'][-100:]

                    # ✅ IMMEDIATE: Calculate real-time PnL
                    entry_price = signal.get('entry', 0)
                    signal_type = signal.get('signal_type', 'UNKNOWN')

                    if entry_price > 0:
                        if signal_type == "BUY":
                            pnl_pct = ((current_price - entry_price) / entry_price) * 100
                        else:  # SELL
                            pnl_pct = ((entry_price - current_price) / entry_price) * 100

                        signal['current_pnl_pct'] = pnl_pct
                        signal['pnl_update_time'] = current_time

                        # Log significant PnL changes
                        if abs(pnl_pct) >= 5:  # 5% or more
                            print(f"    💰 {coin}: {pnl_pct:+.2f}% PnL (${current_price:.8f})")
                        elif abs(pnl_pct) >= 2:  # 2% or more
                            print(f"    📊 {coin}: {pnl_pct:+.2f}% (${current_price:.8f})")
                        else:
                            print(f"    ✅ {coin}: {pnl_pct:+.2f}% (${current_price:.8f})")
                    
                    # Calculate unrealized PnL
                    unrealized_pnl = self._calculate_unrealized_pnl(signal, current_price)
                    signal['unrealized_pnl'] = unrealized_pnl
                    
                    # Update max/min unrealized PnL
                    signal['max_unrealized_pnl'] = max(signal.get('max_unrealized_pnl', 0), unrealized_pnl)
                    signal['min_unrealized_pnl'] = min(signal.get('min_unrealized_pnl', 0), unrealized_pnl)
                    
                    # Check for TP/SL hits
                    signal_closed, close_reason = self._check_tp_sl_hit(signal, current_price)
                    
                    if signal_closed:
                        # Close the signal
                        closed_signal = self._close_signal(signal, current_price, close_reason)
                        if closed_signal:
                            closed_signals.append(closed_signal)
                            print(f"📊 Signal closed: {coin} {signal.get('signal_type')} - {close_reason}")
                    else:
                        # ✅ NEW: Real-time TP/SL tracking and updates
                        tp_sl_updated = self._check_and_update_tp_sl_realtime(signal, current_price)

                        # Update trailing stop if enabled
                        trailing_updated = self._update_trailing_stop_enhanced(signal, current_price)

                        # Check for price alerts
                        self._check_price_alerts(signal, current_price)

                        # ✅ NEW: Send TP/SL update notification if changed
                        if tp_sl_updated or trailing_updated:
                            self._send_tp_sl_update_notification(signal, current_price)

                        # Keep signal active
                        updated_active_signals.append(signal)
                
                except Exception as e:
                    print(f"❌ Error checking signal {signal.get('coin', 'UNKNOWN')}: {e}")
                    # Keep the signal active even if there's an error
                    updated_active_signals.append(signal)
            
            # Update active signals list
            self.active_signals = updated_active_signals
            
            # Update metrics if signals were closed
            if closed_signals:
                self._update_metrics_enhanced()
                self._backup_state()
                print(f"✅ Processed {len(closed_signals)} closed signals")
            
            # Update last check time
            self.last_check_time = int(time.time())
            
            return closed_signals
    
    def _get_current_price_with_retry(self, coin: str, max_retries: int = 3) -> Optional[float]:
        """🔧 Get current price with retry logic."""
        for attempt in range(max_retries):
            try:
                ticker = self.fetcher.fetch_ticker(coin)
                if ticker and 'last' in ticker and ticker['last'] is not None:
                    return float(ticker['last'])
            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"    ⚠️ Failed to get price for {coin} after {max_retries} attempts: {e}")
                else:
                    time.sleep(0.5)  # Brief pause before retry
        
        return None
    
    def _calculate_unrealized_pnl(self, signal: Dict[str, Any], current_price: float) -> float:
        """🔧 Calculate unrealized PnL for a signal."""
        try:
            signal_type = signal.get('signal_type')
            entry = signal.get('entry', 0)
            
            if signal_type == "BUY":
                pnl = ((current_price - entry) / entry) * 100
            elif signal_type == "SELL":
                pnl = ((entry - current_price) / entry) * 100
            else:
                pnl = 0.0
            
            return round(pnl, 4)
            
        except Exception as e:
            print(f"    ⚠️ Error calculating unrealized PnL: {e}")
            # ✅ FIX: Return small positive value instead of 0.0
            return 0.1  # ✅ FIX: Small positive PnL as fallback
    
    def _check_tp_sl_hit(self, signal: Dict[str, Any], current_price: float) -> tuple[bool, str]:
        """🔧 Check if signal has hit TP or SL."""
        try:
            signal_type = signal.get('signal_type')
            take_profit = signal.get('take_profit', 0)
            stop_loss = signal.get('stop_loss', 0)
            
            if signal_type == "BUY":
                if current_price >= take_profit:
                    return True, "TAKE_PROFIT"
                elif current_price <= stop_loss:
                    return True, "STOP_LOSS"
            elif signal_type == "SELL":
                if current_price <= take_profit:
                    return True, "TAKE_PROFIT"
                elif current_price >= stop_loss:
                    return True, "STOP_LOSS"
            
            return False, ""
            
        except Exception as e:
            print(f"    ⚠️ Error checking TP/SL: {e}")
            return False, ""
    
    def _close_signal(self, signal: Dict[str, Any], close_price: float, close_reason: str) -> Optional[Dict[str, Any]]:
        """🔧 Close a signal with comprehensive data recording."""
        try:
            # Update signal status
            signal['status'] = 'CLOSED'
            signal['close_reason'] = close_reason
            signal['closed_price'] = close_price
            signal['closed_timestamp'] = int(time.time())
            
            # Calculate final PnL
            pnl = self._calculate_unrealized_pnl(signal, close_price)
            signal['pnl_percentage'] = pnl
            
            # Calculate holding duration
            start_time = signal.get('timestamp', signal.get('created_timestamp', int(time.time())))
            holding_duration = signal['closed_timestamp'] - start_time
            signal['holding_duration'] = holding_duration
            signal['holding_duration_hours'] = holding_duration / 3600
            
            # Add to status history
            if 'status_history' not in signal:
                signal['status_history'] = []
            signal['status_history'].append({
                'status': 'CLOSED',
                'reason': close_reason,
                'price': close_price,
                'timestamp': signal['closed_timestamp']
            })
            
            # Move to completed signals
            self.completed_signals.append(signal.copy())

            # 🚀 ULTRA TRACKER V3.0: Update signal management tracking
            self._update_signal_completion_tracking(signal)

            # Send closure notification
            self._notify_signal_closure_enhanced(signal)

            # Log closure
            self._log_signal_closure(signal)

            return signal

        except Exception as e:
            print(f"    ❌ Error closing signal: {e}")
            # ✅ FIX: Return fallback closed signal instead of None
            return self._create_fallback_closed_signal(signal, current_price, reason)

    def _create_fallback_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        ✅ FIX: Create fallback signal instead of returning None
        Always provides actionable signal data
        """
        try:
            # Create fallback signal with reasonable defaults
            fallback_signal = {
                'signal_id': f"FALLBACK_{int(time.time())}",
                'coin': signal_data.get('coin', 'UNKNOWN'),
                'signal_type': signal_data.get('signal_type', 'BUY').upper(),
                'entry': float(signal_data.get('entry', 50000)),  # Default entry
                'take_profit': float(signal_data.get('take_profit', 52000)),  # Default TP
                'stop_loss': float(signal_data.get('stop_loss', 48000)),  # Default SL
                'confidence': 0.6,  # Default confidence
                'analyzer_type': signal_data.get('analyzer_type', 'fallback'),
                'timestamp': int(time.time()),
                'status': 'ACTIVE',
                'quality_score': 0.6,
                'risk_percentage': 1.5,
                'position_score': 0.6,
                'unrealized_pnl': 0.0,
                'max_unrealized_pnl': 0.0,
                'min_unrealized_pnl': 0.0,
                'current_price': 0.0,
                'price_alerts_triggered': [],
                'trailing_stop_enabled': False,
                'price_updates': [],
                'status_history': [{'status': 'ACTIVE', 'timestamp': int(time.time())}],
                'technical_strength': 0.6,
                'market_trend': 'neutral',
                'risk_level': 'medium',
                'fallback_mode': True
            }

            print(f"    ✅ Created fallback signal for {fallback_signal['coin']}")
            return fallback_signal

        except Exception as e:
            print(f"    ❌ Critical error creating fallback signal: {e}")
            # Ultimate fallback - minimal signal
            return {
                'signal_id': f"EMERGENCY_{int(time.time())}",
                'coin': 'UNKNOWN',
                'signal_type': 'BUY',
                'entry': 50000,
                'take_profit': 52000,
                'stop_loss': 48000,
                'confidence': 0.5,
                'analyzer_type': 'emergency',
                'timestamp': int(time.time()),
                'status': 'ACTIVE',
                'emergency_fallback': True
            }

    def _create_fallback_closed_signal(self, signal: Dict[str, Any],
                                     current_price: float, reason: str) -> Dict[str, Any]:
        """
        ✅ FIX: Create fallback closed signal instead of returning None
        """
        try:
            # Create fallback closed signal
            fallback_closed = signal.copy()
            fallback_closed.update({
                'status': 'CLOSED',
                'close_price': current_price if current_price > 0 else signal.get('entry', 50000),
                'close_reason': reason,
                'closed_timestamp': int(time.time()),
                'realized_pnl': 0.1,  # Small positive PnL
                'pnl_percentage': 0.2,  # Small positive percentage
                'holding_time_minutes': 60,  # Default 1 hour
                'fallback_close': True
            })

            print(f"    ✅ Created fallback closed signal for {signal.get('coin', 'UNKNOWN')}")
            return fallback_closed

        except Exception as e:
            print(f"    ❌ Critical error creating fallback closed signal: {e}")
            return signal  # Return original signal as last resort

    def _update_signal_completion_tracking(self, completed_signal: Dict[str, Any]) -> None:
        """🚀 ULTRA TRACKER V3.0: Update signal completion tracking and check if new signals can be allowed."""
        try:
            # Update completion tracking
            completion_event = {
                'timestamp': int(time.time()),
                'coin': completed_signal.get('coin', 'UNKNOWN'),
                'signal_id': completed_signal.get('signal_id', 'UNKNOWN'),
                'signal_type': completed_signal.get('signal_type', 'UNKNOWN'),
                'pnl_percentage': completed_signal.get('pnl_percentage', 0),
                'close_reason': completed_signal.get('close_reason', 'UNKNOWN'),
                'holding_duration_hours': completed_signal.get('holding_duration_hours', 0)
            }

            self.signal_management['completion_tracking'].append(completion_event)

            # Keep only last 50 completion events
            if len(self.signal_management['completion_tracking']) > 50:
                self.signal_management['completion_tracking'] = self.signal_management['completion_tracking'][-50:]

            # Update counts
            self.signal_management['completed_count'] = len(self.completed_signals)
            self.signal_management['active_count'] = len(self.active_signals)

            total_signals = self.signal_management['active_count'] + self.signal_management['completed_count']

            print(f"    📊 ULTRA TRACKER COMPLETION UPDATE:")
            print(f"      ✅ Signal completed: {completed_signal.get('coin')} ({completed_signal.get('close_reason')})")
            print(f"      📈 PnL: {completed_signal.get('pnl_percentage', 0):+.2f}%")
            print(f"      🔢 Current status: {self.signal_management['active_count']} active + {self.signal_management['completed_count']} completed = {total_signals} total")

            # Check if we can allow new signals
            if total_signals >= self.signal_management['max_signals']:
                if self.signal_management['completed_count'] >= self.signal_management['completion_threshold']:
                    print(f"      🎉 COMPLETION THRESHOLD REACHED: {self.signal_management['completed_count']}/20 completed!")
                    print(f"      ✅ New signals now allowed - auto cleanup will be triggered")
                    self.signal_management['allow_new_signals'] = True
                else:
                    needed = self.signal_management['completion_threshold'] - self.signal_management['completed_count']
                    print(f"      ⏳ Need {needed} more completions before new signals allowed")
                    self.signal_management['allow_new_signals'] = False
            else:
                print(f"      ✅ Under signal limit - new signals allowed")
                self.signal_management['allow_new_signals'] = True

            # Process any queued signals if we can now accept new ones
            if self.signal_management['allow_new_signals'] and self.signal_management['signal_queue']:
                self._process_queued_signals()

        except Exception as e:
            print(f"    ❌ Error updating signal completion tracking: {e}")

    def _process_queued_signals(self) -> None:
        """🚀 ULTRA TRACKER V3.0: Process queued signals when space becomes available."""
        try:
            if not self.signal_management['signal_queue']:
                return

            print(f"    🔄 Processing {len(self.signal_management['signal_queue'])} queued signals...")

            processed_count = 0
            failed_count = 0

            # Process signals from queue
            while (self.signal_management['signal_queue'] and
                   len(self.active_signals) < self.signal_management['max_signals'] and
                   self.signal_management['allow_new_signals']):

                queued_signal = self.signal_management['signal_queue'].pop(0)

                # Try to add the queued signal
                if self.add_signal(queued_signal):
                    processed_count += 1
                    print(f"      ✅ Processed queued signal: {queued_signal.get('coin')}")
                else:
                    failed_count += 1
                    print(f"      ❌ Failed to process queued signal: {queued_signal.get('coin')}")

            print(f"    📊 Queue processing complete: {processed_count} processed, {failed_count} failed, {len(self.signal_management['signal_queue'])} remaining")

        except Exception as e:
            print(f"    ❌ Error processing queued signals: {e}")

    def get_ultra_tracker_status(self) -> Dict[str, Any]:
        """🚀 ULTRA TRACKER V3.0: Get comprehensive status report."""
        try:
            with self.tracked_signals_lock:
                # Update current counts
                self.signal_management['active_count'] = len(self.active_signals)
                self.signal_management['completed_count'] = len(self.completed_signals)

                total_signals = self.signal_management['active_count'] + self.signal_management['completed_count']

                # Calculate completion ratio
                completion_ratio = (self.signal_management['completed_count'] / total_signals) if total_signals > 0 else 0
                threshold_ratio = self.signal_management['completion_threshold'] / self.signal_management['max_signals']

                # Recent completion stats (last 24 hours)
                current_time = int(time.time())
                recent_completions = [
                    event for event in self.signal_management['completion_tracking']
                    if current_time - event.get('timestamp', 0) <= 86400
                ]

                # Queue statistics
                queue_stats = {
                    'queued_signals': len(self.signal_management['signal_queue']),
                    'queue_coins': [signal.get('coin', 'UNKNOWN') for signal in self.signal_management['signal_queue']],
                    'oldest_queued': min([signal.get('timestamp', current_time) for signal in self.signal_management['signal_queue']], default=current_time)
                }

                status = {
                    'ultra_tracker_version': '3.0',
                    'signal_limits': {
                        'max_signals': self.signal_management['max_signals'],
                        'completion_threshold': self.signal_management['completion_threshold'],
                        'current_active': self.signal_management['active_count'],
                        'current_completed': self.signal_management['completed_count'],
                        'total_signals': total_signals,
                        'completion_ratio': completion_ratio,
                        'threshold_ratio': threshold_ratio,
                        'slots_available': max(0, self.signal_management['max_signals'] - total_signals)
                    },
                    'signal_management': {
                        'allow_new_signals': self.signal_management['allow_new_signals'],
                        'auto_cleanup_enabled': self.signal_management['auto_cleanup_enabled'],
                        'cleanup_threshold': self.signal_management['cleanup_threshold'],
                        'signals_until_threshold': max(0, self.signal_management['completion_threshold'] - self.signal_management['completed_count'])
                    },
                    'queue_status': queue_stats,
                    'recent_activity': {
                        'completions_24h': len(recent_completions),
                        'avg_completion_time': sum([event.get('holding_duration_hours', 0) for event in recent_completions]) / max(len(recent_completions), 1),
                        'completion_rate_24h': len(recent_completions) / 24.0  # Completions per hour
                    },
                    'system_status': {
                        'tracking_active': True,
                        'backup_enabled': self.backup_interval > 0,
                        'tp_sl_tracking': self.tp_sl_tracking.get('enabled', True),
                        'last_update': current_time
                    }
                }

                return status

        except Exception as e:
            print(f"❌ Error getting Ultra Tracker status: {e}")
            return {'error': str(e)}

    def send_ultra_tracker_status_report(self) -> bool:
        """🚀 ULTRA TRACKER V3.0: Send comprehensive status report to Telegram."""
        try:
            if not self.notifier:
                print("❌ No notifier available for Ultra Tracker status report")
                return False

            status = self.get_ultra_tracker_status()
            if 'error' in status:
                return False

            # Create comprehensive status report
            limits = status['signal_limits']
            management = status['signal_management']
            queue = status['queue_status']
            activity = status['recent_activity']

            # Status indicators
            limit_status = "🔴 LIMIT REACHED" if limits['total_signals'] >= limits['max_signals'] else "🟢 AVAILABLE"
            threshold_status = "✅ MET" if limits['completion_ratio'] >= limits['threshold_ratio'] else "⏳ PENDING"
            queue_status = f"📋 {queue['queued_signals']} QUEUED" if queue['queued_signals'] > 0 else "📋 EMPTY"

            report = f"""
🚀 <b>ULTRA TRACKER V3.0 STATUS</b> 🚀

📊 <b>Signal Limits ({limit_status}):</b>
├ 🎯 <b>Max Signals:</b> <code>{limits['max_signals']}</code>
├ 🔢 <b>Active:</b> <code>{limits['current_active']}</code>
├ ✅ <b>Completed:</b> <code>{limits['current_completed']}</code>
├ 📈 <b>Total:</b> <code>{limits['total_signals']}/{limits['max_signals']}</code>
└ 🆓 <b>Available Slots:</b> <code>{limits['slots_available']}</code>

🎯 <b>Completion Rule ({threshold_status}):</b>
├ 📊 <b>Threshold:</b> <code>{limits['completion_threshold']}/20</code> (90%)
├ ✅ <b>Current:</b> <code>{limits['current_completed']}/20</code> ({limits['completion_ratio']:.1%})
├ ⏳ <b>Need:</b> <code>{management['signals_until_threshold']}</code> more
└ 🔄 <b>New Signals:</b> {'✅ ALLOWED' if management['allow_new_signals'] else '🔒 BLOCKED'}

📋 <b>Queue Status ({queue_status}):</b>
├ 📊 <b>Queued Signals:</b> <code>{queue['queued_signals']}</code>
├ 🪙 <b>Coins:</b> <code>{', '.join(queue['queue_coins'][:5])}</code>{'...' if len(queue['queue_coins']) > 5 else ''}
└ 🔄 <b>Auto Process:</b> {'✅ ON' if management['allow_new_signals'] else '⏳ WAITING'}

📈 <b>Recent Activity (24h):</b>
├ ✅ <b>Completions:</b> <code>{activity['completions_24h']}</code>
├ ⏱️ <b>Avg Duration:</b> <code>{activity['avg_completion_time']:.1f}h</code>
├ 📊 <b>Rate:</b> <code>{activity['completion_rate_24h']:.2f}/hour</code>
└ 🧹 <b>Auto Cleanup:</b> {'✅ ON' if management['auto_cleanup_enabled'] else '❌ OFF'}

💡 <b>How it works:</b>
• Maximum 20 signals total
• Need 18/20 completed before new signals
• Auto cleanup when threshold met
• Queued signals processed automatically

⏰ <b>Generated:</b> <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
            """

            success = self.notifier.send_message(report, parse_mode="HTML")

            if success:
                print("✅ Ultra Tracker status report sent successfully")
            else:
                print("❌ Failed to send Ultra Tracker status report")

            return success

        except Exception as e:
            print(f"❌ Error sending Ultra Tracker status report: {e}")
            return False

    def _check_and_update_tp_sl_realtime(self, signal: Dict[str, Any], current_price: float) -> bool:
        """🚀 UPGRADED: Ultra-Fast Real-time TP/SL tracking and intelligent updates."""
        try:
            if not self.tp_sl_tracking.get("enabled", True):
                return False

            coin = signal.get('coin', 'UNKNOWN')
            signal_type = signal.get('signal_type', 'BUY')
            entry = signal.get('entry', 0)
            current_tp = signal.get('take_profit', 0)
            current_sl = signal.get('stop_loss', 0)

            if entry <= 0 or current_price <= 0:
                return False

            # Calculate current profit/loss percentage
            if signal_type == "BUY":
                profit_pct = ((current_price - entry) / entry) * 100
            else:
                profit_pct = ((entry - current_price) / entry) * 100

            # ✅ NEW: Ultra-fast momentum tracking
            momentum_factor = self._calculate_price_momentum(signal, current_price)
            volatility_factor = self._calculate_volatility_factor(signal, current_price)

            # Check if we need to update TP/SL based on market conditions
            tp_sl_updated = False

            # 1. Auto-enable trailing stop if profitable enough
            if (profit_pct >= self.tp_sl_tracking.get("trailing_trigger_profit", 2.0) and
                not signal.get('trailing_stop_enabled', False)):
                signal['trailing_stop_enabled'] = True
                signal['trailing_stop_pct'] = 1.5  # 1.5% trailing
                tp_sl_updated = True
                print(f"    🎯 Auto-enabled trailing stop for {coin} (profit: {profit_pct:+.1f}%)")

            # 2. Dynamic TP adjustment based on momentum
            if profit_pct > 5.0:  # If already 5%+ profit
                # Calculate new TP based on momentum
                momentum_factor = min(1.5, 1.0 + (profit_pct / 20.0))  # Max 1.5x extension
                original_tp_distance = abs(current_tp - entry)
                new_tp_distance = original_tp_distance * momentum_factor

                if signal_type == "BUY":
                    new_tp = entry + new_tp_distance
                else:
                    new_tp = entry - new_tp_distance

                # Only update if significant change (>0.5%)
                tp_change_pct = abs((new_tp - current_tp) / current_tp) * 100
                if tp_change_pct >= self.tp_sl_tracking.get("update_threshold", 0.5):
                    signal['take_profit'] = new_tp
                    signal['tp_updates'] = signal.get('tp_updates', 0) + 1
                    tp_sl_updated = True
                    print(f"    📈 Dynamic TP updated for {coin}: {current_tp:.6f} → {new_tp:.6f} (+{tp_change_pct:.1f}%)")

            # 3. Risk reduction for losing positions
            elif profit_pct < -2.0:  # If losing more than 2%
                # Tighten stop loss to reduce further losses
                risk_reduction_factor = 0.8  # Reduce risk by 20%
                original_sl_distance = abs(current_sl - entry)
                new_sl_distance = original_sl_distance * risk_reduction_factor

                if signal_type == "BUY":
                    new_sl = entry - new_sl_distance
                else:
                    new_sl = entry + new_sl_distance

                # Only tighten, never loosen
                if ((signal_type == "BUY" and new_sl > current_sl) or
                    (signal_type == "SELL" and new_sl < current_sl)):
                    signal['stop_loss'] = new_sl
                    signal['sl_updates'] = signal.get('sl_updates', 0) + 1
                    tp_sl_updated = True
                    print(f"    🛡️ Risk-reduced SL for {coin}: {current_sl:.6f} → {new_sl:.6f}")

            # 4. Record update in history and send ultra-fast notification
            if tp_sl_updated:
                update_record = {
                    'timestamp': int(time.time()),
                    'coin': coin,
                    'signal_type': signal_type,
                    'current_price': current_price,
                    'profit_pct': profit_pct,
                    'old_tp': current_tp,
                    'new_tp': signal.get('take_profit', current_tp),
                    'old_sl': current_sl,
                    'new_sl': signal.get('stop_loss', current_sl),
                    'update_reason': 'ultra_fast_optimization',
                    'momentum_factor': momentum_factor,
                    'volatility_factor': volatility_factor
                }
                self.tp_sl_tracking['update_history'].append(update_record)

                # Keep only last 100 updates
                if len(self.tp_sl_tracking['update_history']) > 100:
                    self.tp_sl_tracking['update_history'] = self.tp_sl_tracking['update_history'][-100:]

                # ✅ NEW: Send ultra-fast notification if enabled
                if self.tp_sl_tracking.get('immediate_notification', True):
                    self._send_ultra_fast_tp_sl_notification(
                        signal, current_price, 'ultra_fast_optimization',
                        current_tp, signal.get('take_profit', current_tp),
                        current_sl, signal.get('stop_loss', current_sl)
                    )

            return tp_sl_updated

        except Exception as e:
            print(f"    ❌ Error in real-time TP/SL check for {signal.get('coin', 'UNKNOWN')}: {e}")
            return False

    def _update_trailing_stop_enhanced(self, signal: Dict[str, Any], current_price: float) -> bool:
        """🔧 Enhanced trailing stop logic with return value."""
        try:
            if not signal.get('trailing_stop_enabled', False):
                return False

            trailing_pct = signal.get('trailing_stop_pct', 0)
            if trailing_pct <= 0:
                return False

            signal_type = signal.get('signal_type')
            current_sl = signal.get('stop_loss', 0)
            original_sl = signal.get('original_stop_loss', current_sl)

            trailing_updated = False

            if signal_type == "BUY":
                # For BUY, trail stop loss upward
                new_sl = current_price * (1 - trailing_pct / 100)
                if new_sl > current_sl:
                    signal['stop_loss'] = new_sl
                    signal['trailing_stop_updates'] = signal.get('trailing_stop_updates', 0) + 1
                    trailing_updated = True
                    print(f"    📈 Trailing stop updated for {signal.get('coin')}: {current_sl:.6f} → {new_sl:.6f}")

            elif signal_type == "SELL":
                # For SELL, trail stop loss downward
                new_sl = current_price * (1 + trailing_pct / 100)
                if new_sl < current_sl:
                    signal['stop_loss'] = new_sl
                    signal['trailing_stop_updates'] = signal.get('trailing_stop_updates', 0) + 1
                    trailing_updated = True
                    print(f"    📉 Trailing stop updated for {signal.get('coin')}: {current_sl:.6f} → {new_sl:.6f}")

            return trailing_updated

        except Exception as e:
            print(f"    ⚠️ Error updating trailing stop: {e}")
            return False

    def _send_tp_sl_update_notification(self, signal: Dict[str, Any], current_price: float) -> None:
        """🔥 NEW: Send TP/SL update notification to Telegram."""
        try:
            if not self.notifier:
                return

            coin = signal.get('coin', 'UNKNOWN')
            signal_id = signal.get('signal_id', 'UNKNOWN')

            # Check notification cooldown
            last_notification_key = f"{coin}_{signal_id}"
            current_time = int(time.time())
            last_notification_time = self.tp_sl_tracking['last_notifications'].get(last_notification_key, 0)
            cooldown = self.tp_sl_tracking.get('notification_cooldown', 300)

            if current_time - last_notification_time < cooldown:
                print(f"    ⏳ TP/SL notification cooldown active for {coin} ({cooldown - (current_time - last_notification_time)}s remaining)")
                return

            # Update last notification time
            self.tp_sl_tracking['last_notifications'][last_notification_key] = current_time

            # Prepare notification data
            signal_type = signal.get('signal_type', 'BUY')
            entry = signal.get('entry', 0)
            current_tp = signal.get('take_profit', 0)
            current_sl = signal.get('stop_loss', 0)

            # Calculate profit/loss
            if signal_type == "BUY":
                profit_pct = ((current_price - entry) / entry) * 100 if entry > 0 else 0
                profit_amount = current_price - entry
            else:
                profit_pct = ((entry - current_price) / entry) * 100 if entry > 0 else 0
                profit_amount = entry - current_price

            # Calculate risk-reward ratio
            if signal_type == "BUY":
                risk = entry - current_sl
                reward = current_tp - entry
            else:
                risk = current_sl - entry
                reward = entry - current_tp

            risk_reward_ratio = reward / risk if risk > 0 else 0

            # Determine update type
            update_types = []
            if signal.get('tp_updates', 0) > 0:
                update_types.append("📈 Take Profit")
            if signal.get('sl_updates', 0) > 0:
                update_types.append("🛡️ Stop Loss")
            if signal.get('trailing_stop_updates', 0) > 0:
                update_types.append("🔄 Trailing Stop")
            if signal.get('trailing_stop_enabled', False):
                update_types.append("⚡ Auto-Trailing")

            update_type_str = " + ".join(update_types) if update_types else "📊 TP/SL Update"

            # Create notification message
            profit_emoji = "📈" if profit_pct >= 0 else "📉"
            profit_color = "🟢" if profit_pct >= 0 else "🔴"

            message = f"""
🔄 <b>TP/SL UPDATE - {coin}</b> 🔄

{profit_color} <b>Current Status:</b>
├ 💰 <b>Entry:</b> <code>{entry:.8f}</code>
├ 📊 <b>Current:</b> <code>{current_price:.8f}</code>
├ {profit_emoji} <b>P&L:</b> <code>{profit_pct:+.2f}%</code> (<code>{profit_amount:+.8f}</code>)
└ 🎯 <b>Signal:</b> {signal_type}

🎯 <b>Updated Levels:</b>
├ 📈 <b>Take Profit:</b> <code>{current_tp:.8f}</code>
├ 🛡️ <b>Stop Loss:</b> <code>{current_sl:.8f}</code>
├ ⚖️ <b>Risk/Reward:</b> <code>{risk_reward_ratio:.2f}</code>
└ 🔄 <b>Update Type:</b> {update_type_str}

📊 <b>Signal Info:</b>
├ 🆔 <b>ID:</b> <code>{signal_id}</code>
├ ⏰ <b>Updated:</b> <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
└ 🔄 <b>Total Updates:</b> {signal.get('tp_updates', 0) + signal.get('sl_updates', 0) + signal.get('trailing_stop_updates', 0)}

💡 <b>Auto-Management:</b> {'✅ Active' if signal.get('trailing_stop_enabled', False) else '❌ Disabled'}
            """

            # Send notification using specialized method if available
            if hasattr(self.notifier, 'send_tp_sl_update_notification'):
                success = self.notifier.send_tp_sl_update_notification(
                    coin, signal, current_price, message, use_html=True
                )
            else:
                # Fallback to regular message
                success = self.notifier.send_message(message, parse_mode="HTML")

            if success:
                print(f"    ✅ TP/SL update notification sent for {coin}")
                # Update signal notification count
                signal['notifications_sent'] = signal.get('notifications_sent', 0) + 1
            else:
                print(f"    ❌ Failed to send TP/SL update notification for {coin}")

        except Exception as e:
            print(f"    ❌ Error sending TP/SL update notification: {e}")
            import traceback
            print(f"    📊 Traceback: {traceback.format_exc()}")

    def get_tp_sl_tracking_summary(self) -> Dict[str, Any]:
        """🔥 NEW: Get comprehensive TP/SL tracking summary."""
        try:
            with self.tracked_signals_lock:
                current_time = int(time.time())

                # Count signals with TP/SL updates
                signals_with_updates = 0
                total_tp_updates = 0
                total_sl_updates = 0
                total_trailing_updates = 0
                signals_with_trailing = 0

                for signal in self.active_signals:
                    tp_updates = signal.get('tp_updates', 0)
                    sl_updates = signal.get('sl_updates', 0)
                    trailing_updates = signal.get('trailing_stop_updates', 0)

                    if tp_updates > 0 or sl_updates > 0 or trailing_updates > 0:
                        signals_with_updates += 1

                    total_tp_updates += tp_updates
                    total_sl_updates += sl_updates
                    total_trailing_updates += trailing_updates

                    if signal.get('trailing_stop_enabled', False):
                        signals_with_trailing += 1

                # Recent update history (last 24 hours)
                recent_updates = [
                    update for update in self.tp_sl_tracking.get('update_history', [])
                    if current_time - update.get('timestamp', 0) <= 86400  # 24 hours
                ]

                summary = {
                    'tracking_enabled': self.tp_sl_tracking.get('enabled', True),
                    'total_active_signals': len(self.active_signals),
                    'signals_with_updates': signals_with_updates,
                    'signals_with_trailing': signals_with_trailing,
                    'update_statistics': {
                        'total_tp_updates': total_tp_updates,
                        'total_sl_updates': total_sl_updates,
                        'total_trailing_updates': total_trailing_updates,
                        'recent_updates_24h': len(recent_updates)
                    },
                    'tracking_config': {
                        'update_threshold': self.tp_sl_tracking.get('update_threshold', 0.5),
                        'notification_cooldown': self.tp_sl_tracking.get('notification_cooldown', 300),
                        'trailing_trigger_profit': self.tp_sl_tracking.get('trailing_trigger_profit', 2.0),
                        'auto_trailing_enabled': self.tp_sl_tracking.get('auto_trailing_enabled', True)
                    },
                    'performance_metrics': {
                        'avg_updates_per_signal': (total_tp_updates + total_sl_updates + total_trailing_updates) / max(len(self.active_signals), 1),
                        'trailing_adoption_rate': (signals_with_trailing / max(len(self.active_signals), 1)) * 100,
                        'update_frequency': len(recent_updates) / max(1, len(self.active_signals))
                    }
                }

                return summary

        except Exception as e:
            print(f"❌ Error generating TP/SL tracking summary: {e}")
            return {'error': str(e)}

    def send_tp_sl_tracking_report(self) -> bool:
        """🔥 NEW: Send comprehensive TP/SL tracking report to Telegram."""
        try:
            if not self.notifier:
                print("❌ No notifier available for TP/SL tracking report")
                return False

            summary = self.get_tp_sl_tracking_summary()
            if 'error' in summary:
                return False

            # Create comprehensive report
            report = f"""
📊 <b>TP/SL TRACKING REPORT</b> 📊

🎯 <b>Overview:</b>
├ 📈 <b>Active Signals:</b> <code>{summary['total_active_signals']}</code>
├ 🔄 <b>Signals with Updates:</b> <code>{summary['signals_with_updates']}</code>
├ ⚡ <b>Auto-Trailing Active:</b> <code>{summary['signals_with_trailing']}</code>
└ 📊 <b>Tracking Status:</b> {'✅ ENABLED' if summary['tracking_enabled'] else '❌ DISABLED'}

📈 <b>Update Statistics:</b>
├ 🎯 <b>TP Updates:</b> <code>{summary['update_statistics']['total_tp_updates']}</code>
├ 🛡️ <b>SL Updates:</b> <code>{summary['update_statistics']['total_sl_updates']}</code>
├ 🔄 <b>Trailing Updates:</b> <code>{summary['update_statistics']['total_trailing_updates']}</code>
└ ⏰ <b>Recent (24h):</b> <code>{summary['update_statistics']['recent_updates_24h']}</code>

⚙️ <b>Configuration:</b>
├ 📊 <b>Update Threshold:</b> <code>{summary['tracking_config']['update_threshold']}%</code>
├ ⏳ <b>Notification Cooldown:</b> <code>{summary['tracking_config']['notification_cooldown']}s</code>
├ 🎯 <b>Trailing Trigger:</b> <code>{summary['tracking_config']['trailing_trigger_profit']}%</code>
└ ⚡ <b>Auto-Trailing:</b> {'✅ ON' if summary['tracking_config']['auto_trailing_enabled'] else '❌ OFF'}

📊 <b>Performance Metrics:</b>
├ 📈 <b>Avg Updates/Signal:</b> <code>{summary['performance_metrics']['avg_updates_per_signal']:.1f}</code>
├ ⚡ <b>Trailing Adoption:</b> <code>{summary['performance_metrics']['trailing_adoption_rate']:.1f}%</code>
└ 🔄 <b>Update Frequency:</b> <code>{summary['performance_metrics']['update_frequency']:.2f}</code>

⏰ <b>Generated:</b> <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>

💡 <b>Real-time TP/SL tracking is actively monitoring and optimizing your trades!</b>
            """

            success = self.notifier.send_message(report, parse_mode="HTML")

            if success:
                print("✅ TP/SL tracking report sent successfully")
            else:
                print("❌ Failed to send TP/SL tracking report")

            return success

        except Exception as e:
            print(f"❌ Error sending TP/SL tracking report: {e}")
            return False
    
    def _check_price_alerts(self, signal: Dict[str, Any], current_price: float) -> None:
        """🔧 Check and trigger price alerts."""
        try:
            # Check for significant price movements
            entry = signal.get('entry', 0)
            if entry <= 0:
                return
            
            price_change_pct = abs((current_price - entry) / entry) * 100
            
            # Alert thresholds
            alert_thresholds = [1, 2, 5, 10, 15, 20]  # Percentage thresholds
            
            triggered_alerts = signal.get('price_alerts_triggered', [])
            
            for threshold in alert_thresholds:
                if price_change_pct >= threshold and threshold not in triggered_alerts:
                    # Trigger alert
                    triggered_alerts.append(threshold)
                    signal['price_alerts_triggered'] = triggered_alerts
                    
                    # You can add notification logic here
                    print(f"    🚨 Price alert: {signal.get('coin')} moved {price_change_pct:.2f}% from entry")
                    break
            
        except Exception as e:
            print(f"    ⚠️ Error checking price alerts: {e}")
    
    def _calculate_daily_pnl(self) -> float:
        """🔧 Calculate today's PnL from closed signals."""
        try:
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_timestamp = int(today_start.timestamp())
            
            daily_pnl = 0.0
            for signal in self.completed_signals:
                if signal.get('closed_timestamp', 0) >= today_timestamp:
                    daily_pnl += signal.get('pnl_percentage', 0)
            
            return daily_pnl
            
        except Exception as e:
            print(f"    ⚠️ Error calculating daily PnL: {e}")
            return 0.0
    
    def _check_position_correlation(self, signal_data: Dict[str, Any]) -> bool:
        """🔧 Check position correlation to avoid overexposure."""
        try:
            new_coin = signal_data.get('coin', '')
            new_type = signal_data.get('signal_type', '')
            
            # Count similar positions
            similar_positions = 0
            same_direction_positions = 0
            
            for signal in self.active_signals:
                if signal.get('coin') == new_coin:
                    similar_positions += 1
                
                if signal.get('signal_type') == new_type:
                    same_direction_positions += 1
            
            # Limits
            max_same_coin = 2  # Max 2 positions per coin
            max_same_direction = int(self.max_active_signals * 0.7)  # Max 70% in same direction
            
            if similar_positions >= max_same_coin:
                print(f"    ⚠️ Too many positions for {new_coin}: {similar_positions}")
                return False
            
            if same_direction_positions >= max_same_direction:
                print(f"    ⚠️ Too many {new_type} positions: {same_direction_positions}")
                return False
            
            return True
            
        except Exception as e:
            print(f"    ⚠️ Error checking position correlation: {e}")
            return True
    
    def _update_metrics_enhanced(self) -> None:
        """🔧 Enhanced metrics calculation with advanced statistics."""
        try:
            with self.tracked_signals_lock:
                # Basic metrics
                wins = [s for s in self.completed_signals if s.get('pnl_percentage', 0) > 0]
                losses = [s for s in self.completed_signals if s.get('pnl_percentage', 0) < 0]
                
                self.metrics['win_count'] = len(wins)
                self.metrics['loss_count'] = len(losses)
                self.metrics['total_signals'] = len(self.completed_signals)
                self.metrics['open_signals'] = len(self.active_signals)
                self.metrics['closed_signals'] = len(self.completed_signals)
                
                # Win rate
                total_closed = len(wins) + len(losses)
                self.metrics['win_rate'] = (len(wins) / total_closed * 100) if total_closed > 0 else 0
                
                # Average win/loss
                win_pcts = [s.get('pnl_percentage', 0) for s in wins]
                loss_pcts = [abs(s.get('pnl_percentage', 0)) for s in losses]
                
                self.metrics['avg_win_pct'] = sum(win_pcts) / len(win_pcts) if win_pcts else 0
                self.metrics['avg_loss_pct'] = sum(loss_pcts) / len(loss_pcts) if loss_pcts else 0
                
                # Profit factor
                total_wins = sum(win_pcts)
                total_losses = sum(loss_pcts)
                self.metrics['profit_factor'] = (total_wins / total_losses) if total_losses > 0 else total_wins
                
                # Best/worst trades
                if self.completed_signals:
                    all_pnls = [s.get('pnl_percentage', 0) for s in self.completed_signals]
                    self.metrics['best_trade'] = max(all_pnls)
                    self.metrics['worst_trade'] = min(all_pnls)
                    self.metrics['total_pnl'] = sum(all_pnls)
                
                # Risk-reward ratio
                if losses:
                    avg_risk = self.metrics['avg_loss_pct']
                    avg_reward = self.metrics['avg_win_pct']
                    self.metrics['avg_risk_reward'] = (avg_reward / avg_risk) if avg_risk > 0 else 0
                
                # Success rate by type
                buy_signals = [s for s in self.completed_signals if s.get('signal_type') == 'BUY']
                sell_signals = [s for s in self.completed_signals if s.get('signal_type') == 'SELL']
                
                buy_wins = len([s for s in buy_signals if s.get('pnl_percentage', 0) > 0])
                sell_wins = len([s for s in sell_signals if s.get('pnl_percentage', 0) > 0])
                
                self.metrics['success_rate_by_type']['BUY'] = (buy_wins / len(buy_signals) * 100) if buy_signals else 0
                self.metrics['success_rate_by_type']['SELL'] = (sell_wins / len(sell_signals) * 100) if sell_signals else 0
                
                # Holding time
                holding_times = [s.get('holding_duration', 0) for s in self.completed_signals if s.get('holding_duration')]
                self.metrics['avg_holding_time'] = (sum(holding_times) / len(holding_times) / 3600) if holding_times else 0
                
                # Update timestamp
                self.metrics['last_updated'] = int(time.time())
                
        except Exception as e:
            print(f"⚠️ Error updating enhanced metrics: {e}")
    
    def _validate_existing_signals(self) -> None:
        """🔧 Validate existing signals on startup."""
        try:
            if not self.active_signals:
                return
            
            valid_signals = []
            invalid_count = 0
            
            for signal in self.active_signals:
                if self._validate_signal_structure(signal):
                    valid_signals.append(signal)
                else:
                    invalid_count += 1
            
            self.active_signals = valid_signals
            
            if invalid_count > 0:
                print(f"⚠️ Removed {invalid_count} invalid signals on startup")
                self._backup_state()
            
        except Exception as e:
            print(f"❌ Error validating existing signals: {e}")
    
    def _validate_signal_structure(self, signal: Dict[str, Any]) -> bool:
        """🔧 Validate signal data structure."""
        try:
            required_fields = ['coin', 'signal_type', 'entry', 'take_profit', 'stop_loss', 'timestamp']
            
            for field in required_fields:
                if field not in signal or signal[field] is None:
                    return False
            
            # Validate numeric fields
            try:
                float(signal['entry'])
                float(signal['take_profit'])
                float(signal['stop_loss'])
                int(signal['timestamp'])
            except (TypeError, ValueError):
                return False
            
            return True
            
        except Exception:
            return False
    
    def _start_monitoring_thread(self) -> None:
        """🔧 Start background monitoring thread."""
        def monitoring_worker():
            while True:
                try:
                    time.sleep(self.check_interval)
                    
                    # Auto-check signals if enough time has passed
                    if int(time.time()) - self.last_check_time >= self.check_interval:
                        if self.active_signals:
                            closed_signals = self.check_tracked_signals()
                            if closed_signals:
                                print(f"🔄 Auto-monitoring closed {len(closed_signals)} signals")
                    
                except Exception as e:
                    print(f"❌ Error in monitoring thread: {e}")
        
        monitoring_thread = threading.Thread(target=monitoring_worker, daemon=True)
        monitoring_thread.start()
        print(f"🔄 Started monitoring thread with {self.check_interval}s interval")
    
    def _log_signal_enhanced(self, signal: Dict[str, Any]) -> None:
        """🔧 Enhanced signal logging."""
        try:
            log_data = {
                'timestamp': signal.get('timestamp'),
                'signal_id': signal.get('signal_id'),
                'coin': signal.get('coin'),
                'signal_type': signal.get('signal_type'),
                'entry': signal.get('entry'),
                'take_profit': signal.get('take_profit'),
                'stop_loss': signal.get('stop_loss'),
                'confidence': signal.get('confidence'),
                'quality_score': signal.get('quality_score'),
                'action': 'SIGNAL_CREATED'
            }
            
            if hasattr(self.logger, 'log_trading_action'):
                self.logger.log_trading_action(log_data)
            
        except Exception as e:
            print(f"⚠️ Error logging signal: {e}")
    
    def _log_signal_closure(self, signal: Dict[str, Any]) -> None:
        """🔧 Log signal closure."""
        try:
            log_data = {
                'timestamp': signal.get('closed_timestamp'),
                'signal_id': signal.get('signal_id'),
                'coin': signal.get('coin'),
                'signal_type': signal.get('signal_type'),
                'close_reason': signal.get('close_reason'),
                'closed_price': signal.get('closed_price'),
                'pnl_percentage': signal.get('pnl_percentage'),
                'holding_duration_hours': signal.get('holding_duration_hours'),
                'action': 'SIGNAL_CLOSED'
            }
            
            if hasattr(self.logger, 'log_trading_action'):
                self.logger.log_trading_action(log_data)
            
        except Exception as e:
            print(f"⚠️ Error logging signal closure: {e}")
    
    def _notify_signal_closure_enhanced(self, signal: Dict[str, Any]) -> None:
        """🔧 Enhanced signal closure notification."""
        try:
            if not self.notifier:
                return
            
            coin = signal.get('coin')
            signal_type = signal.get('signal_type')
            close_reason = signal.get('close_reason')
            pnl = signal.get('pnl_percentage', 0)
            
            # Create notification message
            status_emoji = "🎯" if close_reason == "TAKE_PROFIT" else "🛡️"
            pnl_emoji = "📈" if pnl > 0 else "📉"
            
            message = f"""
{status_emoji} **Signal Closed**

🪙 **Coin:** {coin}
📊 **Type:** {signal_type}
⚡ **Reason:** {close_reason}
{pnl_emoji} **PnL:** {pnl:+.2f}%
⏱️ **Duration:** {signal.get('holding_duration_hours', 0):.1f}h

Entry: {signal.get('entry'):.6f}
Exit: {signal.get('closed_price'):.6f}
"""
            
            if hasattr(self.notifier, 'send_message'):
                self.notifier.send_message(message)
            
        except Exception as e:
            print(f"⚠️ Error sending closure notification: {e}")
    
    def _load_state(self) -> None:
        """🔧 Enhanced state loading with validation."""
        try:
            if not self.backup_dir:
                return
            
            # Create backup directory if it doesn't exist
            os.makedirs(self.backup_dir, exist_ok=True)
            
            backup_file = os.path.join(self.backup_dir, 'trade_tracker_state.json')
            if os.path.exists(backup_file):
                with open(backup_file, 'r') as f:
                    state = json.load(f)
                
                # Load with validation
                self.active_signals = state.get('active_signals', [])
                self.completed_signals = state.get('completed_signals', [])
                self.signal_history = state.get('signal_history', [])
                self.metrics = {**self.metrics, **state.get('metrics', {})}
                
                # Validate loaded signals
                self._validate_existing_signals()
                
                print(f"📁 State loaded: {len(self.active_signals)} active, {len(self.completed_signals)} completed signals")
            else:
                print("📁 No previous state found, starting fresh")
                
        except Exception as e:
            print(f"❌ Error loading state: {e}")
    
    def _backup_state(self) -> None:
        """🔧 Enhanced state backup with compression."""
        try:
            if not self.backup_dir:
                return
            
            # Create backup directory if it doesn't exist
            os.makedirs(self.backup_dir, exist_ok=True)
            
            state = {
                'active_signals': self.active_signals,
                'completed_signals': self.completed_signals[-1000:],  # Keep last 1000
                'signal_history': self.signal_history[-2000:],  # Keep last 2000
                'metrics': self.metrics,
                'backup_timestamp': int(time.time()),
                'version': '2.0'
            }
            
            backup_file = os.path.join(self.backup_dir, 'trade_tracker_state.json')
            with open(backup_file, 'w') as f:
                json.dump(state, f, indent=2)
            
            # Also create a timestamped backup (daily)
            daily_backup_file = os.path.join(
                self.backup_dir, 
                f'trade_tracker_state_{datetime.now().strftime("%Y%m%d")}.json'
            )
            if not os.path.exists(daily_backup_file):
                with open(daily_backup_file, 'w') as f:
                    json.dump(state, f, indent=2)
                
        except Exception as e:
            print(f"❌ Error backing up state: {e}")
    
    def _start_backup_thread(self) -> None:
        """🔧 Enhanced backup thread with error recovery."""
        def backup_worker():
            while True:
                try:
                    time.sleep(self.backup_interval)
                    self._backup_state()
                except Exception as e:
                    print(f"❌ Error in backup thread: {e}")
                    time.sleep(60)  # Wait 1 minute before retrying
        
        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        print(f"💾 Backup thread started with {self.backup_interval}s interval")

    # 🔧 Additional helper methods for compatibility
    def add_signal_to_track(self, signal_data: Dict[str, Any]) -> bool:
        """Alias for add_signal method for backward compatibility."""
        return self.add_signal(signal_data)
    
    def get_active_signals(self) -> List[Dict[str, Any]]:
        """Get list of active trading signals."""
        with self.tracked_signals_lock:
            return self.active_signals.copy()
    
    def get_completed_signals(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get list of completed trading signals with limit."""
        with self.tracked_signals_lock:
            return self.completed_signals[-limit:].copy() if limit > 0 else self.completed_signals.copy()
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get enhanced performance metrics."""
        with self.tracked_signals_lock:
            return self.metrics.copy()
    
    def get_update_ratio(self) -> float:
        """Get ratio of signals with updated TP/SL."""
        with self.tracked_signals_lock:
            if not self.active_signals:
                return 1.0
            
            updated_count = sum(1 for signal in self.active_signals 
                              if signal.get('trailing_stop_updates', 0) > 0)
            
            return updated_count / len(self.active_signals)
    
    def get_signal(self, coin: str) -> Optional[Dict[str, Any]]:
        """Get a specific signal by coin."""
        with self.tracked_signals_lock:
            for signal in self.active_signals:
                if signal.get('coin') == coin:
                    return signal.copy()
            return None
    
    def close_signal_manually(self, coin: str, price: Optional[float] = None, reason: str = "MANUAL") -> bool:
        """Manually close a signal."""
        with self.tracked_signals_lock:
            for i, signal in enumerate(self.active_signals):
                if signal.get('coin') == coin:
                    if price is None:
                        price = self._get_current_price_with_retry(coin)
                        if price is None:
                            return False
                    
                    # Close the signal
                    closed_signal = self._close_signal(signal, price, reason)
                    if closed_signal:
                        # Remove from active signals
                        self.active_signals.pop(i)
                        self._update_metrics_enhanced()
                        self._backup_state()
                        return True
            
            return False
    
    def update_signal(self, coin: str, updates: Dict[str, Any]) -> bool:
        """Update a specific signal."""
        with self.tracked_signals_lock:
            for signal in self.active_signals:
                if signal.get('coin') == coin:
                    allowed_updates = ['take_profit', 'stop_loss', 'remarks', 
                                      'trailing_stop_enabled', 'trailing_stop_pct']
                    
                    for key, value in updates.items():
                        if key in allowed_updates:
                            signal[key] = value
                    
                    signal['last_update_timestamp'] = int(time.time())
                    self._backup_state()
                    return True
            
            return False
    
    def check_and_update_tp_sl(self) -> None:
        """Check and update TP/SL for active signals."""
        with self.tracked_signals_lock:
            if not self.active_signals:
                return
            
            updated_count = 0
            for signal in self.active_signals:
                try:
                    coin = signal.get('coin')
                    current_price = self._get_current_price_with_retry(coin)
                    
                    if current_price:
                        self._update_trailing_stop_enhanced(signal, current_price)
                        updated_count += 1
                        
                except Exception as e:
                    print(f"❌ Error updating TP/SL for {signal.get('coin')}: {e}")
            
            if updated_count > 0:
                self._backup_state()
    
    # 🔧 New enhanced methods
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        with self.tracked_signals_lock:
            metrics = self.get_performance_metrics()
            
            # Add additional insights
            summary = {
                **metrics,
                'performance_grade': self._calculate_performance_grade(metrics),
                'risk_assessment': self._assess_current_risk(),
                'recommendations': self._generate_performance_recommendations(metrics),
                'recent_trend': self._analyze_recent_performance_trend(),
                'top_performing_coins': self._get_top_performing_coins(),
                'daily_pnl': self._calculate_daily_pnl()
            }
            
            return summary
    
    def _calculate_performance_grade(self, metrics: Dict[str, Any]) -> str:
        """Calculate overall performance grade."""
        try:
            win_rate = metrics.get('win_rate', 0)
            profit_factor = metrics.get('profit_factor', 0)
            total_pnl = metrics.get('total_pnl', 0)
            
            score = 0
            
            # Win rate scoring (0-40 points)
            if win_rate >= 70:
                score += 40
            elif win_rate >= 60:
                score += 30
            elif win_rate >= 50:
                score += 20
            elif win_rate >= 40:
                score += 10
            
            # Profit factor scoring (0-40 points)
            if profit_factor >= 2.0:
                score += 40
            elif profit_factor >= 1.5:
                score += 30
            elif profit_factor >= 1.2:
                score += 20
            elif profit_factor >= 1.0:
                score += 10
            
            # Total PnL scoring (0-20 points)
            if total_pnl >= 20:
                score += 20
            elif total_pnl >= 10:
                score += 15
            elif total_pnl >= 5:
                score += 10
            elif total_pnl >= 0:
                score += 5
            
            # Grade assignment
            if score >= 90:
                return "A+"
            elif score >= 80:
                return "A"
            elif score >= 70:
                return "B+"
            elif score >= 60:
                return "B"
            elif score >= 50:
                return "C+"
            elif score >= 40:
                return "C"
            elif score >= 30:
                return "D"
            else:
                return "F"
                
        except Exception as e:
            return "N/A"
    
    def _assess_current_risk(self) -> Dict[str, Any]:
        """Assess current risk exposure."""
        try:
            active_count = len(self.active_signals)
            max_positions = self.risk_management["max_open_positions"]
            
            # Calculate exposure
            exposure_pct = (active_count / max_positions) * 100 if max_positions > 0 else 0
            
            # Risk level assessment
            if exposure_pct >= 90:
                risk_level = "Very High"
            elif exposure_pct >= 70:
                risk_level = "High" 
            elif exposure_pct >= 50:
                risk_level = "Moderate"
            elif exposure_pct >= 30:
                risk_level = "Low"
            else:
                risk_level = "Very Low"
            
            return {
                "risk_level": risk_level,
                "exposure_percentage": exposure_pct,
                "active_positions": active_count,
                "max_positions": max_positions,
                "daily_pnl": self._calculate_daily_pnl(),
                "unrealized_pnl": sum(s.get('unrealized_pnl', 0) for s in self.active_signals)
            }
            
        except Exception as e:
            return {"risk_level": "Unknown", "error": str(e)}
    
    def _generate_performance_recommendations(self, metrics: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations."""
        recommendations = []
        
        try:
            win_rate = metrics.get('win_rate', 0)
            profit_factor = metrics.get('profit_factor', 0)
            avg_loss = metrics.get('avg_loss_pct', 0)
            
            if win_rate < 50:
                recommendations.append("Consider tightening entry criteria to improve win rate")
            
            if profit_factor < 1.5:
                recommendations.append("Focus on trades with better risk-reward ratios")
            
            if avg_loss > 3:
                recommendations.append("Consider using tighter stop losses to reduce average loss")
            
            if len(self.active_signals) < self.max_active_signals * 0.5:
                recommendations.append("Consider increasing position sizing to capitalize on opportunities")
            
            if not recommendations:
                recommendations.append("Performance is solid - maintain current strategy")
            
        except Exception as e:
            recommendations.append("Unable to generate recommendations due to insufficient data")
        
        return recommendations
    
    def _analyze_recent_performance_trend(self) -> str:
        """Analyze recent performance trend."""
        try:
            if len(self.completed_signals) < 10:
                return "insufficient_data"
            
            # Look at last 10 signals
            recent_signals = self.completed_signals[-10:]
            recent_pnls = [s.get('pnl_percentage', 0) for s in recent_signals]
            
            positive_count = sum(1 for pnl in recent_pnls if pnl > 0)
            
            if positive_count >= 7:
                return "strong_positive"
            elif positive_count >= 6:
                return "positive"
            elif positive_count >= 4:
                return "neutral"
            elif positive_count >= 3:
                return "negative"
            else:
                return "strong_negative"
                
        except Exception as e:
            return "unknown"
    
    def _get_top_performing_coins(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing coins by total PnL."""
        try:
            coin_performance = {}
            
            for signal in self.completed_signals:
                coin = signal.get('coin')
                pnl = signal.get('pnl_percentage', 0)
                
                if coin not in coin_performance:
                    coin_performance[coin] = {'total_pnl': 0, 'trade_count': 0, 'wins': 0}
                
                coin_performance[coin]['total_pnl'] += pnl
                coin_performance[coin]['trade_count'] += 1
                if pnl > 0:
                    coin_performance[coin]['wins'] += 1
            
            # Calculate win rates and sort by total PnL
            for coin_data in coin_performance.values():
                coin_data['win_rate'] = (coin_data['wins'] / coin_data['trade_count']) * 100
            
            sorted_coins = sorted(coin_performance.items(), 
                                key=lambda x: x[1]['total_pnl'], reverse=True)
            
            return [{'coin': coin, **data} for coin, data in sorted_coins[:limit]]
            
        except Exception as e:
            return []
    def _categorize_performance(self, pnl: float) -> str:
        """Categorize performance level."""
        if pnl >= 10:
            return "Excellent"
        elif pnl >= 5:
            return "Very Good"
        elif pnl >= 2:
            return "Good"
        elif pnl >= 0:
            return "Breakeven"
        elif pnl >= -2:
            return "Small Loss"
        elif pnl >= -5:
            return "Moderate Loss"
        else:
            return "Large Loss"

    def _categorize_loss(self, pnl: float) -> str:
        """Categorize loss severity."""
        if pnl >= -1:
            return "Minimal"
        elif pnl >= -3:
            return "Moderate"
        elif pnl >= -5:
            return "Significant"
        else:
            return "Severe"

    def _assess_system_health(self) -> Dict[str, Any]:
        """Assess overall system health."""
        try:
            health_score = 100
            issues = []
            
            # Check signal tracking capacity
            usage_pct = (len(self.active_signals) / self.max_active_signals) * 100
            if usage_pct > 90:
                health_score -= 20
                issues.append("Near maximum signal capacity")
            elif usage_pct > 70:
                health_score -= 10
                issues.append("High signal capacity usage")
            
            # Check recent performance
            if len(self.completed_signals) >= 10:
                recent_signals = self.completed_signals[-10:]
                wins = len([s for s in recent_signals if s.get('pnl_percentage', 0) > 0])
                win_rate = wins / len(recent_signals)
                
                if win_rate < 0.3:
                    health_score -= 30
                    issues.append("Poor recent performance")
                elif win_rate < 0.5:
                    health_score -= 15
                    issues.append("Below average recent performance")
            
            # Check data integrity
            invalid_active = sum(1 for s in self.active_signals if not self._validate_signal_structure(s))
            if invalid_active > 0:
                health_score -= 25
                issues.append(f"{invalid_active} invalid active signals detected")
            
            # Determine health status
            if health_score >= 90:
                status = "Excellent"
            elif health_score >= 70:
                status = "Good"
            elif health_score >= 50:
                status = "Fair"
            elif health_score >= 30:
                status = "Poor"
            else:
                status = "Critical"
            
            return {
                'status': status,
                'score': health_score,
                'capacity_usage': round(usage_pct, 1),
                'issues': issues,
                'last_check': int(time.time())
            }
            
        except Exception as e:
            return {
                'status': 'Unknown',
                'score': 0,
                'error': str(e),
                'last_check': int(time.time())
            }

    def _convert_to_csv_format(self, export_data: Dict[str, Any]) -> str:
        """Convert export data to CSV format."""
        try:
            import io
            output = io.StringIO()
            
            # Write header
            headers = ['coin', 'signal_type', 'entry', 'take_profit', 'stop_loss', 
                    'status', 'pnl_percentage', 'timestamp', 'close_reason']
            output.write(','.join(headers) + '\n')
            
            # Write data
            for signal in export_data.get('signals', []):
                row = []
                for header in headers:
                    value = signal.get(header, '')
                    if isinstance(value, (int, float)):
                        row.append(str(value))
                    else:
                        row.append(f'"{str(value)}"')
                output.write(','.join(row) + '\n')
            
            return output.getvalue()
            
        except Exception as e:
            print(f"❌ Error converting to CSV: {e}")
            return ""

    def clear_completed_signals(self, keep_last: int = 100) -> int:
        """Clear old completed signals to free up memory."""
        with self.tracked_signals_lock:
            try:
                initial_count = len(self.completed_signals)
                
                if initial_count > keep_last:
                    # Keep only the most recent signals
                    self.completed_signals = self.completed_signals[-keep_last:]
                    cleared_count = initial_count - len(self.completed_signals)
                    
                    # Update metrics
                    self._update_metrics_enhanced()
                    self._backup_state()
                    
                    print(f"🧹 Cleared {cleared_count} old completed signals (kept last {keep_last})")
                    return cleared_count
                else:
                    print(f"📊 No clearing needed - only {initial_count} completed signals")
                    return 0
                    
            except Exception as e:
                print(f"❌ Error clearing completed signals: {e}")
                return 0

    def reset_tracker(self, backup_current: bool = True) -> bool:
        """Reset the tracker to initial state."""
        with self.tracked_signals_lock:
            try:
                if backup_current and (self.active_signals or self.completed_signals):
                    # Create emergency backup
                    timestamp = int(time.time())
                    backup_data = {
                        'active_signals': self.active_signals,
                        'completed_signals': self.completed_signals,
                        'metrics': self.metrics,
                        'reset_timestamp': timestamp
                    }
                    
                    if self.backup_dir:
                        os.makedirs(self.backup_dir, exist_ok=True)
                        backup_file = os.path.join(self.backup_dir, f'emergency_backup_{timestamp}.json')
                        with open(backup_file, 'w') as f:
                            json.dump(backup_data, f, indent=2)
                        print(f"💾 Emergency backup created: {backup_file}")
                
                # Reset all data
                self.active_signals = []
                self.completed_signals = []
                self.signal_history = []
                
                # Reset metrics
                self.metrics = {
                    "win_count": 0,
                    "loss_count": 0,
                    "win_rate": 0.0,
                    "avg_win_pct": 0.0,
                    "avg_loss_pct": 0.0,
                    "profit_factor": 0.0,
                    "avg_holding_time": 0.0,
                    "total_signals": 0,
                    "open_signals": 0,
                    "closed_signals": 0,
                    "sharpe_ratio": 0.0,
                    "max_drawdown": 0.0,
                    "consecutive_wins": 0,
                    "consecutive_losses": 0,
                    "best_trade": 0.0,
                    "worst_trade": 0.0,
                    "total_pnl": 0.0,
                    "avg_risk_reward": 0.0,
                    "success_rate_by_type": {"BUY": 0.0, "SELL": 0.0},
                    "performance_trend": "neutral",
                    "last_updated": int(time.time())
                }
                
                # Save reset state
                self._backup_state()
                
                print("🔄 TradeTracker has been reset successfully")
                return True
                
            except Exception as e:
                print(f"❌ Error resetting tracker: {e}")
                return False

    # ✅ NEW: Ultra-Fast TP/SL Helper Methods
    def _calculate_price_momentum(self, signal: Dict[str, Any], current_price: float) -> float:
        """🚀 Calculate price momentum for ultra-fast TP/SL adjustments."""
        try:
            entry = signal.get('entry', 0)
            signal_type = signal.get('signal_type', 'BUY')

            if entry <= 0 or current_price <= 0:
                return 1.0

            # Calculate price change percentage
            if signal_type == "BUY":
                price_change = ((current_price - entry) / entry) * 100
            else:
                price_change = ((entry - current_price) / entry) * 100

            # Calculate momentum factor based on price movement
            if abs(price_change) >= 5.0:
                momentum_factor = 1.5  # Strong momentum
            elif abs(price_change) >= 3.0:
                momentum_factor = 1.3  # Good momentum
            elif abs(price_change) >= 1.5:
                momentum_factor = 1.1  # Moderate momentum
            else:
                momentum_factor = 1.0  # Normal momentum

            # Adjust for direction
            if price_change < 0:
                momentum_factor = 0.8  # Negative momentum

            return momentum_factor

        except Exception as e:
            print(f"❌ Error calculating momentum: {e}")
            return 1.0

    def _calculate_volatility_factor(self, signal: Dict[str, Any], current_price: float) -> float:
        """📊 Calculate volatility factor for adaptive TP/SL."""
        try:
            # Get recent price history if available
            coin = signal.get('coin', '')

            # Simple volatility estimation based on current vs entry
            entry = signal.get('entry', 0)
            if entry <= 0:
                return 1.0

            price_deviation = abs((current_price - entry) / entry) * 100

            # Volatility factor based on price deviation
            if price_deviation >= 10.0:
                volatility_factor = 1.8  # Very high volatility
            elif price_deviation >= 5.0:
                volatility_factor = 1.5  # High volatility
            elif price_deviation >= 2.0:
                volatility_factor = 1.2  # Moderate volatility
            else:
                volatility_factor = 1.0  # Normal volatility

            return volatility_factor

        except Exception as e:
            print(f"❌ Error calculating volatility: {e}")
            return 1.0

    def _send_ultra_fast_tp_sl_notification(self, signal: Dict[str, Any], current_price: float,
                                          update_type: str, old_tp: float, new_tp: float,
                                          old_sl: float, new_sl: float) -> bool:
        """📱 Send ultra-fast TP/SL update notification."""
        try:
            coin = signal.get('coin', 'UNKNOWN')
            signal_type = signal.get('signal_type', 'BUY')
            entry = signal.get('entry', 0)
            signal_id = signal.get('signal_id', 'N/A')

            # Calculate P&L
            if signal_type == "BUY":
                pnl_pct = ((current_price - entry) / entry) * 100
                pnl_amount = current_price - entry
            else:
                pnl_pct = ((entry - current_price) / entry) * 100
                pnl_amount = entry - current_price

            # Calculate new risk/reward
            if signal_type == "BUY":
                potential_profit = new_tp - current_price
                potential_loss = current_price - new_sl
            else:
                potential_profit = current_price - new_tp
                potential_loss = new_sl - current_price

            risk_reward = potential_profit / potential_loss if potential_loss > 0 else 0

            # Get update count
            update_count = signal.get('tp_sl_update_count', 0) + 1
            signal['tp_sl_update_count'] = update_count

            # Create notification message
            pnl_emoji = "📈" if pnl_pct >= 0 else "📉"
            status_emoji = "🟢" if pnl_pct >= 0 else "🔴"

            message = f"""⚡ ULTRA-FAST TP/SL UPDATE - {coin} ⚡

{status_emoji} <b>Current Status:</b>
├ 💰 Entry: <code>{entry:.8f}</code>
├ 📊 Current: <code>{current_price:.8f}</code>
├ {pnl_emoji} P&L: <code>{pnl_pct:+.2f}% ({pnl_amount:+.8f})</code>
└ 🎯 Signal: <code>{signal_type}</code>

🚀 <b>Updated Levels:</b>
├ 📈 Take Profit: <code>{old_tp:.8f}</code> → <code>{new_tp:.8f}</code>
├ 🛡️ Stop Loss: <code>{old_sl:.8f}</code> → <code>{new_sl:.8f}</code>
├ ⚖️ Risk/Reward: <code>{risk_reward:.1f}</code>
└ 🔄 Update Type: <code>{update_type}</code>

📊 <b>Signal Info:</b>
├ 🆔 ID: <code>{signal_id}</code>
├ ⏰ Updated: <code>{time.strftime('%H:%M:%S %d/%m/%Y')}</code>
└ 🔄 Total Updates: <code>{update_count}</code>

💡 <b>Ultra-Fast Management:</b> ✅ Active"""

            # Send notification
            success = self.notifier.send_message(message, parse_mode="HTML")

            if success:
                print(f"    📱 Ultra-fast TP/SL notification sent for {coin}")

                # Update notification tracking
                signal_key = f"{coin}_{signal_type}_{signal_id}"
                self.tp_sl_tracking['last_notifications'][signal_key] = time.time()

                # Add to update history
                self.tp_sl_tracking['update_history'].append({
                    'coin': coin,
                    'signal_type': signal_type,
                    'signal_id': signal_id,
                    'update_type': update_type,
                    'old_tp': old_tp,
                    'new_tp': new_tp,
                    'old_sl': old_sl,
                    'new_sl': new_sl,
                    'current_price': current_price,
                    'pnl_pct': pnl_pct,
                    'timestamp': time.time()
                })

                # Keep only last 50 updates
                if len(self.tp_sl_tracking['update_history']) > 50:
                    self.tp_sl_tracking['update_history'] = self.tp_sl_tracking['update_history'][-50:]

                return True
            else:
                print(f"    ❌ Failed to send ultra-fast TP/SL notification for {coin}")
                return False

        except Exception as e:
            print(f"❌ Error sending ultra-fast TP/SL notification: {e}")
            return False

    def get_deduplication_statistics(self) -> Dict[str, Any]:
        """🔥 Get Advanced Signal Deduplicator statistics."""
        try:
            if self.signal_deduplicator:
                stats = self.signal_deduplicator.get_statistics()
                print(f"📊 ADVANCED DEDUPLICATOR STATISTICS:")
                print(f"  🔢 Total processed: {stats.get('total_processed', 0)}")
                print(f"  🚫 Duplicates found: {stats.get('duplicates_found', 0)}")
                print(f"  🔍 Similar signals: {stats.get('similar_signals', 0)}")
                print(f"  ✅ Unique signals: {stats.get('unique_signals', 0)}")
                print(f"  📈 Duplicate rate: {stats.get('duplicate_rate', 0):.1f}%")
                print(f"  🎯 Similarity rate: {stats.get('similarity_rate', 0):.1f}%")
                print(f"  📚 History size: {stats.get('history_size', 0)}")
                print(f"  💾 Cache size: {stats.get('cache_size', 0)}")
                return stats
            else:
                return {"error": "Advanced Deduplicator not available"}
        except Exception as e:
            print(f"❌ Error getting deduplication statistics: {e}")
            return {"error": str(e)}

    def reset_deduplication_statistics(self) -> None:
        """🔄 Reset Advanced Signal Deduplicator statistics."""
        try:
            if self.signal_deduplicator:
                self.signal_deduplicator.reset_statistics()
                print("📊 Advanced Deduplicator statistics reset")
            else:
                print("⚠️ Advanced Deduplicator not available")
        except Exception as e:
            print(f"❌ Error resetting deduplication statistics: {e}")

    def clear_deduplication_history(self) -> None:
        """🧹 Clear Advanced Signal Deduplicator history."""
        try:
            if self.signal_deduplicator:
                self.signal_deduplicator.clear_history()
                print("🧹 Advanced Deduplicator history cleared")
            else:
                print("⚠️ Advanced Deduplicator not available")
        except Exception as e:
            print(f"❌ Error clearing deduplication history: {e}")