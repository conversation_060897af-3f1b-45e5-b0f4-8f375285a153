#!/usr/bin/env python3
"""
🧪 TEST: Consensus Signal Original Format
Test để xác nhận consensus signal đã trở về format cũ
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from telegram_notifier import EnhancedTelegramNotifier

def create_test_consensus_data():
    """Create test consensus data"""
    return {
        'consensus_score': 0.639,
        'confidence': 0.75,
        'signal_quality': {
            'strength': 0.82,
            'overall_quality': 0.78
        }
    }

def create_test_signal_data():
    """Create test signal data"""
    return {
        'signal_id': 'SIG_BTC/USDT_1749972075',
        'coin': 'BTC/USDT',
        'signal_type': 'SELL',
        'entry': 105367.43000000,
        'take_profit': 98749.20776578,
        'stop_loss': 110989.82100000,
        'risk_reward_ratio': 1.18,
        'primary_tf': '4h'
    }

def test_original_format():
    """Test that consensus signal uses original format"""
    print("\n🧪 === TESTING ORIGINAL CONSENSUS FORMAT ===")
    
    try:
        # Initialize notifier
        notifier = EnhancedTelegramNotifier(
            bot_token="test_token",
            chat_id="-1002301937119"
        )
        
        # Create test data
        consensus_data = create_test_consensus_data()
        signal_data = create_test_signal_data()
        
        # Generate detailed caption
        detailed_caption = notifier._create_detailed_consensus_signal_caption(
            coin="BTC/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data
        )
        
        print("✅ Original format caption generated!")
        print("\n📝 GENERATED CAPTION:")
        print("=" * 60)
        print(detailed_caption)
        print("=" * 60)
        
        # Check for original format elements (should NOT have new detailed elements)
        original_elements = [
            "CONSENSUS SIGNAL REPORT - BTC/USDT",
            "TRADING SIGNAL",
            "Signal Type: SELL",
            "Entry Price:",
            "Take Profit:",
            "Stop Loss:",
            "Risk/Reward:",
            "Consensus Score:",
            "ENHANCED CONSENSUS ANALYSIS",
            "Multi-Algorithm Consensus with Auto-Chart Generation"
        ]
        
        # Elements that should NOT be present (new detailed format)
        new_detailed_elements = [
            "CONTRIBUTING ALGORITHMS",
            "AI Analysis:",
            "Fibonacci:",
            "SIGNAL QUALITY:",
            "Strength:",
            "Overall Quality:",
            "ENHANCEMENT FEATURES:",
            "Volume Spike:",
            "Trend Strength:"
        ]
        
        # Check original elements are present
        missing_original = []
        for element in original_elements:
            if element not in detailed_caption:
                missing_original.append(element)
        
        # Check new detailed elements are NOT present
        found_new_elements = []
        for element in new_detailed_elements:
            if element in detailed_caption:
                found_new_elements.append(element)
        
        if missing_original:
            print(f"\n❌ Missing original elements:")
            for element in missing_original:
                print(f"  - {element}")
            return False
        
        if found_new_elements:
            print(f"\n❌ Found new detailed elements (should not be present):")
            for element in found_new_elements:
                print(f"  - {element}")
            return False
        
        print(f"\n✅ Original format confirmed!")
        print(f"✅ All original elements present: {len(original_elements)}")
        print(f"✅ No new detailed elements found: 0/{len(new_detailed_elements)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing original format: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run test"""
    print("🧪 === CONSENSUS SIGNAL ORIGINAL FORMAT TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    success = test_original_format()
    end_time = time.time()
    
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nOriginal Format Test: {status} ({end_time - start_time:.2f}s)")
    
    if success:
        print("\n🎉 SUCCESS! Consensus signals are using original format!")
        print("\n📋 CONFIRMED:")
        print("✅ Original detailed format maintained")
        print("✅ No new detailed elements added")
        print("✅ Clean and concise message structure")
        print("✅ Compatible with existing system")
    else:
        print("\n⚠️ Test failed. Format may have been modified.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
