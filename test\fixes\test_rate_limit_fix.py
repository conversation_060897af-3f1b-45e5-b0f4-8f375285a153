#!/usr/bin/env python3
"""
🔄 Test Rate Limit Fix - Test rate limiting improvements
"""

import os
import sys
import pandas as pd
import numpy as np
import time
from datetime import datetime

def test_rate_limit_fix():
    """🔄 Test rate limiting fix."""
    print(f"🔄 RATE LIMIT FIX TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=50, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 50)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 5000, 50)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Test multiple chart sends with rate limiting
        print(f"\n🔄 Testing multiple chart sends with rate limiting...")
        
        charts_to_send = [
            {
                'type': 'fibonacci',
                'data': {
                    'trend_direction': 'UPTREND',
                    'pivot_high': current_price * 1.05,
                    'pivot_low': current_price * 0.95,
                    'retracement_levels': [
                        {'ratio': 0.236, 'price': current_price * 0.98, 'strength': 0.8},
                        {'ratio': 0.382, 'price': current_price * 0.96, 'strength': 0.9}
                    ],
                    'extension_levels': [
                        {'ratio': 1.618, 'price': current_price * 1.08, 'strength': 0.85}
                    ]
                }
            },
            {
                'type': 'volume_profile',
                'data': {
                    'vpoc': {'price': current_price * 1.02, 'volume': 1000000},
                    'value_area': {'high': current_price * 1.05, 'low': current_price * 0.95},
                    'signals': {'primary_signal': 'BUY', 'confidence': 0.75}
                }
            },
            {
                'type': 'ai_analysis',
                'data': {
                    'prediction': 'BUY',
                    'confidence': 0.85,
                    'model_results': {'XGBoost': {'prediction': 'BUY', 'confidence': 0.9}},
                    'market_sentiment': 'BULLISH'
                }
            }
        ]
        
        success_count = 0
        
        for i, chart_info in enumerate(charts_to_send, 1):
            print(f"\n{i}️⃣ Sending {chart_info['type']} chart...")
            
            try:
                # Generate chart
                if chart_info['type'] == 'fibonacci':
                    chart_path = chart_gen.generate_fibonacci_chart("BTC/USDT", chart_info['data'], ohlcv_data, current_price)
                    caption = chart_gen._create_fibonacci_caption("BTC/USDT", chart_info['data'], current_price)
                elif chart_info['type'] == 'volume_profile':
                    chart_path = chart_gen.generate_volume_profile_chart("BTC/USDT", chart_info['data'], ohlcv_data, current_price)
                    caption = chart_gen._create_volume_profile_caption("BTC/USDT", chart_info['data'], current_price)
                elif chart_info['type'] == 'ai_analysis':
                    chart_path = chart_gen.generate_ai_analysis_chart("BTC/USDT", chart_info['data'], ohlcv_data, current_price)
                    caption = chart_gen._create_ai_analysis_caption("BTC/USDT", chart_info['data'], current_price)
                
                if chart_path:
                    print(f"  📊 Chart generated: {chart_path}")
                    
                    # Send with rate limiting built into notifier
                    success = notifier.send_photo(
                        photo_path=chart_path,
                        caption=caption,
                        chat_id=chat_id,
                        parse_mode="HTML"
                    )
                    
                    if success:
                        print(f"  ✅ {chart_info['type']} chart sent successfully")
                        success_count += 1
                    else:
                        print(f"  ❌ {chart_info['type']} chart send failed")
                    
                    # Additional delay between different chart types
                    if i < len(charts_to_send):
                        print(f"  ⏳ Waiting 3 seconds before next chart...")
                        time.sleep(3)
                        
                else:
                    print(f"  ❌ {chart_info['type']} chart generation failed")
                    
            except Exception as chart_error:
                print(f"  ❌ Error with {chart_info['type']}: {chart_error}")
        
        print(f"\n📊 RATE LIMIT TEST RESULTS:")
        print(f"  📤 Charts sent successfully: {success_count}/{len(charts_to_send)}")
        print(f"  📈 Success rate: {success_count/len(charts_to_send)*100:.1f}%")
        
        if success_count == len(charts_to_send):
            print(f"  🎉 All charts sent successfully! Rate limiting fix works!")
            return True
        elif success_count > 0:
            print(f"  ⚠️ Partial success. Rate limiting improved but not perfect.")
            return True
        else:
            print(f"  ❌ No charts sent successfully. Rate limiting fix needs work.")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rate_limit_fix()
    if success:
        print(f"\n🎉 RATE LIMIT FIX TEST PASSED!")
        print(f"📱 Check your Telegram for multiple charts with detailed reports")
    else:
        print(f"\n💥 RATE LIMIT FIX TEST FAILED!")
