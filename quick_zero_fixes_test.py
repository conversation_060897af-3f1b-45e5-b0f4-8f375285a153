#!/usr/bin/env python3
"""
🚨 QUICK ZERO FIXES TEST
Quick test to verify zero value fixes
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test for zero value fixes"""
    print("🚨 QUICK ZERO FIXES TEST")
    print("=" * 50)
    
    try:
        print("🔍 Testing imports...")
        from dump_detector import DumpDetector
        print("✅ DumpDetector imported")
        
        print("\n🔍 Testing initialization...")
        dump_detector = DumpDetector(
            sensitivity=0.6,
            min_volume_threshold=2.0,
            whale_threshold=50000,
            lookback_period=60
        )
        print("✅ DumpDetector initialized")
        
        print("\n🔍 Testing orderbook analysis with empty data...")
        orderbook_signals = dump_detector.ultra_detector._analyze_orderbook_deterioration({}, 100.0)
        print(f"📊 Orderbook signals: {orderbook_signals}")
        
        all_non_zero = all(score > 0 for score in orderbook_signals.values())
        if all_non_zero:
            print("✅ All orderbook signals return baseline scores instead of 0")
        else:
            print("❌ Some orderbook signals still return 0")
            return False
        
        print("\n🔍 Testing bid support with empty orderbook...")
        bid_support = dump_detector.ultra_detector._analyze_bid_support_weakness({}, 100.0)
        print(f"📊 Bid support weakness: {bid_support:.3f}")
        
        if bid_support > 0:
            print("✅ Bid support returns baseline score instead of 0")
        else:
            print("❌ Bid support still returns 0")
            return False
        
        print("\n🔍 Testing ask wall with empty orderbook...")
        ask_wall = dump_detector.ultra_detector._detect_ask_wall_building({}, 100.0)
        print(f"📊 Ask wall building: {ask_wall:.3f}")
        
        if ask_wall > 0:
            print("✅ Ask wall returns baseline score instead of 0")
        else:
            print("❌ Ask wall still returns 0")
            return False
        
        print("\n🎉 ALL QUICK TESTS PASSED!")
        print("✅ Zero value fixes are working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    print(f"\n🎯 Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
