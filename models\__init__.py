# This file makes the 'models' directory a Python package.

"""
AI Models Package for Trading Bot

This package contains various AI model implementations for trading signal prediction.
"""

# Import base class
from .base_ai_model import BaseAIModel

# Import model implementations
try:
    from .xgboost_model import XGBoostModel
except ImportError:
    XGBoostModel = None

try:
    from .randomforest_model import RandomForestModel
except ImportError:
    RandomForestModel = None

try:
    from .gradientboost_model import GradientBoostModel
except ImportError:
    GradientBoostModel = None

try:
    from .lstm_model import LSTMModel
except ImportError:
    LSTMModel = None

try:
    from .cnn_model import CNNModel
except ImportError:
    CNNModel = None

try:
    from .transformer_model import TransformerModel
except ImportError:
    TransformerModel = None

try:
    from .tcn_model import TCNModel
except ImportError:
    TCNModel = None

try:
    from .a2c_model import A2CModel
except ImportError:
    A2CModel = None

try:
    from .dqn_model import DQNModel
except ImportError:
    DQNModel = None

try:
    from .ppo_model import PPOModel
except ImportError:
    PPOModel = None

try:
    from .gan_model import GANModel
except ImportError:
    GANModel = None

__all__ = [
    'BaseAIModel',
    'XGBoostModel',
    'RandomForestModel', 
    'GradientBoostModel',
    'LSTMModel',
    'CNNModel',
    'TransformerModel',
    'TCNModel',
    'A2CModel',
    'DQNModel',
    'PPOModel',
    'GANModel'
]

# Model availability check
AVAILABLE_MODELS = {}
for model_name, model_class in [
    ('XGBoost', XGBoostModel),
    ('RandomForest', RandomForestModel),
    ('GradientBoost', GradientBoostModel),
    ('LSTM', LSTMModel),
    ('CNN', CNNModel),
    ('Transformer', TransformerModel),
    ('TCN', TCNModel),
    ('A2C', A2CModel),
    ('DQN', DQNModel),
    ('PPO', PPOModel),
    ('GAN', GANModel)
]:
    AVAILABLE_MODELS[model_name] = model_class is not None

print(f"AI Models Package initialized with {sum(AVAILABLE_MODELS.values())} available models")
