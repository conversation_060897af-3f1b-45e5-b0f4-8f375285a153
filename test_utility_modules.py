#!/usr/bin/env python3
"""
🧪 TEST UTILITY MODULES
=======================

Test để kiểm tra UTILITY_MODULES trong main_bot.py.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_utility_modules():
    """Test utility modules import."""
    print("🧪 TESTING UTILITY MODULES")
    print("=" * 50)
    
    try:
        # Test individual imports first
        print("📦 Testing individual imports...")
        
        print("  📊 Importing chart_generator...")
        import chart_generator
        print("  ✅ chart_generator imported successfully")
        
        print("  📱 Importing telegram_notifier...")
        import telegram_notifier
        print("  ✅ telegram_notifier imported successfully")
        
        print("  🔧 Importing bot_warning_message...")
        import bot_warning_message
        print("  ✅ bot_warning_message imported successfully")
        
        # Now test main_bot imports
        print("\n📦 Testing main_bot imports...")
        
        # Import just the beginning of main_bot to see UTILITY_MODULES
        print("  📦 Importing main_bot module...")
        import main_bot
        print("  ✅ main_bot imported successfully")
        
        # Check UTILITY_MODULES
        if hasattr(main_bot, 'UTILITY_MODULES'):
            utility_modules = main_bot.UTILITY_MODULES
            print(f"\n📊 UTILITY_MODULES found: {len(utility_modules)} modules")
            for name, module in utility_modules.items():
                print(f"  📦 {name}: {type(module)}")
                
            # Check chart_generator specifically
            if 'chart_generator' in utility_modules:
                chart_gen_module = utility_modules['chart_generator']
                print(f"\n📊 chart_generator module: {chart_gen_module}")
                
                if hasattr(chart_gen_module, 'EnhancedChartGenerator'):
                    print("  ✅ EnhancedChartGenerator class found in UTILITY_MODULES")
                    return True
                else:
                    print("  ❌ EnhancedChartGenerator class not found in UTILITY_MODULES")
                    return False
            else:
                print("  ❌ chart_generator not found in UTILITY_MODULES")
                return False
        else:
            print("  ❌ UTILITY_MODULES not found in main_bot")
            return False
        
    except Exception as e:
        print(f"❌ Utility modules test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_utility_modules()
    if success:
        print("\n🎯 UTILITY MODULES TEST COMPLETED SUCCESSFULLY")
    else:
        print("\n❌ UTILITY MODULES TEST FAILED")
