#!/usr/bin/env python3
"""
🚨 TEST IMPROVED DUMP DETECTOR V5.0
Test the new improved dump detector for accuracy and reduced false positives
"""

import sys
import os
import pandas as pd
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_improved_dump_detector():
    """Test the improved dump detector"""
    print("🚨 TESTING IMPROVED DUMP DETECTOR V5.0")
    print("=" * 60)
    
    try:
        # Import the improved dump detector
        from dump_detector import DumpDetector, UltraEarlyDumpAlert
        
        print("✅ Successfully imported improved dump detector")
        
        # Initialize detector
        detector = DumpDetector(sensitivity=0.55)
        print("✅ Successfully initialized dump detector")
        
        # Create test market data
        test_data = create_test_market_data()
        
        # Test scenarios
        test_scenarios = [
            {
                "name": "Normal Market",
                "data": create_normal_market_data(),
                "expected_alert": False,
                "description": "Normal market conditions - should NOT trigger alert"
            },
            {
                "name": "Weak Dump Signal",
                "data": create_weak_dump_data(),
                "expected_alert": False,
                "description": "Weak dump signals - should NOT trigger alert (reduced false positives)"
            },
            {
                "name": "Strong Dump Signal",
                "data": create_strong_dump_data(),
                "expected_alert": True,
                "description": "Strong dump signals - should trigger alert"
            },
            {
                "name": "Critical Dump Signal",
                "data": create_critical_dump_data(),
                "expected_alert": True,
                "description": "Critical dump signals - should definitely trigger alert"
            }
        ]
        
        print(f"\n🧪 Running {len(test_scenarios)} test scenarios...")
        
        results = []
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📊 Test {i}: {scenario['name']}")
            print(f"  📝 Description: {scenario['description']}")
            
            # Run analysis
            alert = detector.analyze_dump_probability("TESTCOIN/USDT", scenario['data'])
            
            # Check result
            got_alert = alert is not None
            expected_alert = scenario['expected_alert']
            
            print(f"  🎯 Expected Alert: {'✅ YES' if expected_alert else '❌ NO'}")
            print(f"  🔍 Got Alert: {'✅ YES' if got_alert else '❌ NO'}")
            
            if got_alert:
                print(f"  📊 Alert Details:")
                print(f"    - Probability: {alert.dump_probability:.1%}")
                print(f"    - Confidence: {alert.confidence_score:.1%}")
                print(f"    - Risk Level: {alert.risk_level}")
                print(f"    - Stage: {alert.stage}")
                print(f"    - Estimated Magnitude: {alert.estimated_dump_magnitude:.1%}")
                print(f"    - Minutes to Dump: {alert.minutes_to_dump}")
            
            # Evaluate result
            if got_alert == expected_alert:
                print(f"  ✅ TEST PASSED")
                results.append(True)
            else:
                print(f"  ❌ TEST FAILED")
                results.append(False)
        
        # Summary
        passed = sum(results)
        total = len(results)
        success_rate = (passed / total) * 100
        
        print(f"\n" + "=" * 60)
        print(f"🎯 TEST RESULTS SUMMARY")
        print(f"=" * 60)
        print(f"✅ Tests Passed: {passed}/{total}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 75:
            print(f"🎉 IMPROVED DUMP DETECTOR WORKING CORRECTLY!")
            print(f"✅ Reduced false positives while maintaining accuracy")
            print(f"✅ High standards for dump detection (65% prob, 70% conf, 3+ indicators)")
            return True
        else:
            print(f"⚠️ IMPROVED DUMP DETECTOR NEEDS ADJUSTMENT")
            print(f"❌ Success rate below 75%")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_market_data():
    """Create basic test market data"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    
    # Generate realistic OHLCV data
    np.random.seed(42)  # For reproducible results
    
    base_price = 100.0
    prices = []
    volumes = []
    
    for i in range(100):
        # Random walk with slight downward bias
        change = np.random.normal(0, 0.01)
        base_price *= (1 + change)
        
        # OHLCV for this period
        open_price = base_price
        high_price = open_price * (1 + abs(np.random.normal(0, 0.005)))
        low_price = open_price * (1 - abs(np.random.normal(0, 0.005)))
        close_price = open_price + np.random.normal(0, 0.002)
        volume = np.random.normal(1000, 200)
        
        prices.append([open_price, high_price, low_price, close_price])
        volumes.append(max(100, volume))
    
    df = pd.DataFrame(prices, columns=['open', 'high', 'low', 'close'], index=dates)
    df['volume'] = volumes
    
    return {
        'ohlcv_data': df,
        'current_price': df['close'].iloc[-1]
    }

def create_normal_market_data():
    """Create normal market conditions - should NOT trigger alert"""
    data = create_test_market_data()
    
    # Modify to be more normal (small movements, normal volume)
    df = data['ohlcv_data'].copy()
    
    # Small price movements
    for i in range(len(df)):
        if i > 0:
            change = np.random.normal(0, 0.002)  # Very small movements
            df.iloc[i] = df.iloc[i-1] * (1 + change)
    
    # Normal volume
    df['volume'] = np.random.normal(1000, 100, len(df))
    
    data['ohlcv_data'] = df
    data['current_price'] = df['close'].iloc[-1]
    
    return data

def create_weak_dump_data():
    """Create weak dump signals - should NOT trigger alert (reduced false positives)"""
    data = create_test_market_data()
    df = data['ohlcv_data'].copy()
    
    # Weak dump signals (should be filtered out)
    # Small price decline
    for i in range(80, len(df)):
        df.loc[df.index[i], 'close'] *= 0.998  # 0.2% decline per period
        df.loc[df.index[i], 'low'] *= 0.997
        df.loc[df.index[i], 'high'] *= 0.999
    
    # Slightly elevated volume (but not extreme)
    df.loc[df.index[80:], 'volume'] *= 1.5  # 50% volume increase
    
    data['ohlcv_data'] = df
    data['current_price'] = df['close'].iloc[-1]
    
    return data

def create_strong_dump_data():
    """Create strong dump signals - should trigger alert"""
    data = create_test_market_data()
    df = data['ohlcv_data'].copy()

    # Strong dump signals
    # Significant price decline with proper OHLC relationships
    base_price = df['close'].iloc[84]
    for i in range(85, len(df)):
        # Progressive decline
        decline_factor = 0.985 ** (i - 84)  # Cumulative decline

        # Set close price
        new_close = base_price * decline_factor
        df.loc[df.index[i], 'close'] = new_close

        # Set OHLC properly
        df.loc[df.index[i], 'open'] = df.loc[df.index[i-1], 'close'] if i > 85 else base_price
        df.loc[df.index[i], 'high'] = max(df.loc[df.index[i], 'open'], new_close) * 1.002
        df.loc[df.index[i], 'low'] = min(df.loc[df.index[i], 'open'], new_close) * 0.998

    # High volume with price decline
    for i in range(85, len(df)):
        df.loc[df.index[i], 'volume'] *= 3.0  # 3x volume increase

    # Break support levels
    support_level = df['low'].iloc[:80].min()
    current_price = df['close'].iloc[-1]
    if current_price > support_level * 0.95:  # Force below support
        for i in range(len(df)-5, len(df)):
            df.loc[df.index[i], 'close'] = support_level * 0.92
            df.loc[df.index[i], 'low'] = support_level * 0.90
            df.loc[df.index[i], 'high'] = support_level * 0.95

    data['ohlcv_data'] = df
    data['current_price'] = df['close'].iloc[-1]

    return data

def create_critical_dump_data():
    """Create critical dump signals - should definitely trigger alert"""
    data = create_test_market_data()
    df = data['ohlcv_data'].copy()

    # Critical dump signals
    # Severe price decline with proper OHLC relationships
    base_price = df['close'].iloc[89]
    for i in range(90, len(df)):
        # Aggressive decline
        decline_factor = 0.970 ** (i - 89)  # Cumulative severe decline

        # Set close price
        new_close = base_price * decline_factor
        df.loc[df.index[i], 'close'] = new_close

        # Set OHLC properly
        df.loc[df.index[i], 'open'] = df.loc[df.index[i-1], 'close'] if i > 90 else base_price
        df.loc[df.index[i], 'high'] = max(df.loc[df.index[i], 'open'], new_close) * 1.001
        df.loc[df.index[i], 'low'] = min(df.loc[df.index[i], 'open'], new_close) * 0.995

    # Very high volume with severe decline
    for i in range(90, len(df)):
        df.loc[df.index[i], 'volume'] *= 5.0  # 5x volume increase

    # Clear support breakdown
    support_level = df['low'].iloc[:85].min()
    for i in range(len(df)-10, len(df)):
        df.loc[df.index[i], 'close'] = support_level * 0.85  # 15% below support
        df.loc[df.index[i], 'low'] = support_level * 0.80
        df.loc[df.index[i], 'high'] = support_level * 0.88

    # Lower highs and lower lows pattern
    for i in range(len(df)-10, len(df)):
        if i > len(df)-10:
            prev_high = df.loc[df.index[i-1], 'high']
            df.loc[df.index[i], 'high'] = min(df.loc[df.index[i], 'high'], prev_high * 0.98)

    data['ohlcv_data'] = df
    data['current_price'] = df['close'].iloc[-1]

    return data

if __name__ == "__main__":
    success = test_improved_dump_detector()
    
    print(f"\n🎯 FINAL RESULT: {'SUCCESS' if success else 'NEEDS_IMPROVEMENT'}")
    
    if success:
        print("\n🎉 IMPROVED DUMP DETECTOR V5.0 VERIFICATION COMPLETE!")
        print("✅ Reduced false positives")
        print("✅ Maintained accuracy for real dump signals")
        print("✅ High standards prevent noise")
        print("✅ Ready for production use")
        
        print("\n📊 Key Improvements:")
        print("  🎯 65% minimum dump probability (was variable)")
        print("  🎯 70% minimum confidence (was 60%)")
        print("  🎯 3+ indicators required (was 1-2)")
        print("  🎯 Only 4 key indicators (was 20+)")
        print("  🎯 Strict scoring with penalties")
        print("  🎯 No baseline assumptions")
        
    else:
        print("\n⚠️ IMPROVED DUMP DETECTOR NEEDS FURTHER TUNING")
        print("🔧 Consider adjusting thresholds")
        print("🔧 Review indicator weights")
        print("🔧 Test with more scenarios")
    
    sys.exit(0 if success else 1)
