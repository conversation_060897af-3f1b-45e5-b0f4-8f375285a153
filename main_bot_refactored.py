#!/usr/bin/env python3
"""
🚀 ENHANCED TRADING BOT V5.0 - REFACTORED MAIN ENTRY POINT
==========================================================

Simplified and modular main entry point for the Enhanced Trading Bot.
This refactored version uses modular components for better maintainability.

Features:
- Modular architecture with separated concerns
- Clean main entry point with minimal code
- Enhanced error handling and health monitoring
- Comprehensive admin system management
- Production-ready with full functionality

Author: AI Trading Bot Team
Version: 5.0 - Refactored
License: Proprietary
"""

import os
import sys
import time
import argparse
import traceback
import warnings
from datetime import datetime
from typing import Optional, Dict, Any

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv("E:/BOT-2/.env")
    print("✅ Environment variables loaded successfully")
except ImportError:
    print("⚠️ python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"⚠️ Error loading environment variables: {e}")

# Import modular components
try:
    from core import TradingBot, enhanced_module_loader, MODULE_LOADING_STATS, SystemHealthMonitor
    from admin import AdminSystemManager
    print("✅ Modular components imported successfully")
except ImportError as e:
    print(f"❌ Failed to import modular components: {e}")
    print("💡 Falling back to original main_bot.py structure...")
    # Fallback to original import
    try:
        from main_bot import TradingBot
        print("✅ Fallback to original TradingBot successful")
    except ImportError:
        print("❌ Critical error: Cannot import TradingBot")
        sys.exit(1)

class EnhancedTradingBotManager:
    """🚀 Enhanced Trading Bot Manager with modular architecture."""
    
    def __init__(self):
        """Initialize the bot manager."""
        self.bot = None
        self.health_monitor = None
        self.admin_manager = None
        self.startup_time = time.time()
        self.initialization_stats = {
            "start_time": self.startup_time,
            "modules_loaded": 0,
            "modules_failed": 0,
            "health_score": 0.0,
            "ready": False
        }
    
    def initialize_bot(self) -> bool:
        """🔧 Initialize the trading bot with modular components."""
        try:
            print("🔧 Initializing Enhanced Trading Bot V5.0 (Refactored)...")
            
            # Initialize core bot
            print("  📊 Initializing core trading bot...")
            self.bot = TradingBot()
            
            if self.bot:
                self.initialization_stats["modules_loaded"] += 1
                print("    ✅ Core trading bot initialized")
            else:
                self.initialization_stats["modules_failed"] += 1
                print("    ❌ Core trading bot initialization failed")
                return False
            
            # Initialize health monitor
            print("  🏥 Initializing system health monitor...")
            self.health_monitor = SystemHealthMonitor(self.bot)
            
            if self.health_monitor:
                self.initialization_stats["modules_loaded"] += 1
                print("    ✅ Health monitor initialized")
            else:
                self.initialization_stats["modules_failed"] += 1
                print("    ❌ Health monitor initialization failed")
            
            # Initialize admin manager
            print("  👑 Initializing admin system manager...")
            self.admin_manager = AdminSystemManager(self.bot)
            
            if self.admin_manager:
                admin_success = self.admin_manager.initialize_admin_systems()
                if admin_success:
                    self.initialization_stats["modules_loaded"] += 1
                    print("    ✅ Admin system manager initialized")
                else:
                    self.initialization_stats["modules_failed"] += 1
                    print("    ⚠️ Admin system manager partially initialized")
            else:
                self.initialization_stats["modules_failed"] += 1
                print("    ❌ Admin system manager initialization failed")
            
            # Calculate initialization success
            total_modules = self.initialization_stats["modules_loaded"] + self.initialization_stats["modules_failed"]
            success_rate = self.initialization_stats["modules_loaded"] / total_modules if total_modules > 0 else 0
            
            self.initialization_stats["health_score"] = success_rate
            self.initialization_stats["ready"] = success_rate >= 0.6  # 60% minimum for ready status
            
            initialization_time = time.time() - self.startup_time
            
            print(f"\n🎯 Initialization Summary:")
            print(f"  📊 Modules loaded: {self.initialization_stats['modules_loaded']}")
            print(f"  ❌ Modules failed: {self.initialization_stats['modules_failed']}")
            print(f"  📈 Success rate: {success_rate*100:.1f}%")
            print(f"  ⏱️ Time: {initialization_time:.2f}s")
            print(f"  🚀 Ready: {'✅' if self.initialization_stats['ready'] else '❌'}")
            
            return self.initialization_stats["ready"]
            
        except Exception as e:
            print(f"❌ Bot initialization failed: {e}")
            traceback.print_exc()
            return False
    
    def perform_health_check(self) -> Dict[str, Any]:
        """🏥 Perform comprehensive health check."""
        try:
            if not self.health_monitor:
                return {"error": "Health monitor not available"}
            
            print("🏥 Performing system health check...")
            health_result = self.health_monitor.perform_quick_health_check()
            
            health_score = health_result.get("health_score", 0.0)
            
            print(f"📊 Health Check Results:")
            print(f"  🎯 Health Score: {health_score*100:.1f}%")
            print(f"  🔧 Core Systems: {health_result.get('core_systems_ready', 0)}/{health_result.get('total_core_systems', 0)}")
            print(f"  📱 Communication: {'✅' if health_result.get('communication_ready', False) else '❌'}")
            print(f"  🧠 AI System: {'✅' if health_result.get('ai_ready', False) else '❌'}")
            print(f"  💾 Backup: {'✅' if health_result.get('backup_ready', False) else '❌'}")
            print(f"  📝 Logging: {'✅' if health_result.get('logging_ready', False) else '❌'}")
            
            # Get recommendations
            recommendations = self.health_monitor.get_system_recommendations()
            if recommendations:
                print(f"\n💡 Recommendations:")
                for rec in recommendations[:3]:  # Show top 3 recommendations
                    print(f"  {rec}")
            
            return health_result
            
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return {"error": str(e), "health_score": 0.0}
    
    def run_bot(self, mode: str = "full") -> bool:
        """🚀 Run the trading bot in specified mode."""
        try:
            if not self.bot:
                print("❌ Bot not initialized")
                return False
            
            print(f"\n🚀 Starting Enhanced Trading Bot V5.0 in {mode.upper()} mode...")
            
            # Run based on mode
            if mode == "basic":
                print("📊 Features: Core trading analysis only")
                return self.bot.run()
            
            elif mode == "telegram":
                print("📱 Features: Full Telegram integration with admin commands")
                return self.bot.run_with_telegram_integration()
            
            else:  # full mode
                print("🎯 Features: All systems operational")
                print("📱 Telegram Integration: ✅ Active")
                print("👑 Admin Commands: ✅ Available")
                print("📊 Member Management: ✅ Active")
                print("🚨 Warning System: ✅ Active")
                print("📱 QR Code System: ✅ Integrated")
                return self.bot.run_with_telegram_integration()
                
        except Exception as e:
            print(f"❌ Bot execution failed: {e}")
            traceback.print_exc()
            return False
    
    def get_status_summary(self) -> Dict[str, Any]:
        """📊 Get comprehensive status summary."""
        try:
            summary = {
                "initialization": self.initialization_stats,
                "health": None,
                "admin_systems": None,
                "module_stats": MODULE_LOADING_STATS,
                "uptime": time.time() - self.startup_time
            }
            
            # Get health status
            if self.health_monitor:
                summary["health"] = self.health_monitor.perform_quick_health_check()
            
            # Get admin status
            if self.admin_manager:
                summary["admin_systems"] = self.admin_manager.get_admin_system_status()
            
            return summary
            
        except Exception as e:
            return {"error": str(e)}

def main():
    """🚀 Enhanced main execution function with modular architecture."""
    print("=" * 80)
    print("🚀 ENHANCED TRADING BOT V5.0 - REFACTORED ARCHITECTURE")
    print("=" * 80)
    print("🧠 AI Models: 11+ machine learning models with ensemble consensus")
    print("📊 Algorithms: Volume Profile | Point & Figure | Fourier | Fibonacci | Consensus")
    print("🔍 Detection: Pump/Dump | Whale Activity | Market Manipulation | Early Warning")
    print("📱 Telegram: Multi-chat routing | Admin system | Member management | QR codes")
    print("🎯 Features: Ultra-fast TP/SL | Dynamic adjustments | Cross-asset analysis")
    print("⚡ Performance: Parallel processing | Adaptive timing | Smart coin selection")
    print("🔧 Architecture: Modular design | Separated concerns | Enhanced maintainability")
    print("=" * 80)

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Enhanced Trading Bot V5.0 - Refactored')
    parser.add_argument('--mode', choices=['basic', 'full', 'telegram'], default='full',
                       help='Bot operation mode (default: full)')
    parser.add_argument('--config', help='Custom configuration file path')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--test', action='store_true', help='Run in test mode')
    parser.add_argument('--health-check', action='store_true', help='Perform health check only')

    args = parser.parse_args()

    # Set debug mode if requested
    if args.debug:
        print("🔧 Debug mode enabled")
        import logging
        logging.basicConfig(level=logging.DEBUG)

    try:
        # Initialize bot manager
        bot_manager = EnhancedTradingBotManager()
        
        # Initialize bot
        if not bot_manager.initialize_bot():
            print("❌ Bot initialization failed - cannot continue")
            return 1
        
        # Handle different modes
        if args.health_check:
            print("\n🏥 Running health check only...")
            health_result = bot_manager.perform_health_check()
            health_score = health_result.get("health_score", 0.0)
            
            if health_score >= 0.8:
                print("\n🏆 EXCELLENT: System is ready for production!")
                return 0
            elif health_score >= 0.6:
                print("\n✅ GOOD: System is operational with minor issues")
                return 0
            elif health_score >= 0.4:
                print("\n⚠️ WARNING: System has significant issues")
                return 1
            else:
                print("\n🔴 CRITICAL: System requires immediate attention")
                return 2
        
        elif args.test:
            print("\n🧪 Running in TEST mode...")
            health_result = bot_manager.perform_health_check()
            health_score = health_result.get("health_score", 0.0)
            
            print(f"\n🧪 Test completed - Health Score: {health_score*100:.1f}%")
            return 0 if health_score >= 0.5 else 1
        
        else:
            # Perform health check before running
            health_result = bot_manager.perform_health_check()
            health_score = health_result.get("health_score", 0.0)
            
            if health_score < 0.5:
                print("🔴 System health is critically low!")
                response = input("Continue with degraded functionality? (y/N): ")
                if response.lower() != 'y':
                    print("❌ Bot startup cancelled by user")
                    return 1
            
            # Run bot
            success = bot_manager.run_bot(args.mode)
            return 0 if success else 1

    except KeyboardInterrupt:
        print("\n\n⏹️ Bot stopped by user (Ctrl+C)")
        print("🔧 Performing graceful shutdown...")
        print("✅ Shutdown completed successfully")
        return 0

    except Exception as e:
        print(f"\n❌ Critical error during bot execution: {e}")
        print("🔧 Error details:")
        traceback.print_exc()
        print("\n📞 Support: Please report this error with the above traceback")
        return 1

if __name__ == "__main__":
    """Main entry point with enhanced error handling."""
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)
