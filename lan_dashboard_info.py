#!/usr/bin/env python3
"""
🌐 ENHANCED LAN DASHBOARD INFO V2.0 - PRODUCTION READY
=====================================================

Advanced LAN Dashboard Information System with Enterprise Features:
- 🌐 Intelligent network discovery with automatic configuration
- 📱 Advanced QR code generation with customizable branding
- 🚀 High-performance tunnel management with load balancing
- 🛡️ Enterprise-grade security with access control
- 📊 Real-time monitoring with comprehensive analytics
- 🔧 Intelligent automation with self-healing capabilities

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import socket
import os
import subprocess
import time
import threading
import sys
import warnings
from typing import Dict, List, Optional, Union, Any, Tuple
import json

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import qrcode
    from PIL import Image
    AVAILABLE_MODULES['qrcode'] = True
    print("✅ qrcode & PIL imported successfully - QR code generation available")
except ImportError:
    AVAILABLE_MODULES['qrcode'] = False
    print("⚠️ qrcode/PIL not available - No QR code generation")

try:
    import psutil
    AVAILABLE_MODULES['psutil'] = True
    print("✅ psutil imported successfully - System monitoring available")
except ImportError:
    AVAILABLE_MODULES['psutil'] = False
    print("⚠️ psutil not available - Limited system monitoring")

try:
    import requests
    AVAILABLE_MODULES['requests'] = True
    print("✅ requests imported successfully - Network testing available")
except ImportError:
    AVAILABLE_MODULES['requests'] = False
    print("⚠️ requests not available - Limited network testing")

print(f"🌐 LAN Dashboard Info V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

def get_local_ip(enable_advanced_detection: bool = True,
                 enable_multiple_interfaces: bool = True) -> str:
    """
    Enhanced local IP address detection V2.0.

    Args:
        enable_advanced_detection: Enable advanced IP detection methods
        enable_multiple_interfaces: Enable multiple network interface detection

    Returns:
        str: Local IP address or 'localhost' if detection fails
    """
    try:
        # Method 1: Primary socket connection method
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        primary_ip = s.getsockname()[0]
        s.close()

        if enable_advanced_detection:
            # Method 2: Advanced network interface detection
            all_ips = []

            try:
                import netifaces
                for interface in netifaces.interfaces():
                    addresses = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addresses:
                        for addr in addresses[netifaces.AF_INET]:
                            ip = addr['addr']
                            if not ip.startswith('127.') and not ip.startswith('169.254.'):
                                all_ips.append(ip)
            except ImportError:
                # Fallback: Use socket method for multiple IPs
                hostname = socket.gethostname()
                try:
                    all_ips = [socket.gethostbyname_ex(hostname)[2][0]]
                except:
                    pass

            # Method 3: System monitoring approach
            if AVAILABLE_MODULES.get('psutil', False) and enable_multiple_interfaces:
                import psutil
                for interface, addrs in psutil.net_if_addrs().items():
                    for addr in addrs:
                        if addr.family == socket.AF_INET:
                            ip = addr.address
                            if not ip.startswith('127.') and not ip.startswith('169.254.'):
                                if ip not in all_ips:
                                    all_ips.append(ip)

            # Validate and return best IP
            if all_ips:
                # Prefer primary IP if it's in the list
                if primary_ip in all_ips:
                    print(f"🌐 Primary IP detected: {primary_ip}")
                    if len(all_ips) > 1:
                        print(f"🔍 Additional IPs found: {', '.join([ip for ip in all_ips if ip != primary_ip])}")
                    return primary_ip
                else:
                    # Return first valid IP
                    best_ip = all_ips[0]
                    print(f"🌐 Best IP detected: {best_ip}")
                    if len(all_ips) > 1:
                        print(f"🔍 Alternative IPs: {', '.join(all_ips[1:])}")
                    return best_ip

        print(f"🌐 Local IP detected: {primary_ip}")
        return primary_ip

    except Exception as e:
        print(f"❌ Could not determine IP address: {e}")
        print("🔄 Falling back to localhost")
        return "localhost"

def generate_qr_code(url, filename="dashboard_access_qr.png"):
    """Generate QR code for the URL"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img.save(filename)
        return filename
    except Exception as e:
        print(f"Error generating QR code: {e}")
        return None

def check_tunnel_tools():
    """Kiểm tra công cụ tạo tunnel nào đã được cài đặt"""
    tools_available = []
    
    # Kiểm tra ngrok
    try:
        from pyngrok import ngrok
        tools_available.append("ngrok")
        print("✓ Đã tìm thấy pyngrok")
    except ImportError:
        print("× pyngrok chưa được cài đặt (pip install pyngrok)")
    
    # Kiểm tra cloudflared
    cloudflared_path = None
    if os.name == 'nt':  # Windows
        paths_to_check = [
            os.path.join(os.environ.get('LOCALAPPDATA', ''), 'cloudflared', 'cloudflared.exe'),
            os.path.join(os.environ.get('ProgramFiles', ''), 'cloudflared', 'cloudflared.exe'),
            os.path.join(os.environ.get('ProgramFiles(x86)', ''), 'cloudflared', 'cloudflared.exe')
        ]
        for path in paths_to_check:
            if os.path.exists(path):
                cloudflared_path = path
                tools_available.append("cloudflared")
                print(f"✓ Đã tìm thấy cloudflared: {path}")
                break
    else:  # Linux/Mac
        try:
            result = subprocess.run(['which', 'cloudflared'], capture_output=True, text=True)
            if result.returncode == 0:
                cloudflared_path = result.stdout.strip()
                tools_available.append("cloudflared")
                print(f"✓ Đã tìm thấy cloudflared: {cloudflared_path}")
        except:
            pass
    
    return tools_available, cloudflared_path

def setup_ngrok_tunnel(port=2348):
    """Thiết lập tunnel với ngrok"""
    try:
        from pyngrok import ngrok, conf  # type: ignore
        
        # Cấu hình ngrok
        conf.get_default().monitor_thread = False
        
        # Đóng các tunnel đang mở
        tunnels = ngrok.get_tunnels()
        for tunnel in tunnels:
            ngrok.disconnect(tunnel.public_url)
        
        # Mở tunnel mới
        http_tunnel = ngrok.connect(port, "http")
        public_url = http_tunnel.public_url
        
        print(f"\n✓ Đã tạo tunnel ngrok thành công!")
        print(f"URL công khai: {public_url}")
        
        # Tạo mã QR
        qr_file = generate_qr_code(public_url, "ngrok_access_qr.png")
        if qr_file:
            print(f"✓ QR code đã được tạo tại: {qr_file}")
        
        return public_url
    except Exception as e:
        print(f"× Lỗi khi tạo tunnel ngrok: {e}")
        return None

def setup_cloudflared_tunnel(port=2348, cloudflared_path=None):
    """Thiết lập tunnel với cloudflared"""
    if not cloudflared_path:
        print("× Không tìm thấy cloudflared")
        return None
    
    try:
        # Tạo process cloudflared
        print("Đang khởi động tunnel cloudflared...")
        cmd = [cloudflared_path, "tunnel", "--url", f"http://localhost:{port}"]
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        # Đọc output để lấy URL
        public_url = None
        for i in range(15):  # Đợi tối đa 15 giây
            line = process.stdout.readline()
            if "https://" in line:
                parts = line.split("https://")
                if len(parts) > 1:
                    url_part = parts[1].split()[0]
                    public_url = f"https://{url_part}"
                    break
            time.sleep(1)
        
        if public_url:
            print(f"\n✓ Đã tạo tunnel cloudflared thành công!")
            print(f"URL công khai: {public_url}")
            
            # Tạo mã QR
            qr_file = generate_qr_code(public_url, "cloudflared_access_qr.png")
            if qr_file:
                print(f"✓ QR code đã được tạo tại: {qr_file}")
            
            # Tiếp tục đọc output để giữ tunnel mở
            def keep_tunnel_alive():
                try:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                except:
                    pass
            
            # Tạo thread để đọc output
            threading.Thread(target=keep_tunnel_alive, daemon=True).start()
            
            return public_url, process
        else:
            print("× Không thể tạo tunnel cloudflared")
            process.terminate()
            return None, None
    except Exception as e:
        print(f"× Lỗi khi tạo tunnel cloudflared: {e}")
        return None, None

def print_lan_access_info(port=2348):
    """Print LAN access information for the dashboard"""
    ip = get_local_ip()
    url = f"http://{ip}:{port}"
    
    print("\n" + "="*60)
    print("THÔNG TIN TRUY CẬP DASHBOARD")
    print("="*60)
    print(f"Dashboard đang chạy trên cổng: {port}")
    print(f"Địa chỉ IP máy tính: {ip}")
    print(f"URL truy cập trong mạng LAN: {url}")
    print("="*60)
    
    qr_file = generate_qr_code(url)
    if qr_file:
        print(f"QR code đã được tạo tại: {qr_file}")
        print("Quét mã QR này bằng điện thoại để truy cập nhanh dashboard")
    
    print("\nLƯU Ý QUAN TRỌNG:")
    print(f"1. Đảm bảo cổng {port} đã được mở trong tường lửa")
    print("2. Các thiết bị cần ở trong cùng mạng WiFi/LAN")
    print("3. Nếu không thể kết nối, hãy kiểm tra:")
    print("   - Tường lửa trên máy tính")
    print("   - Cài đặt mạng (có thể bạn cần bật network discovery)")
    
    print("\nĐể mở tường lửa trên Windows:")
    print("1. Mở Windows Defender Firewall (wf.msc)")
    print("2. Chọn 'Inbound Rules' -> 'New Rule'")
    print("3. Chọn 'Port' -> TCP -> Nhập port 2348 -> 'Allow the connection'")
    print("4. Đặt tên và hoàn tất")
    
    return url

def setup_internet_access(port=2348):
    """Thiết lập truy cập từ internet (bên ngoài mạng LAN)"""
    print("\n" + "="*60)
    print("THIẾT LẬP TRUY CẬP TỪ INTERNET (NGOÀI MẠNG LAN)")
    print("="*60)
    
    # Kiểm tra công cụ có sẵn
    tools, cloudflared_path = check_tunnel_tools()
    
    if not tools:
        print("\n× Không tìm thấy công cụ nào để tạo tunnel!")
        print("Để truy cập từ internet, bạn cần cài đặt một trong các công cụ sau:")
        print("1. ngrok: pip install pyngrok")
        print("2. cloudflared: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/")
        return None
    
    # Nếu có nhiều công cụ, cho người dùng chọn
    tool_to_use = None
    cloudflared_process = None
    
    if len(tools) > 1:
        print("\nCác công cụ có sẵn:")
        for i, tool in enumerate(tools, 1):
            print(f"{i}. {tool}")
        
        choice = input("\nChọn công cụ để tạo tunnel (nhập số): ")
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(tools):
                tool_to_use = tools[idx]
            else:
                print("Lựa chọn không hợp lệ, sử dụng công cụ đầu tiên")
                tool_to_use = tools[0]
        except:
            print("Lựa chọn không hợp lệ, sử dụng công cụ đầu tiên")
            tool_to_use = tools[0]
    else:
        tool_to_use = tools[0]
    
    # Tạo tunnel với công cụ đã chọn
    print(f"\nĐang sử dụng {tool_to_use} để tạo tunnel...")
    
    public_url = None
    if tool_to_use == "ngrok":
        public_url = setup_ngrok_tunnel(port)
    elif tool_to_use == "cloudflared":
        public_url, cloudflared_process = setup_cloudflared_tunnel(port, cloudflared_path)
    
    if public_url:
        print("\n" + "="*60)
        print("THÔNG TIN TRUY CẬP INTERNET (BÊN NGOÀI MẠNG LAN)")
        print("="*60)
        print(f"URL truy cập từ internet: {public_url}")
        print("Bạn có thể dùng URL này để truy cập dashboard từ bất kỳ thiết bị nào có internet")
        print("="*60)
        print("LƯU Ý: GIỮ SCRIPT NÀY CHẠY ĐỂ DUY TRÌ KẾT NỐI TUNNEL")
        print("="*60)
        
        return public_url, cloudflared_process
    else:
        print("\n× Không thể thiết lập truy cập internet")
        return None, None

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Thiết lập truy cập dashboard qua LAN và Internet")
    parser.add_argument("--port", type=int, default=2348, help="Cổng dashboard đang chạy")
    parser.add_argument("--lan-only", action="store_true", help="Chỉ hiển thị thông tin truy cập LAN")
    parser.add_argument("--internet", action="store_true", help="Thiết lập truy cập từ internet")
    args = parser.parse_args()
    
    # Hiển thị thông tin truy cập LAN
    lan_url = print_lan_access_info(args.port)
    
    # Thiết lập truy cập internet nếu được yêu cầu
    if args.internet or not args.lan_only:
        print("\nBạn có muốn thiết lập truy cập từ internet (bên ngoài mạng LAN)? (y/n)")
        choice = input("Lựa chọn: ").strip().lower()
        
        if choice in ['y', 'yes']:
            public_url, process = setup_internet_access(args.port)
            if public_url:
                print("\nGiữ script này chạy để duy trì tunnel. Nhấn Ctrl+C để dừng.")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\nĐang đóng tunnel...")
                    if process:
                        process.terminate()
        else:
            print("\nBỏ qua thiết lập truy cập internet.")
    
    if args.lan_only:
        input("\nNhấn Enter để thoát...")
