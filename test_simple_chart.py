#!/usr/bin/env python3
"""
🧪 SIMPLE CHART GENERATOR TEST
=============================

Test đơn giản để kiểm tra chart generator.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_chart():
    """Test simple chart generation."""
    print("🧪 TESTING SIMPLE CHART GENERATION")
    print("=" * 50)
    
    try:
        # Test import
        print("📦 Importing chart_generator...")
        import chart_generator
        print("✅ chart_generator imported successfully")
        
        # Test class
        print("🎨 Getting EnhancedChartGenerator class...")
        ChartGen = chart_generator.EnhancedChartGenerator
        print("✅ EnhancedChartGenerator class found")
        
        # Test initialization
        print("🚀 Initializing chart generator...")
        chart_gen = ChartGen(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=True,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=500
        )
        print("✅ Chart generator initialized successfully!")
        
        # Test methods
        print("🔧 Testing methods...")
        methods = [
            'generate_fibonacci_chart',
            'generate_volume_profile_chart', 
            'generate_point_figure_chart',
            'generate_fourier_chart',
            'generate_ai_analysis_chart'
        ]
        
        for method in methods:
            has_method = hasattr(chart_gen, method)
            print(f"  📊 {method}: {'✅' if has_method else '❌'}")
        
        print("\n🎯 SIMPLE CHART TEST COMPLETED SUCCESSFULLY")
        return True
        
    except Exception as e:
        print(f"❌ Simple chart test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_chart()
