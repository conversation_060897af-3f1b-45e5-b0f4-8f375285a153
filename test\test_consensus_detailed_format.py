#!/usr/bin/env python3
"""
🧪 TEST: Consensus Signal Detailed Format
Test để kiểm tra format chi tiết của consensus signal
"""

import sys
import os
import time

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_signal_data():
    """Create comprehensive test signal data"""
    return {
        "signal_id": f"SIG_PYTH/USDT_{int(time.time())}",
        "coin": "PYTH/USDT",
        "coin_category": "DEFI",
        "signal_type": "SELL",
        "entry": 0.10740000,
        "take_profit": 0.09818510,
        "stop_loss": 0.11354327,
        "risk_reward_ratio": 1.50,
        "primary_tf": "4h",
        "context_tfs": ["1h", "4h", "1d"],
        "ai_confidence": 0.85,
        "consensus_score": 0.499,
        "consensus_confidence": 0.78,
        "volume_spike_detected": True,
        "pump_enhanced": False,
        "pump_probability": 0.15,
        "tp_sl_methods": ["ATR Dynamic", "Fibonacci", "Volume Profile", "Support/Resistance"],
        "tp_sl_confidence": 0.82,
        "high_confidence": True,
        "multi_timeframe_confirmed": True
    }

def create_test_consensus_data():
    """Create comprehensive test consensus data"""
    return {
        "consensus_score": 0.499,
        "consensus_confidence": 0.78,
        "consensus_signal": "SELL",
        "algorithm_results": {
            "ai_analyzer": {"signal": "SELL", "confidence": 0.85},
            "volume_profile": {"signal": "SELL", "confidence": 0.75},
            "point_figure": {"signal": "SELL", "confidence": 0.70},
            "fourier_analysis": {"signal": "SELL", "confidence": 0.68},
            "orderbook_analysis": {"signal": "NONE", "confidence": 0.45},
            "fibonacci_analysis": {"signal": "SELL", "confidence": 0.72}
        },
        "total_algorithms": 6,
        "agreeing_algorithms": 5
    }

def test_enhanced_signal_notification():
    """Test enhanced signal notification format"""
    print("🧪 === TESTING ENHANCED CONSENSUS SIGNAL FORMAT ===")
    
    try:
        # Mock the main bot's enhanced signal notification method
        class MockMainBot:
            def _send_enhanced_signal_notification(self, signal_data, consensus_data):
                """Mock implementation of enhanced signal notification"""
                coin = signal_data.get("coin")
                signal_type = signal_data.get("signal_type")
                entry = signal_data.get("entry")
                take_profit = signal_data.get("take_profit")
                stop_loss = signal_data.get("stop_loss")
                risk_reward = signal_data.get("risk_reward_ratio")
                consensus_score = consensus_data.get("consensus_score", 0)
                consensus_confidence = consensus_data.get("consensus_confidence", 0)
                coin_category = signal_data.get("coin_category", "UNKNOWN")
                primary_tf = signal_data.get("primary_tf", "4h")
                
                signal_emoji = "🟢" if signal_type == "BUY" else "🔴"
                signal_header = f"{signal_emoji} SIGNAL TYPE: {signal_type} {signal_emoji}"
                
                # Get algorithm results for detailed analysis
                algorithm_results = consensus_data.get("algorithm_results", {})
                total_algorithms = len(algorithm_results)
                agreeing_algorithms = sum(1 for result in algorithm_results.values() 
                                        if result.get("signal") == signal_type)
                
                # Calculate signal strength
                signal_strength = (agreeing_algorithms / total_algorithms) if total_algorithms > 0 else 0
                
                # Get TP/SL analysis
                tp_sl_methods = signal_data.get("tp_sl_methods", ["ATR", "Fibonacci", "Volume Profile"])
                tp_sl_confidence = signal_data.get("tp_sl_confidence", 0.75)
                
                # Enhancement features
                enhancements = []
                if signal_data.get("high_confidence", False):
                    enhancements.append("🎯 High Confidence Signal")
                if signal_data.get("multi_timeframe_confirmed", False):
                    enhancements.append("📊 Multi-Timeframe Confirmation")
                if signal_data.get("volume_spike_detected", False):
                    enhancements.append("⚡ Volume Spike Detected")
                if signal_data.get("pump_enhanced", False):
                    enhancements.append("🚀 Pump Detection Enhanced")
                if signal_data.get("ai_confidence", 0) > 0.8:
                    enhancements.append("🤖 AI High Confidence")
                
                enhancement_text = "\n".join([f"├ {feature}" for feature in enhancements]) if enhancements else "├ Không có enhancement đặc biệt"
                
                message = f"""
🎯 <b>CONSENSUS SIGNAL - {coin}</b> 🎯

{signal_header}

🪙 <b>{coin} ({coin_category}) | 📈 {primary_tf}</b>

💰 <b>Entry:</b> <code>{entry:.8f}</code>
🎯 <b>Take Profit:</b> <code>{take_profit:.8f}</code>
🛡️ <b>Stop Loss:</b> <code>{stop_loss:.8f}</code>
⚖️ <b>Risk/Reward:</b> <code>{risk_reward:.2f}</code>

🎯 <b>PHÂN TÍCH ĐỒNG THUẬN:</b>
├ <b>Điểm đồng thuận:</b> <code>{consensus_score:.3f}</code>
├ <b>Độ tin cậy:</b> <code>{consensus_confidence:.1%}</code>
├ <b>Sức mạnh tín hiệu:</b> <code>{signal_strength:.1%}</code>
└ <b>Chất lượng tổng thể:</b> <code>{'CAO' if consensus_score > 0.7 else 'TRUNG BÌNH' if consensus_score > 0.5 else 'THẤP'}</code>

📊 <b>PHÂN TÍCH CHI TIẾT:</b>
├ <b>Thuật toán đồng ý:</b> <code>{agreeing_algorithms}/{total_algorithms}</code>
├ <b>Timeframe chính:</b> <code>{primary_tf}</code>
├ <b>Loại coin:</b> <code>{coin_category}</code>
└ <b>Độ tin cậy AI:</b> <code>{signal_data.get('ai_confidence', 0):.1%}</code>

🎯 <b>PHÂN TÍCH TP/SL:</b>
├ <b>Phương pháp sử dụng:</b> <code>{', '.join(tp_sl_methods[:3])}</code>
├ <b>Độ tin cậy TP/SL:</b> <code>{tp_sl_confidence:.1%}</code>
└ <b>Điểm chính xác:</b> <code>{'CAO' if tp_sl_confidence > 0.8 else 'TRUNG BÌNH' if tp_sl_confidence > 0.6 else 'THẤP'}</code>

💡 <b>NÂNG CAO:</b>
{enhancement_text}
└ <b>Tích hợp đa thuật toán:</b> <code>✅ HOÀN CHỈNH</code>

🆔 <b>Signal ID:</b> <code>{signal_data.get("signal_id")}</code>
⏰ <b>Thời gian:</b> <code>{time.strftime('%H:%M:%S %d/%m/%Y')}</code>

⚡ <i>Tín hiệu này đạt tiêu chuẩn nghiêm ngặt với {total_algorithms} thuật toán phân tích và dynamic TP/SL calculation.</i>
                """
                
                return message.strip()
        
        # Create test data
        signal_data = create_test_signal_data()
        consensus_data = create_test_consensus_data()
        
        # Test the enhanced format
        mock_bot = MockMainBot()
        message = mock_bot._send_enhanced_signal_notification(signal_data, consensus_data)
        
        print("✅ Enhanced Consensus Signal Format Generated:")
        print("=" * 80)
        print(message)
        print("=" * 80)
        
        # Validate required elements (accounting for HTML tags)
        required_elements = [
            "CONSENSUS SIGNAL - PYTH/USDT",
            "SIGNAL TYPE: SELL",
            "PYTH/USDT (DEFI)",
            "Entry:",
            "Take Profit:",
            "Stop Loss:",
            "Risk/Reward:",
            "PHÂN TÍCH ĐỒNG THUẬN:",
            "Điểm đồng thuận:",
            "Độ tin cậy:",
            "Sức mạnh tín hiệu:",
            "Chất lượng tổng thể:",
            "PHÂN TÍCH CHI TIẾT:",
            "Thuật toán đồng ý:",
            "Timeframe chính:",
            "Loại coin:",
            "Độ tin cậy AI:",
            "PHÂN TÍCH TP/SL:",
            "Phương pháp sử dụng:",
            "Độ tin cậy TP/SL:",
            "Điểm chính xác:",
            "NÂNG CAO:",
            "Signal ID:",
            "Thời gian:",
            "Tín hiệu này đạt tiêu chuẩn nghiêm ngặt"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in message:
                missing_elements.append(element)
        
        if not missing_elements:
            print("✅ All required elements present in message")
            
            # Check specific values
            if "0.10740000" in message and "0.09818510" in message and "0.11354327" in message:
                print("✅ Price values correctly formatted")
            else:
                print("❌ Price values missing or incorrect")
            
            if "5/6" in message:  # 5 agreeing out of 6 total algorithms
                print("✅ Algorithm agreement correctly calculated")
            else:
                print("❌ Algorithm agreement calculation incorrect")
            
            if "78.0%" in message:  # Consensus confidence
                print("✅ Consensus confidence correctly formatted")
            else:
                print("❌ Consensus confidence formatting incorrect")
            
            if "CAO" in message:  # TP/SL confidence rating
                print("✅ TP/SL confidence rating correctly calculated")
            else:
                print("❌ TP/SL confidence rating incorrect")
            
            return True
        else:
            print("❌ Missing required elements:")
            for element in missing_elements:
                print(f"  - {element}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run consensus signal format test"""
    print("🧪 === CONSENSUS SIGNAL DETAILED FORMAT TEST ===")
    
    success = test_enhanced_signal_notification()
    
    if success:
        print("\n🎉 SUCCESS: Enhanced consensus signal format working!")
        print("✅ Detailed Vietnamese format implemented")
        print("✅ All required sections present")
        print("✅ Algorithm analysis included")
        print("✅ TP/SL analysis detailed")
        print("✅ Enhancement features listed")
        print("✅ Ready to replace old format")
    else:
        print("\n❌ FAILED: Format test failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
