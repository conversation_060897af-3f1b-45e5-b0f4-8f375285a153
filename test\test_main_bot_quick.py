#!/usr/bin/env python3
"""
🧪 QUICK TEST: Main Bot
Test nhanh để kiểm tra main bot initialization
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """Run quick test"""
    print("🧪 === QUICK MAIN BOT TEST ===")
    
    try:
        print("🤖 Testing main_bot import...")
        
        # Test the specific line that was causing the error
        print("🏷️ Testing CoinCategorizer fix...")
        import coin_categorizer
        
        categorizer = coin_categorizer.CoinCategorizer(auto_update=False)
        
        # Test the fixed logic
        if hasattr(categorizer, 'use_dynamic_sectors') and categorizer.use_dynamic_sectors:
            total_coins = len(categorizer.dynamic_sectors.get('all_coins', []))
            mode = "Dynamic"
        elif hasattr(categorizer, 'known_coins'):
            total_coins = len(categorizer.known_coins)
            mode = "Static"
        else:
            total_coins = 0
            mode = "Unknown"
        
        print(f"✅ Coin count logic works: {total_coins} coins ({mode} mode)")
        
        # Test the main bot import (without full initialization)
        print("🤖 Testing main_bot import...")
        import main_bot
        print("✅ main_bot import successful")
        
        print("🎉 SUCCESS: Main bot can be imported!")
        print("✅ CoinCategorizer dynamic list issue has been fixed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
