#!/usr/bin/env python3
"""
🔧 55% THRESHOLD VERIFICATION TEST
Test that PUMP/DUMP alerts are sent when above 55% threshold
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_55_threshold_configuration():
    """Test that thresholds are properly configured to 55%."""
    print("🔧 TESTING 55% THRESHOLD CONFIGURATION")
    print("=" * 50)
    
    try:
        import main_bot
        
        print(f"📊 Current Threshold Configuration:")
        print(f"  📉 DUMP Alert Threshold: {main_bot.DUMP_ALERT_THRESHOLD*100:.1f}%")
        print(f"  📈 PUMP Alert Threshold: {main_bot.PUMP_ALERT_THRESHOLD*100:.1f}%")
        print(f"  🎯 PUMP/DUMP Min Confidence: {main_bot.PUMP_DUMP_MIN_CONFIDENCE*100:.1f}%")
        
        # Check if thresholds are at expected levels
        expected_threshold = 0.55  # 55%
        
        if main_bot.DUMP_ALERT_THRESHOLD == expected_threshold:
            print(f"✅ DUMP threshold properly set: {main_bot.DUMP_ALERT_THRESHOLD*100:.1f}% = {expected_threshold*100:.1f}%")
            dump_threshold_ok = True
        else:
            print(f"❌ DUMP threshold incorrect: {main_bot.DUMP_ALERT_THRESHOLD*100:.1f}% ≠ {expected_threshold*100:.1f}%")
            dump_threshold_ok = False
        
        if main_bot.PUMP_ALERT_THRESHOLD == expected_threshold:
            print(f"✅ PUMP threshold properly set: {main_bot.PUMP_ALERT_THRESHOLD*100:.1f}% = {expected_threshold*100:.1f}%")
            pump_threshold_ok = True
        else:
            print(f"❌ PUMP threshold incorrect: {main_bot.PUMP_ALERT_THRESHOLD*100:.1f}% ≠ {expected_threshold*100:.1f}%")
            pump_threshold_ok = False
        
        if main_bot.PUMP_DUMP_MIN_CONFIDENCE == expected_threshold:
            print(f"✅ Min confidence properly set: {main_bot.PUMP_DUMP_MIN_CONFIDENCE*100:.1f}% = {expected_threshold*100:.1f}%")
            min_confidence_ok = True
        else:
            print(f"❌ Min confidence incorrect: {main_bot.PUMP_DUMP_MIN_CONFIDENCE*100:.1f}% ≠ {expected_threshold*100:.1f}%")
            min_confidence_ok = False
        
        return dump_threshold_ok and pump_threshold_ok and min_confidence_ok
        
    except Exception as e:
        print(f"❌ Threshold configuration test failed: {e}")
        return False

def test_55_threshold_logic():
    """Test 55% threshold logic for PUMP/DUMP alerts."""
    print("\n🔍 TESTING 55% THRESHOLD LOGIC")
    print("=" * 50)
    
    try:
        import main_bot
        
        # Test scenarios with 55% threshold
        test_cases = [
            {"probability": 0.48, "should_send": False, "description": "48% - Below 55% threshold"},
            {"probability": 0.50, "should_send": False, "description": "50% - Below 55% threshold"},
            {"probability": 0.54, "should_send": False, "description": "54% - Just below 55% threshold"},
            {"probability": 0.55, "should_send": True, "description": "55% - At threshold"},
            {"probability": 0.60, "should_send": True, "description": "60% - Above threshold"},
            {"probability": 0.65, "should_send": True, "description": "65% - Above threshold"},
            {"probability": 0.70, "should_send": True, "description": "70% - Well above threshold"},
            {"probability": 0.75, "should_send": True, "description": "75% - Well above threshold"}
        ]
        
        all_passed = True
        
        print("🧪 DUMP Alert Logic Tests:")
        for i, case in enumerate(test_cases, 1):
            probability = case["probability"]
            should_send = case["should_send"]
            description = case["description"]
            
            # Test DUMP logic
            would_send = probability >= main_bot.DUMP_ALERT_THRESHOLD
            
            print(f"  Test {i}: {description}")
            print(f"    📊 Probability: {probability:.1%}")
            print(f"    🎯 Threshold: {main_bot.DUMP_ALERT_THRESHOLD:.1%}")
            print(f"    🤔 Expected: {'SEND' if should_send else 'SUPPRESS'}")
            print(f"    🔍 Actual: {'SEND' if would_send else 'SUPPRESS'}")
            
            if would_send == should_send:
                print(f"    ✅ PASS")
            else:
                print(f"    ❌ FAIL")
                all_passed = False
        
        print("\n🧪 PUMP Alert Logic Tests:")
        for i, case in enumerate(test_cases, 1):
            probability = case["probability"]
            should_send = case["should_send"]
            description = case["description"]
            
            # Test PUMP logic
            would_send = probability >= main_bot.PUMP_ALERT_THRESHOLD
            
            print(f"  Test {i}: {description}")
            print(f"    📊 Probability: {probability:.1%}")
            print(f"    🎯 Threshold: {main_bot.PUMP_ALERT_THRESHOLD:.1%}")
            print(f"    🤔 Expected: {'SEND' if should_send else 'SUPPRESS'}")
            print(f"    🔍 Actual: {'SEND' if would_send else 'SUPPRESS'}")
            
            if would_send == should_send:
                print(f"    ✅ PASS")
            else:
                print(f"    ❌ FAIL")
                all_passed = False
        
        if all_passed:
            print("\n✅ ALL 55% THRESHOLD LOGIC TESTS PASSED")
        else:
            print("\n❌ SOME 55% THRESHOLD LOGIC TESTS FAILED")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 55% threshold logic test failed: {e}")
        return False

def test_threshold_comparison():
    """Compare old vs new threshold behavior."""
    print("\n📊 TESTING THRESHOLD COMPARISON")
    print("=" * 50)
    
    try:
        import main_bot
        
        # Test cases that show the difference between 70% and 55%
        comparison_cases = [
            {"probability": 0.48, "old_70": False, "new_55": False, "description": "48% - Still suppressed"},
            {"probability": 0.55, "old_70": False, "new_55": True, "description": "55% - Now allowed (was suppressed)"},
            {"probability": 0.60, "old_70": False, "new_55": True, "description": "60% - Now allowed (was suppressed)"},
            {"probability": 0.65, "old_70": False, "new_55": True, "description": "65% - Now allowed (was suppressed)"},
            {"probability": 0.70, "old_70": True, "new_55": True, "description": "70% - Still allowed"},
            {"probability": 0.75, "old_70": True, "new_55": True, "description": "75% - Still allowed"}
        ]
        
        print("📈 Threshold Impact Analysis:")
        print("  Probability | Old (70%) | New (55%) | Change")
        print("  ------------|-----------|-----------|--------")
        
        newly_allowed_count = 0
        
        for case in comparison_cases:
            probability = case["probability"]
            old_result = case["old_70"]
            new_result = case["new_55"]
            description = case["description"]
            
            # Check actual new threshold
            actual_new = probability >= main_bot.PUMP_ALERT_THRESHOLD
            
            old_status = "SEND" if old_result else "BLOCK"
            new_status = "SEND" if actual_new else "BLOCK"
            
            if not old_result and actual_new:
                change = "✅ NEW"
                newly_allowed_count += 1
            elif old_result and actual_new:
                change = "✅ SAME"
            else:
                change = "🚫 SAME"
            
            print(f"  {probability:>10.1%} | {old_status:>9} | {new_status:>9} | {change}")
        
        print(f"\n📊 Summary:")
        print(f"  🎯 New threshold: {main_bot.PUMP_ALERT_THRESHOLD:.1%}")
        print(f"  📈 Newly allowed signals: {newly_allowed_count}")
        print(f"  📊 Quality range: 55% - 100%")
        print(f"  ⚖️ Balance: More signals while maintaining quality")
        
        return True
        
    except Exception as e:
        print(f"❌ Threshold comparison test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 55% THRESHOLD VERIFICATION TEST")
    print("=" * 60)
    
    # Test threshold configuration
    config_ok = test_55_threshold_configuration()
    
    # Test threshold logic
    logic_ok = test_55_threshold_logic()
    
    # Test threshold comparison
    comparison_ok = test_threshold_comparison()
    
    # Overall results
    print("\n" + "=" * 60)
    print("🎯 55% THRESHOLD VERIFICATION RESULTS")
    print("=" * 60)
    
    print(f"⚙️ Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"🔍 Logic Tests: {'✅ PASS' if logic_ok else '❌ FAIL'}")
    print(f"📊 Comparison: {'✅ PASS' if comparison_ok else '❌ FAIL'}")
    
    overall_success = config_ok and logic_ok and comparison_ok
    
    if overall_success:
        print("\n🎉 55% THRESHOLD SUCCESSFULLY CONFIGURED!")
        print("✅ PUMP/DUMP alerts will be sent when probability >= 55%")
        print("✅ More signals while maintaining quality")
        print("✅ Better balance between quality and quantity")
        
        print("\n📊 Expected behavior with 55% threshold:")
        print("  🚫 48% PUMP/DUMP alert → SUPPRESSED")
        print("  🚫 50% PUMP/DUMP alert → SUPPRESSED")
        print("  🚫 54% PUMP/DUMP alert → SUPPRESSED")
        print("  ✅ 55% PUMP/DUMP alert → SENT")
        print("  ✅ 60% PUMP/DUMP alert → SENT")
        print("  ✅ 65% PUMP/DUMP alert → SENT")
        print("  ✅ 70% PUMP/DUMP alert → SENT")
        print("  ✅ 75% PUMP/DUMP alert → SENT")
        
        print("\n🎯 Benefits of 55% threshold:")
        print("  📈 More trading opportunities")
        print("  ⚖️ Balanced quality vs quantity")
        print("  🎯 Still filters out low-quality signals")
        print("  📊 Achievable threshold for crypto markets")
        
    else:
        print("\n⚠️ 55% THRESHOLD CONFIGURATION FAILED")
        if not config_ok:
            print("🔧 Fix threshold configuration")
        if not logic_ok:
            print("🔧 Fix threshold logic")
        if not comparison_ok:
            print("🔧 Fix threshold comparison")
    
    print(f"\n🎯 Final Result: {'SUCCESS' if overall_success else 'NEEDS_ATTENTION'}")
    sys.exit(0 if overall_success else 1)
