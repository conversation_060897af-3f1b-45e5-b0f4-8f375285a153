#!/usr/bin/env python3
"""
🧪 Test script để kiểm tra Consensus Signal có hiển thị đầy đủ thông tin chi tiết
"""

import json
import time
from datetime import datetime

def test_consensus_signal_data():
    """Test consensus signal data structure"""
    
    # ✅ Simulate consensus_data with contributing algorithms
    consensus_data = {
        "signal": "BUY",
        "consensus_score": 0.393,
        "confidence": 0.75,
        "signal_quality": {
            "strength": 0.8,
            "overall_quality": 0.7,
            "tp_sl_methods_count": 5,
            "algorithm_diversity": 4,
            "confidence_score": 0.75
        },
        "contributing_algorithms": [
            {
                "name": "AI_Analysis",
                "signal": "BUY",
                "confidence": 0.85
            },
            {
                "name": "<PERSON><PERSON><PERSON><PERSON>",
                "signal": "BUY", 
                "confidence": 0.70
            },
            {
                "name": "Volume_Profile",
                "signal": "BUY",
                "confidence": 0.65
            },
            {
                "name": "Orderbook",
                "signal": "BUY",
                "confidence": 0.60
            }
        ]
    }
    
    # ✅ Simulate signal_data with enhancement features
    signal_data = {
        "signal_id": "SIG_GPS/USDT_1749968384",
        "coin": "GPS/USDT",
        "coin_category": "ALTCOIN",
        "signal_type": "BUY",
        "entry": 0.02190000,
        "take_profit": 0.02840104,
        "stop_loss": 0.02041041,
        "risk_reward_ratio": 4.36,
        "primary_tf": "4h",
        "ai_confidence": 0.85,
        "consensus_score": 0.393,
        "consensus_confidence": 0.75,
        "volume_spike_detected": True,
        "pump_enhanced": True,
        "pump_probability": 0.65,
        "ai_enhanced": True,
        "whale_activity": False,
        "high_confidence": True,
        "multi_timeframe_confirmed": True,
        "tp_sl_methods": ["AI_Ensemble", "Fibonacci", "Volume_Profile", "Support_Resistance", "ATR"],
        "tp_sl_confidence": 0.8,
        "enhancement_features": [
            "Volume Spike",
            "Pump (65.0%)",
            "AI Enhanced", 
            "High Confidence",
            "Multi-Analyzer (4 methods)",
            "Strong Consensus"
        ],
        "timestamp": time.time()
    }
    
    print("🧪 TESTING CONSENSUS SIGNAL DATA STRUCTURE")
    print("=" * 60)
    
    # Test consensus_data
    print(f"📊 CONSENSUS DATA:")
    print(f"  Signal: {consensus_data.get('signal')}")
    print(f"  Score: {consensus_data.get('consensus_score')}")
    print(f"  Contributing Algorithms: {len(consensus_data.get('contributing_algorithms', []))}")
    
    for algo in consensus_data.get('contributing_algorithms', []):
        print(f"    - {algo['name']}: {algo['signal']} ({algo['confidence']:.1%})")
    
    print(f"\n🎯 SIGNAL DATA:")
    print(f"  Signal Type: {signal_data.get('signal_type')}")
    print(f"  Entry: {signal_data.get('entry')}")
    print(f"  TP: {signal_data.get('take_profit')}")
    print(f"  SL: {signal_data.get('stop_loss')}")
    print(f"  R/R: {signal_data.get('risk_reward_ratio')}")
    
    print(f"\n💡 ENHANCEMENT FEATURES:")
    for feature in signal_data.get('enhancement_features', []):
        print(f"  ✅ {feature}")
    
    print(f"\n🔍 ENHANCEMENT FLAGS:")
    enhancement_flags = [
        'volume_spike_detected', 'pump_enhanced', 'ai_enhanced', 
        'whale_activity', 'high_confidence', 'multi_timeframe_confirmed'
    ]
    
    for flag in enhancement_flags:
        value = signal_data.get(flag, False)
        status = "✅" if value else "❌"
        print(f"  {status} {flag}: {value}")
    
    # Test message generation
    print(f"\n📝 TESTING MESSAGE GENERATION:")
    print("=" * 60)
    
    # Simulate analysis methods breakdown
    analysis_methods = consensus_data.get("contributing_algorithms", [])
    methods_breakdown = []
    
    if analysis_methods:
        for method in analysis_methods:
            method_name = method.get("name", "Unknown")
            method_signal = method.get("signal", "NONE")
            method_confidence = method.get("confidence", 0)
            methods_breakdown.append(f"├ {method_name}: {method_signal} ({method_confidence:.1%})")
    
    analysis_methods_breakdown = "\n".join(methods_breakdown)
    
    # Simulate enhancement features
    enhancements = []
    
    if signal_data.get("volume_spike_detected"):
        enhancements.append("├ ⚡ Volume Spike Detected")
    
    if signal_data.get("pump_enhanced"):
        pump_prob = signal_data.get("pump_probability", 0)
        enhancements.append(f"├ 🚀 Pump Enhanced ({pump_prob:.1%})")
    
    if signal_data.get("ai_enhanced"):
        enhancements.append("├ 🤖 AI Enhanced")
    
    if signal_data.get("whale_activity"):
        enhancements.append("├ 🐋 Whale Activity")
    
    if signal_data.get("high_confidence"):
        enhancements.append("├ 🎯 High Confidence Signal")
    
    if signal_data.get("multi_timeframe_confirmed"):
        enhancements.append("├ ⏰ Multi-Timeframe Confirmed")
    
    if consensus_data.get("consensus_score", 0) >= 0.8:
        enhancements.append("├ 🏆 Strong Consensus")
    
    if len(analysis_methods) >= 3:
        enhancements.append(f"├ 📊 Multi-Analyzer Consensus ({len(analysis_methods)} methods)")
    
    enhancement_features = "\n".join(enhancements) if enhancements else "├ Không có enhancement đặc biệt"
    
    # Generate test message
    coin = signal_data.get("coin", "GPS/USDT")
    signal_type = signal_data.get("signal_type", "NONE")
    signal_emoji = "🟢" if signal_type == "BUY" else "🔴" if signal_type == "SELL" else "🟡"
    
    test_message = f"""
🎯 <b>CONSENSUS SIGNAL - {coin}</b> 🎯

{signal_emoji} <b>Signal Type: {signal_type}</b>
💰 Entry: <code>{signal_data.get("entry", 0):.8f}</code>
🎯 Take Profit: <code>{signal_data.get("take_profit", 0):.8f}</code>
🛡️ Stop Loss: <code>{signal_data.get("stop_loss", 0):.8f}</code>
⚖️ Risk/Reward: <code>{signal_data.get("risk_reward_ratio", 0):.2f}</code>

📊 <b>Consensus Score:</b> <code>{consensus_data.get("consensus_score", 0):.3f}</code>
🎯 <b>Signal ID:</b> <code>{signal_data.get("signal_id", f"SIG_{int(time.time())}")}</code>

🎯 <b>PHÂN TÍCH ĐỒNG THUẬN:</b>
├ <b>Điểm đồng thuận:</b> <code>{consensus_data.get("consensus_score", 0):.3f}/1.000</code>
├ <b>Độ tin cậy:</b> <code>{consensus_data.get("confidence", 0):.3f}/1.000</code>
├ <b>Sức mạnh tín hiệu:</b> <code>{consensus_data.get("signal_quality", {}).get("strength", 0):.3f}/1.000</code>
└ <b>Chất lượng tổng thể:</b> <code>{consensus_data.get("signal_quality", {}).get("overall_quality", 0):.3f}/1.000</code>

📊 <b>PHÂN TÍCH CHI TIẾT:</b>
{analysis_methods_breakdown}

🎯 <b>PHÂN TÍCH TP/SL:</b>
├ <b>Phương pháp sử dụng:</b> {len(signal_data.get("tp_sl_methods", []))} algorithms
├ <b>Độ tin cậy TP/SL:</b> <code>{signal_data.get("tp_sl_confidence", 0):.3f}/1.000</code>
└ <b>Điểm chính xác:</b> <code>CAO</code>

💡 <b>NÂNG CAO:</b>
{enhancement_features}

⏰ <b>Thời gian:</b> <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>

<b>⚡ Tín hiệu này đạt tiêu chuẩn nghiêm ngặt và thể hiện cơ hội thị trường có độ tin cậy cao.</b>
    """
    
    print("📝 GENERATED MESSAGE:")
    print(test_message)
    
    print(f"\n✅ TEST COMPLETED")
    print(f"📊 Analysis methods found: {len(analysis_methods)}")
    print(f"💡 Enhancement features found: {len(enhancements)}")
    print(f"🎯 Message length: {len(test_message)} characters")
    
    return {
        "consensus_data": consensus_data,
        "signal_data": signal_data,
        "message": test_message,
        "analysis_methods_count": len(analysis_methods),
        "enhancement_features_count": len(enhancements)
    }

if __name__ == "__main__":
    test_result = test_consensus_signal_data()
    
    print(f"\n🎉 TEST SUMMARY:")
    print(f"✅ Consensus data structure: OK")
    print(f"✅ Signal data structure: OK") 
    print(f"✅ Analysis methods: {test_result['analysis_methods_count']} found")
    print(f"✅ Enhancement features: {test_result['enhancement_features_count']} found")
    print(f"✅ Message generation: OK")
    
    # Save test data
    with open("test_consensus_signal_data.json", "w") as f:
        json.dump({
            "consensus_data": test_result["consensus_data"],
            "signal_data": test_result["signal_data"]
        }, f, indent=2, default=str)
    
    print(f"💾 Test data saved to test_consensus_signal_data.json")
