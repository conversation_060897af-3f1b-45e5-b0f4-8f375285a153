#!/usr/bin/env python3
"""
🎯 Final Test - Test chart with detailed report after fixes
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def final_test():
    """🎯 Final test."""
    print(f"🎯 FINAL TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=50, freq='H')  # Smaller dataset
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 50)  # Less volatile
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 5000, 50)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Create simple Fibonacci data
        fibonacci_data = {
            'trend_direction': 'UPTREND',
            'pivot_high': current_price * 1.05,
            'pivot_low': current_price * 0.95,
            'retracement_levels': [
                {'ratio': 0.236, 'price': current_price * 0.98, 'strength': 0.8},
                {'ratio': 0.382, 'price': current_price * 0.96, 'strength': 0.9}
            ],
            'extension_levels': [
                {'ratio': 1.618, 'price': current_price * 1.08, 'strength': 0.85}
            ]
        }
        
        print(f"🌀 Generating Fibonacci chart...")
        chart_path = chart_gen.generate_fibonacci_chart("BTC/USDT", fibonacci_data, ohlcv_data, current_price)
        
        if not chart_path:
            print(f"❌ Chart generation failed")
            return False
        
        print(f"✅ Chart generated: {chart_path}")
        
        # Check file
        abs_path = os.path.abspath(chart_path)
        if not os.path.exists(abs_path):
            print(f"❌ Chart file does not exist: {abs_path}")
            return False
        
        file_size = os.path.getsize(abs_path) / (1024 * 1024)
        print(f"📊 File size: {file_size:.2f} MB")
        print(f"📁 Absolute path: {abs_path}")
        
        # Create simple caption first
        simple_caption = f"""🌀 <b>FIBONACCI ANALYSIS - BTC/USDT</b>

💰 Price: <code>{current_price:.2f}</code>
📈 Trend: <b>UPTREND</b>
🎯 Levels: 2 retracement, 1 extension

⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}"""
        
        print(f"📝 Simple caption ({len(simple_caption)} chars)")
        print(f"📤 Sending chart with simple caption...")
        
        success = notifier.send_photo(
            photo_path=abs_path,
            caption=simple_caption,
            chat_id=chat_id,
            parse_mode="HTML"
        )
        
        if success:
            print(f"✅ SUCCESS! Chart with simple caption sent")
            print(f"📱 Check your Telegram for the Fibonacci chart")
            return True
        else:
            print(f"❌ FAILED! Chart sending failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = final_test()
    if success:
        print(f"\n🎉 FINAL TEST PASSED!")
        print(f"📱 You should see a Fibonacci chart in Telegram")
    else:
        print(f"\n💥 FINAL TEST FAILED!")
