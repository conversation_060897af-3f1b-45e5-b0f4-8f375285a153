#!/usr/bin/env python3
"""
🧪 TEST CHART GENERATION ISSUES
===============================

Test để kiểm tra các vấn đề về chart generation cho Fibonacci và PUMP/DUMP alerts.
"""

import os
import sys
import pandas as pd
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_ohlcv_data():
    """Create sample OHLCV data for testing."""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    
    # Generate realistic price data
    base_price = 50000
    price_changes = np.random.normal(0, 0.02, 100)  # 2% volatility
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.uniform(1000000, 5000000)
        
        data.append({
            'timestamp': dates[i],
            'open': prices[i-1] if i > 0 else price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df

def test_fibonacci_chart_generation():
    """Test Fibonacci chart generation."""
    print("🧪 TESTING FIBONACCI CHART GENERATION")
    print("=" * 50)
    
    try:
        import chart_generator
        
        # Create chart generator
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=False,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=100
        )
        
        # Create sample data
        ohlcv_data = create_sample_ohlcv_data()
        current_price = ohlcv_data['close'].iloc[-1]
        
        # Create sample Fibonacci data
        fibonacci_data = {
            "status": "success",
            "retracement_levels": [
                {"level": 0.236, "price": current_price * 0.95},
                {"level": 0.382, "price": current_price * 0.92},
                {"level": 0.618, "price": current_price * 0.88}
            ],
            "extension_levels": [
                {"level": 1.272, "price": current_price * 1.05},
                {"level": 1.618, "price": current_price * 1.08}
            ],
            "confluence_zones": [],
            "pivot_high": current_price * 1.1,
            "pivot_low": current_price * 0.9,
            "trend_direction": "UPTREND",
            "confidence": 0.75
        }
        
        print(f"📊 Testing Fibonacci chart generation...")
        print(f"  - Current Price: {current_price:.2f}")
        print(f"  - OHLCV Data: {len(ohlcv_data)} bars")
        print(f"  - Fibonacci Levels: {len(fibonacci_data['retracement_levels'])} retracement, {len(fibonacci_data['extension_levels'])} extension")
        
        # Test chart generation
        chart_path = chart_gen.generate_fibonacci_chart(
            coin="TESTCOIN/USDT",
            fibonacci_data=fibonacci_data,
            ohlcv_data=ohlcv_data,
            current_price=current_price
        )
        
        if chart_path and os.path.exists(chart_path):
            print(f"  ✅ Fibonacci chart generated successfully: {chart_path}")
            print(f"  📊 Chart file size: {os.path.getsize(chart_path)} bytes")
            return True
        else:
            print(f"  ❌ Fibonacci chart generation failed")
            print(f"  🔍 Chart path returned: {chart_path}")
            return False
            
    except Exception as e:
        print(f"❌ Fibonacci chart test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pump_dump_chart_generation():
    """Test PUMP/DUMP chart generation."""
    print("\n🧪 TESTING PUMP/DUMP CHART GENERATION")
    print("=" * 50)
    
    try:
        import chart_generator
        
        # Create chart generator
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=False,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=100
        )
        
        # Create sample data
        ohlcv_data = create_sample_ohlcv_data()
        current_price = ohlcv_data['close'].iloc[-1]
        
        # Test PUMP chart
        pump_data = {
            "current_price": current_price,
            "pump_probability": 0.75,
            "intensity": 0.8,
            "volume_spike_factor": 3.5,
            "targets": [current_price * 1.03, current_price * 1.05, current_price * 1.08],
            "estimated_time": "5-15 min",
            "indicators": ["Volume spike", "Price momentum"]
        }
        
        print(f"🚀 Testing PUMP chart generation...")
        print(f"  - Current Price: {current_price:.2f}")
        print(f"  - Pump Probability: {pump_data['pump_probability']:.1%}")
        print(f"  - Targets: {len(pump_data['targets'])} levels")
        
        pump_chart_path = chart_gen.generate_pump_alert_chart(
            coin="TESTCOIN/USDT",
            pump_data=pump_data,
            ohlcv_data=ohlcv_data,
            current_price=current_price
        )
        
        pump_success = pump_chart_path and os.path.exists(pump_chart_path)
        if pump_success:
            print(f"  ✅ PUMP chart generated successfully: {pump_chart_path}")
            print(f"  📊 Chart file size: {os.path.getsize(pump_chart_path)} bytes")
        else:
            print(f"  ❌ PUMP chart generation failed")
            print(f"  🔍 Chart path returned: {pump_chart_path}")
        
        # Test DUMP chart
        dump_data = {
            "current_price": current_price,
            "dump_probability": 0.70,
            "severity_level": "HIGH",
            "support_levels": [current_price * 0.97, current_price * 0.94, current_price * 0.91],
            "estimated_time": "5-15 min",
            "indicators": ["Selling pressure", "Support break"]
        }
        
        print(f"\n📉 Testing DUMP chart generation...")
        print(f"  - Current Price: {current_price:.2f}")
        print(f"  - Dump Probability: {dump_data['dump_probability']:.1%}")
        print(f"  - Support Levels: {len(dump_data['support_levels'])} levels")
        
        dump_chart_path = chart_gen.generate_dump_alert_chart(
            coin="TESTCOIN/USDT",
            dump_data=dump_data,
            ohlcv_data=ohlcv_data,
            current_price=current_price
        )
        
        dump_success = dump_chart_path and os.path.exists(dump_chart_path)
        if dump_success:
            print(f"  ✅ DUMP chart generated successfully: {dump_chart_path}")
            print(f"  📊 Chart file size: {os.path.getsize(dump_chart_path)} bytes")
        else:
            print(f"  ❌ DUMP chart generation failed")
            print(f"  🔍 Chart path returned: {dump_chart_path}")
        
        return pump_success and dump_success
        
    except Exception as e:
        print(f"❌ PUMP/DUMP chart test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_early_warning_format():
    """Test early warning message format."""
    print("\n🧪 TESTING EARLY WARNING MESSAGE FORMAT")
    print("=" * 50)
    
    current_price = 50000.0
    
    # Test PUMP warning
    pump_warning = {
        "type": "EARLY_PUMP_WARNING",
        "coin": "TESTCOIN/USDT",
        "current_price": current_price,
        "predicted_price": current_price * 1.03,
        "targets": [current_price * 1.03, current_price * 1.05, current_price * 1.08],
        "probability": 0.75,
        "confidence": 0.68,
        "risk_level": "MEDIUM",
        "indicators": ["Volume pre-spike pattern detected", "Price momentum building"]
    }
    
    print(f"🚀 PUMP Warning Format Test:")
    print(f"  - Type: {pump_warning['type']}")
    print(f"  - Current Price: {pump_warning['current_price']:.8f}")
    print(f"  - Predicted Price: {pump_warning['predicted_price']:.8f}")
    print(f"  - Price Change: {((pump_warning['predicted_price'] - pump_warning['current_price']) / pump_warning['current_price'] * 100):+.1f}%")
    
    # Test DUMP warning
    dump_warning = {
        "type": "EARLY_DUMP_WARNING",
        "coin": "TESTCOIN/USDT",
        "current_price": current_price,
        "predicted_price": current_price * 0.97,
        "support_levels": [current_price * 0.97, current_price * 0.94, current_price * 0.91],
        "probability": 0.70,
        "confidence": 0.65,
        "risk_level": "HIGH",
        "indicators": ["Volume pre-spike pattern detected", "Price momentum building"]
    }
    
    print(f"\n📉 DUMP Warning Format Test:")
    print(f"  - Type: {dump_warning['type']}")
    print(f"  - Current Price: {dump_warning['current_price']:.8f}")
    print(f"  - Predicted Price: {dump_warning['predicted_price']:.8f}")
    print(f"  - Price Change: {((dump_warning['predicted_price'] - dump_warning['current_price']) / dump_warning['current_price'] * 100):+.1f}%")
    
    # Check if both have required fields
    required_fields = ["current_price", "predicted_price", "probability", "confidence"]
    
    pump_has_all = all(field in pump_warning for field in required_fields)
    dump_has_all = all(field in dump_warning for field in required_fields)
    
    print(f"\n📊 Format Validation:")
    print(f"  🚀 PUMP has all required fields: {'✅' if pump_has_all else '❌'}")
    print(f"  📉 DUMP has all required fields: {'✅' if dump_has_all else '❌'}")
    
    return pump_has_all and dump_has_all

def main():
    """Run all chart generation tests."""
    print("🧪 TESTING CHART GENERATION ISSUES")
    print("=" * 70)
    
    # Run tests
    test1 = test_fibonacci_chart_generation()
    test2 = test_pump_dump_chart_generation()
    test3 = test_early_warning_format()
    
    # Summary
    print(f"\n🎯 CHART GENERATION TEST SUMMARY:")
    print("=" * 50)
    print(f"📊 Fibonacci Chart Generation: {'✅ WORKING' if test1 else '❌ BROKEN'}")
    print(f"🚀📉 PUMP/DUMP Chart Generation: {'✅ WORKING' if test2 else '❌ BROKEN'}")
    print(f"⚡ Early Warning Format: {'✅ CORRECT' if test3 else '❌ MISSING FIELDS'}")
    
    total_passed = sum([test1, test2, test3])
    print(f"\n🏆 TOTAL TESTS PASSED: {total_passed}/3")
    
    if total_passed == 3:
        print(f"\n🎉 ALL CHART GENERATION WORKING!")
        print(f"\n📋 POSSIBLE ISSUES IN PRODUCTION:")
        print(f"1. 🔍 Chart generation methods exist and work")
        print(f"2. 🔍 Early warning format is correct")
        print(f"3. ❓ Issue might be:")
        print(f"   - OHLCV data not passed correctly")
        print(f"   - Chart generation fails due to data quality")
        print(f"   - Telegram sending fails")
        print(f"   - Chart files get deleted too quickly")
        print(f"   - Signal quality filters blocking charts")
    else:
        print(f"\n⚠️ {3-total_passed} chart generation issues found.")
        print(f"Please check the failed tests above.")
        
        if not test1:
            print(f"\n🔧 FIBONACCI CHART FIX NEEDED:")
            print(f"  - Check generate_fibonacci_chart method")
            print(f"  - Verify fibonacci_data structure")
            print(f"  - Check OHLCV data format")
            
        if not test2:
            print(f"\n🔧 PUMP/DUMP CHART FIX NEEDED:")
            print(f"  - Check generate_pump_alert_chart method")
            print(f"  - Check generate_dump_alert_chart method")
            print(f"  - Verify pump/dump data structure")
            
        if not test3:
            print(f"\n🔧 EARLY WARNING FORMAT FIX NEEDED:")
            print(f"  - Add current_price to warning data")
            print(f"  - Add predicted_price to warning data")
            print(f"  - Ensure all required fields present")

if __name__ == "__main__":
    main()
