#!/usr/bin/env python3
"""Quick test for reliability"""

import main_bot

# Get health score
health_score = main_bot.MODULE_LOADING_STATS["health_score"]
print(f"Health Score: {health_score*100:.1f}%")

# Determine status
if health_score >= 0.85:
    status = "EXCELLENT"
elif health_score >= 0.75:
    status = "GOOD"
elif health_score >= 0.6:
    status = "ACCEPTABLE"
elif health_score >= 0.4:
    status = "REDUCED"
else:
    status = "POOR"

print(f"Status: {status}")

# Check if target achieved
if 0.70 <= health_score <= 0.80:
    print("✅ TARGET ACHIEVED: 75% reliability target!")
elif health_score > 0.80:
    print("⚠️ ABOVE TARGET: More reliable than 75%")
else:
    print("❌ BELOW TARGET: Less reliable than 75%")
