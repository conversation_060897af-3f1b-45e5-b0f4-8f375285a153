#!/usr/bin/env python3
"""
🧪 TEST INDIVIDUAL MODULES
=========================

Test từng module riêng lẻ để tìm vấn đề.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_generator_only():
    """Test chart generator only."""
    print("🧪 TESTING CHART GENERATOR ONLY")
    print("=" * 50)
    
    try:
        print("📦 Importing chart_generator...")
        import chart_generator
        print("✅ chart_generator imported successfully")
        
        print("🎨 Creating chart generator...")
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,  # No telegram notifier
            enable_auto_delete=False,  # Disable auto delete
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=100
        )
        print("✅ Chart generator created successfully")
        
        # Test methods
        methods = [
            'generate_fibonacci_chart',
            'generate_volume_profile_chart', 
            'generate_point_figure_chart',
            'generate_fourier_chart',
            'generate_ai_analysis_chart'
        ]
        
        print("🔧 Testing methods:")
        for method in methods:
            has_method = hasattr(chart_gen, method)
            print(f"  📊 {method}: {'✅' if has_method else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chart generator test failed: {e}")
        traceback.print_exc()
        return False

def test_telegram_notifier_minimal():
    """Test telegram notifier with minimal setup."""
    print("\n🧪 TESTING TELEGRAM NOTIFIER MINIMAL")
    print("=" * 50)
    
    try:
        print("📦 Importing telegram_notifier...")
        
        # Set minimal environment to avoid background services
        os.environ['TELEGRAM_BOT_TOKEN'] = 'test_token'
        os.environ['TELEGRAM_CHAT_ID'] = 'test_chat'
        
        import telegram_notifier
        print("✅ telegram_notifier imported successfully")
        
        # Don't actually initialize - just check class exists
        notifier_class = telegram_notifier.EnhancedTelegramNotifier
        print(f"✅ EnhancedTelegramNotifier class found: {notifier_class}")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram notifier test failed: {e}")
        traceback.print_exc()
        return False

def test_chart_generation_simple():
    """Test simple chart generation."""
    print("\n🧪 TESTING SIMPLE CHART GENERATION")
    print("=" * 50)
    
    try:
        # Import chart generator
        import chart_generator
        
        # Create chart generator without telegram notifier
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=False,
            enable_advanced_styling=False,  # Disable advanced features
            enable_interactive_charts=False,
            max_storage_mb=50
        )
        
        # Create minimal sample data
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        dates = pd.date_range(start=datetime.now() - timedelta(hours=50), periods=50, freq='1H')
        sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': [50000 + i * 10 for i in range(50)],
            'high': [50100 + i * 10 for i in range(50)],
            'low': [49900 + i * 10 for i in range(50)],
            'close': [50050 + i * 10 for i in range(50)],
            'volume': [1000 + i * 5 for i in range(50)]
        })
        sample_data.set_index('timestamp', inplace=True)
        
        # Test simple fibonacci chart
        fibonacci_data = {
            "status": "success",
            "retracement_levels": [
                {"ratio": 0.236, "price": 50000 * 0.976},
                {"ratio": 0.382, "price": 50000 * 0.962}
            ],
            "extension_levels": [
                {"ratio": 1.618, "price": 50000 * 1.062}
            ],
            "signals": {"overall_signal": "BUY", "confidence": 0.8}
        }
        
        print("🎨 Testing fibonacci chart generation...")
        chart_path = chart_gen.generate_fibonacci_chart("BTCUSDT", fibonacci_data, sample_data, 50000.0)
        
        if chart_path and os.path.exists(chart_path):
            print(f"✅ Chart generated successfully: {chart_path}")
            print(f"📁 File size: {os.path.getsize(chart_path)} bytes")
            return True
        else:
            print("❌ Chart generation failed")
            return False
        
    except Exception as e:
        print(f"❌ Simple chart generation test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 TESTING INDIVIDUAL MODULES")
    print("=" * 70)
    
    # Test 1: Chart generator only
    success1 = test_chart_generator_only()
    
    # Test 2: Telegram notifier minimal
    success2 = test_telegram_notifier_minimal()
    
    # Test 3: Simple chart generation
    success3 = test_chart_generation_simple()
    
    print("\n🎯 TEST RESULTS:")
    print(f"  📊 Chart Generator: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  📱 Telegram Notifier: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"  🎨 Chart Generation: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 and success3:
        print("\n✅ CHART SYSTEM IS WORKING!")
        print("The issue is likely with main_bot.py initialization or telegram notifier background services.")
    else:
        print("\n❌ CHART SYSTEM HAS ISSUES!")
        print("Need to fix chart generator or dependencies.")
