#!/usr/bin/env python3
"""
🚨 WARNING SYSTEM TEST
======================

Test script để kiểm tra warning system integration
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_warning_messages():
    """Test warning messages"""
    print("🚨 === TESTING WARNING MESSAGES ===")
    print("=" * 50)
    
    try:
        from bot_warning_message import get_warning_message, add_warning_footer, WARNING_CONFIG
        
        print("✅ Warning module imported successfully")
        
        # Test different warning types
        warning_types = [
            "general",
            "consensus", 
            "money_flow",
            "whale",
            "manipulation",
            "startup",
            "daily"
        ]
        
        print(f"\n🔧 Testing {len(warning_types)} warning types...")
        
        for warning_type in warning_types:
            warning = get_warning_message(warning_type)
            if warning and len(warning.strip()) > 0:
                print(f"  ✅ {warning_type}: {len(warning)} characters")
            else:
                print(f"  ❌ {warning_type}: Empty or missing")
        
        # Test footer
        test_message = "🎯 Test trading signal for BTC/USDT"
        message_with_footer = add_warning_footer(test_message)
        
        if len(message_with_footer) > len(test_message):
            print(f"  ✅ Footer: Added {len(message_with_footer) - len(test_message)} characters")
        else:
            print(f"  ❌ Footer: Not added properly")
        
        # Test configuration
        print(f"\n🔧 Warning configuration:")
        for key, value in WARNING_CONFIG.items():
            print(f"  • {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing warning messages: {e}")
        return False

def test_warning_integration():
    """Test warning integration in bot"""
    print("\n🤖 === TESTING WARNING INTEGRATION ===")
    print("=" * 50)
    
    try:
        # Test main bot integration
        print("🔧 Testing main bot warning integration...")
        
        # Check if main_bot has warning imports
        import main_bot
        
        # Check if TradingBot has warning methods
        bot_class = main_bot.TradingBot
        
        warning_methods = [
            'add_warning_to_signal',
            'send_signal_with_warning'
        ]
        
        print("🔧 Checking warning methods in TradingBot...")
        for method in warning_methods:
            if hasattr(bot_class, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing warning integration: {e}")
        return False

def test_startup_warning():
    """Test startup warning functionality"""
    print("\n🚀 === TESTING STARTUP WARNING ===")
    print("=" * 50)
    
    try:
        from bot_warning_message import get_warning_message, WARNING_CONFIG
        
        # Test startup warning
        if WARNING_CONFIG.get("startup_warning", True):
            startup_warning = get_warning_message("startup")
            print("✅ Startup warning enabled")
            print(f"📝 Warning length: {len(startup_warning)} characters")
            print(f"📋 Warning preview: {startup_warning[:100]}...")
        else:
            print("❌ Startup warning disabled")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing startup warning: {e}")
        return False

def test_signal_warnings():
    """Test signal warning functionality"""
    print("\n📊 === TESTING SIGNAL WARNINGS ===")
    print("=" * 50)
    
    try:
        from bot_warning_message import get_warning_message, add_warning_footer, WARNING_CONFIG
        
        # Test signal types
        signal_types = [
            ("general", "General trading signal"),
            ("consensus", "Consensus signal from multiple algorithms"),
            ("money_flow", "Money flow analysis signal"),
            ("whale", "Whale activity detection"),
            ("manipulation", "Market manipulation alert")
        ]
        
        print("🔧 Testing signal warnings...")
        
        for signal_type, description in signal_types:
            print(f"\n📊 {signal_type.upper()} SIGNAL:")
            print(f"   Description: {description}")
            
            # Create test signal
            test_signal = f"🎯 {description} for BTC/USDT\n💰 Entry: $45,000\n🎯 TP: $47,000\n🛑 SL: $43,000"
            
            # Add warning if enabled
            if WARNING_CONFIG.get("show_warning_on_signals", True):
                warning = get_warning_message(signal_type)
                test_signal_with_warning = f"{test_signal}\n\n{warning}"
                print(f"   ✅ Warning added: +{len(warning)} characters")
            else:
                test_signal_with_warning = test_signal
                print(f"   ❌ Warning disabled")
            
            # Add footer if enabled
            if WARNING_CONFIG.get("show_footer_on_all", True):
                final_signal = add_warning_footer(test_signal_with_warning)
                footer_length = len(final_signal) - len(test_signal_with_warning)
                print(f"   ✅ Footer added: +{footer_length} characters")
            else:
                final_signal = test_signal_with_warning
                print(f"   ❌ Footer disabled")
            
            print(f"   📏 Total length: {len(final_signal)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing signal warnings: {e}")
        return False

def test_warning_config():
    """Test warning configuration"""
    print("\n⚙️ === TESTING WARNING CONFIGURATION ===")
    print("=" * 50)
    
    try:
        from bot_warning_message import WARNING_CONFIG
        
        print("🔧 Current warning configuration:")
        
        config_items = [
            ("show_warning_on_signals", "Show warning on all signals"),
            ("show_detailed_warning", "Show detailed warning"),
            ("show_daily_reminder", "Show daily reminder"),
            ("show_footer_on_all", "Show footer on all messages"),
            ("warning_frequency", "Warning frequency"),
            ("startup_warning", "Startup warning")
        ]
        
        for key, description in config_items:
            value = WARNING_CONFIG.get(key, "NOT SET")
            status = "✅ ENABLED" if value else "❌ DISABLED"
            if isinstance(value, str):
                status = f"📝 {value}"
            print(f"  • {description}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing warning config: {e}")
        return False

def show_warning_examples():
    """Show warning examples"""
    print("\n📋 === WARNING EXAMPLES ===")
    print("=" * 50)
    
    try:
        from bot_warning_message import get_warning_message
        
        print("🚨 STARTUP WARNING:")
        print(get_warning_message("startup"))
        
        print("\n" + "="*50)
        print("⚠️ GENERAL SIGNAL WARNING:")
        print(get_warning_message("general"))
        
        print("\n" + "="*50)
        print("🎯 CONSENSUS WARNING:")
        print(get_warning_message("consensus"))
        
        return True
        
    except Exception as e:
        print(f"❌ Error showing warning examples: {e}")
        return False

def main():
    """Main test function"""
    print("🚨 === WARNING SYSTEM TEST ===")
    print("🎯 Testing warning system integration and functionality")
    print()
    
    # Run tests
    test1 = test_warning_messages()
    test2 = test_warning_integration()
    test3 = test_startup_warning()
    test4 = test_signal_warnings()
    test5 = test_warning_config()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Warning Messages", test1),
        ("Warning Integration", test2),
        ("Startup Warning", test3),
        ("Signal Warnings", test4),
        ("Warning Configuration", test5)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed")
    
    # Show examples
    show_warning_examples()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ WARNING SYSTEM STATUS:")
        print("  • Warning messages: FUNCTIONAL")
        print("  • Bot integration: COMPLETE")
        print("  • Startup warning: READY")
        print("  • Signal warnings: ACTIVE")
        print("  • Configuration: VALID")
        print("\n🚨 WARNING FEATURES:")
        print("  • Startup warning when bot starts")
        print("  • Signal warnings on trading signals")
        print("  • Footer warnings on all messages")
        print("  • Configurable warning types")
        print("  • Multiple warning levels")
        print("\n🚀 READY TO USE:")
        print("  1. Start bot: python start_bot_with_admin.py")
        print("  2. Check startup warning in admin chat")
        print("  3. Test signals with warnings")
        print("  4. Verify member welcome with warnings")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the configuration and try again.")

if __name__ == "__main__":
    main()
