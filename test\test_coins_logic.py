#!/usr/bin/env python3
"""
🧪 TEST: Coins Logic
Test logic để kiểm tra cải tiến về số lượng coins
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_manual_classification():
    """Test manual classification expansion"""
    print("🧪 === TESTING MANUAL CLASSIFICATION EXPANSION ===")
    
    try:
        print("🌊 Testing MoneyFlowAnalyzer manual classification...")
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        # Create analyzer instance
        analyzer = MoneyFlowAnalyzer()
        
        # Test the manual classification method directly
        test_coins = [
            'BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'AVAX', 'DOT', 'ATOM',  # Layer1
            'DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BONK',  # Meme
            'UNI', 'AAVE', 'COMP', 'SUSHI', 'CRV',  # DeFi
            'MATIC', 'OP', 'ARB', 'LRC',  # Layer2
            'AXS', 'SAND', 'MANA', 'ENJ', 'GALA',  # Gaming
            'FET', 'AGIX', 'OCEAN', 'RNDR', 'TAO',  # AI
            'LINK', 'BAND', 'TRB', 'API3',  # Oracle
            'FIL', 'AR', 'STORJ',  # Storage
        ]
        
        print(f"🔍 Testing {len(test_coins)} coins classification...")
        
        classified_count = 0
        sector_counts = {}
        
        for coin in test_coins:
            try:
                # Test the classification method
                sector = analyzer._classify_coin_by_keywords(coin.lower(), f"{coin}USDT")
                if sector and sector != 'Infrastructure':  # Infrastructure is default
                    classified_count += 1
                    sector_counts[sector] = sector_counts.get(sector, 0) + 1
                    print(f"  ✅ {coin}: {sector}")
                else:
                    print(f"  ⚠️ {coin}: {sector} (default)")
            except Exception as e:
                print(f"  ❌ {coin}: Error - {e}")
        
        print(f"\n📊 Classification Results:")
        print(f"  ✅ Successfully classified: {classified_count}/{len(test_coins)} ({classified_count/len(test_coins)*100:.1f}%)")
        
        print(f"\n📋 Sector Distribution:")
        for sector, count in sorted(sector_counts.items()):
            print(f"  {sector}: {count} coins")
        
        if classified_count >= len(test_coins) * 0.8:  # 80% success rate
            print(f"\n🎉 SUCCESS: High classification rate achieved!")
            print(f"✅ {classified_count}/{len(test_coins)} coins properly classified")
        else:
            print(f"\n⚠️ MODERATE: Classification rate could be improved")
            print(f"⚠️ {classified_count}/{len(test_coins)} coins properly classified")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_volume_threshold():
    """Test volume threshold logic"""
    print("\n💰 Testing volume threshold logic...")
    
    try:
        # Simulate ticker data
        mock_tickers = [
            {'symbol': 'BTCUSDT', 'quoteVolume': '50000000000'},  # $50B
            {'symbol': 'ETHUSDT', 'quoteVolume': '20000000000'},  # $20B
            {'symbol': 'DOGEUSDT', 'quoteVolume': '1000000000'},  # $1B
            {'symbol': 'PEPEUSDT', 'quoteVolume': '500000000'},   # $500M
            {'symbol': 'SMALLCOINUSDT', 'quoteVolume': '50000'},  # $50K (below threshold)
            {'symbol': 'MEDIUMCOINUSDT', 'quoteVolume': '200000'}, # $200K (above threshold)
        ]
        
        # Test the new logic (volume > $100K)
        active_pairs = []
        for ticker in mock_tickers:
            symbol = ticker['symbol']
            volume = float(ticker['quoteVolume'])
            
            if symbol.endswith('USDT') and volume > 100000:  # $100K threshold
                active_pairs.append(symbol)
        
        print(f"📊 Mock test results:")
        print(f"  Total mock tickers: {len(mock_tickers)}")
        print(f"  Coins above $100K volume: {len(active_pairs)}")
        print(f"  Filtered coins: {[coin.replace('USDT', '') for coin in active_pairs]}")
        
        # Should include all except SMALLCOINUSDT
        expected_count = 5  # All except SMALLCOINUSDT
        if len(active_pairs) == expected_count:
            print(f"✅ Volume filtering working correctly!")
        else:
            print(f"⚠️ Volume filtering may have issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run tests"""
    print("🧪 === COINS LOGIC TEST ===")
    
    success1 = test_manual_classification()
    success2 = test_volume_threshold()
    
    if success1 and success2:
        print("\n🎉 SUCCESS: Coins logic improvements working!")
        print("✅ Manual classification expanded")
        print("✅ Volume threshold lowered to include more coins")
        print("✅ Should now support 300+ coins instead of 50")
    else:
        print("\n❌ FAILED: Some tests failed")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
