#!/usr/bin/env python3
"""
🧪 Simple Test: Dynamic Coin Categorizer
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🧪 === SIMPLE DYNAMIC CATEGORIZER TEST ===")
    
    try:
        # Test import
        print("📦 Testing imports...")
        from coin_categorizer import CoinCategorizer
        print("✅ CoinCategorizer imported")
        
        # Test dynamic initialization
        print("\n🔄 Testing dynamic initialization...")
        categorizer = CoinCategorizer(use_dynamic_sectors=True)
        print("✅ Dynamic categorizer initialized")
        
        # Test basic categorization
        print("\n🏷️ Testing basic categorization...")
        test_coins = ['BTC/USDT', 'ETH/USDT', 'DOGE/USDT']
        
        for coin in test_coins:
            category = categorizer.get_coin_category(coin)
            print(f"  {coin}: {category}")
        
        # Test sector info
        print("\n📊 Testing sector info...")
        sector_info = categorizer.get_dynamic_sector_info()
        
        if sector_info.get('enabled'):
            print(f"✅ Dynamic mode enabled")
            print(f"  📈 Sectors: {sector_info.get('total_sectors', 0)}")
            print(f"  🪙 Coins: {sector_info.get('total_coins', 0)}")
        else:
            print("⚠️ Dynamic mode not enabled")
        
        print("\n🎉 SIMPLE TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)
