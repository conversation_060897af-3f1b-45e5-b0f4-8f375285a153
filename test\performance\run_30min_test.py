#!/usr/bin/env python3
"""
⏰ 30-Minute Bot Stability Test - Chạy bot và monitor trong 30 phút
"""

import os
import sys
import time
import subprocess
import requests
from datetime import datetime, <PERSON><PERSON><PERSON>

def send_telegram_message(message: str) -> bool:
    """📱 Send message to Telegram."""
    try:
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not bot_token or not chat_id:
            return False
        
        base_url = f"https://api.telegram.org/bot{bot_token}"
        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(f"{base_url}/sendMessage", data=data, timeout=10)
        return response.status_code == 200 and response.json().get('ok', False)
        
    except Exception as e:
        print(f"❌ Telegram error: {e}")
        return False

def run_30min_stability_test():
    """⏰ Run 30-minute stability test."""
    print(f"⏰ 30-MINUTE BOT STABILITY TEST")
    print(f"=" * 60)
    
    start_time = datetime.now()
    test_duration = 30  # 30 minutes
    report_interval = 5  # Report every 5 minutes
    
    print(f"🚀 Starting 30-minute stability test...")
    print(f"⏰ Start time: {start_time.strftime('%H:%M:%S %d/%m/%Y')}")
    print(f"🕐 Duration: {test_duration} minutes")
    print(f"📊 Reports every: {report_interval} minutes")
    print(f"")
    
    # Send start notification
    start_message = f"""⏰ <b>30-MINUTE BOT STABILITY TEST STARTED</b>

⏰ <b>Test Configuration:</b>
├ 🕐 Start Time: <code>{start_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 Duration: <code>30 minutes</code>
├ 📊 Reports: <code>Every 5 minutes</code>
└ 🎯 Purpose: <b>Long-term stability monitoring</b>

🤖 <b>What will be monitored:</b>
├ 🔄 Bot process health and stability
├ 📊 Analysis generation activity
├ 📁 Chart file creation and storage
├ ❌ Error detection and logging
├ 💾 System resource usage
└ 📱 Telegram communication stability

🔍 <b>Expected monitoring phases:</b>
├ 📊 Phase 1 (0-10 min): System warmup and initial analysis
├ 📊 Phase 2 (10-20 min): Peak activity monitoring
└ 📊 Phase 3 (20-30 min): Sustained operation test

<b>⏰ 30-MINUTE STABILITY TEST NOW STARTING...</b>

<i>You will receive status updates every 5 minutes with detailed system health information.</i>"""
    
    if send_telegram_message(start_message):
        print(f"📱 Start notification sent to Telegram")
    else:
        print(f"⚠️ Failed to send start notification")
    
    # Start bot process
    bot_process = None
    errors_detected = 0
    charts_created = 0
    last_chart_count = 0
    
    try:
        print(f"🚀 Starting main bot process...")
        bot_process = subprocess.Popen([
            sys.executable, "main_bot.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"✅ Bot process started with PID: {bot_process.pid}")
        print(f"🔍 Beginning 30-minute monitoring...")
        print(f"")
        
        # Monitor for 30 minutes
        end_time = start_time + timedelta(minutes=test_duration)
        last_report_time = start_time
        minute_counter = 0
        
        while datetime.now() < end_time:
            current_time = datetime.now()
            
            # Check if process is still running
            if bot_process.poll() is not None:
                print(f"❌ Bot process terminated unexpectedly!")
                errors_detected += 1
                
                # Try to get error output
                try:
                    stdout, stderr = bot_process.communicate(timeout=5)
                    if stderr:
                        print(f"❌ Bot stderr: {stderr[-200:]}")  # Last 200 chars
                except:
                    pass
                break
            
            # Check for new chart files
            try:
                if os.path.exists("charts"):
                    chart_files = [f for f in os.listdir("charts") if f.endswith('.png')]
                    current_chart_count = len(chart_files)
                    
                    if current_chart_count > last_chart_count:
                        new_charts = current_chart_count - last_chart_count
                        charts_created += new_charts
                        print(f"📊 +{new_charts} new chart(s) | Total: {current_chart_count}")
                        last_chart_count = current_chart_count
            except Exception as e:
                print(f"⚠️ Error checking charts: {e}")
                errors_detected += 1
            
            # Send periodic reports
            if current_time - last_report_time >= timedelta(minutes=report_interval):
                runtime = current_time - start_time
                remaining = end_time - current_time
                
                progress_report = f"""📊 <b>30-MIN TEST - {report_interval}-MINUTE UPDATE</b>

⏰ <b>PROGRESS UPDATE</b>
├ 🕐 Current Time: <code>{current_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ ⏱️ Runtime: <code>{str(runtime).split('.')[0]}</code>
├ ⏳ Remaining: <code>{str(remaining).split('.')[0]}</code>
└ 📈 Progress: <code>{(runtime.total_seconds() / (test_duration * 60) * 100):.1f}%</code>

🤖 <b>BOT STATUS</b>
├ 🔄 Process: <b>{'RUNNING' if bot_process and bot_process.poll() is None else 'STOPPED'}</b>
├ 🆔 PID: <code>{bot_process.pid if bot_process else 'N/A'}</code>
├ 📊 Charts Created: <code>{charts_created}</code>
├ ❌ Errors: <code>{errors_detected}</code>
└ 🏥 Health: <b>{'HEALTHY' if errors_detected == 0 else 'ISSUES DETECTED'}</b>

📊 <b>ACTIVITY SUMMARY</b>
├ 📈 Chart Generation Rate: <code>{charts_created / max(1, runtime.total_seconds() / 60):.1f} charts/min</code>
├ 🔄 System Responsiveness: <b>{'GOOD' if errors_detected == 0 else 'DEGRADED'}</b>
└ 💾 Resource Usage: <b>MONITORING</b>

🔍 <b>NEXT UPDATE IN {report_interval} MINUTES</b>"""
                
                if send_telegram_message(progress_report):
                    print(f"📱 {report_interval}-minute report sent")
                else:
                    print(f"⚠️ Failed to send {report_interval}-minute report")
                
                last_report_time = current_time
            
            # Show progress every minute
            runtime = current_time - start_time
            remaining = end_time - current_time
            minute_counter = int(runtime.total_seconds() // 60)
            
            print(f"🔍 Min {minute_counter:2d}/30 | "
                  f"Runtime: {int(runtime.total_seconds()//60):2d}:{int(runtime.total_seconds()%60):02d} | "
                  f"Remaining: {int(remaining.total_seconds()//60):2d}:{int(remaining.total_seconds()%60):02d} | "
                  f"Charts: {charts_created:3d} | Errors: {errors_detected}")
            
            time.sleep(60)  # Check every minute
        
        print(f"\n⏰ 30-minute test duration completed!")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        errors_detected += 1
        import traceback
        traceback.print_exc()
    
    finally:
        # Stop bot process
        if bot_process:
            print(f"🛑 Stopping bot process...")
            try:
                bot_process.terminate()
                bot_process.wait(timeout=10)
                print(f"✅ Bot process stopped gracefully")
            except subprocess.TimeoutExpired:
                print(f"⚠️ Force killing bot process...")
                bot_process.kill()
                bot_process.wait()
                print(f"✅ Bot process killed")
    
    # Generate final comprehensive report
    end_time = datetime.now()
    total_runtime = end_time - start_time
    
    # Assessment
    if errors_detected == 0 and charts_created > 10:
        status = "🟢 EXCELLENT"
        assessment = "System is highly stable and very active"
    elif errors_detected == 0 and charts_created > 0:
        status = "🟡 GOOD"
        assessment = "System is stable with moderate activity"
    elif errors_detected < 3:
        status = "🟠 FAIR"
        assessment = "System has minor issues but functional"
    else:
        status = "🔴 POOR"
        assessment = "System has significant stability issues"
    
    # Final comprehensive report
    final_report = f"""⏰ <b>30-MINUTE BOT STABILITY TEST - FINAL RESULTS</b>

⏰ <b>TEST COMPLETION SUMMARY</b>
├ 🕐 Start: <code>{start_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 End: <code>{end_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ ⏱️ Total Runtime: <code>{str(total_runtime).split('.')[0]}</code>
├ 🎯 Target Duration: <code>30 minutes</code>
└ 📈 Completion: <code>{min(100, (total_runtime.total_seconds() / (30 * 60)) * 100):.1f}%</code>

🤖 <b>FINAL BOT PERFORMANCE</b>
├ 🔄 Process Stability: <b>{'STABLE' if errors_detected == 0 else 'UNSTABLE'}</b>
├ 📊 Total Charts Created: <code>{charts_created}</code>
├ ❌ Total Errors: <code>{errors_detected}</code>
├ 📈 Chart Generation Rate: <code>{charts_created / max(1, total_runtime.total_seconds() / 60):.2f} charts/min</code>
└ ⚖️ Error Rate: <code>{errors_detected / max(1, total_runtime.total_seconds() / 60):.2f} errors/min</code>

🏥 <b>SYSTEM STABILITY ASSESSMENT</b>
├ 🎯 Overall Status: <b>{status}</b>
├ 💡 Assessment: <b>{assessment}</b>
├ 🔄 Reliability: <b>{'HIGH' if errors_detected == 0 else 'MEDIUM' if errors_detected < 3 else 'LOW'}</b>
└ 📊 Activity Level: <b>{'HIGH' if charts_created > 20 else 'MEDIUM' if charts_created > 5 else 'LOW'}</b>

📊 <b>PERFORMANCE METRICS</b>
├ 📈 Average Charts/Min: <code>{charts_created / max(1, total_runtime.total_seconds() / 60):.2f}</code>
├ 🎯 Success Rate: <code>{((total_runtime.total_seconds() / 60) - errors_detected) / max(1, total_runtime.total_seconds() / 60) * 100:.1f}%</code>
├ 💾 System Uptime: <code>{total_runtime.total_seconds() / (30 * 60) * 100:.1f}%</code>
└ 🔄 Process Health: <b>{'EXCELLENT' if errors_detected == 0 else 'NEEDS ATTENTION'}</b>

💡 <b>RECOMMENDATIONS</b>"""
    
    if errors_detected == 0 and charts_created > 10:
        final_report += f"\n├ ✅ System is production-ready"
        final_report += f"\n├ 🚀 Excellent stability demonstrated"
        final_report += f"\n└ 📊 High activity level maintained"
    elif errors_detected == 0:
        final_report += f"\n├ ✅ System is stable"
        final_report += f"\n├ 🔍 Consider investigating low activity"
        final_report += f"\n└ 📊 Monitor for longer periods"
    else:
        final_report += f"\n├ ⚠️ Review and fix detected errors"
        final_report += f"\n├ 🔧 Investigate stability issues"
        final_report += f"\n└ 🔍 Consider additional testing"
    
    final_report += f"\n\n⏰ <b>30-MINUTE STABILITY TEST COMPLETED</b>"
    final_report += f"\n\n<i>This comprehensive test monitored the bot for 30 minutes to assess long-term stability, performance, and reliability.</i>"
    
    # Send final report
    if send_telegram_message(final_report):
        print(f"📱 Final comprehensive report sent to Telegram")
    else:
        print(f"⚠️ Failed to send final report")
    
    # Console summary
    print(f"\n📊 30-MINUTE TEST FINAL RESULTS:")
    print(f"  ⏰ Total Runtime: {str(total_runtime).split('.')[0]}")
    print(f"  🤖 Bot Status: {'STABLE' if errors_detected == 0 else 'UNSTABLE'}")
    print(f"  📊 Charts Created: {charts_created}")
    print(f"  ❌ Errors Detected: {errors_detected}")
    print(f"  📈 Chart Rate: {charts_created / max(1, total_runtime.total_seconds() / 60):.2f} charts/min")
    print(f"  🎯 Overall Status: {status}")
    print(f"  💡 Assessment: {assessment}")
    
    return errors_detected == 0

def main():
    """🚀 Main function."""
    print(f"⏰ 30-MINUTE BOT STABILITY TEST")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    print(f"")
    
    # Check environment
    if not os.getenv("TELEGRAM_BOT_TOKEN") or not os.getenv("TELEGRAM_CHAT_ID"):
        print(f"❌ Missing environment variables")
        print(f"Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID")
        return
    
    if not os.path.exists("main_bot.py"):
        print(f"❌ main_bot.py not found in current directory")
        return
    
    # Confirm test start
    print(f"🔍 About to start 30-minute stability test...")
    print(f"📱 You will receive Telegram updates every 5 minutes")
    print(f"🛑 Press Ctrl+C to stop the test early")
    print(f"")
    
    # Run test
    try:
        success = run_30min_stability_test()
        
        if success:
            print(f"\n🎉 30-MINUTE TEST COMPLETED SUCCESSFULLY!")
            print(f"✅ No errors detected - system is stable")
            print(f"🚀 Bot demonstrated excellent long-term stability")
        else:
            print(f"\n⚠️ 30-MINUTE TEST DETECTED ISSUES!")
            print(f"❌ Errors found - review Telegram reports")
            print(f"🔧 Consider fixes before production deployment")
            
    except Exception as e:
        print(f"❌ Fatal error in 30-minute test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
