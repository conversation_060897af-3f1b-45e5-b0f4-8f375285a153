#!/usr/bin/env python3
"""
🧪 TEST: Consensus Signal Correct Detailed Format
Test để xác nhận consensus signal đang sử dụng format chi tiết đúng từ dòng 2763-2796
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from telegram_notifier import EnhancedTelegramNotifier

def create_test_consensus_data():
    """Create test consensus data with all required fields"""
    return {
        'consensus_score': 0.639,
        'confidence': 0.75,
        'signal_quality': {
            'strength': 0.82,
            'overall_quality': 0.78
        },
        'contributing_algorithms': [
            {
                'algorithm': 'AI Analysis',
                'signal': 'SELL',
                'confidence': 0.85,
                'details': 'Strong bearish pattern detected'
            },
            {
                'algorithm': 'Fibonacci',
                'signal': 'SELL',
                'confidence': 0.72,
                'details': 'Price at 61.8% retracement resistance'
            },
            {
                'algorithm': 'Volume Profile',
                'signal': 'SELL',
                'confidence': 0.68,
                'details': 'High volume node resistance'
            }
        ]
    }

def create_test_signal_data():
    """Create test signal data with all required fields"""
    return {
        'signal_id': 'SIG_BTC/USDT_1749972075',
        'coin': 'BTC/USDT',
        'coin_category': 'MAJOR',
        'signal_type': 'SELL',
        'entry': 105367.43000000,
        'take_profit': 98749.20776578,
        'stop_loss': 110989.82100000,
        'risk_reward_ratio': 1.18,
        'primary_tf': '4h',
        'tp_sl_methods': ['ATR', 'Fibonacci', 'Volume Profile', 'Support/Resistance'],
        'tp_sl_confidence': 0.78,
        'high_confidence': True,
        'multi_timeframe_confirmed': True
    }

def test_consensus_signal_detailed_format():
    """Test that consensus signal uses the correct detailed format"""
    print("\n🧪 === TESTING CONSENSUS SIGNAL DETAILED FORMAT ===")
    
    try:
        # Initialize notifier
        notifier = EnhancedTelegramNotifier(
            bot_token="test_token",
            chat_id="-1002301937119"
        )
        
        # Override send_message and send_photo to capture messages
        sent_messages = []
        sent_photos = []
        
        def mock_send_message(message, chat_id=None, parse_mode="HTML"):
            sent_messages.append({
                'message': message,
                'chat_id': chat_id,
                'parse_mode': parse_mode
            })
            print(f"📤 MOCK: Would send message to {chat_id}")
            return True
        
        def mock_send_photo(photo_path, caption="", chat_id=None, parse_mode="HTML"):
            sent_photos.append({
                'photo_path': photo_path,
                'caption': caption,
                'chat_id': chat_id,
                'parse_mode': parse_mode
            })
            print(f"📸 MOCK: Would send photo to {chat_id}")
            return True
        
        # Store original methods
        original_send_message = notifier.send_message
        original_send_photo = notifier.send_photo
        
        # Apply mocks
        notifier.send_message = mock_send_message
        notifier.send_photo = mock_send_photo
        
        # Create test data
        consensus_data = create_test_consensus_data()
        signal_data = create_test_signal_data()
        
        # Test consensus signal method
        result = notifier.send_consensus_signal(
            coin="BTC/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data,
            use_html=True,
            ohlcv_data=None,  # No chart generation
            chart_generator=None
        )
        
        # Restore original methods
        notifier.send_message = original_send_message
        notifier.send_photo = original_send_photo
        
        print(f"✅ Consensus signal method completed: {result}")
        print(f"📤 Messages sent: {len(sent_messages)}")
        print(f"📸 Photos sent: {len(sent_photos)}")
        
        if sent_messages:
            message_content = sent_messages[0]['message']
            
            print("\n📝 SENT MESSAGE CONTENT:")
            print("=" * 80)
            print(message_content)
            print("=" * 80)
            
            # Check for correct detailed format elements
            expected_elements = [
                "🎯 CONSENSUS SIGNAL - BTC/USDT 🎯",
                "🔴 SIGNAL TYPE: SELL 🔴",
                "🪙 BTC/USDT (MAJOR) | 📈 4h",
                "💰 Entry:",
                "🎯 Take Profit:",
                "🛡️ Stop Loss:",
                "⚖️ Risk/Reward:",
                "🎯 PHÂN TÍCH ĐỒNG THUẬN:",
                "├ Điểm đồng thuận:",
                "├ Độ tin cậy:",
                "├ Sức mạnh tín hiệu:",
                "└ Chất lượng tổng thể:",
                "📊 PHÂN TÍCH CHI TIẾT:",
                "🎯 PHÂN TÍCH TP/SL:",
                "├ Phương pháp sử dụng:",
                "├ Độ tin cậy TP/SL:",
                "└ Điểm chính xác:",
                "💡 NÂNG CAO:",
                "🆔 Signal ID:",
                "⏰ Thời gian:",
                "⚡ Tín hiệu này đạt tiêu chuẩn nghiêm ngặt"
            ]
            
            missing_elements = []
            for element in expected_elements:
                if element not in message_content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"\n❌ Missing elements in detailed format:")
                for element in missing_elements:
                    print(f"  - {element}")
                return False
            else:
                print(f"\n✅ All expected detailed elements found!")
                print(f"✅ Found {len(expected_elements)}/{len(expected_elements)} elements")
                
                # Check specific values
                if "105367.43000000" in message_content:
                    print("✅ Entry price correctly formatted")
                if "98749.20776578" in message_content:
                    print("✅ Take profit correctly formatted")
                if "110989.82100000" in message_content:
                    print("✅ Stop loss correctly formatted")
                if "1.18:1" in message_content:
                    print("✅ Risk/reward correctly formatted")
                if "0.639/1.000" in message_content:
                    print("✅ Consensus score correctly formatted")
                if "SIG_BTC/USDT_1749972075" in message_content:
                    print("✅ Signal ID correctly included")
                
                return True
        else:
            print("❌ No messages were sent")
            return False
        
    except Exception as e:
        print(f"❌ Error testing consensus signal format: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run test"""
    print("🧪 === CONSENSUS SIGNAL CORRECT DETAILED FORMAT TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    success = test_consensus_signal_detailed_format()
    end_time = time.time()
    
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nDetailed Format Test: {status} ({end_time - start_time:.2f}s)")
    
    if success:
        print("\n🎉 SUCCESS! Consensus signals are using correct detailed format!")
        print("\n📋 CONFIRMED DETAILED FORMAT INCLUDES:")
        print("✅ Signal type with emoji indicators")
        print("✅ Coin category and timeframe")
        print("✅ Entry, TP, SL with proper formatting")
        print("✅ Risk/reward ratio")
        print("✅ Phân tích đồng thuận (4 metrics)")
        print("✅ Phân tích chi tiết (contributing algorithms)")
        print("✅ Phân tích TP/SL (methods and confidence)")
        print("✅ Nâng cao (enhancement features)")
        print("✅ Signal ID and timestamp")
        print("✅ Professional closing statement")
    else:
        print("\n⚠️ Test failed. Detailed format may not be working correctly.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
