#!/usr/bin/env python3
"""
🔍 Debug Test - Minimal test to check what's working
"""

import os
import sys
from datetime import datetime

print(f"🔍 DEBUG TEST STARTED")
print(f"⏰ Time: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
print(f"📁 Current directory: {os.getcwd()}")
print(f"🐍 Python version: {sys.version}")

# Test 1: Check environment variables
print(f"\n1️⃣ Checking environment variables...")
bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
chat_id = os.getenv("TELEGRAM_CHAT_ID")

if bot_token:
    print(f"  ✅ TELEGRAM_BOT_TOKEN: SET (length: {len(bot_token)})")
else:
    print(f"  ❌ TELEGRAM_BOT_TOKEN: NOT SET")

if chat_id:
    print(f"  ✅ TELEGRAM_CHAT_ID: {chat_id}")
else:
    print(f"  ❌ TELEGRAM_CHAT_ID: NOT SET")

# Test 2: Check file existence
print(f"\n2️⃣ Checking file existence...")
files_to_check = [
    "telegram_notifier.py",
    "chart_generator.py", 
    "main_bot.py",
    "data_fetcher.py"
]

for file in files_to_check:
    if os.path.exists(file):
        print(f"  ✅ {file}: EXISTS")
    else:
        print(f"  ❌ {file}: NOT FOUND")

# Test 3: Try basic imports
print(f"\n3️⃣ Testing basic imports...")
try:
    import pandas as pd
    print(f"  ✅ pandas: {pd.__version__}")
except Exception as e:
    print(f"  ❌ pandas: {e}")

try:
    import matplotlib
    print(f"  ✅ matplotlib: {matplotlib.__version__}")
except Exception as e:
    print(f"  ❌ matplotlib: {e}")

try:
    import requests
    print(f"  ✅ requests: {requests.__version__}")
except Exception as e:
    print(f"  ❌ requests: {e}")

# Test 4: Try importing our modules
print(f"\n4️⃣ Testing our module imports...")
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    # Try telegram_notifier
    try:
        from telegram_notifier import EnhancedTelegramNotifier
        print(f"  ✅ telegram_notifier: IMPORTED")
        
        # Try initializing
        try:
            if bot_token and chat_id:
                notifier = EnhancedTelegramNotifier(bot_token, chat_id)
                print(f"  ✅ telegram_notifier: INITIALIZED")
                print(f"      Chat ID: {notifier.chat_id}")
            else:
                print(f"  ❌ telegram_notifier: Missing bot_token or chat_id")
                notifier = None
            
            # Try basic test
            if notifier:
                try:
                    result = notifier.test_basic_photo_send()
                    if result:
                        print(f"  ✅ telegram_notifier: BASIC TEST PASSED")
                    else:
                        print(f"  ❌ telegram_notifier: BASIC TEST FAILED")
                except Exception as e:
                    print(f"  ❌ telegram_notifier basic test error: {e}")
            else:
                print(f"  ⚠️ telegram_notifier: Skipping test (not initialized)")
                
        except Exception as e:
            print(f"  ❌ telegram_notifier initialization error: {e}")
            
    except Exception as e:
        print(f"  ❌ telegram_notifier import error: {e}")

except Exception as e:
    print(f"  ❌ Module import setup error: {e}")

print(f"\n🔍 DEBUG TEST COMPLETED")
print(f"⏰ Time: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
