#!/usr/bin/env python3
"""
🧪 TEST: Signal Manager Integration Fix
Test để kiểm tra fix cho lỗi 'signal_manager' attribute
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_signal_manager_integration_with_ultra_tracker():
    """Test SignalManagerIntegration with Ultra Tracker (no signal_manager)"""
    print("🧪 === TESTING SIGNAL MANAGER INTEGRATION WITH ULTRA TRACKER ===")
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Mock Ultra Tracker
        class MockUltraTracker:
            def __init__(self):
                self.signal_management = {
                    'max_signals': 20,
                    'completion_threshold': 18,
                    'shared_pool_mode': True
                }
                self.analyzer_tracking = {
                    'analyzer_chats': {
                        'ai_analysis': "-1002608968097_620",
                        'fibonacci': "-1002608968097_619",
                        'volume_profile': "-1002608968097_621",
                        'consensus': "-1002301937119"
                    },
                    'analyzer_stats': {
                        'ai_analysis': {'sent': 0, 'completed': 0},
                        'fibonacci': {'sent': 0, 'completed': 0},
                        'consensus': {'sent': 0, 'completed': 0}
                    }
                }
                self.signals = []
            
            def add_signal(self, signal_data):
                self.signals.append(signal_data)
                return True
            
            def get_ultra_tracker_status(self):
                return {
                    'signal_limits': {
                        'max_signals': 20,
                        'completion_threshold': 18,
                        'active_signals': len(self.signals),
                        'can_send_new': len(self.signals) < 18
                    }
                }
        
        # Mock Telegram Notifier
        class MockTelegramNotifier:
            def send_fibonacci_signal(self, coin, fibonacci_data, current_price, **kwargs):
                print(f"    📤 Mock sending Fibonacci signal for {coin}")
                return True
            
            def send_consensus_signal(self, coin, consensus_data, signal_data, **kwargs):
                print(f"    📤 Mock sending Consensus signal for {coin}")
                return True
        
        # Initialize with Ultra Tracker (should NOT create signal_manager)
        mock_tracker = MockUltraTracker()
        mock_notifier = MockTelegramNotifier()
        
        integration = SignalManagerIntegration(
            telegram_notifier=mock_notifier,
            data_fetcher=None,
            trade_tracker=mock_tracker
        )
        
        print(f"✅ SignalManagerIntegration initialized with Ultra Tracker")
        print(f"  Has signal_manager: {hasattr(integration, 'signal_manager')}")
        print(f"  Has trade_tracker: {hasattr(integration, 'trade_tracker')}")
        
        # Test Fibonacci signal (this was causing the error)
        fibonacci_data = {
            "signals": {"primary_signal": "SELL", "confidence": 0.75},
            "trading_levels": {
                "entry_price": 100.0,
                "take_profit": 95.0,
                "stop_loss": 105.0
            }
        }
        
        print(f"\n🧪 Testing Fibonacci signal sending...")
        result = integration.send_fibonacci_signal(
            coin="WBETH/USDT",
            fibonacci_data=fibonacci_data,
            current_price=100.0
        )
        
        if result:
            print(f"✅ Fibonacci signal sent successfully")
        else:
            print(f"❌ Fibonacci signal failed")
        
        # Test Consensus signal
        consensus_data = {"consensus_score": 0.8, "confidence": 0.75}
        signal_data = {
            "signal_type": "SELL",
            "entry": 100.0,
            "take_profit": 95.0,
            "stop_loss": 105.0
        }
        
        print(f"\n🧪 Testing Consensus signal sending...")
        result2 = integration.send_consensus_signal(
            coin="WBETH/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data
        )
        
        if result2:
            print(f"✅ Consensus signal sent successfully")
        else:
            print(f"❌ Consensus signal failed")
        
        # Test analyzer status
        print(f"\n🧪 Testing analyzer status...")
        status = integration.get_analyzer_status("fibonacci")
        print(f"  Fibonacci status: {status}")
        
        return result and result2
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_manager_integration_fallback():
    """Test SignalManagerIntegration fallback mode (with signal_manager)"""
    print("\n🧪 === TESTING SIGNAL MANAGER INTEGRATION FALLBACK MODE ===")
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Mock Telegram Notifier
        class MockTelegramNotifier:
            def send_fibonacci_signal(self, coin, fibonacci_data, current_price, **kwargs):
                print(f"    📤 Mock sending Fibonacci signal for {coin}")
                return True
        
        # Initialize without Ultra Tracker (should create signal_manager)
        integration = SignalManagerIntegration(
            telegram_notifier=MockTelegramNotifier(),
            data_fetcher=None,
            trade_tracker=None  # No Ultra Tracker
        )
        
        print(f"✅ SignalManagerIntegration initialized in fallback mode")
        print(f"  Has signal_manager: {hasattr(integration, 'signal_manager')}")
        print(f"  Has trade_tracker: {hasattr(integration, 'trade_tracker')}")
        
        # Test that signal_manager exists in fallback mode
        if hasattr(integration, 'signal_manager'):
            print(f"✅ signal_manager created in fallback mode")
            return True
        else:
            print(f"❌ signal_manager not created in fallback mode")
            return False
        
    except Exception as e:
        print(f"❌ Fallback test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ultra_tracker_path_selection():
    """Test that methods correctly choose Ultra Tracker vs signal_manager path"""
    print("\n🧪 === TESTING PATH SELECTION LOGIC ===")
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Mock components
        class MockUltraTracker:
            def __init__(self):
                self.signal_management = {'max_signals': 20}
                self.analyzer_tracking = {'analyzer_chats': {}, 'analyzer_stats': {}}
                self.signals = []
            
            def add_signal(self, signal_data):
                self.signals.append(signal_data)
                return True
            
            def get_ultra_tracker_status(self):
                return {'signal_limits': {'active_signals': 5, 'can_send_new': True}}
        
        class MockTelegramNotifier:
            def send_fibonacci_signal(self, *args, **kwargs):
                return True
        
        # Test with Ultra Tracker
        integration_with_tracker = SignalManagerIntegration(
            telegram_notifier=MockTelegramNotifier(),
            trade_tracker=MockUltraTracker()
        )
        
        # Test without Ultra Tracker
        integration_without_tracker = SignalManagerIntegration(
            telegram_notifier=MockTelegramNotifier(),
            trade_tracker=None
        )
        
        print(f"✅ Path selection test:")
        print(f"  With Ultra Tracker - has signal_manager: {hasattr(integration_with_tracker, 'signal_manager')}")
        print(f"  Without Ultra Tracker - has signal_manager: {hasattr(integration_without_tracker, 'signal_manager')}")
        
        # Should be: False (with tracker), True (without tracker)
        expected_with = False
        expected_without = True
        
        actual_with = hasattr(integration_with_tracker, 'signal_manager')
        actual_without = hasattr(integration_without_tracker, 'signal_manager')
        
        if actual_with == expected_with and actual_without == expected_without:
            print(f"✅ Path selection working correctly")
            return True
        else:
            print(f"❌ Path selection incorrect:")
            print(f"  With tracker: expected {expected_with}, got {actual_with}")
            print(f"  Without tracker: expected {expected_without}, got {actual_without}")
            return False
        
    except Exception as e:
        print(f"❌ Path selection test error: {e}")
        return False

def main():
    """Run all signal manager integration fix tests"""
    print("🧪 === SIGNAL MANAGER INTEGRATION FIX TEST ===")
    
    test1 = test_signal_manager_integration_with_ultra_tracker()
    test2 = test_signal_manager_integration_fallback()
    test3 = test_ultra_tracker_path_selection()
    
    if test1 and test2 and test3:
        print("\n🎉 SUCCESS: Signal Manager Integration fixes working!")
        print("✅ Ultra Tracker path working (no signal_manager attribute error)")
        print("✅ Fallback path working (signal_manager created)")
        print("✅ Path selection logic correct")
        print("✅ Fibonacci and Consensus signals working")
        print("✅ Ready for production deployment")
    else:
        print("\n❌ FAILED: Some integration fixes not working")
    
    return test1 and test2 and test3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
