#!/usr/bin/env python3
"""
⚡ Quick Stability Test - Test nhanh 5 phút để demo
"""

import os
import sys
import time
import threading
import requests
import subprocess
from datetime import datetime, timedelta
from collections import defaultdict

def send_telegram_message(message: str) -> bool:
    """📱 Send message to Telegram."""
    try:
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not bot_token or not chat_id:
            print(f"❌ Missing Telegram credentials")
            return False
        
        base_url = f"https://api.telegram.org/bot{bot_token}"
        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(f"{base_url}/sendMessage", data=data, timeout=10)
        return response.status_code == 200 and response.json().get('ok', False)
        
    except Exception as e:
        print(f"❌ Error sending Telegram message: {e}")
        return False

def quick_stability_test():
    """⚡ Run quick 5-minute stability test."""
    print(f"⚡ QUICK STABILITY TEST (5 MINUTES)")
    print(f"=" * 60)
    
    start_time = datetime.now()
    test_duration = 5  # 5 minutes
    errors_detected = []
    analysis_count = defaultdict(int)
    
    # Send start notification
    start_message = f"""⚡ <b>QUICK STABILITY TEST STARTED</b>

⏰ <b>Test Configuration:</b>
├ 🕐 Duration: <code>5 minutes</code>
├ 📊 Purpose: <b>Quick system health check</b>
└ 🎯 Goal: <b>Detect any immediate issues</b>

🔍 <b>What we're checking:</b>
├ 🤖 Bot process startup
├ ❌ Error detection
├ 📊 Analysis activity
└ 🔄 System responsiveness

<b>⚡ QUICK TEST STARTING NOW...</b>"""
    
    send_telegram_message(start_message)
    print(f"📱 Start notification sent")
    
    # Start bot process
    bot_process = None
    try:
        print(f"🚀 Starting main bot process...")
        bot_process = subprocess.Popen([
            sys.executable, "main_bot.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
           text=True, bufsize=1, universal_newlines=True)
        
        print(f"✅ Bot process started with PID: {bot_process.pid}")
        
        # Monitor for 5 minutes
        end_time = start_time + timedelta(minutes=test_duration)
        
        error_patterns = [
            'Error', 'Exception', 'Traceback', 'Failed', 
            'UnboundLocalError', 'NoneType', 'Unknown analysis type'
        ]
        
        print(f"🔍 Monitoring bot for {test_duration} minutes...")
        
        while datetime.now() < end_time:
            current_time = datetime.now()
            
            # Check if process is still running
            if bot_process.poll() is not None:
                print(f"❌ Bot process terminated unexpectedly!")
                errors_detected.append({
                    'timestamp': current_time,
                    'type': 'PROCESS_TERMINATED',
                    'message': 'Bot process terminated unexpectedly'
                })
                break
            
            # Read output (Windows compatible)
            try:
                # Simple polling approach for Windows
                line = None
                try:
                    # Try to read a line with timeout
                    import threading
                    import queue

                    def read_line(process, q):
                        try:
                            line = process.stdout.readline()
                            if line:
                                q.put(line.strip())
                        except:
                            pass

                    q = queue.Queue()
                    t = threading.Thread(target=read_line, args=(bot_process, q))
                    t.daemon = True
                    t.start()
                    t.join(timeout=0.1)

                    if not q.empty():
                        line = q.get_nowait()

                except:
                    pass

                if line:
                    print(f"BOT: {line}")

                    # Check for errors
                    for pattern in error_patterns:
                        if pattern.lower() in line.lower():
                            errors_detected.append({
                                'timestamp': current_time,
                                'type': pattern,
                                'message': line
                            })
                            print(f"🚨 ERROR DETECTED: {pattern}")

                    # Count analysis types
                    if 'analysis' in line.lower():
                        for analysis_type in ['fibonacci', 'volume', 'ai', 'pump', 'dump', 'consensus', 'orderbook']:
                            if analysis_type in line.lower():
                                analysis_count[analysis_type] += 1

            except Exception:
                pass  # Continue monitoring
            
            # Show progress
            runtime = current_time - start_time
            remaining = end_time - current_time
            if runtime.total_seconds() % 30 < 1:  # Every 30 seconds
                print(f"🔍 Runtime: {int(runtime.total_seconds()//60)}:{int(runtime.total_seconds()%60):02d} | Remaining: {int(remaining.total_seconds()//60)}:{int(remaining.total_seconds()%60):02d} | Errors: {len(errors_detected)}")
            
            time.sleep(1)
        
        print(f"⏰ Test duration completed")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        errors_detected.append({
            'timestamp': datetime.now(),
            'type': 'TEST_ERROR',
            'message': str(e)
        })
    
    finally:
        # Stop bot process
        if bot_process:
            print(f"🛑 Stopping bot process...")
            try:
                bot_process.terminate()
                bot_process.wait(timeout=5)
                print(f"✅ Bot process stopped")
            except subprocess.TimeoutExpired:
                bot_process.kill()
                print(f"⚠️ Bot process killed")
    
    # Generate final report
    end_time = datetime.now()
    total_runtime = end_time - start_time
    
    final_report = f"""⚡ <b>QUICK STABILITY TEST - RESULTS</b>

⏰ <b>TEST SUMMARY</b>
├ 🕐 Duration: <code>{str(total_runtime).split('.')[0]}</code>
├ ❌ Errors Found: <code>{len(errors_detected)}</code>
└ 📊 Analysis Count: <code>{sum(analysis_count.values())}</code>

🤖 <b>BOT PERFORMANCE</b>
├ 🚀 Startup: <b>{'SUCCESS' if bot_process else 'FAILED'}</b>
├ 🔄 Process Stability: <b>{'STABLE' if not any(e['type'] == 'PROCESS_TERMINATED' for e in errors_detected) else 'UNSTABLE'}</b>
└ 📊 Activity Level: <b>{'ACTIVE' if sum(analysis_count.values()) > 0 else 'IDLE'}</b>"""

    if analysis_count:
        final_report += f"\n\n📊 <b>ANALYSIS BREAKDOWN</b>"
        for analysis_type, count in sorted(analysis_count.items()):
            final_report += f"\n├ {analysis_type.title()}: <code>{count}</code>"

    if errors_detected:
        final_report += f"\n\n❌ <b>ERRORS DETECTED</b>"
        error_types = defaultdict(int)
        for error in errors_detected:
            error_types[error['type']] += 1
        
        for error_type, count in sorted(error_types.items()):
            final_report += f"\n├ {error_type}: <code>{count}</code>"
        
        final_report += f"\n\n🔍 <b>RECENT ERRORS</b>"
        for error in errors_detected[-3:]:
            timestamp = error['timestamp'].strftime('%H:%M:%S')
            message = error['message'][:60] + "..." if len(error['message']) > 60 else error['message']
            final_report += f"\n├ <code>{timestamp}</code>: {message}"

    # Assessment
    if len(errors_detected) == 0:
        assessment = "🟢 EXCELLENT - No issues detected"
    elif len(errors_detected) < 3:
        assessment = "🟡 GOOD - Minor issues only"
    else:
        assessment = "🔴 NEEDS ATTENTION - Multiple errors detected"

    final_report += f"\n\n🏥 <b>QUICK ASSESSMENT</b>"
    final_report += f"\n└ 🎯 Status: <b>{assessment}</b>"

    final_report += f"\n\n💡 <b>RECOMMENDATION</b>"
    if len(errors_detected) == 0:
        final_report += f"\n├ ✅ System appears stable"
        final_report += f"\n└ 🚀 Ready for longer testing"
    else:
        final_report += f"\n├ 🔧 Review detected errors"
        final_report += f"\n└ 🔍 Consider fixes before production"

    final_report += f"\n\n⚡ <b>QUICK STABILITY TEST COMPLETED</b>"

    # Send final report
    send_telegram_message(final_report)
    print(f"📱 Final report sent to Telegram")
    
    # Console summary
    print(f"\n📊 QUICK TEST RESULTS:")
    print(f"  ⏰ Runtime: {str(total_runtime).split('.')[0]}")
    print(f"  ❌ Errors: {len(errors_detected)}")
    print(f"  📊 Analysis: {sum(analysis_count.values())}")
    print(f"  🎯 Assessment: {assessment}")
    
    return len(errors_detected) == 0

def main():
    """🚀 Main function."""
    print(f"⚡ QUICK STABILITY TEST")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    print(f"")
    
    # Check environment
    if not os.getenv("TELEGRAM_BOT_TOKEN") or not os.getenv("TELEGRAM_CHAT_ID"):
        print(f"❌ Missing environment variables")
        print(f"Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID")
        return
    
    if not os.path.exists("main_bot.py"):
        print(f"❌ main_bot.py not found")
        return
    
    # Run quick test
    try:
        success = quick_stability_test()
        
        if success:
            print(f"\n🎉 QUICK TEST PASSED!")
            print(f"✅ No errors detected - system appears stable")
            print(f"🚀 Ready for longer stability testing")
        else:
            print(f"\n⚠️ QUICK TEST FOUND ISSUES!")
            print(f"❌ Errors detected - review before production")
            print(f"🔧 Check Telegram for detailed error report")
            
    except Exception as e:
        print(f"❌ Fatal error in quick test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
