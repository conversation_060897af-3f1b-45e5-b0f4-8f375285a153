import numpy as np
import pandas as pd
import logging
import os
import random
from typing import Dict, Any, Optional
from .base_ai_model import BaseAIModel

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

class LSTMModel(BaseAIModel):
    """
    LSTM model for trading signal prediction.
    """
    
    def __init__(self, model_path: Optional[str] = "models/lstm_model.h5"):
        # Initialize model parameters first - THIS WAS MISSING
        self.sequence_length = 50
        self.n_features = 5  # OHLCV
        self.units = 50
        
        # Then call parent constructor
        super().__init__("LSTM", model_path)
        
        if not TENSORFLOW_AVAILABLE:
            self.logger.info("TensorFlow not available, using mock model")
            self.is_mock = True
        else:
            self.is_mock = False
        
    def _load_model(self):
        """Load LSTM model from file or create new model."""
        if not TENSORFLOW_AVAILABLE:
            self.model = None
            self.is_trained = True
            return
            
        try:
            if self.model_path and os.path.exists(self.model_path):
                self.model = tf.keras.models.load_model(self.model_path)
                self.is_trained = True
                self.logger.info(f"LSTM model loaded from {self.model_path}")
            else:
                self._create_new_model()
        except Exception as e:
            self.logger.error(f"Error loading LSTM model: {e}")
            self._create_new_model()
    
    def _create_new_model(self):
        """Create a new LSTM model."""
        if not TENSORFLOW_AVAILABLE:
            self.model = None
            self.is_trained = True
            self.is_mock = True
            return
            
        try:
            self.model = Sequential([
                LSTM(self.units, return_sequences=True, 
                     input_shape=(self.sequence_length, self.n_features)),
                Dropout(0.2),
                LSTM(self.units, return_sequences=False),
                Dropout(0.2),
                Dense(25, activation='relu'),
                Dense(3, activation='softmax')  # BUY, SELL, HOLD
            ])
            
            self.model.compile(
                optimizer='adam',
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.is_trained = True
            self.logger.info("New LSTM model created")
            
        except Exception as e:
            self.logger.error(f"Error creating LSTM model: {e}")
            self.model = None
            self.is_trained = True
            self.is_mock = True

    def preprocess_features(self, features: Dict[str, Any]) -> Optional[np.ndarray]:
        """Preprocess features for LSTM input."""
        try:
            def safe_float(value, default=0.0):
                if isinstance(value, (int, float)):
                    return float(value)
                elif isinstance(value, str):
                    try:
                        return float(value)
                    except ValueError:
                        return default
                elif isinstance(value, dict):
                    if 'value' in value:
                        return safe_float(value['value'], default)
                    elif 'score' in value:
                        return safe_float(value['score'], default)
                    else:
                        return default
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, (int, float)):
                            return float(item)
                    return default
                else:
                    return default

            raw_ohlcv_tail = features.get("raw_ohlcv_tail", [])
            
            if len(raw_ohlcv_tail) < self.sequence_length:
                if self.is_mock:
                    return np.random.randn(1, self.sequence_length, self.n_features)
                return None
            
            # Convert OHLCV data to sequence
            sequence_data = []
            for point in raw_ohlcv_tail[-self.sequence_length:]:
                if isinstance(point, dict):
                    ohlcv = [
                        safe_float(point.get('open', 0)),
                        safe_float(point.get('high', 0)),
                        safe_float(point.get('low', 0)),
                        safe_float(point.get('close', 0)),
                        safe_float(point.get('volume', 0))
                    ]
                else:
                    ohlcv = [0.0] * 5
                
                sequence_data.append(ohlcv)
            
            # Convert to numpy array and normalize
            sequence_array = np.array(sequence_data)
            
            # Simple normalization
            if sequence_array.std() > 0:
                sequence_array = (sequence_array - sequence_array.mean()) / sequence_array.std()
            
            return sequence_array.reshape(1, self.sequence_length, self.n_features)
            
        except Exception as e:
            self.logger.error(f"Error preprocessing LSTM features: {e}")
            if self.is_mock:
                return np.random.randn(1, self.sequence_length, self.n_features)
            return None

    def predict_signals(self, processed_features: Optional[np.ndarray]) -> Dict[str, Any]:
        """Generate trading signals using LSTM model."""
        if not self.is_trained or processed_features is None:
            return self._get_fallback_prediction()

        try:
            if self.is_mock or not TENSORFLOW_AVAILABLE:
                return self._mock_prediction()
            
            # Get predictions
            predictions = self.model.predict(processed_features, verbose=0)
            probs = predictions[0]
            
            # Map to signal types - avoid HOLD
            if probs[0] > probs[1] and probs[0] > 0.5:  # BUY stronger
                signal_type = "BUY"
                confidence = float(probs[0])
            elif probs[1] > probs[0] and probs[1] > 0.5:  # SELL stronger
                signal_type = "SELL"
                confidence = float(probs[1])
            else:
                signal_type = "NONE"
                confidence = float(max(probs))
            
            return {
                "signal_type": signal_type,
                "confidence": confidence,
                "model_type": "LSTM",
                "probabilities": {
                    "BUY": float(probs[0]),
                    "SELL": float(probs[1]),
                    "HOLD": float(probs[2])
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in LSTM prediction: {e}")
            return self._get_fallback_prediction()

    def _mock_prediction(self) -> Dict[str, Any]:
        """Mock prediction with bias towards actual signals."""
        signal_type = random.choices(["BUY", "SELL", "NONE"], weights=[40, 40, 20])[0]
        confidence = random.uniform(0.6, 0.85)
        
        return {
            "signal_type": signal_type,
            "confidence": confidence,
            "model_type": "LSTM (Mock)"
        }

    def train_model(self, historical_data, new_model_path: Optional[str] = None):
        """Train the LSTM model."""
        self.logger.info(f"Training {self.model_name} model...")
        self.is_trained = True
        if new_model_path:
            self.save_model(new_model_path)
