#!/usr/bin/env python3
"""
🧪 Test script to verify the Volume Profile and Orderbook analyzer fixes
"""

import os
import sys
import pandas as pd
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_sample_data():
    """Create sample OHLCV data for testing"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    
    # Create realistic price data
    base_price = 50000.0
    price_data = []
    current_price = base_price
    
    for i in range(100):
        # Add some random walk
        change = np.random.normal(0, 0.02)  # 2% volatility
        current_price *= (1 + change)
        price_data.append(current_price)
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': price_data,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in price_data],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in price_data],
        'close': price_data,
        'volume': [np.random.uniform(1000, 10000) for _ in range(100)]
    })
    
    return df

def create_sample_orderbook():
    """Create sample orderbook data for testing"""
    current_price = 50000.0
    
    # Create bid levels (below current price)
    bid_levels = []
    for i in range(20):
        price = current_price * (1 - (i + 1) * 0.001)  # 0.1% steps down
        quantity = np.random.uniform(0.1, 5.0)
        bid_levels.append({"price": price, "quantity": quantity})
    
    # Create ask levels (above current price)
    ask_levels = []
    for i in range(20):
        price = current_price * (1 + (i + 1) * 0.001)  # 0.1% steps up
        quantity = np.random.uniform(0.1, 5.0)
        ask_levels.append({"price": price, "quantity": quantity})
    
    return {
        "bids": bid_levels,
        "asks": ask_levels,
        "timestamp": 1640995200000  # Sample timestamp
    }

def test_volume_profile_analyzer():
    """Test the enhanced Volume Profile analyzer"""
    print("🧪 Testing Enhanced Volume Profile Analyzer...")
    print("=" * 60)
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        # Initialize analyzer
        analyzer = VolumeProfileAnalyzer()
        
        # Create sample data
        df = create_sample_data()
        current_price = df['close'].iloc[-1]
        
        print(f"📊 Sample data created:")
        print(f"  - Rows: {len(df)}")
        print(f"  - Current price: {current_price:.2f}")
        print(f"  - Volume sum: {df['volume'].sum():,.0f}")
        
        # Run analysis
        print(f"\n🔍 Running Volume Profile analysis...")
        result = analyzer.analyze_volume_profile(df)
        
        print(f"\n📊 Volume Profile Results:")
        print(f"  - Status: {result.get('status')}")
        
        if result.get("status") == "success":
            signals = result.get("signals", {})
            signal = signals.get("primary_signal", "NONE")
            confidence = signals.get("confidence", 0)
            
            print(f"  - Signal: {signal}")
            print(f"  - Confidence: {confidence:.3f} ({confidence:.1%})")
            
            # Check if signal is no longer NONE
            if signal != "NONE":
                print(f"  ✅ SUCCESS: Volume Profile now generates valid signals!")
                print(f"  📈 Signal details:")
                reasoning = signals.get("reasoning", [])
                for reason in reasoning[:3]:  # Show first 3 reasons
                    print(f"    - {reason}")
                return True
            else:
                print(f"  ❌ ISSUE: Volume Profile still returns NONE signal")
                return False
        else:
            print(f"  ❌ FAILED: Volume Profile analysis failed")
            print(f"  📝 Message: {result.get('message', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import VolumeProfileAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Volume Profile analyzer: {e}")
        return False

def test_orderbook_analyzer():
    """Test the enhanced Orderbook analyzer"""
    print("\n🧪 Testing Enhanced Orderbook Analyzer...")
    print("=" * 60)
    
    try:
        from orderbook_analyzer import OrderbookAnalyzer
        
        # Initialize analyzer
        analyzer = OrderbookAnalyzer()
        
        # Create sample data
        orderbook_data = create_sample_orderbook()
        current_price = 50000.0
        
        print(f"📊 Sample orderbook created:")
        print(f"  - Bid levels: {len(orderbook_data['bids'])}")
        print(f"  - Ask levels: {len(orderbook_data['asks'])}")
        print(f"  - Current price: {current_price:.2f}")
        
        # Run analysis
        print(f"\n🔍 Running Orderbook analysis...")
        result = analyzer.analyze_orderbook(orderbook_data, "BTC/USDT", current_price)
        
        print(f"\n📊 Orderbook Results:")
        print(f"  - Status: {result.get('status')}")
        
        if result.get("status") == "success":
            signals = result.get("signals", {})
            signal = signals.get("primary_signal", "NONE")
            confidence = signals.get("confidence", 0)
            
            print(f"  - Signal: {signal}")
            print(f"  - Confidence: {confidence:.3f} ({confidence:.1%})")
            
            # Check if signal is no longer NONE
            if signal != "NONE":
                print(f"  ✅ SUCCESS: Orderbook now generates valid signals!")
                
                # Show additional details
                imbalance = result.get("imbalance", {})
                primary_imbalance = imbalance.get("primary_imbalance", 0)
                bid_ask_ratio = imbalance.get("bid_ask_ratio", 1.0)
                
                print(f"  📈 Signal details:")
                print(f"    - Primary imbalance: {primary_imbalance:+.3f}")
                print(f"    - Bid/Ask ratio: {bid_ask_ratio:.3f}")
                print(f"    - Recommendation: {signals.get('recommendation', 'N/A')}")
                return True
            else:
                print(f"  ❌ ISSUE: Orderbook still returns NONE signal")
                
                # Debug information
                imbalance = result.get("imbalance", {})
                primary_imbalance = imbalance.get("primary_imbalance", 0)
                bid_ask_ratio = imbalance.get("bid_ask_ratio", 1.0)
                
                print(f"  🔍 Debug info:")
                print(f"    - Primary imbalance: {primary_imbalance:+.3f}")
                print(f"    - Bid/Ask ratio: {bid_ask_ratio:.3f}")
                print(f"    - Confidence: {confidence:.3f}")
                return False
        else:
            print(f"  ❌ FAILED: Orderbook analysis failed")
            print(f"  📝 Message: {result.get('message', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import OrderbookAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Orderbook analyzer: {e}")
        return False

def test_consensus_integration():
    """Test how the fixed analyzers integrate with consensus"""
    print("\n🧪 Testing Consensus Integration...")
    print("=" * 60)
    
    try:
        from consensus_analyzer import ConsensusAnalyzer
        
        # Initialize analyzer
        consensus = ConsensusAnalyzer()
        
        # Create sample consensus input with our improved signals
        consensus_input = {
            "coin": "BTC/USDT",
            "volume_profile": {
                "signal": "BUY",
                "confidence": 0.45  # Above the new 0.4 threshold
            },
            "orderbook": {
                "signals": {
                    "primary_signal": "BUY", 
                    "confidence": 0.50  # Above the new threshold
                }
            },
            "point_figure": {
                "signal": "BUY",
                "confidence": 0.65
            },
            "fibonacci": {
                "signal": "BUY", 
                "confidence": 0.55
            },
            "fourier": {
                "signal": "BUY",
                "confidence": 0.60
            }
        }
        
        print(f"📊 Sample consensus input:")
        print(f"  - Volume Profile: {consensus_input['volume_profile']['signal']} ({consensus_input['volume_profile']['confidence']:.1%})")
        print(f"  - Orderbook: {consensus_input['orderbook']['signals']['primary_signal']} ({consensus_input['orderbook']['signals']['confidence']:.1%})")
        print(f"  - Point & Figure: {consensus_input['point_figure']['signal']} ({consensus_input['point_figure']['confidence']:.1%})")
        print(f"  - Fibonacci: {consensus_input['fibonacci']['signal']} ({consensus_input['fibonacci']['confidence']:.1%})")
        print(f"  - Fourier: {consensus_input['fourier']['signal']} ({consensus_input['fourier']['confidence']:.1%})")
        
        # Run consensus analysis
        print(f"\n🔍 Running Consensus analysis...")
        result = consensus.analyze_consensus(consensus_input)
        
        print(f"\n📊 Consensus Results:")
        print(f"  - Status: {result.get('status')}")
        
        if result.get("status") == "success":
            consensus_data = result.get("consensus", {})
            signal = consensus_data.get("signal", "NONE")
            confidence = consensus_data.get("confidence", 0)
            consensus_score = consensus_data.get("consensus_score", 0)
            
            print(f"  - Signal: {signal}")
            print(f"  - Confidence: {confidence:.3f} ({confidence:.1%})")
            print(f"  - Consensus Score: {consensus_score:.3f}")
            
            # Check contributing signals
            contributing = result.get("contributing_algorithms", [])
            print(f"  - Contributing signals: {len(contributing)}")
            
            for contrib in contributing:
                name = contrib.get("name", "Unknown")
                contrib_signal = contrib.get("signal", "NONE")
                contrib_conf = contrib.get("confidence", 0)
                weight = contrib.get("weight", 0)
                print(f"    • {name}: {contrib_signal} ({contrib_conf:.1%}, weight: {weight:.3f})")
            
            if signal != "NONE" and len(contributing) >= 3:
                print(f"  ✅ SUCCESS: Consensus now generates valid signals with multiple contributors!")
                return True
            else:
                print(f"  ⚠️ PARTIAL: Consensus signal={signal}, contributors={len(contributing)}")
                return False
        else:
            print(f"  ❌ FAILED: Consensus analysis failed")
            print(f"  📝 Error: {result.get('error', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import ConsensusAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Consensus integration: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 ANALYZER FIXES VERIFICATION TEST SUITE")
    print("=" * 70)
    print(f"⏰ Test started at: {pd.Timestamp.now()}")
    print()
    
    # Run all tests
    test_results = []
    
    # Test 1: Volume Profile analyzer
    vp_result = test_volume_profile_analyzer()
    test_results.append(("Volume Profile Analyzer", vp_result))
    
    # Test 2: Orderbook analyzer
    ob_result = test_orderbook_analyzer()
    test_results.append(("Orderbook Analyzer", ob_result))
    
    # Test 3: Consensus integration
    consensus_result = test_consensus_integration()
    test_results.append(("Consensus Integration", consensus_result))
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 70)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED - Analyzer fixes are working correctly!")
        print("✅ Volume Profile and Orderbook analyzers now generate valid signals")
        print("✅ Consensus analyzer can now process these signals properly")
    else:
        print("❌ SOME TESTS FAILED - Issues remain in the analyzer fixes")
    
    print(f"⏰ Test completed at: {pd.Timestamp.now()}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
