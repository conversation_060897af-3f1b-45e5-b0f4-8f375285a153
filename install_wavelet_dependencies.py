#!/usr/bin/env python3
"""
🌊 Install Wavelet Analysis Dependencies
Automatically install PyWavelets and verify the enhanced wavelet analysis system
"""

import subprocess
import sys
import importlib

def install_package(package_name, version_spec=""):
    """Install a Python package using pip"""
    try:
        if version_spec:
            package_spec = f"{package_name}{version_spec}"
        else:
            package_spec = package_name
            
        print(f"📦 Installing {package_spec}...")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_spec
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ {package_name} installed successfully")
            return True
        else:
            print(f"❌ Failed to install {package_name}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Installation of {package_name} timed out")
        return False
    except Exception as e:
        print(f"❌ Error installing {package_name}: {e}")
        return False

def check_package_availability(package_name, import_name=None):
    """Check if a package is available for import"""
    try:
        if import_name is None:
            import_name = package_name
            
        importlib.import_module(import_name)
        print(f"✅ {package_name} is available")
        return True
    except ImportError:
        print(f"❌ {package_name} is not available")
        return False

def test_wavelet_functionality():
    """Test basic wavelet functionality"""
    try:
        print("🧪 Testing wavelet functionality...")
        
        import pywt
        import numpy as np
        
        # Create test signal
        t = np.linspace(0, 1, 1000)
        signal = np.sin(2 * np.pi * 5 * t) + 0.5 * np.sin(2 * np.pi * 10 * t)
        
        # Test continuous wavelet transform
        scales = np.arange(1, 31)
        coefficients, frequencies = pywt.cwt(signal, scales, 'morl')
        
        print(f"   📊 CWT coefficients shape: {coefficients.shape}")
        print(f"   🎯 Frequencies shape: {frequencies.shape}")
        
        # Test discrete wavelet transform
        coeffs = pywt.wavedec(signal, 'db4', level=4)
        print(f"   📈 DWT coefficients: {len(coeffs)} levels")
        
        print("✅ Wavelet functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ Wavelet functionality test failed: {e}")
        return False

def test_enhanced_fourier_analyzer():
    """Test the enhanced Fourier analyzer with wavelet support"""
    try:
        print("🌊 Testing Enhanced Fourier Analyzer...")
        
        # Add current directory to path
        sys.path.append('.')
        
        from fourier_analyzer import EnhancedFourierAnalyzer
        
        # Create analyzer with wavelet analysis enabled
        analyzer = EnhancedFourierAnalyzer(enable_wavelet_analysis=True)
        
        print(f"   🔧 Wavelet Analysis: {'Enabled' if analyzer.enable_wavelet_analysis else 'Disabled'}")
        print(f"   📦 PyWavelets Available: {'Yes' if analyzer.pywt_available else 'No'}")
        
        if analyzer.pywt_available:
            print("✅ Enhanced Fourier Analyzer with advanced wavelet support is ready")
        else:
            print("⚠️ Enhanced Fourier Analyzer will use fallback wavelet analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Fourier Analyzer test failed: {e}")
        return False

def main():
    """Main installation and verification process"""
    print("🌊 Enhanced Wavelet Analysis Dependencies Installer")
    print("=" * 60)
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Check current PyWavelets availability
    print("\n📋 Step 1: Checking current PyWavelets availability...")
    if check_package_availability("PyWavelets", "pywt"):
        print("   ℹ️ PyWavelets is already installed")
        success_count += 1
    else:
        print("   ⚠️ PyWavelets needs to be installed")
    
    # Step 2: Install PyWavelets
    print("\n📋 Step 2: Installing PyWavelets...")
    if install_package("PyWavelets", ">=1.4.1"):
        success_count += 1
    
    # Step 3: Verify installation
    print("\n📋 Step 3: Verifying PyWavelets installation...")
    if check_package_availability("PyWavelets", "pywt"):
        success_count += 1
    
    # Step 4: Test wavelet functionality
    print("\n📋 Step 4: Testing wavelet functionality...")
    if test_wavelet_functionality():
        success_count += 1
    
    # Step 5: Test enhanced Fourier analyzer
    print("\n📋 Step 5: Testing Enhanced Fourier Analyzer integration...")
    if test_enhanced_fourier_analyzer():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🎯 Installation Results: {success_count}/{total_steps} steps completed")
    
    if success_count == total_steps:
        print("🎉 Enhanced Wavelet Analysis is fully operational!")
        print("\n🚀 Next Steps:")
        print("   1. Restart your trading bot")
        print("   2. Wavelet Analysis will now show: ✅ Enabled (Advanced Multi-Wavelet)")
        print("   3. Enjoy enhanced cycle detection and market analysis!")
        return True
    else:
        print("⚠️ Some installation steps failed")
        print("\n🔧 Troubleshooting:")
        print("   1. Check your internet connection")
        print("   2. Try running: pip install PyWavelets --upgrade")
        print("   3. The system will still work with fallback wavelet analysis")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
