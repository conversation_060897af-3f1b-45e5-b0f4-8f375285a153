#!/usr/bin/env python3
"""
🧪 Test All Clean Chart Generation
Test all upgraded clean chart generation functionality
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_ohlcv_data(periods: int = 100) -> pd.DataFrame:
    """📊 Create sample OHLCV data for testing."""
    
    # Generate realistic price data
    base_price = 0.32
    dates = pd.date_range(start=datetime.now() - timedelta(hours=periods), periods=periods, freq='h')
    
    # Generate price movements
    np.random.seed(42)  # For reproducible results
    price_changes = np.random.normal(0, 0.002, periods)  # 0.2% volatility
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(0.001, new_price))  # Ensure positive prices
    
    # Create OHLCV data
    ohlcv_data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from close price
        close = price
        open_price = close * (1 + np.random.normal(0, 0.001))
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.002)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.002)))
        volume = np.random.uniform(1000000, 5000000)  # Random volume
        
        ohlcv_data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(ohlcv_data)
    df.set_index('timestamp', inplace=True)
    return df

def create_sample_data_sets(current_price: float, ohlcv_periods: int = 100) -> dict:
    """🎯 Create all sample data sets for testing."""
    return {
        'consensus': {
            'consensus_signal': 'BUY',
            'consensus_score': 0.847,
            'consensus_confidence': 0.905,
            'total_algorithms': 6,
            'agreeing_algorithms': 5,
        },
        'signal': {
            'signal_type': 'BUY',
            'entry': current_price,
            'take_profit': current_price * 1.05,
            'stop_loss': current_price * 0.98,
        },
        'fibonacci': {
            'trend_direction': 'BULLISH',
            'retracement_levels': [
                {'ratio': 0.236, 'price': current_price * 0.98},
                {'ratio': 0.382, 'price': current_price * 0.96},
                {'ratio': 0.618, 'price': current_price * 0.92},
            ],
            'extension_levels': [
                {'ratio': 1.272, 'price': current_price * 1.08},
                {'ratio': 1.618, 'price': current_price * 1.12},
            ]
        },
        'volume_profile': {
            'vpoc': {'price': current_price * 0.995, 'volume': 2500000},
            'high_volume_nodes': [
                {'price': current_price * 0.99, 'volume': 2000000},
                {'price': current_price * 1.01, 'volume': 1800000},
            ],
            'low_volume_nodes': [
                {'price': current_price * 1.03, 'volume': 500000},
            ]
        },
        'ai_analysis': {
            'ensemble_signal': 'BUY',
            'ensemble_confidence': 0.89,
            'prediction_levels': {
                'support': current_price * 0.97,
                'resistance': current_price * 1.04
            }
        },
        'pump_alert': {
            'pump_probability': 0.78,
            'intensity': 3.2,
            'volume_spike_level': 2.5
        },
        'dump_alert': {
            'dump_probability': 0.65,
            'severity_level': 'HIGH',
            'support_levels': [
                {'price': current_price * 0.95},
                {'price': current_price * 0.92},
            ]
        },
        'point_figure': {
            'trend_lines': [
                {'price': current_price * 1.02, 'type': 'bullish'},
                {'price': current_price * 0.98, 'type': 'bearish'},
            ],
            'support_levels': [
                {'price': current_price * 0.96},
                {'price': current_price * 0.94},
            ],
            'resistance_levels': [
                {'price': current_price * 1.04},
                {'price': current_price * 1.06},
            ],
            'price_targets': [
                {'price': current_price * 1.08},
                {'price': current_price * 0.92},
            ]
        },
        'orderbook': {
            'significant_bid_levels': [
                {'price': current_price * 0.999, 'volume': 1000000},
                {'price': current_price * 0.995, 'volume': 800000},
            ],
            'significant_ask_levels': [
                {'price': current_price * 1.001, 'volume': 900000},
                {'price': current_price * 1.005, 'volume': 700000},
            ],
            'support_resistance_levels': [
                {'price': current_price * 0.97, 'type': 'support'},
                {'price': current_price * 1.03, 'type': 'resistance'},
            ]
        },
        'fourier': {
            'fourier_prediction': [current_price * (1 + 0.001 * i) for i in range(20)],
            'dominant_cycle': 2.5,
            'trend_component': [current_price * (1 + 0.0005 * i) for i in range(ohlcv_periods)]
        }
    }

def test_all_clean_charts():
    """🧪 Test all clean chart generation methods."""
    print("🧪 Testing ALL Clean Chart Generation Methods...")
    print("=" * 60)
    
    try:
        # Import chart generator
        from chart_generator import EnhancedChartGenerator
        
        # Create chart generator
        chart_gen = EnhancedChartGenerator()
        
        # Create sample data
        ohlcv_data = create_sample_ohlcv_data(100)
        current_price = ohlcv_data['close'].iloc[-1]
        data_sets = create_sample_data_sets(current_price, 100)
        
        print(f"📊 Sample data created:")
        print(f"  - OHLCV periods: {len(ohlcv_data)}")
        print(f"  - Current price: {current_price:.8f}")
        print(f"  - Price range: {ohlcv_data['low'].min():.8f} - {ohlcv_data['high'].max():.8f}")
        
        # Test results
        test_results = {}
        
        # Test 1: Clean Consensus Chart
        print(f"\n🎯 Test 1: Clean Consensus Chart")
        try:
            consensus_chart = chart_gen.generate_consensus_chart(
                "TEST/USDT", data_sets['consensus'], data_sets['signal'], ohlcv_data
            )
            test_results['consensus'] = consensus_chart and os.path.exists(consensus_chart)
            if test_results['consensus']:
                print(f"  ✅ Clean consensus chart: {os.path.basename(consensus_chart)}")
            else:
                print(f"  ❌ Failed to generate clean consensus chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['consensus'] = False
        
        # Test 2: Clean Fibonacci Chart
        print(f"\n🌀 Test 2: Clean Fibonacci Chart")
        try:
            fibonacci_chart = chart_gen.generate_fibonacci_chart(
                "TEST/USDT", data_sets['fibonacci'], ohlcv_data, current_price
            )
            test_results['fibonacci'] = fibonacci_chart and os.path.exists(fibonacci_chart)
            if test_results['fibonacci']:
                print(f"  ✅ Clean Fibonacci chart: {os.path.basename(fibonacci_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Fibonacci chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['fibonacci'] = False
        
        # Test 3: Clean Volume Profile Chart
        print(f"\n📊 Test 3: Clean Volume Profile Chart")
        try:
            volume_chart = chart_gen.generate_volume_profile_chart(
                "TEST/USDT", data_sets['volume_profile'], ohlcv_data, current_price
            )
            test_results['volume_profile'] = volume_chart and os.path.exists(volume_chart)
            if test_results['volume_profile']:
                print(f"  ✅ Clean Volume Profile chart: {os.path.basename(volume_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Volume Profile chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['volume_profile'] = False
        
        # Test 4: Clean AI Analysis Chart
        print(f"\n🤖 Test 4: Clean AI Analysis Chart")
        try:
            ai_chart = chart_gen.generate_ai_analysis_chart(
                "TEST/USDT", data_sets['ai_analysis'], ohlcv_data, current_price
            )
            test_results['ai_analysis'] = ai_chart and os.path.exists(ai_chart)
            if test_results['ai_analysis']:
                print(f"  ✅ Clean AI Analysis chart: {os.path.basename(ai_chart)}")
            else:
                print(f"  ❌ Failed to generate clean AI Analysis chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['ai_analysis'] = False
        
        # Test 5: Clean Pump Alert Chart
        print(f"\n🚀 Test 5: Clean Pump Alert Chart")
        try:
            pump_chart = chart_gen.generate_pump_alert_chart(
                "TEST/USDT", data_sets['pump_alert'], ohlcv_data, current_price
            )
            test_results['pump_alert'] = pump_chart and os.path.exists(pump_chart)
            if test_results['pump_alert']:
                print(f"  ✅ Clean Pump Alert chart: {os.path.basename(pump_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Pump Alert chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['pump_alert'] = False
        
        # Test 6: Clean Dump Alert Chart
        print(f"\n📉 Test 6: Clean Dump Alert Chart")
        try:
            dump_chart = chart_gen.generate_dump_alert_chart(
                "TEST/USDT", data_sets['dump_alert'], ohlcv_data, current_price
            )
            test_results['dump_alert'] = dump_chart and os.path.exists(dump_chart)
            if test_results['dump_alert']:
                print(f"  ✅ Clean Dump Alert chart: {os.path.basename(dump_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Dump Alert chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['dump_alert'] = False

        # Test 7: Clean Point & Figure Chart
        print(f"\n📈 Test 7: Clean Point & Figure Chart")
        try:
            pf_chart = chart_gen.generate_point_figure_chart(
                "TEST/USDT", data_sets['point_figure'], ohlcv_data, current_price
            )
            test_results['point_figure'] = pf_chart and os.path.exists(pf_chart)
            if test_results['point_figure']:
                print(f"  ✅ Clean Point & Figure chart: {os.path.basename(pf_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Point & Figure chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['point_figure'] = False

        # Test 8: Clean Orderbook Chart
        print(f"\n📋 Test 8: Clean Orderbook Chart")
        try:
            orderbook_chart = chart_gen.generate_orderbook_chart(
                "TEST/USDT", data_sets['orderbook'], ohlcv_data, current_price
            )
            test_results['orderbook'] = orderbook_chart and os.path.exists(orderbook_chart)
            if test_results['orderbook']:
                print(f"  ✅ Clean Orderbook chart: {os.path.basename(orderbook_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Orderbook chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['orderbook'] = False

        # Test 9: Clean Fourier Chart
        print(f"\n🌊 Test 9: Clean Fourier Chart")
        try:
            fourier_chart = chart_gen.generate_fourier_chart(
                "TEST/USDT", data_sets['fourier'], ohlcv_data, current_price
            )
            test_results['fourier'] = fourier_chart and os.path.exists(fourier_chart)
            if test_results['fourier']:
                print(f"  ✅ Clean Fourier chart: {os.path.basename(fourier_chart)}")
            else:
                print(f"  ❌ Failed to generate clean Fourier chart")
        except Exception as e:
            print(f"  ❌ Error: {e}")
            test_results['fourier'] = False
        
        # Summary
        print(f"\n🎯 Test Results Summary:")
        print(f"=" * 40)
        passed = sum(test_results.values())
        total = len(test_results)
        
        for chart_type, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {chart_type.upper()}: {status}")
        
        print(f"\n📊 Overall Results: {passed}/{total} tests passed")
        
        if passed == total:
            print(f"\n🎉 ALL CLEAN CHART TESTS PASSED!")
            print(f"🚀 All 9 chart types now generate clean candlestick-only charts!")
            print(f"📁 Charts saved in: {chart_gen.output_dir}")
            print(f"\n🎨 Clean Chart Features:")
            print(f"  ✅ Only candlesticks + technical lines")
            print(f"  ✅ No text annotations or information panels")
            print(f"  ✅ Beautiful colors and clean styling")
            print(f"  ✅ Perfect for Telegram mobile viewing")
            print(f"\n📊 Chart Types Upgraded:")
            print(f"  🎯 Consensus Signal Charts")
            print(f"  🌀 Fibonacci Analysis Charts")
            print(f"  📊 Volume Profile Charts")
            print(f"  🤖 AI Analysis Charts")
            print(f"  🚀 Pump Alert Charts")
            print(f"  📉 Dump Alert Charts")
            print(f"  📈 Point & Figure Charts")
            print(f"  📋 Orderbook Analysis Charts")
            print(f"  🌊 Fourier Analysis Charts")
        else:
            print(f"\n⚠️ {total - passed} tests failed - check error messages above")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Error testing clean chart generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 ALL Clean Chart Generation Test Suite")
    print("=" * 70)
    
    success = test_all_clean_charts()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 Clean chart system is ready for production!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 Check the error messages above.")
