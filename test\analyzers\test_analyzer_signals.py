#!/usr/bin/env python3
"""
🧪 Test script để kiểm tra Volume Profile và Orderbook analyzers không trả về NONE
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_sample_data():
    """Tạo dữ liệu mẫu để test"""
    try:
        # Create 100 candles of sample data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=100), 
                             end=datetime.now(), freq='1H')
        
        # Generate realistic price data
        base_price = 50000
        prices = []
        volumes = []
        
        for i in range(len(dates)):
            change = np.random.normal(0, 0.02)
            if i == 0:
                price = base_price
            else:
                price = prices[-1] * (1 + change)
            prices.append(price)
            volumes.append(np.random.uniform(1000, 10000))
        
        # Create OHLCV data
        data = []
        for i, (date, price, volume) in enumerate(zip(dates, prices, volumes)):
            high = price * (1 + abs(np.random.normal(0, 0.01)))
            low = price * (1 - abs(np.random.normal(0, 0.01)))
            open_price = price * (1 + np.random.normal(0, 0.005))
            close_price = price * (1 + np.random.normal(0, 0.005))
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ Created sample data: {len(df)} candles")
        return df
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return None

def create_sample_orderbook():
    """Tạo orderbook mẫu"""
    try:
        current_price = 50000
        
        # Create bid levels
        bid_levels = []
        for i in range(10):
            price = current_price * (1 - (i + 1) * 0.001)  # 0.1% steps down
            quantity = np.random.uniform(1, 100)
            bid_levels.append({"price": price, "quantity": quantity})
        
        # Create ask levels
        ask_levels = []
        for i in range(10):
            price = current_price * (1 + (i + 1) * 0.001)  # 0.1% steps up
            quantity = np.random.uniform(1, 100)
            ask_levels.append({"price": price, "quantity": quantity})
        
        print(f"✅ Created sample orderbook: {len(bid_levels)} bids, {len(ask_levels)} asks")
        return bid_levels, ask_levels
        
    except Exception as e:
        print(f"❌ Error creating sample orderbook: {e}")
        return [], []

def test_volume_profile_analyzer():
    """Test Volume Profile Analyzer"""
    print("📊 TESTING VOLUME PROFILE ANALYZER")
    print("=" * 60)
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        # Create analyzer
        analyzer = VolumeProfileAnalyzer()
        
        # Create sample data
        df = create_sample_data()
        if df is None:
            return False
        
        current_price = df['close'].iloc[-1]
        
        # Test analysis
        print(f"🧪 Testing Volume Profile analysis for sample data...")
        print(f"  📊 Data points: {len(df)}")
        print(f"  💰 Current price: {current_price:.2f}")
        
        # Run analysis
        result = analyzer.analyze(df, current_price)
        
        # Check result
        if result and result.get("status") == "success":
            signals = result.get("signals", {})
            primary_signal = signals.get("primary_signal", "NONE")
            confidence = signals.get("confidence", 0)
            
            print(f"  📊 Analysis result: {result.get('status')}")
            print(f"  🎯 Primary signal: {primary_signal}")
            print(f"  💪 Confidence: {confidence:.3f}")
            
            # Check if signal is valid
            if primary_signal in ["BUY", "SELL"]:
                print(f"  ✅ Volume Profile: Valid signal generated")
                return True
            else:
                print(f"  ❌ Volume Profile: Invalid signal '{primary_signal}'")
                return False
        else:
            print(f"  ❌ Volume Profile: Analysis failed")
            return False
            
    except ImportError as e:
        print(f"❌ Cannot import VolumeProfileAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Volume Profile analyzer: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_orderbook_analyzer():
    """Test Orderbook Analyzer"""
    print("\n📈 TESTING ORDERBOOK ANALYZER")
    print("=" * 60)
    
    try:
        from orderbook_analyzer import OrderbookAnalyzer
        
        # Create analyzer
        analyzer = OrderbookAnalyzer()
        
        # Create sample data
        bid_levels, ask_levels = create_sample_orderbook()
        if not bid_levels or not ask_levels:
            return False
        
        current_price = (bid_levels[0]["price"] + ask_levels[0]["price"]) / 2
        
        # Test analysis
        print(f"🧪 Testing Orderbook analysis for sample data...")
        print(f"  📊 Bid levels: {len(bid_levels)}")
        print(f"  📊 Ask levels: {len(ask_levels)}")
        print(f"  💰 Current price: {current_price:.2f}")
        
        # Run analysis
        result = analyzer.analyze("TEST/USDT", bid_levels, ask_levels, current_price)
        
        # Check result
        if result and result.get("analysis_complete"):
            signals = result.get("signals", {})
            primary_signal = signals.get("primary_signal", "NONE")
            confidence = signals.get("confidence", 0)
            
            print(f"  📊 Analysis result: Complete")
            print(f"  🎯 Primary signal: {primary_signal}")
            print(f"  💪 Confidence: {confidence:.3f}")
            
            # Check if signal is valid
            if primary_signal in ["BUY", "SELL", "WEAK_BUY", "WEAK_SELL"]:
                print(f"  ✅ Orderbook: Valid signal generated")
                return True
            else:
                print(f"  ❌ Orderbook: Invalid signal '{primary_signal}'")
                return False
        else:
            print(f"  ❌ Orderbook: Analysis failed")
            return False
            
    except ImportError as e:
        print(f"❌ Cannot import OrderbookAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Orderbook analyzer: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_scenarios():
    """Test multiple scenarios để đảm bảo không có NONE signals"""
    print("\n🔄 TESTING MULTIPLE SCENARIOS")
    print("=" * 60)
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        from orderbook_analyzer import OrderbookAnalyzer
        
        vp_analyzer = VolumeProfileAnalyzer()
        ob_analyzer = OrderbookAnalyzer()
        
        test_scenarios = [
            "Normal market",
            "High volatility",
            "Low volume",
            "Extreme prices",
            "Minimal data"
        ]
        
        vp_results = []
        ob_results = []
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n🧪 Scenario {i+1}: {scenario}")
            
            # Create different data for each scenario
            if scenario == "High volatility":
                # High volatility data
                df = create_sample_data()
                if df is not None:
                    df['high'] = df['high'] * 1.05
                    df['low'] = df['low'] * 0.95
            elif scenario == "Low volume":
                # Low volume data
                df = create_sample_data()
                if df is not None:
                    df['volume'] = df['volume'] * 0.1
            elif scenario == "Minimal data":
                # Minimal data (only 5 candles)
                df = create_sample_data()
                if df is not None:
                    df = df.tail(5)
            else:
                df = create_sample_data()
            
            if df is None:
                continue
                
            current_price = df['close'].iloc[-1]
            
            # Test Volume Profile
            try:
                vp_result = vp_analyzer.analyze(df, current_price)
                vp_signal = vp_result.get("signals", {}).get("primary_signal", "NONE") if vp_result else "NONE"
                vp_results.append(vp_signal)
                print(f"  📊 VP Signal: {vp_signal}")
            except Exception as e:
                print(f"  ❌ VP Error: {e}")
                vp_results.append("ERROR")
            
            # Test Orderbook
            try:
                bid_levels, ask_levels = create_sample_orderbook()
                ob_result = ob_analyzer.analyze("TEST/USDT", bid_levels, ask_levels, current_price)
                ob_signal = ob_result.get("signals", {}).get("primary_signal", "NONE") if ob_result else "NONE"
                ob_results.append(ob_signal)
                print(f"  📈 OB Signal: {ob_signal}")
            except Exception as e:
                print(f"  ❌ OB Error: {e}")
                ob_results.append("ERROR")
        
        # Analyze results
        print(f"\n📊 SCENARIO TEST RESULTS:")
        print(f"  Volume Profile signals: {vp_results}")
        print(f"  Orderbook signals: {ob_results}")
        
        # Check for NONE signals
        vp_none_count = vp_results.count("NONE")
        ob_none_count = ob_results.count("NONE")
        
        print(f"\n🎯 NONE Signal Analysis:")
        print(f"  Volume Profile NONE signals: {vp_none_count}/{len(vp_results)}")
        print(f"  Orderbook NONE signals: {ob_none_count}/{len(ob_results)}")
        
        success = (vp_none_count == 0) and (ob_none_count == 0)
        
        if success:
            print(f"  ✅ SUCCESS: No NONE signals detected!")
        else:
            print(f"  ❌ FAILURE: NONE signals still present")
        
        return success
        
    except Exception as e:
        print(f"❌ Error in multiple scenarios test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 ANALYZER SIGNAL VALIDATION TEST")
    print("=" * 70)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Volume Profile Analyzer
    vp_result = test_volume_profile_analyzer()
    
    # Test 2: Orderbook Analyzer
    ob_result = test_orderbook_analyzer()
    
    # Test 3: Multiple scenarios
    scenario_result = test_multiple_scenarios()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 ANALYZER SIGNAL TEST RESULTS")
    print("=" * 70)
    
    tests = [
        ("Volume Profile Analyzer", vp_result),
        ("Orderbook Analyzer", ob_result),
        ("Multiple Scenarios", scenario_result)
    ]
    
    all_passed = True
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Volume Profile analyzer generates valid signals")
        print("✅ Orderbook analyzer generates valid signals")
        print("✅ No NONE signals detected in multiple scenarios")
        print("✅ Analyzers ready for consensus analysis")
        
        print(f"\n🔧 Expected behavior:")
        print(f"  • Volume Profile: Always returns BUY or SELL")
        print(f"  • Orderbook: Always returns BUY, SELL, WEAK_BUY, or WEAK_SELL")
        print(f"  • Consensus analysis will receive valid signals")
        print(f"  • No more 'signal=NONE' debug messages")
    else:
        print("❌ SOME TESTS FAILED")
        print("Analyzers may still return NONE signals")
        print("Check the fallback mechanisms")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
