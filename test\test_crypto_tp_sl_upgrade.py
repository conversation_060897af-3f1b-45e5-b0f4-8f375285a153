#!/usr/bin/env python3
"""
🧪 TEST: Crypto TP/SL Upgrade
Test để kiểm tra nâng cấp TP/SL cho crypto volatility
"""

import sys
import os
import pandas as pd
import numpy as np

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_mock_crypto_data():
    """Create mock crypto OHLCV data with high volatility"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    
    # Simulate crypto volatility (higher than stocks)
    np.random.seed(42)
    base_price = 50000  # BTC-like price
    
    # High volatility returns (5-10% daily moves common in crypto)
    returns = np.random.normal(0, 0.05, 100)  # 5% hourly volatility
    
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    data = []
    for i in range(len(dates)):
        price = prices[i]
        high = price * (1 + abs(np.random.normal(0, 0.02)))
        low = price * (1 - abs(np.random.normal(0, 0.02)))
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': dates[i],
            'open': price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume
        })
    
    return pd.DataFrame(data).set_index('timestamp')

def test_crypto_mode_vs_traditional():
    """Test crypto mode vs traditional mode differences"""
    print("🧪 === TESTING CRYPTO MODE VS TRADITIONAL MODE ===")
    
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        # Create mock data
        ohlcv_data = create_mock_crypto_data()
        entry_price = 50000.0
        signal_type = "BUY"
        analysis_data = {}
        
        print("🔧 Testing Traditional Mode...")
        traditional_analyzer = IntelligentTPSLAnalyzer(crypto_mode=False)
        traditional_result = traditional_analyzer.calculate_intelligent_tp_sl(
            signal_type, entry_price, ohlcv_data, analysis_data
        )
        
        print("🚀 Testing Crypto Mode...")
        crypto_analyzer = IntelligentTPSLAnalyzer(crypto_mode=True)
        crypto_result = crypto_analyzer.calculate_intelligent_tp_sl(
            signal_type, entry_price, ohlcv_data, analysis_data
        )
        
        if traditional_result["status"] == "success" and crypto_result["status"] == "success":
            # Compare results
            trad_tp = traditional_result["take_profit"]
            trad_sl = traditional_result["stop_loss"]
            trad_risk = abs(entry_price - trad_sl)
            trad_reward = abs(trad_tp - entry_price)
            
            crypto_tp = crypto_result["take_profit"]
            crypto_sl = crypto_result["stop_loss"]
            crypto_risk = abs(entry_price - crypto_sl)
            crypto_reward = abs(crypto_tp - entry_price)
            
            print(f"\n📊 COMPARISON RESULTS:")
            print(f"Entry Price: ${entry_price:,.2f}")
            print(f"\n📈 Traditional Mode:")
            print(f"  Take Profit: ${trad_tp:,.2f} (+{(trad_reward/entry_price)*100:.2f}%)")
            print(f"  Stop Loss: ${trad_sl:,.2f} (-{(trad_risk/entry_price)*100:.2f}%)")
            print(f"  Risk/Reward: {traditional_result['risk_reward_ratio']:.2f}")
            
            print(f"\n🚀 Crypto Mode:")
            print(f"  Take Profit: ${crypto_tp:,.2f} (+{(crypto_reward/entry_price)*100:.2f}%)")
            print(f"  Stop Loss: ${crypto_sl:,.2f} (-{(crypto_risk/entry_price)*100:.2f}%)")
            print(f"  Risk/Reward: {crypto_result['risk_reward_ratio']:.2f}")
            
            # Calculate improvements
            sl_improvement = (crypto_risk / trad_risk - 1) * 100
            tp_improvement = (crypto_reward / trad_reward - 1) * 100
            
            print(f"\n✅ CRYPTO IMPROVEMENTS:")
            print(f"  Stop Loss Distance: {sl_improvement:+.1f}% wider (better for crypto volatility)")
            print(f"  Take Profit Distance: {tp_improvement:+.1f}% wider (better for crypto opportunities)")
            
            # Validate improvements
            if sl_improvement > 20:  # At least 20% wider SL
                print(f"  ✅ SL improvement: {sl_improvement:.1f}% - GOOD for crypto volatility")
            else:
                print(f"  ⚠️ SL improvement: {sl_improvement:.1f}% - May need more adjustment")
            
            if tp_improvement > 10:  # At least 10% wider TP
                print(f"  ✅ TP improvement: {tp_improvement:.1f}% - GOOD for crypto opportunities")
            else:
                print(f"  ⚠️ TP improvement: {tp_improvement:.1f}% - May need more adjustment")
            
            return True
        else:
            print("❌ One or both analyzers failed")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_volatility_scenarios():
    """Test different volatility scenarios"""
    print("\n💥 Testing different volatility scenarios...")
    
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        analyzer = IntelligentTPSLAnalyzer(crypto_mode=True)
        entry_price = 50000.0
        
        scenarios = [
            {"name": "Low Volatility", "vol": 0.02},    # 2% volatility
            {"name": "Medium Volatility", "vol": 0.05}, # 5% volatility  
            {"name": "High Volatility", "vol": 0.10},   # 10% volatility
            {"name": "Extreme Volatility", "vol": 0.15} # 15% volatility
        ]
        
        print(f"\n📊 VOLATILITY SCENARIO TESTING:")
        
        for scenario in scenarios:
            # Create data with specific volatility
            np.random.seed(42)
            dates = pd.date_range(start='2024-01-01', periods=50, freq='1H')
            returns = np.random.normal(0, scenario["vol"], 50)
            
            prices = [entry_price]
            for ret in returns:
                prices.append(prices[-1] * (1 + ret))
            
            ohlcv_data = pd.DataFrame({
                'open': prices[:-1],
                'high': [p * 1.02 for p in prices[:-1]],
                'low': [p * 0.98 for p in prices[:-1]],
                'close': prices[:-1],
                'volume': [1000] * (len(prices)-1)
            }, index=dates)
            
            result = analyzer.calculate_intelligent_tp_sl(
                "BUY", entry_price, ohlcv_data, {}
            )
            
            if result["status"] == "success":
                risk = abs(entry_price - result["stop_loss"])
                reward = abs(result["take_profit"] - entry_price)
                risk_pct = (risk / entry_price) * 100
                reward_pct = (reward / entry_price) * 100
                
                print(f"\n  {scenario['name']} ({scenario['vol']*100:.0f}% vol):")
                print(f"    SL Distance: {risk_pct:.2f}%")
                print(f"    TP Distance: {reward_pct:.2f}%")
                print(f"    R:R Ratio: {result['risk_reward_ratio']:.2f}")
                
                # Validate that higher volatility = wider SL
                if scenario["vol"] >= 0.10 and risk_pct >= 4.0:
                    print(f"    ✅ High volatility handled well (SL ≥ 4%)")
                elif scenario["vol"] <= 0.02 and risk_pct <= 3.0:
                    print(f"    ✅ Low volatility handled well (SL ≤ 3%)")
                else:
                    print(f"    ⚠️ Volatility handling could be improved")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in volatility testing: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 === CRYPTO TP/SL UPGRADE TEST ===")
    
    success1 = test_crypto_mode_vs_traditional()
    success2 = test_volatility_scenarios()
    
    if success1 and success2:
        print("\n🎉 SUCCESS: Crypto TP/SL upgrades working!")
        print("✅ Crypto mode provides wider SL ranges")
        print("✅ Volatility-adaptive adjustments working")
        print("✅ Better suited for crypto market characteristics")
    else:
        print("\n❌ FAILED: Some tests failed")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
