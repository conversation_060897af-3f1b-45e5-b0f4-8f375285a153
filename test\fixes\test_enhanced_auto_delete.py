#!/usr/bin/env python3
"""
🧪 Test script để kiểm tra Enhanced Auto-Delete functionality
"""

import os
import time
import shutil
from datetime import datetime

def test_auto_delete_configuration():
    """Kiểm tra cấu hình auto-delete hiện tại"""
    print("🔧 KIỂM TRA CẤU HÌNH AUTO-DELETE")
    print("=" * 60)
    
    try:
        # Import chart generator để kiểm tra cấu hình
        from chart_generator import ChartGenerator
        
        # Initialize với mock telegram notifier
        class MockTelegramNotifier:
            def send_photo(self, photo_path, caption, chat_id, parse_mode="HTML"):
                print(f"    📤 MOCK SEND: {os.path.basename(photo_path)} → {chat_id}")
                return True  # Simulate successful send
        
        mock_notifier = MockTelegramNotifier()
        chart_gen = ChartGenerator(output_dir="test_charts", telegram_notifier=mock_notifier)
        
        print(f"📊 Cấu hình Auto-Delete:")
        print(f"  🗑️ Auto-delete after send: {'✅ ENABLED' if chart_gen.auto_delete_after_send else '❌ DISABLED'}")
        print(f"  🗑️ Auto-delete failed charts: {'✅ ENABLED' if chart_gen.auto_delete_failed_charts else '❌ DISABLED'}")
        print(f"  ⏰ Keep charts minutes: {chart_gen.keep_charts_minutes} minutes")
        print(f"  💾 Max storage: {chart_gen.max_chart_storage_mb}MB")
        print(f"  📱 Auto-send charts: {'✅ ENABLED' if chart_gen.auto_send_charts else '❌ DISABLED'}")
        
        return chart_gen
        
    except ImportError as e:
        print(f"❌ Cannot import ChartGenerator: {e}")
        return None
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return None

def create_test_chart_file(chart_gen, filename="test_chart.png"):
    """Tạo file chart test"""
    try:
        # Create test charts directory
        os.makedirs(chart_gen.output_dir, exist_ok=True)
        
        # Create a simple test image file
        test_chart_path = os.path.join(chart_gen.output_dir, filename)
        
        # Create a simple PNG file (minimal valid PNG)
        png_header = b'\x89PNG\r\n\x1a\n'
        png_end = b'\x00\x00\x00\x00IEND\xaeB`\x82'
        
        # Create minimal PNG content
        with open(test_chart_path, 'wb') as f:
            f.write(png_header)
            f.write(b'\x00' * 100)  # Some dummy data
            f.write(png_end)
        
        # Track creation
        chart_gen._track_chart_creation(test_chart_path)
        
        print(f"  ✅ Created test chart: {test_chart_path}")
        return test_chart_path
        
    except Exception as e:
        print(f"  ❌ Error creating test chart: {e}")
        return None

def test_immediate_auto_delete(chart_gen):
    """Test immediate auto-delete after successful send"""
    print("\n🚀 TESTING IMMEDIATE AUTO-DELETE AFTER SUCCESSFUL SEND")
    print("=" * 60)
    
    try:
        # Create test chart
        test_chart = create_test_chart_file(chart_gen, "immediate_delete_test.png")
        if not test_chart:
            return False
        
        print(f"📊 Test scenario: Successful send → Immediate delete")
        print(f"  📁 Chart file: {os.path.basename(test_chart)}")
        print(f"  📏 File size: {os.path.getsize(test_chart) / 1024:.1f}KB")
        print(f"  ✅ File exists before send: {os.path.exists(test_chart)}")
        
        # Test the enhanced auto-delete function directly
        print(f"\n🔄 Simulating successful send + auto-delete...")
        
        # Call the enhanced auto-delete function with success=True
        delete_result = chart_gen._auto_delete_chart_after_send(test_chart, send_success=True)
        
        print(f"\n📊 Results:")
        print(f"  🗑️ Auto-delete executed: {'✅ YES' if delete_result else '❌ NO'}")
        print(f"  📁 File exists after delete: {'❌ STILL EXISTS' if os.path.exists(test_chart) else '✅ DELETED'}")
        
        # Check statistics
        stats = chart_gen.chart_stats['auto_delete']
        print(f"  📊 Delete stats: {stats['deleted_after_send']} successful deletes")
        print(f"  💾 Space saved: {stats['space_saved_mb']:.3f}MB")
        
        success = delete_result and not os.path.exists(test_chart)
        print(f"\n🎯 Test result: {'✅ PASSED' if success else '❌ FAILED'}")
        
        return success
        
    except Exception as e:
        print(f"❌ Error in immediate auto-delete test: {e}")
        return False

def test_failed_send_cleanup(chart_gen):
    """Test cleanup of failed send charts"""
    print("\n🗑️ TESTING FAILED SEND CLEANUP")
    print("=" * 60)
    
    try:
        # Create test chart
        test_chart = create_test_chart_file(chart_gen, "failed_send_test.png")
        if not test_chart:
            return False
        
        print(f"📊 Test scenario: Failed send → Cleanup delete")
        print(f"  📁 Chart file: {os.path.basename(test_chart)}")
        print(f"  ✅ File exists before test: {os.path.exists(test_chart)}")
        
        # Test the auto-delete function with failed send
        print(f"\n🔄 Simulating failed send + cleanup...")
        
        delete_result = chart_gen._auto_delete_chart_after_send(test_chart, send_success=False)
        
        print(f"\n📊 Results:")
        print(f"  🗑️ Cleanup executed: {'✅ YES' if delete_result else '❌ NO'}")
        print(f"  📁 File exists after cleanup: {'❌ STILL EXISTS' if os.path.exists(test_chart) else '✅ DELETED'}")
        
        # Check if cleanup is enabled
        cleanup_enabled = chart_gen.auto_delete_failed_charts
        expected_result = cleanup_enabled
        
        print(f"  ⚙️ Cleanup enabled: {'✅ YES' if cleanup_enabled else '❌ NO'}")
        print(f"  🎯 Expected result: {'DELETE' if expected_result else 'KEEP'}")
        
        success = (delete_result == expected_result) and (os.path.exists(test_chart) != expected_result)
        print(f"\n🎯 Test result: {'✅ PASSED' if success else '❌ FAILED'}")
        
        return success
        
    except Exception as e:
        print(f"❌ Error in failed send cleanup test: {e}")
        return False

def test_full_send_and_delete_workflow(chart_gen):
    """Test full workflow: create → send → auto-delete"""
    print("\n🔄 TESTING FULL SEND + AUTO-DELETE WORKFLOW")
    print("=" * 60)
    
    try:
        # Create test chart
        test_chart = create_test_chart_file(chart_gen, "full_workflow_test.png")
        if not test_chart:
            return False
        
        print(f"📊 Test scenario: Full workflow simulation")
        print(f"  📁 Chart file: {os.path.basename(test_chart)}")
        print(f"  ✅ File exists: {os.path.exists(test_chart)}")
        
        # Simulate the full send workflow
        print(f"\n🔄 Simulating full send workflow...")
        
        # Step 1: Send chart to Telegram (mock)
        caption = "🧪 Test Chart - Auto-Delete Verification"
        target_chat = "-1002608968097_test"
        
        send_success = chart_gen._send_chart_to_telegram(test_chart, caption, target_chat)
        
        print(f"\n📊 Send Results:")
        print(f"  📤 Send successful: {'✅ YES' if send_success else '❌ NO'}")
        
        # Step 2: Auto-delete should have been triggered automatically
        file_exists_after_send = os.path.exists(test_chart)
        
        print(f"  🗑️ File exists after send: {'❌ STILL EXISTS' if file_exists_after_send else '✅ AUTO-DELETED'}")
        
        # If file still exists and send was successful, manually trigger auto-delete
        if file_exists_after_send and send_success:
            print(f"  🔧 Manually triggering auto-delete...")
            delete_result = chart_gen._auto_delete_chart_after_send(test_chart, send_success)
            file_exists_after_manual = os.path.exists(test_chart)
            print(f"  🗑️ Manual delete result: {'✅ DELETED' if delete_result and not file_exists_after_manual else '❌ FAILED'}")
        
        # Final check
        final_file_exists = os.path.exists(test_chart)
        expected_behavior = not final_file_exists if send_success else True
        
        print(f"\n🎯 Final Results:")
        print(f"  📁 File finally deleted: {'✅ YES' if not final_file_exists else '❌ NO'}")
        print(f"  🎯 Expected behavior: {'DELETE if sent' if send_success else 'KEEP if failed'}")
        
        success = (not final_file_exists) if send_success else True
        print(f"  🎯 Test result: {'✅ PASSED' if success else '❌ FAILED'}")
        
        return success
        
    except Exception as e:
        print(f"❌ Error in full workflow test: {e}")
        return False

def cleanup_test_files(chart_gen):
    """Cleanup test files"""
    try:
        if os.path.exists(chart_gen.output_dir):
            shutil.rmtree(chart_gen.output_dir)
            print(f"🧹 Cleaned up test directory: {chart_gen.output_dir}")
    except Exception as e:
        print(f"⚠️ Error cleaning up test files: {e}")

def main():
    """Main test function"""
    print("🧪 ENHANCED AUTO-DELETE FUNCTIONALITY TEST")
    print("=" * 70)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Check configuration
    chart_gen = test_auto_delete_configuration()
    if not chart_gen:
        print("❌ Cannot proceed without ChartGenerator")
        return
    
    # Test 2: Immediate auto-delete after successful send
    test1_result = test_immediate_auto_delete(chart_gen)
    
    # Test 3: Failed send cleanup
    test2_result = test_failed_send_cleanup(chart_gen)
    
    # Test 4: Full workflow
    test3_result = test_full_send_and_delete_workflow(chart_gen)
    
    # Cleanup
    cleanup_test_files(chart_gen)
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 ENHANCED AUTO-DELETE TEST RESULTS")
    print("=" * 70)
    
    tests = [
        ("Immediate Auto-Delete", test1_result),
        ("Failed Send Cleanup", test2_result),
        ("Full Workflow", test3_result)
    ]
    
    all_passed = True
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Enhanced auto-delete functionality is working correctly")
        print("✅ Charts are deleted immediately after successful send")
        print("✅ Failed charts are cleaned up properly")
        print("✅ Full workflow operates as expected")
        
        print(f"\n🔧 Key improvements:")
        print(f"  • Immediate deletion after successful send")
        print(f"  • Enhanced feedback and logging")
        print(f"  • Better error handling")
        print(f"  • Verification of deletion success")
        print(f"  • Improved statistics tracking")
    else:
        print("❌ SOME TESTS FAILED")
        print("Issues may remain in the auto-delete functionality")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
