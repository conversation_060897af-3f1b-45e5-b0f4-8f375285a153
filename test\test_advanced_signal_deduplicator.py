#!/usr/bin/env python3
"""
🧪 TEST ADVANCED SIGNAL DEDUPLICATOR V2.0
Test để kiểm tra hệ thống nâng cao xử lý tín hiệu trùng lặp
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_advanced_signal_deduplicator():
    """Test Advanced Signal Deduplicator V2.0"""
    print("🧪 === TESTING ADVANCED SIGNAL DEDUPLICATOR V2.0 ===")
    print(f"⏰ Test started at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    try:
        from advanced_signal_deduplicator import AdvancedSignalDeduplicator
        print("✅ Advanced Signal Deduplicator imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Advanced Signal Deduplicator: {e}")
        return False
    
    # Test 1: Initialize deduplicator
    print(f"\n🧪 TEST 1: Initialize Advanced Signal Deduplicator")
    
    try:
        deduplicator = AdvancedSignalDeduplicator(
            time_window_minutes=30,
            similarity_threshold=0.85,
            price_tolerance_percent=0.5,
            max_history_size=100
        )
        print("✅ Deduplicator initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize deduplicator: {e}")
        return False
    
    # Test 2: Test unique signals
    print(f"\n🧪 TEST 2: Test unique signals")
    
    unique_signals = [
        {
            "coin": "BTCUSDT",
            "signal_type": "BUY",
            "entry": 50000.0,
            "take_profit": 52000.0,
            "stop_loss": 48000.0,
            "analyzer_type": "ai_analysis"
        },
        {
            "coin": "ETHUSDT", 
            "signal_type": "SELL",
            "entry": 3000.0,
            "take_profit": 2900.0,
            "stop_loss": 3100.0,
            "analyzer_type": "fibonacci"
        },
        {
            "coin": "ADAUSDT",
            "signal_type": "BUY", 
            "entry": 1.5,
            "take_profit": 1.6,
            "stop_loss": 1.4,
            "analyzer_type": "volume_profile"
        }
    ]
    
    unique_count = 0
    for signal in unique_signals:
        is_duplicate, reason, similar_signal = deduplicator.is_duplicate_signal(signal)
        if not is_duplicate:
            unique_count += 1
            print(f"  ✅ {signal['coin']} {signal['signal_type']}: UNIQUE")
        else:
            print(f"  ❌ {signal['coin']} {signal['signal_type']}: DUPLICATE ({reason})")
    
    print(f"  📊 Unique signals: {unique_count}/{len(unique_signals)}")
    
    # Test 3: Test exact duplicates
    print(f"\n🧪 TEST 3: Test exact duplicate detection")
    
    # Try to add the same signal again
    duplicate_signal = unique_signals[0].copy()  # Same as first signal
    is_duplicate, reason, similar_signal = deduplicator.is_duplicate_signal(duplicate_signal)
    
    if is_duplicate:
        print(f"  ✅ Exact duplicate correctly detected: {reason}")
    else:
        print(f"  ❌ Exact duplicate NOT detected")
    
    # Test 4: Test similar signals
    print(f"\n🧪 TEST 4: Test similar signal detection")
    
    # Create similar signal (same coin, type, but slightly different price)
    similar_signal = {
        "coin": "BTCUSDT",
        "signal_type": "BUY",
        "entry": 50100.0,  # 0.2% difference
        "take_profit": 52100.0,
        "stop_loss": 48100.0,
        "analyzer_type": "fourier"  # Different analyzer
    }
    
    is_duplicate, reason, found_signal = deduplicator.is_duplicate_signal(similar_signal)
    
    if is_duplicate:
        print(f"  ✅ Similar signal correctly detected: {reason}")
        if found_signal:
            print(f"    🔍 Similarity score: {found_signal.similarity_score:.2%}")
    else:
        print(f"  ❌ Similar signal NOT detected")
    
    # Test 5: Test cross-analyzer correlation
    print(f"\n🧪 TEST 5: Test cross-analyzer correlation")
    
    # Create signal from different analyzer but same coin/type
    cross_analyzer_signal = {
        "coin": "ETHUSDT",
        "signal_type": "SELL",
        "entry": 3010.0,  # Slightly different price
        "take_profit": 2910.0,
        "stop_loss": 3110.0,
        "analyzer_type": "point_figure"  # Different analyzer
    }
    
    is_duplicate, reason, found_signal = deduplicator.is_duplicate_signal(cross_analyzer_signal)
    
    if is_duplicate and "correlation" in reason:
        print(f"  ✅ Cross-analyzer correlation correctly detected: {reason}")
    else:
        print(f"  ⚠️ Cross-analyzer correlation not detected (may be normal)")
    
    # Test 6: Test time window
    print(f"\n🧪 TEST 6: Test time window filtering")
    
    # Wait a moment and test recent signal detection
    time.sleep(1)
    
    recent_signal = {
        "coin": "ADAUSDT",
        "signal_type": "BUY",
        "entry": 1.51,  # Very similar price
        "take_profit": 1.61,
        "stop_loss": 1.41,
        "analyzer_type": "orderbook"
    }
    
    is_duplicate, reason, found_signal = deduplicator.is_duplicate_signal(recent_signal)
    
    if is_duplicate:
        print(f"  ✅ Recent signal correctly detected as duplicate: {reason}")
    else:
        print(f"  ⚠️ Recent signal not detected as duplicate")
    
    # Test 7: Get statistics
    print(f"\n🧪 TEST 7: Get deduplication statistics")
    
    try:
        stats = deduplicator.get_statistics()
        print(f"  📊 Statistics retrieved successfully:")
        print(f"    🔢 Total processed: {stats.get('total_processed', 0)}")
        print(f"    🚫 Duplicates found: {stats.get('duplicates_found', 0)}")
        print(f"    🔍 Similar signals: {stats.get('similar_signals', 0)}")
        print(f"    ✅ Unique signals: {stats.get('unique_signals', 0)}")
        print(f"    📈 Duplicate rate: {stats.get('duplicate_rate', 0):.1f}%")
        print(f"    📚 History size: {stats.get('history_size', 0)}")
        print(f"    💾 Cache size: {stats.get('cache_size', 0)}")
    except Exception as e:
        print(f"  ❌ Error getting statistics: {e}")
    
    # Test 8: Performance test
    print(f"\n🧪 TEST 8: Performance test")
    
    start_time = time.time()
    performance_signals = []
    
    # Generate 50 test signals
    for i in range(50):
        signal = {
            "coin": f"TEST{i}USDT",
            "signal_type": "BUY" if i % 2 == 0 else "SELL",
            "entry": 100.0 + i,
            "take_profit": 110.0 + i,
            "stop_loss": 90.0 + i,
            "analyzer_type": ["ai", "fibonacci", "volume_profile", "fourier", "point_figure"][i % 5]
        }
        performance_signals.append(signal)
    
    # Process all signals
    processed = 0
    duplicates = 0
    for signal in performance_signals:
        is_duplicate, reason, found_signal = deduplicator.is_duplicate_signal(signal)
        processed += 1
        if is_duplicate:
            duplicates += 1
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"  📊 Performance results:")
    print(f"    🔢 Signals processed: {processed}")
    print(f"    🚫 Duplicates detected: {duplicates}")
    print(f"    ⏱️ Processing time: {processing_time:.3f} seconds")
    print(f"    🚀 Signals per second: {processed/processing_time:.1f}")
    
    # Final statistics
    print(f"\n📊 FINAL STATISTICS:")
    final_stats = deduplicator.get_statistics()
    total = final_stats.get('total_processed', 0)
    blocked = final_stats.get('duplicates_found', 0) + final_stats.get('similar_signals', 0)
    
    if total > 0:
        efficiency = (blocked / total) * 100
        print(f"  🎯 Deduplication efficiency: {efficiency:.1f}%")
        print(f"  ✅ System successfully blocked {blocked}/{total} duplicate/similar signals")
    
    print(f"\n✅ Advanced Signal Deduplicator V2.0 test completed!")
    print(f"⏰ Test finished at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    return True

if __name__ == "__main__":
    success = test_advanced_signal_deduplicator()
    if success:
        print(f"\n🎉 ADVANCED SIGNAL DEDUPLICATOR TEST PASSED!")
    else:
        print(f"\n❌ ADVANCED SIGNAL DEDUPLICATOR TEST FAILED!")
    
    sys.exit(0 if success else 1)
