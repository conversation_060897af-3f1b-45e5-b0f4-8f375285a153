#!/usr/bin/env python3
"""
Quick test for Ultra Tracker fixes
"""

import sys
import os
import time

print("🔧 Quick Ultra Tracker Test...")

# Test the fallback tracker logic directly
print("📊 Testing fallback signal management logic...")

# Simulate the fallback tracker
_fallback_signal_tracker = {
    'last_signal_time': 0,
    'signal_count_today': 0,
    'last_reset_day': 17  # June 17, 2025
}

current_time = time.time()

# Test rate limiting logic
time_since_last = current_time - _fallback_signal_tracker['last_signal_time']
can_send_new_signal = (
    _fallback_signal_tracker['signal_count_today'] < 10 and
    time_since_last > 900  # 15 minutes
)

print(f"📊 Fallback Tracker Status:")
print(f"  • Signal count today: {_fallback_signal_tracker['signal_count_today']}")
print(f"  • Time since last signal: {time_since_last:.0f} seconds")
print(f"  • Can send new signal: {'✅ YES' if can_send_new_signal else '❌ NO'}")

# Test signal sending
if can_send_new_signal:
    print("✅ Signal permission: ALLOWED")
    print("🚀 Ultra Tracker fallback mode is working correctly!")
    
    # Simulate sending a signal
    _fallback_signal_tracker['last_signal_time'] = current_time
    _fallback_signal_tracker['signal_count_today'] += 1
    print(f"📊 Updated tracker: {_fallback_signal_tracker['signal_count_today']} signals today")
else:
    print("⚠️ Signal permission: BLOCKED (rate limited)")

print("\n✅ Fallback logic test completed successfully!")

# Test bot import separately
print("\n🔧 Testing bot import...")
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    # Just test the import without full initialization
    import main_bot
    print("✅ main_bot module imported successfully")
    
    # Check if TradingBot class exists
    if hasattr(main_bot, 'TradingBot'):
        print("✅ TradingBot class found")
    else:
        print("❌ TradingBot class not found")
        
except Exception as e:
    print(f"❌ Import failed: {e}")

print("\n🎉 Quick test completed!")
