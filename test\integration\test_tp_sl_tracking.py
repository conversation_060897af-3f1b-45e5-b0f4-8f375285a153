#!/usr/bin/env python3
"""
🧪 Test Real-time TP/SL Tracking System
"""

import os
import time
import json
from datetime import datetime
from typing import Dict, Any

# Mock classes for testing
class MockNotifier:
    def __init__(self):
        self.messages_sent = []
    
    def send_message(self, message: str, parse_mode: str = None, chat_id: str = None) -> bool:
        print(f"📤 Mock Telegram Message:")
        print(f"Chat: {chat_id or 'default'}")
        print(f"Parse Mode: {parse_mode}")
        print(f"Message: {message[:200]}...")
        self.messages_sent.append({
            'message': message,
            'parse_mode': parse_mode,
            'chat_id': chat_id,
            'timestamp': time.time()
        })
        return True
    
    def send_tp_sl_update_notification(self, coin: str, signal: Dict[str, Any], 
                                     current_price: float, message: str, 
                                     use_html: bool = True) -> bool:
        print(f"🔄 Mock TP/SL Update for {coin}: Price {current_price}")
        return self.send_message(message, "HTML" if use_html else None)

class MockDataFetcher:
    def __init__(self):
        self.mock_prices = {
            'BTCUSDT': 45000.0,
            'ETHUSDT': 3000.0,
            'ADAUSDT': 0.5,
            'BICO/USDT': 0.25
        }
    
    def get_current_price(self, coin: str) -> float:
        # Simulate price changes
        base_price = self.mock_prices.get(coin, 100.0)
        # Add some random variation
        import random
        variation = random.uniform(-0.02, 0.02)  # ±2% variation
        return base_price * (1 + variation)

class MockDataLogger:
    def __init__(self):
        self.logged_signals = []
    
    def log_signal(self, signal_data: Dict[str, Any]) -> None:
        self.logged_signals.append(signal_data)
        print(f"📝 Mock logged signal: {signal_data.get('coin', 'UNKNOWN')}")

def test_tp_sl_tracking():
    """🧪 Test the TP/SL tracking system."""
    print("🧪 Testing Real-time TP/SL Tracking System...")
    print("=" * 60)
    
    # Import the actual TradeTracker
    try:
        import trade_tracker
        print("✅ TradeTracker module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import TradeTracker: {e}")
        return False
    
    # Initialize mock components
    notifier = MockNotifier()
    fetcher = MockDataFetcher()
    logger = MockDataLogger()
    
    # Initialize TradeTracker
    print("\n📈 Initializing TradeTracker with TP/SL tracking...")
    tracker = trade_tracker.TradeTracker(
        notifier=notifier,
        data_fetcher=fetcher,
        data_logger=logger,
        max_active_signals=10,
        backup_interval=0,  # Disable backup for testing
        backup_dir=None
    )
    
    # Test 1: Add a test signal
    print("\n🧪 Test 1: Adding test signal...")
    test_signal = {
        'coin': 'BTCUSDT',
        'signal_type': 'BUY',
        'entry': 44000.0,
        'take_profit': 48000.0,
        'stop_loss': 42000.0,
        'confidence': 0.85,
        'contributing_models': ['LSTM', 'XGBoost'],
        'volume_spike_detected': True,
        'signal_id': 'TEST_BTC_001'
    }
    
    success = tracker.add_signal(test_signal)
    print(f"Signal added: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    # Test 2: Check TP/SL tracking configuration
    print("\n🧪 Test 2: Checking TP/SL tracking configuration...")
    if hasattr(tracker, 'tp_sl_tracking'):
        config = tracker.tp_sl_tracking
        print(f"✅ TP/SL tracking configuration found:")
        print(f"  - Enabled: {config.get('enabled', False)}")
        print(f"  - Update threshold: {config.get('update_threshold', 0)}%")
        print(f"  - Notification cooldown: {config.get('notification_cooldown', 0)}s")
        print(f"  - Auto-trailing enabled: {config.get('auto_trailing_enabled', False)}")
    else:
        print("❌ TP/SL tracking configuration not found")
    
    # Test 3: Simulate price changes and check updates
    print("\n🧪 Test 3: Simulating price changes...")
    
    # Simulate profitable price movement (should trigger trailing stop)
    print("📈 Simulating profitable price movement...")
    test_prices = [44500, 45000, 45500, 46000]  # Increasing prices
    
    for i, price in enumerate(test_prices):
        print(f"\n  📊 Price update {i+1}: {price}")
        fetcher.mock_prices['BTCUSDT'] = price
        
        # Check tracked signals (this should trigger TP/SL updates)
        closed_signals = tracker.check_tracked_signals()
        print(f"  Closed signals: {len(closed_signals)}")
        
        time.sleep(1)  # Small delay
    
    # Test 4: Get TP/SL tracking summary
    print("\n🧪 Test 4: Getting TP/SL tracking summary...")
    if hasattr(tracker, 'get_tp_sl_tracking_summary'):
        summary = tracker.get_tp_sl_tracking_summary()
        print(f"✅ TP/SL tracking summary:")
        print(f"  - Total active signals: {summary.get('total_active_signals', 0)}")
        print(f"  - Signals with updates: {summary.get('signals_with_updates', 0)}")
        print(f"  - Signals with trailing: {summary.get('signals_with_trailing', 0)}")
        
        stats = summary.get('update_statistics', {})
        print(f"  - TP updates: {stats.get('total_tp_updates', 0)}")
        print(f"  - SL updates: {stats.get('total_sl_updates', 0)}")
        print(f"  - Trailing updates: {stats.get('total_trailing_updates', 0)}")
    else:
        print("❌ TP/SL tracking summary method not found")
    
    # Test 5: Send tracking report
    print("\n🧪 Test 5: Sending TP/SL tracking report...")
    if hasattr(tracker, 'send_tp_sl_tracking_report'):
        report_sent = tracker.send_tp_sl_tracking_report()
        print(f"Report sent: {'✅ SUCCESS' if report_sent else '❌ FAILED'}")
    else:
        print("❌ TP/SL tracking report method not found")
    
    # Test 6: Check notifications sent
    print("\n🧪 Test 6: Checking notifications sent...")
    print(f"Total messages sent: {len(notifier.messages_sent)}")
    for i, msg in enumerate(notifier.messages_sent):
        print(f"  Message {i+1}: {msg['message'][:100]}...")
    
    print("\n" + "=" * 60)
    print("✅ TP/SL Tracking System Test Complete!")
    
    return True

if __name__ == "__main__":
    test_tp_sl_tracking()
