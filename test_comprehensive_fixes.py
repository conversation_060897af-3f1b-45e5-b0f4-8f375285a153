#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE VERIFICATION TEST
Test all fixes to ensure no 'none', 'unknown', '0' values are displayed inappropriately
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from typing import Dict, Any, List

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_volume_profile_analyzer():
    """Test Volume Profile Analyzer fixes"""
    print("\n🔍 Testing Volume Profile Analyzer...")
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        # Create test data
        test_data = pd.DataFrame({
            'open': [50000] * 100,
            'high': [50500] * 100,
            'low': [49500] * 100,
            'close': [50000] * 100,
            'volume': [1000000] * 100
        })
        
        analyzer = VolumeProfileAnalyzer()
        result = analyzer.analyze_volume_profile("BTCUSDT", test_data, 50000.0)
        
        # Check for problematic values
        issues = []
        
        def check_dict_for_issues(data, path=""):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(value, dict):
                    check_dict_for_issues(value, current_path)
                elif isinstance(value, str):
                    if value.lower() in ['none', 'unknown', 'null']:
                        issues.append(f"Found '{value}' at {current_path}")
                elif isinstance(value, (int, float)):
                    if value == 0.0 and 'confidence' in key.lower():
                        issues.append(f"Found 0.0 confidence at {current_path}")
        
        check_dict_for_issues(result)
        
        if issues:
            print(f"  ❌ Volume Profile issues found: {issues}")
            return False
        else:
            print(f"  ✅ Volume Profile Analyzer: No issues found")
            return True
            
    except Exception as e:
        print(f"  ❌ Volume Profile test failed: {e}")
        return False

def test_point_figure_analyzer():
    """Test Point Figure Analyzer fixes"""
    print("\n🔍 Testing Point Figure Analyzer...")
    
    try:
        from point_figure_analyzer import PointFigureAnalyzer
        
        # Create test data
        test_data = pd.DataFrame({
            'open': [50000] * 100,
            'high': [50500] * 100,
            'low': [49500] * 100,
            'close': [50000] * 100,
            'volume': [1000000] * 100
        })
        
        analyzer = PointFigureAnalyzer()
        result = analyzer.analyze_point_figure("BTCUSDT", test_data, 50000.0)
        
        # Check for problematic values
        issues = []
        
        def check_for_unknown_values(data, path=""):
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    check_for_unknown_values(value, current_path)
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    check_for_unknown_values(item, f"{path}[{i}]")
            elif isinstance(data, str):
                if data.upper() in ['UNKNOWN', 'NONE', 'NULL']:
                    issues.append(f"Found '{data}' at {path}")
        
        check_for_unknown_values(result)
        
        if issues:
            print(f"  ❌ Point Figure issues found: {issues}")
            return False
        else:
            print(f"  ✅ Point Figure Analyzer: No issues found")
            return True
            
    except Exception as e:
        print(f"  ❌ Point Figure test failed: {e}")
        return False

def test_fourier_analyzer():
    """Test Fourier Analyzer fixes"""
    print("\n🔍 Testing Fourier Analyzer...")
    
    try:
        from fourier_analyzer import FourierAnalyzer
        
        # Create test data
        test_data = pd.DataFrame({
            'open': [50000] * 100,
            'high': [50500] * 100,
            'low': [49500] * 100,
            'close': [50000] * 100,
            'volume': [1000000] * 100
        })
        
        analyzer = FourierAnalyzer()
        result = analyzer.analyze_fourier_signals("BTCUSDT", test_data, 50000.0)
        
        # Check for problematic values
        issues = []
        
        # Check market regime
        market_regime = result.get('analysis_summary', {}).get('market_regime', '')
        if market_regime.lower() in ['unknown', 'none']:
            issues.append(f"Market regime is '{market_regime}'")
        
        if issues:
            print(f"  ❌ Fourier issues found: {issues}")
            return False
        else:
            print(f"  ✅ Fourier Analyzer: No issues found")
            return True
            
    except Exception as e:
        print(f"  ❌ Fourier test failed: {e}")
        return False

def test_ai_model_manager():
    """Test AI Model Manager fixes"""
    print("\n🔍 Testing AI Model Manager...")
    
    try:
        from ai_model_manager import AIModelManager
        
        # Create test data
        test_features = {
            'price_features': [50000, 50100, 49900, 50200],
            'volume_features': [1000000, 1100000, 900000, 1200000],
            'technical_features': [0.5, 0.6, 0.4, 0.7]
        }
        
        manager = AIModelManager()
        result = manager.get_ensemble_prediction(test_features)
        
        # Check for NONE signals
        issues = []
        
        prediction = result.get('prediction', '')
        if prediction.upper() in ['NONE', 'UNKNOWN']:
            issues.append(f"AI prediction is '{prediction}'")
        
        if issues:
            print(f"  ❌ AI Model Manager issues found: {issues}")
            return False
        else:
            print(f"  ✅ AI Model Manager: No issues found")
            return True
            
    except Exception as e:
        print(f"  ❌ AI Model Manager test failed: {e}")
        return False

def test_signal_processor():
    """Test Signal Processor fixes"""
    print("\n🔍 Testing Signal Processor...")
    
    try:
        from signal_processor import SignalProcessor
        
        # Create test data
        test_data = pd.DataFrame({
            'open': [50000] * 100,
            'high': [50500] * 100,
            'low': [49500] * 100,
            'close': [50000] * 100,
            'volume': [1000000] * 100
        })
        
        processor = SignalProcessor()
        result = processor.analyze_fibonacci_signals("BTCUSDT", test_data, 50000.0)
        
        # Check for NONE signals
        issues = []
        
        signal = result.get('signal', '')
        if signal.upper() in ['NONE', 'UNKNOWN']:
            issues.append(f"Signal is '{signal}'")
        
        trend_direction = result.get('trend_direction', '')
        if trend_direction.upper() in ['UNKNOWN', 'NONE']:
            issues.append(f"Trend direction is '{trend_direction}'")
        
        if issues:
            print(f"  ❌ Signal Processor issues found: {issues}")
            return False
        else:
            print(f"  ✅ Signal Processor: No issues found")
            return True
            
    except Exception as e:
        print(f"  ❌ Signal Processor test failed: {e}")
        return False

def test_data_fetcher():
    """Test Data Fetcher fixes"""
    print("\n🔍 Testing Data Fetcher...")
    
    try:
        from data_fetcher import DataFetcher
        
        fetcher = DataFetcher()
        
        # Test fallback ticker
        ticker = fetcher.fetch_ticker("INVALID_SYMBOL")
        
        issues = []
        
        # Check for 0.0 values in important fields
        if ticker.get('last', 0) == 0.0:
            issues.append("Last price is 0.0")
        
        if ticker.get('high', 0) == 0.0:
            issues.append("High price is 0.0")
        
        if ticker.get('volume', 0) == 0.0:
            issues.append("Volume is 0.0")
        
        if issues:
            print(f"  ❌ Data Fetcher issues found: {issues}")
            return False
        else:
            print(f"  ✅ Data Fetcher: No issues found")
            return True
            
    except Exception as e:
        print(f"  ❌ Data Fetcher test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all comprehensive tests"""
    print("🚀 STARTING COMPREHENSIVE VERIFICATION TEST")
    print("=" * 60)
    
    tests = [
        test_volume_profile_analyzer,
        test_point_figure_analyzer,
        test_fourier_analyzer,
        test_ai_model_manager,
        test_signal_processor,
        test_data_fetcher
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 COMPREHENSIVE TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ ALL TESTS PASSED - No 'none', 'unknown', '0' value issues found!")
        return True
    else:
        print(f"❌ {total - passed} tests failed - Issues need to be addressed")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
