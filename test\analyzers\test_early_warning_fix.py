#!/usr/bin/env python3
"""
🧪 Test script để kiểm tra Early Warning System fix
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """Tạo dữ liệu test realistic cho MOVR/USDT"""
    try:
        # Create 100 candles of realistic data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=100), 
                             end=datetime.now(), freq='1H')
        
        # Generate realistic MOVR/USDT-like data
        base_price = 12.5  # MOVR price around $12.5
        prices = []
        volumes = []
        
        for i in range(len(dates)):
            # Add some trend and volatility
            trend_factor = 1 + (i * 0.0002)  # Slight uptrend
            volatility = np.random.normal(0, 0.04)  # 4% volatility
            
            if i == 0:
                price = base_price
            else:
                price = prices[-1] * trend_factor * (1 + volatility)
            
            prices.append(max(1.0, price))  # Prevent negative prices
            
            # Volume with some spikes
            base_volume = np.random.uniform(50000, 200000)
            if i % 8 == 0:  # Volume spike every 8 candles
                base_volume *= 2.5
            volumes.append(base_volume)
        
        # Create OHLCV data
        data = []
        for i, (date, price, volume) in enumerate(zip(dates, prices, volumes)):
            volatility = abs(np.random.normal(0, 0.02))
            
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            open_price = price * (1 + np.random.normal(0, 0.01))
            close_price = price * (1 + np.random.normal(0, 0.01))
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ Created test data: {len(df)} candles")
        print(f"  📊 Price range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
        print(f"  📊 Volume range: {df['volume'].min():,.0f} - {df['volume'].max():,.0f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        return None

def create_test_orderbook():
    """Tạo orderbook test realistic"""
    try:
        current_price = 12.45  # MOVR current price
        
        # Create realistic bid/ask levels
        orderbook_data = {
            'bids': [],
            'asks': []
        }
        
        # Generate bids (below current price)
        for i in range(15):
            price = current_price * (1 - (i + 1) * 0.002)  # 0.2% steps down
            volume = np.random.uniform(50, 1000)
            if i < 3:  # Larger volumes near the spread
                volume *= 3
            orderbook_data['bids'].append([str(price), str(volume)])
        
        # Generate asks (above current price)
        for i in range(15):
            price = current_price * (1 + (i + 1) * 0.002)  # 0.2% steps up
            volume = np.random.uniform(50, 1000)
            if i < 3:  # Larger volumes near the spread
                volume *= 3
            orderbook_data['asks'].append([str(price), str(volume)])
        
        print(f"✅ Created test orderbook:")
        print(f"  📊 Best bid: ${float(orderbook_data['bids'][0][0]):.4f}")
        print(f"  📊 Best ask: ${float(orderbook_data['asks'][0][0]):.4f}")
        
        return orderbook_data
        
    except Exception as e:
        print(f"❌ Error creating orderbook: {e}")
        return None

def test_early_warning_system():
    """Test Early Warning System"""
    print("🚨 TESTING EARLY WARNING SYSTEM FIX")
    print("=" * 60)
    
    try:
        from early_warning_system import EarlyWarningSystem
        
        # Create early warning system
        ews = EarlyWarningSystem(
            pump_threshold=0.3,
            dump_threshold=0.3,
            volume_threshold=2.0,
            price_momentum_threshold=0.015,
            orderbook_imbalance_threshold=0.25,
            cooldown_minutes=10,
            advance_warning_minutes=3
        )
        
        # Create test data
        ohlcv_data = create_test_data()
        orderbook_data = create_test_orderbook()
        
        if ohlcv_data is None or orderbook_data is None:
            return False
        
        current_price = ohlcv_data['close'].iloc[-1]
        
        print(f"\n🧪 Testing early warning analysis for MOVR/USDT...")
        print(f"  📊 Data points: {len(ohlcv_data)}")
        print(f"  💰 Current price: ${current_price:.4f}")
        print(f"  📋 Orderbook levels: {len(orderbook_data['bids'])} bids, {len(orderbook_data['asks'])} asks")
        
        # Run early warning analysis
        result = ews.analyze_early_signals(
            coin="MOVR/USDT",
            ohlcv_data=ohlcv_data,
            current_price=current_price,
            orderbook_data=orderbook_data,
            volume_spike_data=None
        )
        
        # Check results
        if result:
            print(f"\n📊 EARLY WARNING ANALYSIS RESULTS:")
            print(f"  📊 Status: {result.get('status', 'UNKNOWN')}")
            print(f"  🎯 Risk Level: {result.get('risk_level', 'UNKNOWN')}")
            print(f"  💪 Confidence: {result.get('confidence', 0):.1%}")
            print(f"  ⚠️ Warnings: {len(result.get('warnings', []))}")
            
            # Check status field
            status = result.get('status')
            if status == 'success':
                print(f"  ✅ SUCCESS: Analysis completed successfully")
                
                # Check individual indicators
                indicators = result.get('early_indicators', {})
                print(f"\n📊 INDIVIDUAL INDICATORS:")
                
                for indicator_name, indicator_data in indicators.items():
                    indicator_status = indicator_data.get('status', 'UNKNOWN')
                    status_emoji = "✅" if indicator_status == 'success' else "❌" if indicator_status == 'error' else "⚪"
                    print(f"  {status_emoji} {indicator_name}: {indicator_status}")
                
                # Check warnings
                warnings = result.get('warnings', [])
                if warnings:
                    print(f"\n⚠️ WARNINGS GENERATED:")
                    for warning in warnings:
                        warning_type = warning.get('type', 'UNKNOWN')
                        probability = warning.get('probability', 0)
                        print(f"  🚨 {warning_type}: {probability:.1%} probability")
                else:
                    print(f"\n✅ No warnings generated (normal market conditions)")
                
                return True
                
            elif status == 'error':
                error_msg = result.get('error', 'Unknown error')
                print(f"  ❌ ERROR: {error_msg}")
                return False
                
            elif status == 'cooldown':
                print(f"  ⏰ COOLDOWN: Analysis in cooldown period")
                return True
                
            else:
                print(f"  ⚪ UNKNOWN STATUS: {status}")
                return False
        else:
            print(f"\n❌ FAILURE: No analysis result returned")
            return False
            
    except ImportError as e:
        print(f"❌ Cannot import EarlyWarningSystem: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing early warning system: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_scenarios():
    """Test multiple scenarios"""
    print("\n🔄 TESTING MULTIPLE SCENARIOS")
    print("=" * 60)
    
    try:
        from early_warning_system import EarlyWarningSystem
        
        ews = EarlyWarningSystem()
        
        test_scenarios = [
            ("Normal market conditions", create_test_data()),
            ("High volatility", create_test_data()),
            ("Low volume", create_test_data())
        ]
        
        all_passed = True
        
        for scenario_name, test_data in test_scenarios:
            print(f"\n🧪 Testing: {scenario_name}")
            
            if test_data is None:
                print(f"  ❌ FAILED: Could not create test data")
                all_passed = False
                continue
            
            try:
                current_price = test_data['close'].iloc[-1]
                orderbook_data = create_test_orderbook()
                
                result = ews.analyze_early_signals(
                    coin="TEST/USDT",
                    ohlcv_data=test_data,
                    current_price=current_price,
                    orderbook_data=orderbook_data
                )
                
                if result and result.get('status') in ['success', 'cooldown']:
                    print(f"  ✅ PASSED: Analysis completed")
                else:
                    print(f"  ❌ FAILED: Analysis failed or returned error")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ FAILED: Exception - {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error in multiple scenarios test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 EARLY WARNING SYSTEM FIX TEST")
    print("=" * 70)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Basic functionality
    basic_result = test_early_warning_system()
    
    # Test 2: Multiple scenarios
    scenario_result = test_multiple_scenarios()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 EARLY WARNING SYSTEM FIX TEST RESULTS")
    print("=" * 70)
    
    tests = [
        ("Basic Functionality", basic_result),
        ("Multiple Scenarios", scenario_result)
    ]
    
    all_passed = True
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Early Warning System working correctly")
        print("✅ Status field properly returned")
        print("✅ Error handling functional")
        print("✅ Ready for main_bot integration")
        
        print(f"\n🔧 Expected behavior:")
        print(f"  • Early Warning: Always returns status field")
        print(f"  • Main bot: Properly handles success/error/cooldown cases")
        print(f"  • No more 'Unknown error' messages")
        print(f"  • Proper early warning notifications")
    else:
        print("❌ SOME TESTS FAILED")
        print("Early Warning System may still have issues")
        print("Check the error handling and status fields")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
