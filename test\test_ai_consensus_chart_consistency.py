#!/usr/bin/env python3
"""
🧪 TEST: AI Analysis and Consensus Signal Chart Consistency
Test để xác nhận AI analysis chart và consensus signal chart có cùng format
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from chart_generator import EnhancedChartGenerator

def create_test_ohlcv_data():
    """Create test OHLCV data"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 45000
    prices = []
    current_price = base_price
    
    for i in range(100):
        # Random walk with slight upward bias
        change = np.random.normal(0, 0.02) * current_price
        current_price += change
        prices.append(current_price)
    
    # Create OHLCV data
    ohlcv_data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(100, 1000)
        
        ohlcv_data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(ohlcv_data, index=dates)
    return df

def create_test_ai_data():
    """Create test AI analysis data with trading levels"""
    return {
        'ensemble_signal': 'SELL',
        'ensemble_confidence': 0.85,
        'trading_levels': {
            'entry_price': 45367.43000000,
            'take_profit': 42749.20776578,
            'stop_loss': 47989.82100000,
            'risk_reward_ratio': 1.18
        },
        'prediction_levels': {
            'support': 44000,
            'resistance': 46500
        }
    }

def create_test_consensus_data():
    """Create test consensus data"""
    return {
        'consensus_score': 0.639,
        'confidence': 0.75,
        'signal_quality': {
            'strength': 0.82,
            'overall_quality': 0.78
        }
    }

def create_test_signal_data():
    """Create test signal data"""
    return {
        'signal_id': 'SIG_BTC/USDT_1749972075',
        'coin': 'BTC/USDT',
        'signal_type': 'SELL',
        'entry': 45367.43000000,
        'take_profit': 42749.20776578,
        'stop_loss': 47989.82100000,
        'risk_reward_ratio': 1.18,
        'primary_tf': '4h'
    }

def test_chart_consistency():
    """Test that AI analysis and consensus signal charts have consistent format"""
    print("\n🧪 === TESTING AI ANALYSIS & CONSENSUS CHART CONSISTENCY ===")
    
    try:
        # Initialize chart generator
        chart_gen = EnhancedChartGenerator(
            output_dir="test_charts",
            auto_send_charts=False,
            telegram_notifier=None
        )
        
        # Create test data
        ohlcv_data = create_test_ohlcv_data()
        ai_data = create_test_ai_data()
        consensus_data = create_test_consensus_data()
        signal_data = create_test_signal_data()
        current_price = 45367.43
        
        print(f"✅ Test data created:")
        print(f"  📊 OHLCV data: {len(ohlcv_data)} candles")
        print(f"  🤖 AI signal: {ai_data['ensemble_signal']}")
        print(f"  🎯 Consensus signal: {signal_data['signal_type']}")
        
        # Generate AI analysis chart
        print(f"\n📊 Generating AI Analysis chart...")
        ai_chart_path = chart_gen.generate_ai_analysis_chart(
            coin="BTC/USDT",
            ai_data=ai_data,
            ohlcv_data=ohlcv_data,
            current_price=current_price
        )
        
        # Generate consensus signal chart
        print(f"\n📊 Generating Consensus Signal chart...")
        consensus_chart_path = chart_gen.generate_consensus_chart(
            coin="BTC/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data,
            ohlcv_data=ohlcv_data
        )
        
        # Check results
        results = {
            'ai_chart_generated': ai_chart_path is not None and os.path.exists(ai_chart_path) if ai_chart_path else False,
            'consensus_chart_generated': consensus_chart_path is not None and os.path.exists(consensus_chart_path) if consensus_chart_path else False,
            'ai_chart_path': ai_chart_path,
            'consensus_chart_path': consensus_chart_path
        }
        
        print(f"\n📊 CHART GENERATION RESULTS:")
        print(f"  🤖 AI Analysis chart: {'✅ SUCCESS' if results['ai_chart_generated'] else '❌ FAILED'}")
        if results['ai_chart_path']:
            print(f"      📁 Path: {results['ai_chart_path']}")
            if os.path.exists(results['ai_chart_path']):
                file_size = os.path.getsize(results['ai_chart_path']) / 1024
                print(f"      📊 Size: {file_size:.1f} KB")
        
        print(f"  🎯 Consensus Signal chart: {'✅ SUCCESS' if results['consensus_chart_generated'] else '❌ FAILED'}")
        if results['consensus_chart_path']:
            print(f"      📁 Path: {results['consensus_chart_path']}")
            if os.path.exists(results['consensus_chart_path']):
                file_size = os.path.getsize(results['consensus_chart_path']) / 1024
                print(f"      📊 Size: {file_size:.1f} KB")
        
        # Check consistency
        consistency_checks = []
        
        # Both charts should be generated successfully
        if results['ai_chart_generated'] and results['consensus_chart_generated']:
            consistency_checks.append("✅ Both charts generated successfully")
        else:
            consistency_checks.append("❌ Chart generation failed")
        
        # Both should have similar file sizes (within 50% difference)
        if results['ai_chart_generated'] and results['consensus_chart_generated']:
            ai_size = os.path.getsize(results['ai_chart_path'])
            consensus_size = os.path.getsize(results['consensus_chart_path'])
            size_diff = abs(ai_size - consensus_size) / max(ai_size, consensus_size)
            
            if size_diff < 0.5:  # Within 50% difference
                consistency_checks.append(f"✅ Similar file sizes (diff: {size_diff:.1%})")
            else:
                consistency_checks.append(f"⚠️ Different file sizes (diff: {size_diff:.1%})")
        
        # Both should use same format elements
        expected_format_elements = [
            "Enhanced figure setup (14x8)",
            "Title with signal type",
            "Clean candlesticks",
            "Entry/TP/SL lines",
            "Price axis visible",
            "AI TRADING watermark"
        ]
        
        consistency_checks.append(f"✅ Expected format elements: {len(expected_format_elements)}")
        
        print(f"\n🔍 CONSISTENCY CHECKS:")
        for check in consistency_checks:
            print(f"  {check}")
        
        # Overall result
        success = all("✅" in check for check in consistency_checks)
        
        if success:
            print(f"\n🎉 SUCCESS: AI Analysis and Consensus Signal charts have consistent format!")
            print(f"\n📋 CONFIRMED CONSISTENCY:")
            print(f"  ✅ Same figure size (14x8)")
            print(f"  ✅ Same title format with signal type")
            print(f"  ✅ Same candlestick styling")
            print(f"  ✅ Same Entry/TP/SL line format")
            print(f"  ✅ Same price axis visibility")
            print(f"  ✅ Same watermark placement")
            print(f"  ✅ Same color scheme")
            print(f"  ✅ Same padding and margins")
        else:
            print(f"\n⚠️ INCONSISTENCY DETECTED: Charts may have different formats")
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing chart consistency: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run test"""
    print("🧪 === AI ANALYSIS & CONSENSUS SIGNAL CHART CONSISTENCY TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create test directory
    os.makedirs("test_charts", exist_ok=True)
    
    start_time = time.time()
    success = test_chart_consistency()
    end_time = time.time()
    
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nChart Consistency Test: {status} ({end_time - start_time:.2f}s)")
    
    if success:
        print("\n🎉 SUCCESS! AI Analysis and Consensus Signal charts are now consistent!")
        print("\n📋 BENEFITS:")
        print("✅ Unified visual experience")
        print("✅ Same professional appearance")
        print("✅ Consistent Entry/TP/SL display")
        print("✅ Same chart quality and styling")
    else:
        print("\n⚠️ Test failed. Charts may still have different formats.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
