#!/usr/bin/env python3
"""
🔧 RANDOM MODULE FIX TEST
========================

Test script để kiểm tra lỗi random module đã đượ<PERSON> sửa
"""

import sys

def test_random_import():
    """Test random module import"""
    print("🔧 Testing random module import fix...")
    print("-" * 40)
    
    try:
        print("📦 Testing direct random import...")
        import random
        test_value = random.randint(1, 100)
        print(f"  ✅ Direct random import works: {test_value}")
        
        print("📦 Testing main_bot import...")
        import main_bot
        print("  ✅ main_bot imported successfully")
        
        print("🔍 Testing random availability in main_bot...")
        if hasattr(main_bot, 'random'):
            print("  ✅ random module available in main_bot")
        else:
            print("  ❌ random module not available in main_bot")
            return False
        
        print("🎯 Testing random.sample function (the one that failed)...")
        test_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        sample_result = main_bot.random.sample(test_list, 3)
        print(f"  ✅ random.sample works: {sample_result}")
        
        return True
        
    except NameError as e:
        if "random" in str(e):
            print(f"  ❌ Random NameError still exists: {e}")
            return False
        else:
            print(f"  ❌ Other NameError: {e}")
            return False
    except Exception as e:
        print(f"  ❌ Unexpected error: {e}")
        return False

def main():
    """Run random fix test"""
    print("🔧 RANDOM MODULE FIX TEST")
    print("=" * 50)
    
    success = test_random_import()
    
    print("\n" + "=" * 50)
    print("📊 RANDOM FIX TEST RESULT")
    print("=" * 50)
    
    if success:
        print("🎉 SUCCESS!")
        print("✅ Random module import: FIXED")
        print("✅ random.sample function: WORKING")
        print("✅ Bot should no longer crash with 'random' NameError")
        print("\n🚀 Bot is ready to run without random errors!")
        return True
    else:
        print("❌ FAILED!")
        print("Random module issues still exist.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
