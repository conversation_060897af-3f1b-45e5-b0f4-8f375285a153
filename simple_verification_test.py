#!/usr/bin/env python3
"""
🔍 SIMPLE VERIFICATION TEST
Quick test to verify key fixes are working
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test basic imports"""
    print("🔍 Testing basic imports...")
    
    try:
        # Test core analyzers
        from volume_profile_analyzer import VolumeProfileAnalyzer
        print("  ✅ Volume Profile Analyzer imported")
        
        from point_figure_analyzer import PointFigureAnalyzer
        print("  ✅ Point Figure Analyzer imported")
        
        from fourier_analyzer import FourierAnalyzer
        print("  ✅ Fourier Analyzer imported")
        
        from ai_model_manager import AIModelManager
        print("  ✅ AI Model Manager imported")
        
        from signal_processor import SignalProcessor
        print("  ✅ Signal Processor imported")
        
        from data_fetcher import DataFetcher
        print("  ✅ Data Fetcher imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        return False

def test_data_fetcher_fallback():
    """Test Data Fetcher fallback values"""
    print("\n🔍 Testing Data Fetcher fallback values...")
    
    try:
        from data_fetcher import DataFetcher
        
        fetcher = DataFetcher()
        ticker = fetcher.fetch_ticker("INVALID_SYMBOL")
        
        # Check for meaningful fallback values
        issues = []
        
        if ticker.get('last', 0) == 0.0:
            issues.append("Last price is 0.0")
        
        if ticker.get('volume', 0) == 0.0:
            issues.append("Volume is 0.0")
        
        if ticker.get('high', 0) == 0.0:
            issues.append("High price is 0.0")
        
        if issues:
            print(f"  ❌ Issues found: {issues}")
            return False
        else:
            print(f"  ✅ Data Fetcher fallback values are meaningful")
            print(f"    Last: {ticker.get('last', 0)}")
            print(f"    Volume: {ticker.get('volume', 0):,.0f}")
            print(f"    High: {ticker.get('high', 0)}")
            return True
            
    except Exception as e:
        print(f"  ❌ Data Fetcher test failed: {e}")
        return False

def test_ai_model_manager():
    """Test AI Model Manager for NONE signals"""
    print("\n🔍 Testing AI Model Manager...")
    
    try:
        from ai_model_manager import AIModelManager
        
        manager = AIModelManager()
        
        # Create simple test features
        test_features = {
            'price_features': [50000, 50100, 49900, 50200],
            'volume_features': [1000000, 1100000, 900000, 1200000],
            'technical_features': [0.5, 0.6, 0.4, 0.7]
        }
        
        result = manager.get_ensemble_prediction(test_features)
        
        # Check for NONE signals
        prediction = result.get('prediction', '')
        if prediction.upper() in ['NONE', 'UNKNOWN']:
            print(f"  ❌ AI prediction is '{prediction}'")
            return False
        else:
            print(f"  ✅ AI Model Manager: Prediction is '{prediction}' (not NONE)")
            return True
            
    except Exception as e:
        print(f"  ❌ AI Model Manager test failed: {e}")
        return False

def run_simple_verification():
    """Run simple verification tests"""
    print("🚀 STARTING SIMPLE VERIFICATION TEST")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_data_fetcher_fallback,
        test_ai_model_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 SIMPLE VERIFICATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ ALL TESTS PASSED - Key fixes are working!")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = run_simple_verification()
    sys.exit(0 if success else 1)
