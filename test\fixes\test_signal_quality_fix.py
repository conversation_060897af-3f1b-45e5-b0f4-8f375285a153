#!/usr/bin/env python3
"""
🧪 Test Signal Quality Fix
Quick test to verify signal quality calculation works correctly
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_signal_quality_calculation():
    """🧪 Test signal quality calculation logic."""
    print("🧪 Testing Signal Quality Calculation...")
    print("=" * 50)
    
    # Mock data similar to real scenario
    consensus_confidence = 0.905  # From your message
    ai_confidence = 0.935  # From AI Models: BUY (93.5%)
    tp_sl_confidence = 0.710  # From your message
    tp_sl_methods = ["Intelligent_Fallback", "Risk_Management", "Technical_Analysis"]  # Forced fallback
    risk_reward_ratio = 4.78  # From your message
    volume_spike_detected = True  # Assume true
    consensus_score = 0.414  # From your message
    contributing_algorithms = 4  # AI, Point&Figure, Fibonacci, Fourier
    
    print(f"📊 Input Data:")
    print(f"  - Consensus Confidence: {consensus_confidence:.3f}")
    print(f"  - AI Confidence: {ai_confidence:.3f}")
    print(f"  - TP/SL Confidence: {tp_sl_confidence:.3f}")
    print(f"  - TP/SL Methods: {len(tp_sl_methods)} ({', '.join(tp_sl_methods)})")
    print(f"  - Risk/Reward Ratio: {risk_reward_ratio:.2f}")
    print(f"  - Volume Spike: {volume_spike_detected}")
    print(f"  - Consensus Score: {consensus_score:.3f}")
    print(f"  - Contributing Algorithms: {contributing_algorithms}")
    
    # Calculate signal strength (same logic as main_bot.py)
    signal_strength = 0.0
    signal_strength += min(0.3, consensus_confidence * 0.3)  # Consensus confidence (max 30%)
    signal_strength += min(0.2, ai_confidence * 0.2)  # AI confidence (max 20%)
    signal_strength += min(0.2, tp_sl_confidence * 0.2)  # TP/SL confidence (max 20%)
    signal_strength += min(0.15, len(tp_sl_methods) / 10 * 0.15)  # TP/SL methods count (max 15%)
    signal_strength += min(0.1, risk_reward_ratio / 5 * 0.1)  # Risk/reward ratio (max 10%)
    signal_strength += 0.05 if volume_spike_detected else 0  # Volume spike bonus (5%)
    
    # Calculate overall quality
    overall_quality = 0.0
    overall_quality += signal_strength * 0.4  # Signal strength (40%)
    overall_quality += consensus_score * 0.3  # Consensus score (30%)
    overall_quality += min(0.2, contributing_algorithms / 6 * 0.2)  # Algorithm diversity (20%)
    overall_quality += 0.1  # AI enhancement bonus (10%)
    
    # Ensure values are within bounds
    signal_strength = max(0.0, min(1.0, signal_strength))
    overall_quality = max(0.0, min(1.0, overall_quality))
    
    print(f"\n📊 Calculation Breakdown:")
    print(f"  Signal Strength Components:")
    print(f"    - Consensus: {min(0.3, consensus_confidence * 0.3):.3f} (max 0.300)")
    print(f"    - AI: {min(0.2, ai_confidence * 0.2):.3f} (max 0.200)")
    print(f"    - TP/SL: {min(0.2, tp_sl_confidence * 0.2):.3f} (max 0.200)")
    print(f"    - Methods: {min(0.15, len(tp_sl_methods) / 10 * 0.15):.3f} (max 0.150)")
    print(f"    - Risk/Reward: {min(0.1, risk_reward_ratio / 5 * 0.1):.3f} (max 0.100)")
    print(f"    - Volume Spike: {0.05 if volume_spike_detected else 0:.3f} (max 0.050)")
    print(f"    - Total: {signal_strength:.3f}")
    
    print(f"\n  Overall Quality Components:")
    print(f"    - Signal Strength: {signal_strength * 0.4:.3f} (40%)")
    print(f"    - Consensus Score: {consensus_score * 0.3:.3f} (30%)")
    print(f"    - Algorithm Diversity: {min(0.2, contributing_algorithms / 6 * 0.2):.3f} (20%)")
    print(f"    - AI Enhancement: 0.100 (10%)")
    print(f"    - Total: {overall_quality:.3f}")
    
    print(f"\n🎯 Final Results:")
    print(f"  ✅ Signal Strength: {signal_strength:.3f}/1.000")
    print(f"  ✅ Overall Quality: {overall_quality:.3f}/1.000")
    print(f"  ✅ TP/SL Methods Count: {len(tp_sl_methods)}")
    
    # Test consensus_data structure
    consensus_data = {
        "signal_quality": {
            "strength": signal_strength,
            "overall_quality": overall_quality,
            "tp_sl_methods_count": len(tp_sl_methods),
            "algorithm_diversity": contributing_algorithms,
            "confidence_score": (consensus_confidence + tp_sl_confidence + ai_confidence) / 3
        }
    }
    
    print(f"\n📊 Consensus Data Structure:")
    print(f"  consensus_data['signal_quality']['strength'] = {consensus_data['signal_quality']['strength']:.3f}")
    print(f"  consensus_data['signal_quality']['overall_quality'] = {consensus_data['signal_quality']['overall_quality']:.3f}")
    
    # Test telegram message format
    print(f"\n📱 Telegram Message Format Test:")
    strength_display = consensus_data.get("signal_quality", {}).get("strength", 0)
    quality_display = consensus_data.get("signal_quality", {}).get("overall_quality", 0)
    
    print(f"  ├ Sức mạnh tín hiệu: {strength_display:.3f}/1.000")
    print(f"  └ Chất lượng tổng thể: {quality_display:.3f}/1.000")
    
    if strength_display > 0 and quality_display > 0:
        print("\n✅ SUCCESS: Signal quality calculation working correctly!")
        print("✅ Values should now appear in Telegram messages!")
        return True
    else:
        print("\n❌ FAILED: Signal quality calculation still returning 0!")
        return False

def test_tp_sl_methods_fallback():
    """🧪 Test TP/SL methods fallback logic."""
    print("\n🧪 Testing TP/SL Methods Fallback...")
    print("=" * 50)
    
    # Test empty methods
    tp_sl_methods = []
    print(f"Original tp_sl_methods: {tp_sl_methods}")
    
    if not tp_sl_methods or len(tp_sl_methods) == 0:
        print("🚨 CRITICAL: TP/SL methods is empty, forcing fallback methods")
        tp_sl_methods = ["Intelligent_Fallback", "Risk_Management", "Technical_Analysis"]
        print(f"🔧 Forced TP/SL methods: {tp_sl_methods}")
    
    print(f"Final tp_sl_methods count: {len(tp_sl_methods)}")
    
    if len(tp_sl_methods) > 0:
        print("✅ SUCCESS: TP/SL methods fallback working!")
        return True
    else:
        print("❌ FAILED: TP/SL methods still empty!")
        return False

if __name__ == "__main__":
    print("🧪 Signal Quality Fix Test Suite")
    print("=" * 60)
    
    test1_result = test_signal_quality_calculation()
    test2_result = test_tp_sl_methods_fallback()
    
    print("\n🎯 Test Results Summary:")
    print("=" * 60)
    print(f"✅ Signal Quality Calculation: {'PASS' if test1_result else 'FAIL'}")
    print(f"✅ TP/SL Methods Fallback: {'PASS' if test2_result else 'FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 The fixes should resolve the 0.000 values issue!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 Additional debugging may be needed.")
