#!/usr/bin/env python3
"""
🎨 Test Full Improvements - Test all improvements: no truncation, beautiful charts, existing reports
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def test_full_improvements():
    """🎨 Test all improvements together."""
    print(f"🎨 FULL IMPROVEMENTS TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing enhanced chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=200, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 200)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 5000, 200)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Test 1: Fourier Analysis with Enhanced Chart + Full Report
        print(f"\n1️⃣ Testing Fourier Analysis with Enhanced Chart + Full Report...")
        
        fourier_data = {
            'price_cycles': [
                {'period': 40.0, 'confidence': 0.89, 'cycle_type': 'medium'},
                {'period': 18.2, 'confidence': 0.79, 'cycle_type': 'short'},
                {'period': 9.1, 'confidence': 0.65, 'cycle_type': 'short'},
                {'period': 13.3, 'confidence': 0.58, 'cycle_type': 'short'},
                {'period': 7.1, 'confidence': 0.55, 'cycle_type': 'short'}
            ],
            'volume_cycles': [
                {'period': 10.7, 'confidence': 1.00},
                {'period': 5.5, 'confidence': 1.00},
                {'period': 18.8, 'confidence': 0.94}
            ],
            'signals': {
                'overall_signal': 'BULLISH',
                'signal_strength': 0.7,
                'confidence': 0.693,
                'trading_levels': {
                    'entry_price': current_price * 0.99,
                    'take_profit': current_price * 1.05,
                    'stop_loss': current_price * 0.98,
                    'risk_reward_ratio': 5.0,
                    'tp_levels': {
                        'tp1': current_price * 1.03,
                        'tp2': current_price * 1.05,
                        'tp3': current_price * 1.08
                    }
                },
                'timing_signals': [
                    {
                        'type': 'multi_cycle_synchronization',
                        'action': 'STRONG_SELL',
                        'confidence': 1.0,
                        'cycles_involved': 3,
                        'synchronization_type': 'peak_alignment'
                    },
                    {
                        'type': 'cycle_phase_timing',
                        'action': 'PREPARE_SELL',
                        'confidence': 0.982,
                        'cycle_rank': 1,
                        'current_phase': 'peak_approach',
                        'urgency': 'moderate'
                    },
                    {
                        'type': 'trend_reversal_prediction',
                        'action': 'REASSESS',
                        'confidence': 0.893,
                        'next_reversal': 'minor_reversal',
                        'timing_window': {'periods_ahead': 10.0}
                    }
                ]
            },
            'dominant_cycle': 40.0,
            'trend_component': 0.009,
            'seasonal_strength': 0.658,
            'market_regime': {
                'regime_type': 'stable_ranging',
                'volatility_level': 'medium',
                'trend_direction': 'neutral'
            },
            'analysis_metadata': {
                'confidence_level': 0.824,
                'analysis_quality': 'high'
            }
        }
        
        try:
            fourier_success = notifier.send_fourier_analysis_report(
                "BTC/USDT", fourier_data, current_price, use_html=True,
                ohlcv_data=ohlcv_data, chart_generator=chart_gen
            )
            
            if fourier_success:
                print(f"  ✅ Fourier analysis with enhanced chart + full report sent successfully")
            else:
                print(f"  ❌ Fourier analysis with enhanced chart failed")
                
        except Exception as fourier_error:
            print(f"  ❌ Fourier test error: {fourier_error}")
            fourier_success = False
        
        # Wait between sends to avoid rate limiting
        import time
        time.sleep(8)
        
        # Test 2: Volume Profile with Enhanced Chart + Full Report
        print(f"\n2️⃣ Testing Volume Profile with Enhanced Chart + Full Report...")
        
        volume_data = {
            'vpoc': {'price': current_price * 1.02, 'volume': 1000000, 'percentage_of_total': 5.8},
            'value_area': {'high': current_price * 1.05, 'low': current_price * 0.95},
            'signals': {'primary_signal': 'BUY', 'confidence': 0.75},
            'volume_profile': {
                'total_volume': 50000000,
                'original_data_volume': 50000000,
                'calculated_volume': 50000000,
                'volume_flow': {
                    'buying_volume': 25000000,
                    'selling_volume': 25000000,
                    'flow_direction': 'bullish',
                    'flow_strength': 0.75
                },
                'volume_distribution': {f'level_{i}': 1000000 for i in range(50)}
            },
            'trading_levels': {
                'has_trading_levels': True,
                'signal_type': 'BUY',
                'entry_price': current_price,
                'take_profit': current_price * 1.05,
                'stop_loss': current_price * 0.98,
                'risk_reward_ratio': 2.5,
                'tp_levels': {
                    'tp1': current_price * 1.03,
                    'tp2': current_price * 1.05,
                    'tp3': current_price * 1.08
                },
                'vp_analysis': {'signal_confidence': 0.85},
                'calculation_methods': {'target_method': 'Enhanced VP'}
            },
            'recommendation': 'BUY',
            'risk_level': 'MEDIUM',
            'analysis_period': 200
        }
        
        try:
            volume_success = notifier.send_volume_profile_report(
                "BTC/USDT", volume_data, current_price, use_html=True,
                ohlcv_data=ohlcv_data, chart_generator=chart_gen
            )
            
            if volume_success:
                print(f"  ✅ Volume Profile with enhanced chart + full report sent successfully")
            else:
                print(f"  ❌ Volume Profile with enhanced chart failed")
                
        except Exception as volume_error:
            print(f"  ❌ Volume Profile test error: {volume_error}")
            volume_success = False
        
        # Wait between sends
        time.sleep(8)
        
        # Test 3: AI Analysis with Enhanced Chart + Full Report
        print(f"\n3️⃣ Testing AI Analysis with Enhanced Chart + Full Report...")
        
        ai_data = {
            'ensemble_signal': 'BUY',
            'ensemble_confidence': 0.85,
            'model_results': {
                'XGBoost': {'prediction': 'BUY', 'confidence': 0.9},
                'RandomForest': {'prediction': 'BUY', 'confidence': 0.8},
                'LSTM': {'prediction': 'SELL', 'confidence': 0.7},
                'Transformer': {'prediction': 'BUY', 'confidence': 0.95},
                'SVM': {'prediction': 'BUY', 'confidence': 0.85},
                'GradientBoosting': {'prediction': 'BUY', 'confidence': 0.88},
                'AdaBoost': {'prediction': 'SELL', 'confidence': 0.65},
                'ExtraTrees': {'prediction': 'BUY', 'confidence': 0.82},
                'LightGBM': {'prediction': 'BUY', 'confidence': 0.91},
                'CatBoost': {'prediction': 'BUY', 'confidence': 0.87},
                'NeuralNetwork': {'prediction': 'BUY', 'confidence': 0.83}
            },
            'prediction_quality': 'HIGH',
            'technical_analysis': {
                'momentum': 0.6,
                'volatility': 0.02,
                'trend_strength': 0.8
            },
            'market_sentiment': 'BULLISH',
            'recommendation': 'BUY',
            'risk_assessment': 'MEDIUM',
            'trading_levels': {
                'entry_price': current_price,
                'take_profit': current_price * 1.05,
                'stop_loss': current_price * 0.98,
                'risk_reward_ratio': 2.5,
                'tp_levels': {
                    'tp1': current_price * 1.03,
                    'tp2': current_price * 1.05,
                    'tp3': current_price * 1.08
                },
                'confidence_metrics': {
                    'overall_confidence': 0.85
                }
            }
        }
        
        try:
            ai_success = notifier.send_ai_analysis_report(
                "BTC/USDT", ai_data, current_price, use_html=True,
                ohlcv_data=ohlcv_data, chart_generator=chart_gen
            )
            
            if ai_success:
                print(f"  ✅ AI Analysis with enhanced chart + full report sent successfully")
            else:
                print(f"  ❌ AI Analysis with enhanced chart failed")
                
        except Exception as ai_error:
            print(f"  ❌ AI Analysis test error: {ai_error}")
            ai_success = False
        
        # Summary
        print(f"\n🎨 FULL IMPROVEMENTS TEST RESULTS:")
        print(f"  1️⃣ Fourier Analysis: {'✅ PASS' if fourier_success else '❌ FAIL'}")
        print(f"  2️⃣ Volume Profile: {'✅ PASS' if volume_success else '❌ FAIL'}")
        print(f"  3️⃣ AI Analysis: {'✅ PASS' if ai_success else '❌ FAIL'}")
        
        overall_success = fourier_success and volume_success and ai_success
        
        if overall_success:
            print(f"\n🎉 ALL IMPROVEMENTS WORKING PERFECTLY!")
            print(f"✅ No caption truncation - Full detailed reports")
            print(f"✅ Enhanced beautiful charts with high quality")
            print(f"✅ Using existing detailed reports from system")
            print(f"✅ All analysis types have charts + detailed reports")
            print(f"📱 Check your Telegram for beautiful charts with full detailed reports")
        else:
            print(f"\n⚠️ SOME IMPROVEMENTS NEED WORK!")
            print(f"📱 Check Telegram for any successful reports")
        
        return overall_success
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_improvements()
    if success:
        print(f"\n🎉 FULL IMPROVEMENTS TEST PASSED!")
        print(f"🎨 Beautiful charts + Full detailed reports working perfectly!")
    else:
        print(f"\n💥 FULL IMPROVEMENTS TEST FAILED!")
