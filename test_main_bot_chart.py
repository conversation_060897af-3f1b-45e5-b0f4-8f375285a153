#!/usr/bin/env python3
"""
🧪 TEST MAIN BOT CHART INITIALIZATION
====================================

Test để kiểm tra xem main bot có khởi tạo chart generator đúng không.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_bot_chart_init():
    """Test main bot chart initialization."""
    print("🧪 TESTING MAIN BOT CHART INITIALIZATION")
    print("=" * 50)
    
    try:
        # Import main bot
        print("📦 Importing main_bot...")
        from main_bot import TradingBot
        print("✅ main_bot imported successfully")
        
        # Initialize bot with minimal setup
        print("🚀 Initializing trading bot...")
        bot = TradingBot()
        
        # Check chart generator status
        print("\n📊 CHART GENERATOR STATUS:")
        print(f"  📊 hasattr(bot, 'chart_generator'): {hasattr(bot, 'chart_generator')}")
        print(f"  📊 hasattr(bot, 'chart_gen'): {hasattr(bot, 'chart_gen')}")
        
        if hasattr(bot, 'chart_generator'):
            chart_gen = getattr(bot, 'chart_generator')
            print(f"  📊 chart_generator value: {chart_gen}")
            print(f"  📊 chart_generator type: {type(chart_gen)}")
            
            if chart_gen is not None:
                print("  ✅ Chart generator is properly initialized!")
                
                # Test methods
                methods = [
                    'generate_fibonacci_chart',
                    'generate_volume_profile_chart', 
                    'generate_point_figure_chart',
                    'generate_fourier_chart',
                    'generate_ai_analysis_chart'
                ]
                
                print("  🔧 Testing chart generator methods:")
                for method in methods:
                    has_method = hasattr(chart_gen, method)
                    print(f"    📊 {method}: {'✅' if has_method else '❌'}")
                
                return True
            else:
                print("  ❌ Chart generator is None!")
                return False
        else:
            print("  ❌ Chart generator attribute not found!")
            return False
        
    except Exception as e:
        print(f"❌ Main bot chart test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main_bot_chart_init()
    if success:
        print("\n🎯 MAIN BOT CHART TEST COMPLETED SUCCESSFULLY")
    else:
        print("\n❌ MAIN BOT CHART TEST FAILED")
