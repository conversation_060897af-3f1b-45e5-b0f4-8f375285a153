#!/usr/bin/env python3
"""
🔧 PUMP DEBUG TEST
Quick test to see pump analysis debug output
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pump_debug():
    """Test pump analysis debug output."""
    print("🔧 TESTING PUMP ANALYSIS DEBUG")
    print("=" * 50)
    
    try:
        # Test 1: Check if volume detector has the method
        print("\n🔍 TEST 1: Check volume detector method")
        
        import volume_spike_detector
        detector = volume_spike_detector.VolumeSpikeDetector()
        
        has_method = hasattr(detector, 'analyze_pump_patterns')
        print(f"   📊 Has analyze_pump_patterns method: {has_method}")
        
        if has_method:
            print("   ✅ Method exists")
            
            # Test method signature
            import inspect
            sig = inspect.signature(detector.analyze_pump_patterns)
            print(f"   📊 Method signature: {sig}")
            
        else:
            print("   ❌ Method missing")
            return False
        
        # Test 2: Test method call with mock data
        print("\n🔍 TEST 2: Test method call")
        
        import pandas as pd
        import numpy as np
        
        # Create simple test data
        test_data = pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=50, freq='1H'),
            'open': np.random.uniform(50000, 51000, 50),
            'high': np.random.uniform(50500, 51500, 50),
            'low': np.random.uniform(49500, 50500, 50),
            'close': np.random.uniform(50000, 51000, 50),
            'volume': np.random.uniform(1000, 5000, 50)
        })
        
        print(f"   📊 Test data shape: {test_data.shape}")
        
        try:
            result = detector.analyze_pump_patterns(
                test_data,
                orderbook_data={},
                current_price=50500.0
            )
            
            print(f"   ✅ Method call successful")
            print(f"   📊 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
            print(f"   📊 Pump probability: {result.get('pump_probability', 'N/A')}")
            print(f"   📊 Stage: {result.get('stage', 'N/A')}")
            
            return True
            
        except Exception as method_error:
            print(f"   ❌ Method call failed: {method_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 STARTING PUMP DEBUG TEST")
    print("=" * 40)
    
    success = test_pump_debug()
    
    if success:
        print("\n🎉 PUMP DEBUG TEST SUCCESSFUL!")
        print("✅ Volume detector method is working")
        print("✅ Ready to debug main bot pump analysis")
    else:
        print("\n❌ PUMP DEBUG TEST FAILED!")
        print("❌ Volume detector method has issues")
    
    print(f"\n🎯 Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
