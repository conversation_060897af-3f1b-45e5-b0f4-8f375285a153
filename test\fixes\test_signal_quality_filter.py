#!/usr/bin/env python3
"""
🧪 Test Signal Quality Filter Configuration
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_signal_quality_config():
    """🎯 Test signal quality filter configuration."""
    print("🧪 Testing Signal Quality Filter Configuration...")
    print("=" * 60)
    
    # Load configuration
    SIGNAL_QUALITY_FILTER_ENABLED = bool(int(os.getenv("SIGNAL_QUALITY_FILTER_ENABLED", "1")))
    MIN_CONFIDENCE_THRESHOLD = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.70"))
    FIBONACCI_MIN_CONFIDENCE = float(os.getenv("FIBONACCI_MIN_CONFIDENCE", "0.70"))
    POINT_FIGURE_MIN_CONFIDENCE = float(os.getenv("POINT_FIGURE_MIN_CONFIDENCE", "0.70"))
    VOLUME_PROFILE_MIN_CONFIDENCE = float(os.getenv("VOLUME_PROFILE_MIN_CONFIDENCE", "0.70"))
    ORDERBOOK_MIN_CONFIDENCE = float(os.getenv("ORDERBOOK_MIN_CONFIDENCE", "0.70"))
    FOURIER_MIN_CONFIDENCE = float(os.getenv("FOURIER_MIN_CONFIDENCE", "0.70"))
    
    print(f"🎯 Signal Quality Filter Status:")
    print(f"  Filter Enabled: {'✅ YES' if SIGNAL_QUALITY_FILTER_ENABLED else '❌ NO'}")
    print(f"  Global Threshold: {MIN_CONFIDENCE_THRESHOLD:.0%}")
    print()
    
    print(f"📊 Individual Algorithm Thresholds:")
    print(f"  🌀 Fibonacci: {FIBONACCI_MIN_CONFIDENCE:.0%}")
    print(f"  📈 Point & Figure: {POINT_FIGURE_MIN_CONFIDENCE:.0%}")
    print(f"  📊 Volume Profile: {VOLUME_PROFILE_MIN_CONFIDENCE:.0%}")
    print(f"  📋 Orderbook: {ORDERBOOK_MIN_CONFIDENCE:.0%}")
    print(f"  🌊 Fourier: {FOURIER_MIN_CONFIDENCE:.0%}")
    print()
    
    # Test scenarios
    print("🧪 Testing Signal Scenarios:")
    print("-" * 40)
    
    test_signals = [
        {"name": "Fibonacci", "confidence": 0.85, "threshold": FIBONACCI_MIN_CONFIDENCE},
        {"name": "Fibonacci", "confidence": 0.65, "threshold": FIBONACCI_MIN_CONFIDENCE},
        {"name": "Point & Figure", "confidence": 0.75, "threshold": POINT_FIGURE_MIN_CONFIDENCE},
        {"name": "Point & Figure", "confidence": 0.60, "threshold": POINT_FIGURE_MIN_CONFIDENCE},
        {"name": "Volume Profile", "confidence": 0.80, "threshold": VOLUME_PROFILE_MIN_CONFIDENCE},
        {"name": "Volume Profile", "confidence": 0.55, "threshold": VOLUME_PROFILE_MIN_CONFIDENCE},
        {"name": "Orderbook", "confidence": 0.72, "threshold": ORDERBOOK_MIN_CONFIDENCE},
        {"name": "Orderbook", "confidence": 0.68, "threshold": ORDERBOOK_MIN_CONFIDENCE},
        {"name": "Fourier", "confidence": 0.78, "threshold": FOURIER_MIN_CONFIDENCE},
        {"name": "Fourier", "confidence": 0.45, "threshold": FOURIER_MIN_CONFIDENCE},
    ]
    
    for signal in test_signals:
        name = signal["name"]
        confidence = signal["confidence"]
        threshold = signal["threshold"]
        
        if SIGNAL_QUALITY_FILTER_ENABLED:
            will_send = confidence >= threshold
            status = "✅ SEND" if will_send else "❌ SKIP"
        else:
            will_send = True
            status = "📤 SEND (Filter Disabled)"
        
        print(f"  {name:15} | Conf: {confidence:.0%} | Threshold: {threshold:.0%} | {status}")
    
    print()
    print("🎯 Summary:")
    if SIGNAL_QUALITY_FILTER_ENABLED:
        print(f"  ✅ Quality filter is ACTIVE")
        print(f"  📊 Only signals with confidence ≥ {MIN_CONFIDENCE_THRESHOLD:.0%} will be sent")
        print(f"  🎯 This will significantly reduce noise and improve signal quality")
    else:
        print(f"  ⚠️ Quality filter is DISABLED")
        print(f"  📤 All signals will be sent regardless of confidence")
    
    print("=" * 60)
    print("✅ Signal Quality Filter Test Complete!")

def test_signal_filtering_logic():
    """🔬 Test the actual filtering logic."""
    print("\n🔬 Testing Signal Filtering Logic...")
    print("-" * 40)
    
    # Mock signal data
    mock_signals = {
        "fibonacci": {"confidence": 0.85, "signal": "BUY"},
        "point_figure": {"confidence": 0.65, "signal": "SELL"},
        "volume_profile": {"confidence": 0.75, "signal": "BUY"},
        "orderbook": {"confidence": 0.60, "signal": "NEUTRAL"},
        "fourier": {"confidence": 0.80, "signal": "BUY"}
    }
    
    # Load thresholds
    thresholds = {
        "fibonacci": float(os.getenv("FIBONACCI_MIN_CONFIDENCE", "0.70")),
        "point_figure": float(os.getenv("POINT_FIGURE_MIN_CONFIDENCE", "0.70")),
        "volume_profile": float(os.getenv("VOLUME_PROFILE_MIN_CONFIDENCE", "0.70")),
        "orderbook": float(os.getenv("ORDERBOOK_MIN_CONFIDENCE", "0.70")),
        "fourier": float(os.getenv("FOURIER_MIN_CONFIDENCE", "0.70"))
    }
    
    filter_enabled = bool(int(os.getenv("SIGNAL_QUALITY_FILTER_ENABLED", "1")))
    
    print(f"📊 Mock Signal Analysis for BICO/USDT:")
    print(f"Filter Status: {'✅ ENABLED' if filter_enabled else '❌ DISABLED'}")
    print()
    
    qualified_signals = 0
    total_signals = 0
    
    for algo, signal_data in mock_signals.items():
        confidence = signal_data["confidence"]
        signal = signal_data["signal"]
        threshold = thresholds[algo]
        
        total_signals += 1
        
        if filter_enabled:
            qualified = confidence >= threshold and signal != "NEUTRAL"
            if qualified:
                qualified_signals += 1
            status = "✅ QUALIFIED" if qualified else "❌ FILTERED OUT"
        else:
            qualified_signals += 1
            status = "📤 SENT (No Filter)"
        
        print(f"  {algo:15} | {signal:7} | {confidence:.0%} | {status}")
    
    print()
    print(f"📈 Results:")
    print(f"  Total Signals: {total_signals}")
    print(f"  Qualified: {qualified_signals}")
    print(f"  Filtered Out: {total_signals - qualified_signals}")
    print(f"  Quality Rate: {qualified_signals/total_signals:.0%}")

if __name__ == "__main__":
    test_signal_quality_config()
    test_signal_filtering_logic()
