#!/usr/bin/env python3
"""
🧪 BASIC TEST
============

Test cực kỳ đơn giản.
"""

print("🧪 BASIC TEST STARTING")

try:
    print("📦 Testing basic imports...")
    import os
    print("✅ os imported")
    
    import sys
    print("✅ sys imported")
    
    import pandas as pd
    print("✅ pandas imported")
    
    import numpy as np
    print("✅ numpy imported")
    
    import matplotlib
    print("✅ matplotlib imported")
    
    print("📦 Testing chart_generator import...")
    import chart_generator
    print("✅ chart_generator imported")
    
    print("🎯 BASIC TEST COMPLETED SUCCESSFULLY")
    
except Exception as e:
    print(f"❌ BASIC TEST FAILED: {e}")
    import traceback
    traceback.print_exc()
