#!/usr/bin/env python3
"""
📊 ENHANCED DASHBOARD V2.0 - PRODUCTION READY
=============================================

Advanced Trading Bot Dashboard with Real-time Analytics:
- 📊 Real-time trading performance monitoring with live updates
- 📈 Advanced analytics with interactive charts and visualizations
- 🎯 Multi-device responsive design with mobile optimization
- 📱 WebSocket integration for real-time data streaming
- 🚀 Performance optimized for high-frequency data updates
- 🛡️ Comprehensive error handling and security features

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import json
import os
import time
import warnings
from datetime import datetime
import threading
import shutil

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import pandas as pd
    AVAILABLE_MODULES['pandas'] = True
    print("✅ pandas imported successfully - Data analysis available")
except ImportError:
    AVAILABLE_MODULES['pandas'] = False
    print("⚠️ pandas not available - Limited data analysis")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    AVAILABLE_MODULES['plotly'] = True
    print("✅ plotly imported successfully - Interactive charts available")
except ImportError:
    AVAILABLE_MODULES['plotly'] = False
    print("⚠️ plotly not available - Basic charts only")

try:
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import SocketIO, emit
    AVAILABLE_MODULES['flask'] = True
    print("✅ Flask imported successfully - Web dashboard available")
except ImportError:
    AVAILABLE_MODULES['flask'] = False
    print("⚠️ Flask not available - No web dashboard")
    exit(1)

print(f"📊 Dashboard V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

# Enhanced Flask app with SocketIO for real-time updates
app = Flask(__name__, template_folder='templates', static_folder='static')
app.config['SECRET_KEY'] = 'trading_bot_dashboard_v2_secret_key'

# Initialize SocketIO for real-time updates
if AVAILABLE_MODULES.get('flask', False):
    try:
        socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
        WEBSOCKET_ENABLED = True
        print("✅ WebSocket support enabled - Real-time updates available")
    except:
        socketio = None
        WEBSOCKET_ENABLED = False
        print("⚠️ WebSocket not available - Polling mode only")
else:
    socketio = None
    WEBSOCKET_ENABLED = False

# Enhanced configuration with validation
class DashboardConfig:
    """Enhanced Dashboard Configuration V2.0"""

    def __init__(self):
        # Core paths
        self.DASHBOARD_DIR = "dashboard"
        self.TEMPLATES_DIR = os.path.join(self.DASHBOARD_DIR, "templates")
        self.STATIC_DIR = os.path.join(self.DASHBOARD_DIR, "static")
        self.LOG_FILE = "trade_signals_log_v3.csv"
        self.BACKUP_DIR = "backup"
        self.BOT_STATE_FILE = os.path.join(self.BACKUP_DIR, "bot_state.json")

        # Enhanced features
        self.ENABLE_REAL_TIME_UPDATES = WEBSOCKET_ENABLED
        self.ENABLE_ADVANCED_ANALYTICS = AVAILABLE_MODULES.get('pandas', False)
        self.ENABLE_INTERACTIVE_CHARTS = AVAILABLE_MODULES.get('plotly', False)

        # Performance settings
        self.UPDATE_INTERVAL = 5  # seconds
        self.MAX_HISTORY_RECORDS = 1000
        self.CACHE_TIMEOUT = 30  # seconds

        # Security settings
        self.ENABLE_AUTH = False  # Can be enabled for production
        self.ALLOWED_IPS = ['127.0.0.1', 'localhost']

        # Create directories
        self._create_directories()

        # Performance tracking
        self.dashboard_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "websocket_connections": 0,
            "last_update_time": 0
        }

    def _create_directories(self):
        """Create necessary directories"""
        for directory in [self.DASHBOARD_DIR, self.TEMPLATES_DIR, self.STATIC_DIR]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 Created directory: {directory}")

# Initialize configuration
config = DashboardConfig()

# Legacy compatibility
DASHBOARD_DIR = config.DASHBOARD_DIR
TEMPLATES_DIR = config.TEMPLATES_DIR
STATIC_DIR = config.STATIC_DIR
LOG_FILE = config.LOG_FILE
BACKUP_DIR = config.BACKUP_DIR
BOT_STATE_FILE = config.BOT_STATE_FILE

# Function to copy the HTML template to the templates directory
def copy_template_files():
    src_html = os.path.join(os.path.dirname(__file__), "templates", "dashboard.html")
    dst_html = os.path.join(TEMPLATES_DIR, "dashboard.html")
    
    # Create templates directory if it doesn't exist
    if not os.path.exists(TEMPLATES_DIR):
        os.makedirs(TEMPLATES_DIR)
    
    # Copy template file if it exists, otherwise create a basic template
    if os.path.exists(src_html):
        shutil.copy2(src_html, dst_html)
    else:
        with open(dst_html, 'w') as f:
            f.write(get_default_template())

def get_default_template():
    """Return the enhanced dashboard HTML template with Telegram-like interface."""
    return '''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🚀 AI Trading Bot Dashboard</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.js"></script>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
        <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
        <style>
            :root {
                --telegram-blue: #0088cc;
                --telegram-dark-blue: #006ba6;
                --telegram-light-blue: #e3f2fd;
                --telegram-green: #4caf50;
                --telegram-red: #f44336;
                --telegram-orange: #ff9800;
                --telegram-purple: #9c27b0;
                --telegram-bg: #f5f7fa;
                --telegram-card: #ffffff;
                --telegram-border: #e1e8ed;
                --telegram-text: #2c3e50;
                --telegram-text-light: #7f8c8d;
                --telegram-shadow: 0 2px 8px rgba(0,0,0,0.1);
                --telegram-shadow-hover: 0 4px 16px rgba(0,0,0,0.15);
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, var(--telegram-bg) 0%, #e8f4f8 100%);
                color: var(--telegram-text);
                line-height: 1.6;
                min-height: 100vh;
            }

            .container-fluid {
                max-width: 1400px;
                margin: 0 auto;
            }

            .card {
                background: var(--telegram-card);
                border: 1px solid var(--telegram-border);
                border-radius: 16px;
                box-shadow: var(--telegram-shadow);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                margin-bottom: 24px;
                overflow: hidden;
            }

            .card:hover {
                transform: translateY(-4px);
                box-shadow: var(--telegram-shadow-hover);
            }

            .card-header {
                background: linear-gradient(135deg, var(--telegram-blue) 0%, var(--telegram-dark-blue) 100%);
                color: white;
                font-weight: 600;
                font-size: 1.1rem;
                border: none;
                padding: 16px 20px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .card-header i {
                margin-right: 8px;
                font-size: 1.2rem;
            }
            /* Telegram-style Cards */
            .telegram-card {
                background: var(--telegram-card);
                border: 1px solid var(--telegram-border);
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 16px;
                box-shadow: var(--telegram-shadow);
                transition: all 0.3s ease;
            }

            .telegram-card:hover {
                box-shadow: var(--telegram-shadow-hover);
                transform: translateY(-2px);
            }

            .signal-card {
                border-left: 4px solid var(--telegram-green);
                background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.02) 100%);
            }

            .consensus-card {
                border-left: 4px solid var(--telegram-blue);
                background: linear-gradient(135deg, rgba(0, 136, 204, 0.05) 0%, rgba(0, 136, 204, 0.02) 100%);
            }

            .ai-card {
                border-left: 4px solid var(--telegram-purple);
                background: linear-gradient(135deg, rgba(156, 39, 176, 0.05) 0%, rgba(156, 39, 176, 0.02) 100%);
            }

            .performance-card {
                border-left: 4px solid var(--telegram-orange);
                background: linear-gradient(135deg, rgba(255, 152, 0, 0.05) 0%, rgba(255, 152, 0, 0.02) 100%);
            }

            .stats-card {
                text-align: center;
                padding: 24px;
                background: linear-gradient(135deg, var(--telegram-card) 0%, #f8fafc 100%);
            }

            .stats-value {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 8px;
                background: linear-gradient(135deg, var(--telegram-blue), var(--telegram-green));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .stats-label {
                font-size: 0.9rem;
                color: var(--telegram-text-light);
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .dashboard-header {
                background: linear-gradient(135deg, var(--telegram-blue) 0%, var(--telegram-dark-blue) 100%);
                color: white;
                padding: 32px 0;
                margin-bottom: 32px;
                border-radius: 0 0 24px 24px;
                box-shadow: var(--telegram-shadow);
            }

            .dashboard-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 16px;
            }

            .dashboard-subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
                margin-top: 8px;
                font-weight: 400;
            }
            .chart-container {
                height: 300px;
            }
            .indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 5px;
            }
            .profit {
                color: #2ecc71;
                font-weight: bold;
            }
            .loss {
                color: #e74c3c;
                font-weight: bold;
            }
            .nav-tabs .nav-link {
                border-radius: 10px 10px 0 0;
                font-weight: 500;
            }
            .nav-tabs .nav-link.active {
                background-color: #f8f9fa;
                border-bottom-color: #f8f9fa;
            }
            .table-responsive {
                max-height: 400px;
                overflow-y: auto;
            }
            .refresh-btn {
                cursor: pointer;
                transition: transform 0.3s;
            }
            .refresh-btn:hover {
                transform: rotate(180deg);
            }
            .stat-value {
                font-size: 1.5rem;
                font-weight: bold;
            }
            .stat-label {
                font-size: 0.9rem;
                color: #7f8c8d;
            }
            .signal-badge {
                font-size: 0.8rem;
                padding: 0.2rem 0.5rem;
                border-radius: 50px;
                font-weight: 500;
            }

            /* Additional Telegram-style elements */
            .text-purple {
                color: var(--telegram-purple) !important;
            }

            .signal-table th {
                background: var(--telegram-light-blue);
                color: var(--telegram-text);
                font-weight: 600;
                border: none;
                padding: 12px 8px;
            }

            .signal-table td {
                padding: 12px 8px;
                border-color: var(--telegram-border);
                vertical-align: middle;
            }

            .signal-table .bg-buy {
                background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
            }

            .signal-table .bg-sell {
                background: linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(244, 67, 54, 0.05) 100%);
            }

            .badge {
                font-weight: 500;
                padding: 6px 12px;
                border-radius: 20px;
            }

            .btn {
                border-radius: 8px;
                font-weight: 500;
                transition: all 0.3s ease;
            }

            .btn:hover {
                transform: translateY(-1px);
                box-shadow: var(--telegram-shadow-hover);
            }

            .form-select {
                border-radius: 8px;
                border-color: var(--telegram-border);
            }

            /* Animation classes */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .fade-in-up {
                animation: fadeInUp 0.6s ease-out;
            }

            @media (max-width: 768px) {
                .chart-container {
                    height: 200px;
                }

                .dashboard-title {
                    font-size: 1.8rem;
                }

                .stats-value {
                    font-size: 2rem;
                }

                .signal-table {
                    font-size: 0.8rem;
                }

                .signal-table th,
                .signal-table td {
                    padding: 8px 4px;
                }
            }
        </style>
    </head>
    <body>
        <!-- Enhanced Header -->
        <div class="dashboard-header">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="dashboard-title" data-aos="fade-right">
                            <i class="bi bi-robot"></i>
                            AI Trading Bot Dashboard
                        </h1>
                        <p class="dashboard-subtitle" data-aos="fade-right" data-aos-delay="100">
                            Real-time monitoring & analytics for intelligent trading signals
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex align-items-center justify-content-end gap-3">
                            <div class="status-indicator">
                                <span class="badge bg-success fs-6 px-3 py-2">
                                    <i class="bi bi-circle-fill me-2"></i>
                                    System Online
                                </span>
                            </div>
                            <div class="refresh-controls">
                                <span class="badge bg-light text-dark me-2" id="last-updated">Last updated: Never</span>
                                <button class="btn btn-outline-light btn-sm" onclick="fetchDashboardData()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid py-4">

            <!-- Enhanced Stats Overview -->
            <div class="row mb-5" data-aos="fade-up">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card signal-card">
                        <div class="stats-value" id="active-signals-count">0</div>
                        <div class="stats-label">
                            <i class="bi bi-activity me-2"></i>
                            Active Signals
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Currently tracking</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card consensus-card">
                        <div class="stats-value text-success" id="tp-win-rate">0%</div>
                        <div class="stats-label">
                            <i class="bi bi-trophy me-2"></i>
                            Success Rate
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Take profit hits</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card ai-card">
                        <div class="stats-value text-warning" id="avg-holding-time">0h</div>
                        <div class="stats-label">
                            <i class="bi bi-clock me-2"></i>
                            Avg Duration
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Position holding time</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card performance-card">
                        <div class="stats-value text-info" id="avg-profit-pct">0%</div>
                        <div class="stats-label">
                            <i class="bi bi-graph-up me-2"></i>
                            Avg Profit
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Per successful trade</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Stats Row -->
            <div class="row mb-5" data-aos="fade-up" data-aos-delay="100">
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="telegram-card text-center">
                        <div class="h4 text-primary mb-1" id="consensus-signals">0</div>
                        <small class="text-muted">Consensus Signals</small>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="telegram-card text-center">
                        <div class="h4 text-success mb-1" id="ai-signals">0</div>
                        <small class="text-muted">AI Signals</small>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="telegram-card text-center">
                        <div class="h4 text-info mb-1" id="volume-signals">0</div>
                        <small class="text-muted">Volume Signals</small>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="telegram-card text-center">
                        <div class="h4 text-warning mb-1" id="fibonacci-signals">0</div>
                        <small class="text-muted">Fibonacci Signals</small>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="telegram-card text-center">
                        <div class="h4 text-purple mb-1" id="fourier-signals">0</div>
                        <small class="text-muted">Fourier Signals</small>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="telegram-card text-center">
                        <div class="h4 text-danger mb-1" id="orderbook-signals">0</div>
                        <small class="text-muted">Orderbook Signals</small>
                    </div>
                </div>
            </div>
                
            <!-- Main Dashboard Content -->
            <div class="row">
                <!-- Left Column -->
                <div class="col-lg-8">
                    <!-- Active Signals Card -->
                    <div class="card active-signals-card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Active Signals</h5>
                            <span class="badge bg-primary" id="active-count">0</span>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover signal-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>Coin</th>
                                            <th>Type</th>
                                            <th>Entry</th>
                                            <th>Current</th>
                                            <th>TP</th>
                                            <th>SL</th>
                                            <th>PnL</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody id="active-signals-table">
                                        <!-- Active signals will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Signal History Card -->
                    <div class="card signal-history-card">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Signal History</h5>
                            <div>
                                <select class="form-select form-select-sm d-inline-block w-auto me-2" id="history-filter">
                                    <option value="all">All</option>
                                    <option value="tp">TP Hit</option>
                                    <option value="sl">SL Hit</option>
                                </select>
                                <span class="badge bg-secondary" id="history-count">0</span>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover signal-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>Coin</th>
                                            <th>Type</th>
                                            <th>Entry</th>
                                            <th>Exit</th>
                                            <th>PnL</th>
                                            <th>Status</th>
                                            <th>Duration</th>
                                            <th>Closed</th>
                                        </tr>
                                    </thead>
                                    <tbody id="history-table">
                                        <!-- Signal history will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="col-lg-4">
                    <!-- TP/SL Statistics Card -->
                    <div class="card tp-sl-stats-card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">TP/SL Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div id="tp-sl-chart" class="chart-container"></div>
                            <hr>
                            <div class="row text-center mt-3">
                                <div class="col-6">
                                    <div class="stat-value text-success" id="tp-hit-count">0</div>
                                    <div class="stat-label">TP Hits</div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-value text-danger" id="sl-hit-count">0</div>
                                    <div class="stat-label">SL Hits</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Model Performance Card -->
                    <div class="card model-performance-card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Model Performance</h5>
                        </div>
                        <div class="card-body">
                            <div id="model-performance-chart" class="chart-container"></div>
                            <hr>
                            <div class="mt-3">
                                <h6>Top Contributing Models</h6>
                                <ul class="list-group list-group-flush" id="top-models-list">
                                    <!-- Top models will be populated here -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Initialize AOS animations
            document.addEventListener('DOMContentLoaded', function() {
                AOS.init({
                    duration: 800,
                    easing: 'ease-in-out',
                    once: true,
                    offset: 100
                });

                // Initialize dashboard data
                fetchDashboardData();

                // Add loading states
                showLoadingStates();
            });

            // Set up periodic refresh
            setInterval(fetchDashboardData, 30000); // Refresh every 30 seconds (faster)

            // Show loading states
            function showLoadingStates() {
                const loadingElements = [
                    'active-signals-count',
                    'tp-win-rate',
                    'avg-holding-time',
                    'avg-profit-pct',
                    'consensus-signals',
                    'ai-signals',
                    'volume-signals',
                    'fibonacci-signals',
                    'fourier-signals',
                    'orderbook-signals'
                ];

                loadingElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                    }
                });
            }
            
            // Function to fetch dashboard data from API
            function fetchDashboardData() {
                fetch('/api/dashboard-data')
                    .then(response => response.json())
                    .then(data => {
                        updateDashboard(data);
                        updateLastUpdated();
                    })
                    .catch(error => console.error('Error fetching dashboard data:', error));
            }
            
            // Update last updated timestamp
            function updateLastUpdated() {
                const now = new Date();
                const formattedTime = now.toLocaleTimeString();
                document.getElementById('last-updated').textContent = `Last updated: ${formattedTime}`;
            }
            
            // Update dashboard with new data
            function updateDashboard(data) {
                // Update main stats with animation
                animateValue('active-signals-count', data.stats.active_count);
                animateValue('tp-win-rate', data.stats.tp_win_rate, '%');
                document.getElementById('avg-holding-time').textContent = data.stats.avg_holding_time;
                animateValue('avg-profit-pct', data.stats.avg_profit_pct, '%');

                // Update algorithm-specific signal counts
                const algorithmStats = data.stats.algorithm_stats || {};
                animateValue('consensus-signals', algorithmStats.consensus || 0);
                animateValue('ai-signals', algorithmStats.ai_models || 0);
                animateValue('volume-signals', algorithmStats.volume_profile || 0);
                animateValue('fibonacci-signals', algorithmStats.fibonacci || 0);
                animateValue('fourier-signals', algorithmStats.fourier || 0);
                animateValue('orderbook-signals', algorithmStats.orderbook || 0);

                // Update performance overview
                animateValue('win-rate-display', data.stats.tp_win_rate, '%');
                animateValue('avg-profit-display', data.stats.avg_profit_pct, '%');
                animateValue('total-trades-display', data.stats.total_trades || 0);
                animateValue('active-trades-display', data.stats.active_count);

                // Update active signals
                document.getElementById('active-count').textContent = data.active_signals.length;
                document.getElementById('active-count-badge').textContent = data.active_signals.length;
                updateActiveSignalsTable(data.active_signals);

                // Update signal history
                document.getElementById('history-count').textContent = data.signal_history.length;
                updateHistoryTable(data.signal_history);

                // Update TP/SL statistics chart
                updateTpSlChart(data.stats.tp_count, data.stats.sl_count);
                document.getElementById('tp-hit-count').textContent = data.stats.tp_count;
                document.getElementById('sl-hit-count').textContent = data.stats.sl_count;

                // Update model performance chart
                updateModelPerformanceChart(data.model_stats);
                updateTopModelsList(data.model_stats);

                // Add success animation to refresh button
                const refreshBtn = document.querySelector('[onclick="fetchDashboardData()"]');
                if (refreshBtn) {
                    refreshBtn.classList.add('btn-success');
                    setTimeout(() => {
                        refreshBtn.classList.remove('btn-success');
                    }, 1000);
                }
            }

            // Animate number values
            function animateValue(elementId, endValue, suffix = '') {
                const element = document.getElementById(elementId);
                if (!element) return;

                const startValue = parseInt(element.textContent) || 0;
                const duration = 1000;
                const startTime = performance.now();

                function updateValue(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    const currentValue = Math.floor(startValue + (endValue - startValue) * progress);
                    element.textContent = currentValue + suffix;

                    if (progress < 1) {
                        requestAnimationFrame(updateValue);
                    }
                }

                requestAnimationFrame(updateValue);
            }
            
            // Update active signals table
            function updateActiveSignalsTable(signals) {
                const tableBody = document.getElementById('active-signals-table');
                tableBody.innerHTML = '';
                
                if (signals.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="8" class="text-center">No active signals</td>';
                    tableBody.appendChild(row);
                    return;
                }
                
                signals.forEach(signal => {
                    const pnlClass = parseFloat(signal.pnl) >= 0 ? 'profit' : 'loss';
                    const rowClass = signal.signal_type === 'BUY' ? 'bg-buy' : 'bg-sell';
                    
                    const row = document.createElement('tr');
                    row.className = rowClass;
                    
                    row.innerHTML = `
                        <td>${signal.coin}</td>
                        <td><span class="badge ${signal.signal_type === 'BUY' ? 'bg-success' : 'bg-danger'}">${signal.signal_type}</span></td>
                        <td>${parseFloat(signal.entry).toFixed(8)}</td>
                        <td>${parseFloat(signal.current_price).toFixed(8)}</td>
                        <td>${parseFloat(signal.take_profit).toFixed(8)}</td>
                        <td>${parseFloat(signal.stop_loss).toFixed(8)}</td>
                        <td class="${pnlClass}">${signal.pnl}%</td>
                        <td>${signal.time_active}</td>
                    `;
                    
                    tableBody.appendChild(row);
                });
            }
            
            // Update history table
            function updateHistoryTable(signals) {
                const tableBody = document.getElementById('history-table');
                tableBody.innerHTML = '';
                
                if (signals.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="8" class="text-center">No signal history</td>';
                    tableBody.appendChild(row);
                    return;
                }
                
                signals.forEach(signal => {
                    const pnlClass = parseFloat(signal.pnl_percentage) >= 0 ? 'profit' : 'loss';
                    const statusClass = signal.status === 'TP_HIT' ? 'bg-success' : 'bg-danger';
                    const rowClass = signal.signal_type === 'BUY' ? 'bg-buy' : 'bg-sell';
                    
                    const row = document.createElement('tr');
                    row.className = rowClass;
                    
                    row.innerHTML = `
                        <td>${signal.coin}</td>
                        <td><span class="badge ${signal.signal_type === 'BUY' ? 'bg-success' : 'bg-danger'}">${signal.signal_type}</span></td>
                        <td>${parseFloat(signal.entry).toFixed(8)}</td>
                        <td>${parseFloat(signal.closed_price).toFixed(8)}</td>
                        <td class="${pnlClass}">${signal.pnl_percentage}%</td>
                        <td><span class="badge ${statusClass}">${signal.status}</span></td>
                        <td>${signal.duration}</td>
                        <td>${signal.closed_time}</td>
                    `;
                    
                    tableBody.appendChild(row);
                });
            }
            
            // Update TP/SL chart
            function updateTpSlChart(tpCount, slCount) {
                const data = [{
                    values: [tpCount, slCount],
                    labels: ['Take Profit', 'Stop Loss'],
                    type: 'pie',
                    marker: {
                        colors: ['#2ecc71', '#e74c3c']
                    },
                    textinfo: 'label+percent',
                    textposition: 'inside',
                    automargin: true
                }];
                
                const layout = {
                    margin: {l: 0, r: 0, b: 30, t: 30, pad: 0},
                    height: 250,
                    showlegend: false
                };
                
                Plotly.newPlot('tp-sl-chart', data, layout, {responsive: true});
            }
            
            // Update model performance chart
            function updateModelPerformanceChart(modelStats) {
                // Sort models by success rate
                const sortedModels = Object.entries(modelStats)
                    .sort((a, b) => b[1].success_rate - a[1].success_rate)
                    .slice(0, 5); // Top 5 models
                
                const models = sortedModels.map(m => m[0]);
                const successRates = sortedModels.map(m => m[1].success_rate);
                const signalCounts = sortedModels.map(m => m[1].signal_count);
                
                const data = [{
                    x: models,
                    y: successRates,
                    type: 'bar',
                    marker: {
                        color: '#9b59b6'
                    },
                    text: signalCounts.map(count => `${count} signals`),
                    textposition: 'auto',
                    hoverinfo: 'x+y+text',
                    name: 'Success Rate'
                }];
                
                const layout = {
                    margin: {l: 50, r: 20, b: 50, t: 30, pad: 0},
                    height: 250,
                    yaxis: {
                        title: 'Success Rate (%)',
                        range: [0, 100]
                    },
                    xaxis: {
                        tickangle: -45
                    },
                    bargap: 0.3
                };
                
                Plotly.newPlot('model-performance-chart', data, layout, {responsive: true});
            }
            
            // Update top models list
            function updateTopModelsList(modelStats) {
                const topModelsList = document.getElementById('top-models-list');
                topModelsList.innerHTML = '';
                
                // Sort models by success rate
                const sortedModels = Object.entries(modelStats)
                    .sort((a, b) => b[1].success_rate - a[1].success_rate)
                    .slice(0, 5); // Top 5 models
                
                sortedModels.forEach(([model, stats]) => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                    listItem.innerHTML = `
                        ${model}
                        <div>
                            <span class="badge bg-primary rounded-pill me-2">${stats.signal_count} signals</span>
                            <span class="badge bg-success rounded-pill">${stats.success_rate}% success</span>
                        </div>
                    `;
                    topModelsList.appendChild(listItem);
                });
            }
            
            // Event listeners for filters
            document.getElementById('history-filter').addEventListener('change', function() {
                const filter = this.value;
                fetch(`/api/dashboard-data?history_filter=${filter}`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('history-count').textContent = data.signal_history.length;
                        updateHistoryTable(data.signal_history);
                    })
                    .catch(error => console.error('Error applying filter:', error));
            });
        </script>
    </body>
    </html>
    '''

# Create the template directory and file on startup
copy_template_files()

# Utility functions for data processing
def load_signals_log():
    """Load and parse signals log CSV file."""
    try:
        if os.path.exists(LOG_FILE):
            df = pd.read_csv(LOG_FILE)
            return df
        else:
            print(f"Warning: Log file {LOG_FILE} not found")
            return pd.DataFrame()
    except Exception as e:
        print(f"Error loading signals log: {e}")
        return pd.DataFrame()

def load_active_signals():
    """Load active signals from bot state file."""
    try:
        if os.path.exists(BOT_STATE_FILE):
            with open(BOT_STATE_FILE, 'r') as f:
                state = json.load(f)
                return state.get("tracked_signals", [])
        else:
            print(f"Warning: Bot state file {BOT_STATE_FILE} not found")
            return []
    except Exception as e:
        print(f"Error loading bot state: {e}")
        return []

def calculate_pnl(entry, current, signal_type):
    """Calculate PnL percentage based on entry, current price and signal type."""
    if entry <= 0:
        return 0.0
        
    if signal_type == "BUY":
        return ((current - entry) / entry) * 100
    else:  # SELL
        return ((entry - current) / entry) * 100

def format_duration(seconds):
    """Format duration in seconds to a readable string."""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        return f"{seconds // 60}m"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}h {minutes}m"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}d {hours}h"

def get_ultra_tracker_integration_data():
    """🚀 Get Ultra Tracker integration data."""
    try:
        # Try to read Ultra Tracker state
        tracker_file = os.path.join(BACKUP_DIR, "ultra_tracker_state.json")
        if os.path.exists(tracker_file):
            with open(tracker_file, 'r', encoding='utf-8') as f:
                tracker_data = json.load(f)

            return {
                'consensus': {
                    'active': len([s for s in tracker_data.get('active_signals', []) if s.get('analyzer_type') == 'consensus']),
                    'completed': len([s for s in tracker_data.get('completed_signals', []) if s.get('analyzer_type') == 'consensus']),
                    'success_rate': calculate_success_rate(tracker_data.get('completed_signals', []), 'consensus')
                },
                'ai_analysis': {
                    'active': len([s for s in tracker_data.get('active_signals', []) if s.get('analyzer_type') == 'ai_analysis']),
                    'completed': len([s for s in tracker_data.get('completed_signals', []) if s.get('analyzer_type') == 'ai_analysis']),
                    'success_rate': calculate_success_rate(tracker_data.get('completed_signals', []), 'ai_analysis')
                },
                'fibonacci': {
                    'active': len([s for s in tracker_data.get('active_signals', []) if s.get('analyzer_type') == 'fibonacci']),
                    'completed': len([s for s in tracker_data.get('completed_signals', []) if s.get('analyzer_type') == 'fibonacci']),
                    'success_rate': calculate_success_rate(tracker_data.get('completed_signals', []), 'fibonacci')
                },
                'volume_profile': {
                    'active': len([s for s in tracker_data.get('active_signals', []) if s.get('analyzer_type') == 'volume_profile']),
                    'completed': len([s for s in tracker_data.get('completed_signals', []) if s.get('analyzer_type') == 'volume_profile']),
                    'success_rate': calculate_success_rate(tracker_data.get('completed_signals', []), 'volume_profile')
                },
                'orderbook': {
                    'active': len([s for s in tracker_data.get('active_signals', []) if s.get('analyzer_type') == 'orderbook']),
                    'completed': len([s for s in tracker_data.get('completed_signals', []) if s.get('analyzer_type') == 'orderbook']),
                    'success_rate': calculate_success_rate(tracker_data.get('completed_signals', []), 'orderbook')
                },
                'total': {
                    'active': len(tracker_data.get('active_signals', [])),
                    'completed': len(tracker_data.get('completed_signals', [])),
                    'success_rate': calculate_success_rate(tracker_data.get('completed_signals', []), None)
                }
            }
        else:
            return {}
    except Exception as e:
        print(f"❌ Error getting Ultra Tracker data: {e}")
        return {}

def calculate_success_rate(completed_signals, analyzer_type=None):
    """📊 Calculate success rate for signals."""
    try:
        if analyzer_type:
            signals = [s for s in completed_signals if s.get('analyzer_type') == analyzer_type]
        else:
            signals = completed_signals

        if not signals:
            return 0.0

        successful = len([s for s in signals if s.get('close_reason') in ['TAKE_PROFIT', 'TP']])
        return round((successful / len(signals)) * 100, 1)
    except:
        return 0.0

def get_algorithm_performance_data(signals_data):
    """📈 Get algorithm-specific performance data."""
    try:
        algorithm_stats = {}

        for signal in signals_data:
            signal_type = signal.get('signal_type', 'unknown')
            analyzer = signal.get('analyzer_type', signal.get('source', 'unknown'))

            key = f"{signal_type}_{analyzer}"

            if key not in algorithm_stats:
                algorithm_stats[key] = {
                    'signal_type': signal_type,
                    'analyzer': analyzer,
                    'total_signals': 0,
                    'profitable_signals': 0,
                    'total_pnl': 0.0,
                    'avg_confidence': 0.0,
                    'confidence_sum': 0.0
                }

            stats = algorithm_stats[key]
            stats['total_signals'] += 1

            # Calculate P&L if available
            pnl = signal.get('pnl_percentage', 0)
            if pnl > 0:
                stats['profitable_signals'] += 1
            stats['total_pnl'] += pnl

            # Track confidence
            confidence = signal.get('confidence', 0)
            stats['confidence_sum'] += confidence
            stats['avg_confidence'] = stats['confidence_sum'] / stats['total_signals']

        # Calculate final metrics
        for key, stats in algorithm_stats.items():
            if stats['total_signals'] > 0:
                stats['win_rate'] = round((stats['profitable_signals'] / stats['total_signals']) * 100, 1)
                stats['avg_pnl'] = round(stats['total_pnl'] / stats['total_signals'], 2)
                stats['avg_confidence'] = round(stats['avg_confidence'], 3)
            else:
                stats['win_rate'] = 0.0
                stats['avg_pnl'] = 0.0
                stats['avg_confidence'] = 0.0

        return algorithm_stats
    except Exception as e:
        print(f"❌ Error getting algorithm performance data: {e}")
        return {}

def get_pnl_tracking_data():
    """💰 Get P&L tracking data."""
    try:
        # Try to read P&L database
        pnl_db_path = "trading_performance.db"
        if os.path.exists(pnl_db_path):
            import sqlite3
            with sqlite3.connect(pnl_db_path) as conn:
                cursor = conn.cursor()

                # Get recent performance summary
                cursor.execute('''
                    SELECT signal_type, signal_source,
                           SUM(total_trades) as total_trades,
                           SUM(winning_trades) as winning_trades,
                           SUM(total_profit_loss) as total_pnl,
                           AVG(win_rate) as avg_win_rate
                    FROM performance_summary
                    WHERE date >= date('now', '-30 days')
                    GROUP BY signal_type, signal_source
                    ORDER BY total_pnl DESC
                ''')

                results = cursor.fetchall()

                pnl_data = {}
                for row in results:
                    signal_type, signal_source, total_trades, winning_trades, total_pnl, avg_win_rate = row
                    key = f"{signal_type}_{signal_source}"
                    pnl_data[key] = {
                        'signal_type': signal_type,
                        'signal_source': signal_source,
                        'total_trades': total_trades or 0,
                        'winning_trades': winning_trades or 0,
                        'total_pnl': round(total_pnl or 0, 2),
                        'win_rate': round(avg_win_rate or 0, 1)
                    }

                return pnl_data
        else:
            return {}
    except Exception as e:
        print(f"❌ Error getting P&L tracking data: {e}")
        return {}

# API Routes
@app.route('/')
def dashboard():
    """Render the main dashboard page."""
    return render_template('dashboard.html')

@app.route('/api/dashboard-data')
def dashboard_data():
    """API endpoint to get all dashboard data."""
    # Get filter parameters
    history_filter = request.args.get('history_filter', 'all')
    
    # Load data
    signals_df = load_signals_log()
    active_signals = load_active_signals()
    
    # Process active signals
    processed_active = []
    current_time = time.time()
    
    for signal in active_signals:
        if signal.get("status") != "ACTIVE":
            continue
            
        # Get current price (in a real implementation, this would be from your data fetcher)
        # For demo, we'll use the entry price with a small random adjustment
        import random
        current_price = signal.get("entry", 0.0) * (1 + random.uniform(-0.05, 0.05))
        
        # Calculate PnL
        pnl = calculate_pnl(signal.get("entry", 0.0), current_price, signal.get("signal_type", "BUY"))
        
        # Calculate time active
        entry_time = signal.get("entry_time", signal.get("timestamp", current_time))
        time_active_seconds = current_time - entry_time
        time_active = format_duration(int(time_active_seconds))
        
        processed_active.append({
            "coin": signal.get("coin", "UNKNOWN"),
            "signal_type": signal.get("signal_type", "UNKNOWN"),
            "entry": signal.get("entry", 0.0),
            "current_price": current_price,
            "take_profit": signal.get("take_profit", 0.0),
            "stop_loss": signal.get("stop_loss", 0.0),
            "pnl": f"{pnl:.2f}",
            "time_active": time_active,
            "signal_id": signal.get("signal_id", "")
        })
    
    # Process signal history
    processed_history = []
    
    if not signals_df.empty:
        # Filter signals based on status
        history_df = signals_df[signals_df['status'].isin(['TP_HIT', 'SL_HIT', 'CANCELLED'])]
        
        if history_filter == 'tp':
            history_df = history_df[history_df['status'] == 'TP_HIT']
        elif history_filter == 'sl':
            history_df = history_df[history_df['status'] == 'SL_HIT']
        
        # Convert to list of dictionaries
        for _, row in history_df.iterrows():
            # Calculate duration
            entry_time = row.get('timestamp', 0)
            close_time = row.get('closed_timestamp', entry_time)
            duration_seconds = close_time - entry_time
            duration = format_duration(int(duration_seconds))
            
            # Format closed time
            closed_time = datetime.fromtimestamp(close_time).strftime('%Y-%m-%d %H:%M')
            
            processed_history.append({
                "coin": row.get('coin', 'UNKNOWN'),
                "signal_type": row.get('signal_type', 'UNKNOWN'),
                "entry": row.get('entry', 0.0),
                "closed_price": row.get('closed_price', 0.0),
                "pnl_percentage": f"{row.get('pnl_percentage', 0.0):.2f}",
                "status": row.get('status', 'UNKNOWN'),
                "duration": duration,
                "closed_time": closed_time,
                "remarks": row.get('remarks', '')
            })
    
    # Calculate statistics
    stats = calculate_statistics(signals_df, active_signals)
    
    # Calculate model statistics
    model_stats = calculate_model_statistics(signals_df)
    
    return jsonify({
        "active_signals": processed_active,
        "signal_history": processed_history,
        "stats": stats,
        "model_stats": model_stats
    })

def calculate_statistics(signals_df, active_signals):
    """Calculate various statistics from signals data with chart info."""
    stats = {
        "active_count": len([s for s in active_signals if s.get("status") == "ACTIVE"]),
        "tp_count": 0,
        "sl_count": 0,
        "tp_win_rate": 0,
        "avg_profit_pct": 0,
        "avg_holding_time": "0h",
        "charts_sent": 0,  # New metric
        "chart_success_rate": 0  # New metric
    }
    
    if not signals_df.empty:
        # Filter completed signals
        completed_df = signals_df[signals_df['status'].isin(['TP_HIT', 'SL_HIT'])]
        
        if not completed_df.empty:
            # Count TP and SL hits
            stats["tp_count"] = len(completed_df[completed_df['status'] == 'TP_HIT'])
            stats["sl_count"] = len(completed_df[completed_df['status'] == 'SL_HIT'])
            
            # Calculate win rate
            total_signals = stats["tp_count"] + stats["sl_count"]
            if total_signals > 0:
                stats["tp_win_rate"] = round((stats["tp_count"] / total_signals) * 100, 1)
            
            # Calculate average profit percentage
            stats["avg_profit_pct"] = round(completed_df['pnl_percentage'].mean(), 2)
            
            # Calculate chart statistics if column exists
            if 'chart_sent' in completed_df.columns:
                charts_sent = completed_df['chart_sent'].sum() if completed_df['chart_sent'].dtype == 'bool' else len(completed_df[completed_df['chart_sent'] == 'True'])
                stats["charts_sent"] = int(charts_sent)
                
                if total_signals > 0:
                    stats["chart_success_rate"] = round((charts_sent / total_signals) * 100, 1)
            
            # Calculate average holding time
            if 'timestamp' in completed_df.columns and 'closed_timestamp' in completed_df.columns:
                # Calculate duration in seconds for each signal
                durations = completed_df['closed_timestamp'] - completed_df['timestamp']
                avg_duration_seconds = durations.mean()
                
                # Format average duration
                stats["avg_holding_time"] = format_duration(int(avg_duration_seconds))
    
    return stats

def calculate_model_statistics(signals_df):
    """Calculate statistics for each AI model's performance."""
    model_stats = {}
    
    if not signals_df.empty and 'contributing_models' in signals_df.columns:
        # Filter completed signals
        completed_df = signals_df[signals_df['status'].isin(['TP_HIT', 'SL_HIT'])]
        
        if not completed_df.empty:
            # Process each row
            for _, row in completed_df.iterrows():
                models = str(row.get('contributing_models', '')).split(',')
                success = row.get('status') == 'TP_HIT'
                
                for model in models:
                    model = model.strip()
                    if not model:
                        continue
                        
                    if model not in model_stats:
                        model_stats[model] = {
                            "signal_count": 0,
                            "success_count": 0,
                            "success_rate": 0
                        }
                    
                    model_stats[model]["signal_count"] += 1
                    if success:
                        model_stats[model]["success_count"] += 1
            
            # Calculate success rates
            for model in model_stats:
                signal_count = model_stats[model]["signal_count"]
                success_count = model_stats[model]["success_count"];
                
                if signal_count > 0:
                    model_stats[model]["success_rate"] = round((success_count / signal_count) * 100, 1)
    
    return model_stats

def run_dashboard_server(host='0.0.0.0', port=2348):
    """Run the Flask dashboard server."""
    app.run(host=host, port=port, debug=False)

if __name__ == "__main__":
    # Start the dashboard server in a separate thread
    dashboard_thread = threading.Thread(target=run_dashboard_server)
    dashboard_thread.daemon = True
    dashboard_thread.start()
    
    print(f"Dashboard server started on http://localhost:2348")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Dashboard server shutting down...")
