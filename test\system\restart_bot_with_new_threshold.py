#!/usr/bin/env python3
"""
🔄 Restart bot với new threshold configuration
"""

import os
import sys
import time
import subprocess
from dotenv import load_dotenv

def verify_configuration():
    """Verify that configuration is correct"""
    print("🔍 VERIFYING CONFIGURATION")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check environment variables
    env_threshold = os.getenv("MIN_CONFIDENCE_THRESHOLD", "NOT_SET")
    ai_min_conf = os.getenv("AI_REPORT_MIN_CONFIDENCE", "NOT_SET")
    ai_min_quality = os.getenv("AI_TECHNICAL_MIN_QUALITY", "NOT_SET")
    
    print(f"📊 Environment Variables:")
    print(f"  MIN_CONFIDENCE_THRESHOLD = {env_threshold}")
    print(f"  AI_REPORT_MIN_CONFIDENCE = {ai_min_conf}")
    print(f"  AI_TECHNICAL_MIN_QUALITY = {ai_min_quality}")
    
    # Check if values are correct
    expected_values = {
        "MIN_CONFIDENCE_THRESHOLD": "0.80",
        "AI_REPORT_MIN_CONFIDENCE": "0.9",
        "AI_TECHNICAL_MIN_QUALITY": "0.9"
    }
    
    all_correct = True
    for key, expected in expected_values.items():
        actual = os.getenv(key, "NOT_SET")
        if actual == expected:
            print(f"  ✅ {key}: {actual} (correct)")
        else:
            print(f"  ❌ {key}: {actual} (expected: {expected})")
            all_correct = False
    
    return all_correct

def test_import_configuration():
    """Test importing main_bot to see actual values"""
    print("\n🧪 TESTING IMPORT CONFIGURATION")
    print("=" * 50)
    
    try:
        # Clear any cached modules
        modules_to_clear = [
            'main_bot',
            'consensus_analyzer',
            'trigger_consensus_analysis'
        ]
        
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
                print(f"  🧹 Cleared cached module: {module}")
        
        # Import fresh
        import main_bot
        
        print(f"📊 Fresh Import Values:")
        print(f"  MIN_CONFIDENCE_THRESHOLD = {main_bot.MIN_CONFIDENCE_THRESHOLD}")
        print(f"  AI_REPORT_MIN_CONFIDENCE = {main_bot.AI_REPORT_MIN_CONFIDENCE}")
        print(f"  AI_TECHNICAL_MIN_QUALITY = {main_bot.AI_TECHNICAL_MIN_QUALITY}")
        
        # Test consensus analyzer initialization
        if hasattr(main_bot, 'TradingBot'):
            print(f"  TradingBot class available: ✅")
        
        # Check if threshold is correct
        if main_bot.MIN_CONFIDENCE_THRESHOLD == 0.80:
            print(f"  ✅ Consensus threshold is correct: 80%")
            return True
        else:
            print(f"  ❌ Consensus threshold is wrong: {main_bot.MIN_CONFIDENCE_THRESHOLD*100:.1f}%")
            return False
            
    except Exception as e:
        print(f"  ❌ Error importing main_bot: {e}")
        return False

def create_test_consensus_script():
    """Create a test script to verify consensus threshold"""
    print("\n📝 CREATING TEST CONSENSUS SCRIPT")
    print("=" * 50)
    
    test_script = """#!/usr/bin/env python3
import os
from dotenv import load_dotenv

# Load environment
load_dotenv()

# Test consensus threshold
try:
    import main_bot
    import consensus_analyzer
    
    print("🔍 CONSENSUS THRESHOLD TEST")
    print("=" * 40)
    
    # Check main_bot threshold
    threshold = main_bot.MIN_CONFIDENCE_THRESHOLD
    print(f"main_bot.MIN_CONFIDENCE_THRESHOLD = {threshold}")
    print(f"As percentage: {threshold:.1%}")
    
    # Test consensus analyzer
    analyzer = consensus_analyzer.ConsensusAnalyzer(
        min_consensus_score=0.6,
        confidence_threshold=threshold
    )
    
    print(f"ConsensusAnalyzer.confidence_threshold = {analyzer.confidence_threshold}")
    
    # Test comparison
    test_confidence = 0.807
    meets_threshold = test_confidence >= threshold
    
    print(f"\\nTest comparison:")
    print(f"  Test confidence: {test_confidence:.3f} ({test_confidence:.1%})")
    print(f"  Threshold: {threshold:.3f} ({threshold:.1%})")
    print(f"  Result: {test_confidence:.1%} {'≥' if meets_threshold else '<'} {threshold:.1%}")
    print(f"  Meets threshold: {'✅ YES' if meets_threshold else '❌ NO'}")
    
    if meets_threshold:
        print("\\n✅ SUCCESS: 80.7% confidence SHOULD PASS 80% threshold!")
    else:
        print("\\n❌ FAILED: 80.7% confidence should pass 80% threshold!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
"""
    
    try:
        with open("test_consensus_threshold_quick.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        print("  ✅ Created test_consensus_threshold_quick.py")
        return True
    except Exception as e:
        print(f"  ❌ Error creating test script: {e}")
        return False

def run_quick_test():
    """Run quick test to verify threshold"""
    print("\n🧪 RUNNING QUICK THRESHOLD TEST")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, "test_consensus_threshold_quick.py"
        ], capture_output=True, text=True, timeout=30)
        
        print("Test output:")
        print(result.stdout)
        
        if result.stderr:
            print("Test errors:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

def main():
    """Main function"""
    print("🔄 BOT RESTART WITH NEW THRESHOLD CONFIGURATION")
    print("=" * 80)
    print("This script will verify and test the new 80% consensus threshold")
    print()
    
    # Step 1: Verify configuration
    config_ok = verify_configuration()
    
    # Step 2: Test import
    import_ok = test_import_configuration()
    
    # Step 3: Create and run quick test
    test_created = create_test_consensus_script()
    if test_created:
        test_passed = run_quick_test()
    else:
        test_passed = False
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 CONFIGURATION VERIFICATION SUMMARY")
    print("=" * 80)
    
    checks = [
        ("Environment Variables", config_ok),
        ("Import Configuration", import_ok),
        ("Quick Test", test_passed)
    ]
    
    all_passed = True
    for check_name, result in checks:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {check_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Consensus threshold is correctly set to 80%")
        print("✅ Bot should now accept 80.7% confidence signals")
        print()
        print("🚀 Next steps:")
        print("1. Restart your bot process")
        print("2. Monitor logs for 'Required Threshold: 80.0%'")
        print("3. Verify signals with 80%+ confidence are accepted")
        
    else:
        print("❌ SOME CHECKS FAILED")
        print("🔧 Troubleshooting:")
        print("1. Check .env file has MIN_CONFIDENCE_THRESHOLD=0.80")
        print("2. Restart Python interpreter to clear cache")
        print("3. Check for hardcoded 0.85 values in code")
        print("4. Verify bot is reading from correct .env file")
    
    print(f"\\n⏰ Verification completed: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
