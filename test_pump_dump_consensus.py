#!/usr/bin/env python3
"""
🔍 PUMP/DUMP CONSENSUS TEST
Test PUMP/DUMP detection and consensus integration
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pump_dump_consensus():
    """Test PUMP/DUMP consensus integration"""
    print("🚀 STARTING PUMP/DUMP CONSENSUS TEST")
    print("=" * 60)
    
    try:
        # Test 1: Import consensus analyzer
        print("\n🔍 Testing Consensus Analyzer Import...")
        from consensus_analyzer import ConsensusAnalyzer
        print("  ✅ ConsensusAnalyzer imported successfully")
        
        # Initialize consensus analyzer
        consensus_analyzer = ConsensusAnalyzer(
            min_consensus_score=0.55,
            weight_config={
                "ai_models": 0.20,
                "volume_profile": 0.15,
                "point_figure": 0.15,
                "zigzag_fibonacci": 0.15,
                "fourier": 0.05,
                "volume_patterns": 0.05,
                "dump_detector": 0.12,
                "pump_detector": 0.13
            }
        )
        print("  ✅ Consensus analyzer initialized")
        
        # Test 2: Create sample OHLCV data
        print("\n🔍 Creating Sample Market Data...")
        dates = pd.date_range(start=datetime.now() - timedelta(days=1), periods=100, freq='1min')
        np.random.seed(42)
        
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        ohlcv_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices],
            'close': prices,
            'volume': [np.random.uniform(1000000, 2000000) for _ in prices]
        })
        
        print(f"  ✅ Sample OHLCV data created: {len(ohlcv_data)} rows")
        
        # Test 3: Test with PUMP signal
        print("\n🔍 Testing PUMP Signal Consensus...")
        
        pump_consensus_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            "ohlcv_data": ohlcv_data,
            "processed_features": {},
            
            # Basic signals (low confidence)
            "volume_profile": {"signal": "BUY", "confidence": 0.25},
            "point_figure": {"signal": "BUY", "confidence": 0.30},
            "fibonacci": {"signal": "SELL", "confidence": 0.20},
            "fourier": {"signal": "NEUTRAL", "confidence": 0.15},
            "orderbook": {"signals": {"primary_signal": "BUY", "confidence": 0.25}},
            
            # STRONG PUMP signal
            "pump_analysis": {
                "probability": 0.65,  # 65% pump probability
                "stage": "ACTIVE_PUMP",
                "confidence": 0.65,
                "severity": "HIGH"
            },
            
            # Low dump signal
            "dump_analysis": {
                "probability": 0.05,
                "stage": "NONE",
                "confidence": 0.05,
                "severity": "LOW"
            },
            
            "volume_pattern_analysis": {},
            "volume_spike_info": {},
            "pump_detection_results": {}
        }
        
        print(f"  📊 Testing consensus with PUMP signal:")
        print(f"    🚀 Pump: prob=65%, stage=ACTIVE_PUMP")
        print(f"    🚨 Dump: prob=5%, stage=NONE")
        
        # Run consensus analysis
        pump_result = consensus_analyzer.analyze_consensus(pump_consensus_input)
        
        if pump_result and pump_result.get('status') == 'success':
            consensus = pump_result.get('consensus', {})
            print(f"  ✅ PUMP Consensus analysis completed")
            print(f"    🎯 Signal: {consensus.get('signal', 'NONE')}")
            print(f"    🎯 Confidence: {consensus.get('confidence', 0):.1%}")
            print(f"    🎯 Score: {consensus.get('consensus_score', 0):.1%}")
            print(f"    📊 Contributing signals: {consensus.get('signal_count', 0)}")
            
            # Check if PUMP was detected
            if consensus.get('signal') == 'BUY' and consensus.get('confidence', 0) > 0.5:
                print(f"  🎉 SUCCESS: PUMP signal detected and processed in consensus!")
            else:
                print(f"  ⚠️ PUMP signal not properly processed in consensus")
        else:
            print(f"  ❌ PUMP consensus analysis failed")
        
        # Test 4: Test with DUMP signal
        print("\n🔍 Testing DUMP Signal Consensus...")
        
        dump_consensus_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            "ohlcv_data": ohlcv_data,
            "processed_features": {},
            
            # Basic signals (low confidence)
            "volume_profile": {"signal": "SELL", "confidence": 0.25},
            "point_figure": {"signal": "SELL", "confidence": 0.30},
            "fibonacci": {"signal": "BUY", "confidence": 0.20},
            "fourier": {"signal": "NEUTRAL", "confidence": 0.15},
            "orderbook": {"signals": {"primary_signal": "SELL", "confidence": 0.25}},
            
            # Low pump signal
            "pump_analysis": {
                "probability": 0.05,
                "stage": "NONE",
                "confidence": 0.05,
                "severity": "LOW"
            },
            
            # STRONG DUMP signal
            "dump_analysis": {
                "probability": 0.70,  # 70% dump probability
                "stage": "ACTIVE_DUMP",
                "confidence": 0.70,
                "severity": "HIGH"
            },
            
            "volume_pattern_analysis": {},
            "volume_spike_info": {},
            "pump_detection_results": {}
        }
        
        print(f"  📊 Testing consensus with DUMP signal:")
        print(f"    🚨 Dump: prob=70%, stage=ACTIVE_DUMP")
        print(f"    🚀 Pump: prob=5%, stage=NONE")
        
        # Run consensus analysis
        dump_result = consensus_analyzer.analyze_consensus(dump_consensus_input)
        
        if dump_result and dump_result.get('status') == 'success':
            consensus = dump_result.get('consensus', {})
            print(f"  ✅ DUMP Consensus analysis completed")
            print(f"    🎯 Signal: {consensus.get('signal', 'NONE')}")
            print(f"    🎯 Confidence: {consensus.get('confidence', 0):.1%}")
            print(f"    🎯 Score: {consensus.get('consensus_score', 0):.1%}")
            print(f"    📊 Contributing signals: {consensus.get('signal_count', 0)}")
            
            # Check if DUMP was detected
            if consensus.get('signal') == 'SELL' and consensus.get('confidence', 0) > 0.5:
                print(f"  🎉 SUCCESS: DUMP signal detected and processed in consensus!")
            else:
                print(f"  ⚠️ DUMP signal not properly processed in consensus")
        else:
            print(f"  ❌ DUMP consensus analysis failed")
        
        # Test 5: Test with mixed signals (no strong PUMP/DUMP)
        print("\n🔍 Testing Mixed Signals (No Strong PUMP/DUMP)...")
        
        mixed_consensus_input = {
            "coin": "BTC/USDT",
            "symbol": "BTC/USDT",
            "ohlcv_data": ohlcv_data,
            "processed_features": {},
            
            # Mixed signals
            "volume_profile": {"signal": "BUY", "confidence": 0.30},
            "point_figure": {"signal": "SELL", "confidence": 0.35},
            "fibonacci": {"signal": "BUY", "confidence": 0.25},
            "fourier": {"signal": "NEUTRAL", "confidence": 0.20},
            "orderbook": {"signals": {"primary_signal": "BUY", "confidence": 0.30}},
            
            # Weak PUMP/DUMP signals
            "pump_analysis": {
                "probability": 0.15,  # 15% pump probability (weak)
                "stage": "MONITORING",
                "confidence": 0.15,
                "severity": "LOW"
            },
            
            "dump_analysis": {
                "probability": 0.10,  # 10% dump probability (weak)
                "stage": "MONITORING",
                "confidence": 0.10,
                "severity": "LOW"
            },
            
            "volume_pattern_analysis": {},
            "volume_spike_info": {},
            "pump_detection_results": {}
        }
        
        print(f"  📊 Testing consensus with mixed signals:")
        print(f"    🚀 Pump: prob=15%, stage=MONITORING (weak)")
        print(f"    🚨 Dump: prob=10%, stage=MONITORING (weak)")
        
        # Run consensus analysis
        mixed_result = consensus_analyzer.analyze_consensus(mixed_consensus_input)
        
        if mixed_result and mixed_result.get('status') == 'success':
            consensus = mixed_result.get('consensus', {})
            print(f"  ✅ Mixed signals consensus analysis completed")
            print(f"    🎯 Signal: {consensus.get('signal', 'NONE')}")
            print(f"    🎯 Confidence: {consensus.get('confidence', 0):.1%}")
            print(f"    🎯 Score: {consensus.get('consensus_score', 0):.1%}")
            print(f"    📊 Contributing signals: {consensus.get('signal_count', 0)}")
            
            print(f"  ✅ Mixed signals processed normally (no PUMP/DUMP override)")
        else:
            print(f"  ❌ Mixed signals consensus analysis failed")
        
        print("\n" + "=" * 60)
        print("🎯 PUMP/DUMP CONSENSUS TEST COMPLETED")
        
        # Summary
        print(f"\n📊 TEST SUMMARY:")
        print(f"  🚀 PUMP Detection: {'✅ Working' if pump_result and pump_result.get('status') == 'success' else '❌ Failed'}")
        print(f"  🚨 DUMP Detection: {'✅ Working' if dump_result and dump_result.get('status') == 'success' else '❌ Failed'}")
        print(f"  🎯 Consensus Integration: {'✅ Working' if mixed_result and mixed_result.get('status') == 'success' else '❌ Failed'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pump_dump_consensus()
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
