#!/usr/bin/env python3
"""
🧪 Test Error Handling Fixes
Test the comprehensive error handling fixes for None values, zero values, and other errors.
"""

import sys
import os
import time

# Add current directory to path
sys.path.append('.')

def test_enhanced_fallback_logger():
    """Test the Enhanced Fallback Logger with problematic data."""
    print("🧪 Testing Enhanced Fallback Logger...")
    
    try:
        # Import and create the logger
        from main_bot import TradingBot
        
        # Create a minimal bot instance
        bot = TradingBot()
        
        # Create the enhanced fallback logger
        logger = bot.create_enhanced_fallback_logger()
        
        if not logger:
            print("❌ Failed to create Enhanced Fallback Logger")
            return False
        
        # Test 1: Check if log_signal method exists
        if hasattr(logger, 'log_signal'):
            print("✅ log_signal method found")
        else:
            print("❌ log_signal method missing")
            return False
        
        # Test 2: Test with problematic data
        test_data = {
            'coin': 'TESTCOIN',
            'signal_type': 'BUY',
            'entry': None,  # None value
            'take_profit': 0.0,  # Zero value
            'stop_loss': float('nan'),  # NaN value
            'confidence': 'invalid_string',  # Invalid type
            'signal_id': 'test_123',
            'timestamp': time.time()
        }
        
        print("🧪 Testing log_signal with problematic data...")
        result = logger.log_signal(test_data)
        
        if result:
            print("✅ log_signal handled problematic data successfully")
        else:
            print("❌ log_signal failed with problematic data")
            return False
        
        # Test 3: Test data cleaning method
        if hasattr(logger, '_clean_signal_data'):
            cleaned = logger._clean_signal_data(test_data)
            print(f"✅ Data cleaning result: {type(cleaned)}")
            
            # Check if None values were handled
            if cleaned.get('entry') is not None:
                print("✅ None values handled correctly")
            else:
                print("❌ None values not handled")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Fallback Logger test failed: {e}")
        return False

def test_safe_value_handler():
    """Test the safe value handler."""
    print("\n🧪 Testing Safe Value Handler...")
    
    try:
        from main_bot import TradingBot
        
        bot = TradingBot()
        
        if not hasattr(bot, '_safe_value_handler'):
            print("❌ _safe_value_handler method not found")
            return False
        
        # Test None handling
        result_none = bot._safe_value_handler(None, 'float', 0.0, 'test_field')
        if result_none == 0.0:
            print("✅ None value handling works")
        else:
            print(f"❌ None value handling failed: {result_none}")
            return False
        
        # Test NaN handling
        result_nan = bot._safe_value_handler(float('nan'), 'float', 0.0, 'test_field')
        if result_nan == 0.0:
            print("✅ NaN value handling works")
        else:
            print(f"❌ NaN value handling failed: {result_nan}")
            return False
        
        # Test zero value (should be preserved)
        result_zero = bot._safe_value_handler(0.0, 'float', 1.0, 'entry_price')
        if result_zero == 0.0:
            print("✅ Zero value preservation works")
        else:
            print(f"❌ Zero value preservation failed: {result_zero}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Safe Value Handler test failed: {e}")
        return False

def test_signal_data_validation():
    """Test signal data validation."""
    print("\n🧪 Testing Signal Data Validation...")
    
    try:
        from main_bot import TradingBot
        
        bot = TradingBot()
        
        if not hasattr(bot, '_validate_signal_data'):
            print("❌ _validate_signal_data method not found")
            return False
        
        # Test with problematic signal data
        problematic_data = {
            'entry': None,
            'take_profit': float('inf'),
            'stop_loss': 'invalid_string',
            'confidence': None,
            'coin': 123,  # Wrong type
            'contributing_models': 'single_string'  # Should be list
        }
        
        validated = bot._validate_signal_data(problematic_data)
        
        if 'error' in validated:
            print(f"❌ Validation failed: {validated['error']}")
            return False
        
        # Check if validation worked
        if isinstance(validated.get('entry'), float):
            print("✅ Entry validation works")
        else:
            print(f"❌ Entry validation failed: {validated.get('entry')}")
            return False
        
        if isinstance(validated.get('contributing_models'), list):
            print("✅ List field validation works")
        else:
            print(f"❌ List field validation failed: {validated.get('contributing_models')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Signal Data Validation test failed: {e}")
        return False

def test_consensus_analyzer_fixes():
    """Test consensus analyzer error handling fixes."""
    print("\n🧪 Testing Consensus Analyzer Fixes...")
    
    try:
        # Test if consensus analyzer can be imported
        from consensus_analyzer import ConsensusAnalyzer
        
        # Create analyzer instance
        analyzer = ConsensusAnalyzer()
        
        print("✅ Consensus Analyzer imports successfully")
        print("✅ Consensus Analyzer creates instance successfully")
        
        # The actual consensus analysis would require full data setup
        # For now, just verify the class can be instantiated
        return True
        
    except Exception as e:
        print(f"❌ Consensus Analyzer test failed: {e}")
        return False

def main():
    """Run all error handling tests."""
    print("🚀 Starting Error Handling Fixes Test Suite")
    print("=" * 60)
    
    tests = [
        ("Enhanced Fallback Logger", test_enhanced_fallback_logger),
        ("Safe Value Handler", test_safe_value_handler),
        ("Signal Data Validation", test_signal_data_validation),
        ("Consensus Analyzer Fixes", test_consensus_analyzer_fixes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All error handling fixes are working correctly!")
        return True
    else:
        print("⚠️ Some tests failed - please check the error handling implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
