#!/usr/bin/env python3
"""
🧪 TEST PERCENTAGE CALCULATION FIX
=================================

Test để kiểm tra fix cho percentage calculation của SELL signals.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_percentage_calculation():
    """Test percentage calculation fix."""
    print("🧪 TESTING PERCENTAGE CALCULATION FIX")
    print("=" * 50)
    
    # Test the fixed percentage calculation logic
    def calculate_percentage_change(entry_price: float, target_price: float, signal_type: str = None) -> str:
        """Calculate percentage change from entry to target price with proper SELL signal logic."""
        try:
            if entry_price <= 0 or target_price <= 0:
                return "0.00%"

            # ✅ FIX: For SELL signals, show profit/loss correctly
            if signal_type == "SELL":
                # For SELL: profit when target < entry (going down)
                # Take Profit should show positive % when target < entry
                # Stop Loss should show negative % when target > entry
                percentage = ((entry_price - target_price) / entry_price) * 100
            else:
                # For BUY: normal calculation
                percentage = ((target_price - entry_price) / entry_price) * 100
            
            return f"{percentage:+.2f}%"
        except:
            return "0.00%"
    
    # Test cases for SELL signal (like MUBARAK/USDT example)
    print("🔴 TESTING SELL SIGNAL PERCENTAGE:")
    entry = 0.03263260
    take_profit = 0.02840893  # Lower than entry (profit for SELL)
    stop_loss = 0.03480319   # Higher than entry (loss for SELL)
    
    tp_percentage = calculate_percentage_change(entry, take_profit, "SELL")
    sl_percentage = calculate_percentage_change(entry, stop_loss, "SELL")
    
    print(f"Entry: {entry:.8f}")
    print(f"Take Profit: {take_profit:.8f} ({tp_percentage})")
    print(f"Stop Loss: {stop_loss:.8f} ({sl_percentage})")
    
    # Expected results for SELL:
    # TP should be positive (profit when price goes down)
    # SL should be negative (loss when price goes up)
    tp_expected = ((entry - take_profit) / entry) * 100  # Should be +12.94%
    sl_expected = ((entry - stop_loss) / entry) * 100   # Should be -6.65%
    
    print(f"\nExpected TP: +{tp_expected:.2f}%")
    print(f"Expected SL: {sl_expected:.2f}%")
    print(f"Actual TP: {tp_percentage}")
    print(f"Actual SL: {sl_percentage}")
    
    tp_correct = tp_percentage == f"+{tp_expected:.2f}%"
    sl_correct = sl_percentage == f"{sl_expected:.2f}%"
    
    print(f"\nTP Calculation: {'✅ CORRECT' if tp_correct else '❌ WRONG'}")
    print(f"SL Calculation: {'✅ CORRECT' if sl_correct else '❌ WRONG'}")
    
    # Test cases for BUY signal
    print("\n🟢 TESTING BUY SIGNAL PERCENTAGE:")
    entry_buy = 50000.0
    take_profit_buy = 52500.0  # Higher than entry (profit for BUY)
    stop_loss_buy = 47500.0    # Lower than entry (loss for BUY)
    
    tp_percentage_buy = calculate_percentage_change(entry_buy, take_profit_buy, "BUY")
    sl_percentage_buy = calculate_percentage_change(entry_buy, stop_loss_buy, "BUY")
    
    print(f"Entry: {entry_buy:.2f}")
    print(f"Take Profit: {take_profit_buy:.2f} ({tp_percentage_buy})")
    print(f"Stop Loss: {stop_loss_buy:.2f} ({sl_percentage_buy})")
    
    # Expected results for BUY:
    # TP should be positive (profit when price goes up)
    # SL should be negative (loss when price goes down)
    tp_expected_buy = ((take_profit_buy - entry_buy) / entry_buy) * 100  # Should be +5.00%
    sl_expected_buy = ((stop_loss_buy - entry_buy) / entry_buy) * 100    # Should be -5.00%
    
    print(f"\nExpected TP: +{tp_expected_buy:.2f}%")
    print(f"Expected SL: {sl_expected_buy:.2f}%")
    print(f"Actual TP: {tp_percentage_buy}")
    print(f"Actual SL: {sl_percentage_buy}")
    
    tp_correct_buy = tp_percentage_buy == f"+{tp_expected_buy:.2f}%"
    sl_correct_buy = sl_percentage_buy == f"{sl_expected_buy:.2f}%"
    
    print(f"\nTP Calculation: {'✅ CORRECT' if tp_correct_buy else '❌ WRONG'}")
    print(f"SL Calculation: {'✅ CORRECT' if sl_correct_buy else '❌ WRONG'}")
    
    # Summary
    all_correct = tp_correct and sl_correct and tp_correct_buy and sl_correct_buy
    
    print(f"\n🎯 PERCENTAGE CALCULATION FIX SUMMARY:")
    print(f"🔴 SELL Signal: {'✅ FIXED' if (tp_correct and sl_correct) else '❌ NOT FIXED'}")
    print(f"🟢 BUY Signal: {'✅ WORKING' if (tp_correct_buy and sl_correct_buy) else '❌ BROKEN'}")
    print(f"🏆 Overall: {'✅ ALL FIXED' if all_correct else '❌ NEEDS WORK'}")
    
    if all_correct:
        print("\n🎉 PERCENTAGE CALCULATION IS NOW CORRECT!")
        print("SELL signals will now show proper profit/loss percentages:")
        print("  - Take Profit: Positive % when target < entry (profit)")
        print("  - Stop Loss: Negative % when target > entry (loss)")
    
    return all_correct

if __name__ == "__main__":
    test_percentage_calculation()
