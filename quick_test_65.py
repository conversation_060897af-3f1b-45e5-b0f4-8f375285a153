#!/usr/bin/env python3
"""
🔧 QUICK SIGNAL STRENGTH 65% TEST
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_strength():
    """Quick test of signal strength configuration."""
    print("🔧 QUICK SIGNAL STRENGTH TEST")
    print("=" * 40)
    
    try:
        # Import main_bot to get configuration
        import main_bot
        
        print(f"📊 MIN_SIGNAL_STRENGTH: {main_bot.MIN_SIGNAL_STRENGTH}")
        print(f"📊 As percentage: {main_bot.MIN_SIGNAL_STRENGTH * 100:.1f}%")
        
        # Test the ROSE case
        rose_strength = 0.417  # 41.7% from the log
        
        print(f"\n🌹 ROSE/USDT Case:")
        print(f"  Signal Strength: {rose_strength:.1%}")
        print(f"  Threshold: {main_bot.MIN_SIGNAL_STRENGTH:.1%}")
        
        if rose_strength >= main_bot.MIN_SIGNAL_STRENGTH:
            print(f"  Result: ✅ WOULD PASS")
        else:
            print(f"  Result: ❌ STILL REJECTED")
        
        # Test some other values
        test_values = [0.60, 0.65, 0.70, 0.75]
        print(f"\n📊 Other Test Values:")
        for val in test_values:
            result = "✅ PASS" if val >= main_bot.MIN_SIGNAL_STRENGTH else "❌ FAIL"
            print(f"  {val:.1%}: {result}")
        
        # Check if threshold is 65%
        if abs(main_bot.MIN_SIGNAL_STRENGTH - 0.65) < 0.001:
            print(f"\n✅ SUCCESS: Signal strength threshold is 65%")
            return True
        else:
            print(f"\n❌ FAIL: Signal strength threshold is {main_bot.MIN_SIGNAL_STRENGTH:.1%}, not 65%")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_signal_strength()
    print(f"\n🎯 Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
