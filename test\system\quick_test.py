#!/usr/bin/env python3
"""
⚡ Quick Test - Minimal test for chart with detailed report
"""

import os
import sys
from datetime import datetime

def quick_test():
    """⚡ Quick test."""
    print(f"⚡ QUICK TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token:
        print(f"❌ TELEGRAM_BOT_TOKEN not set")
        return False
        
    if not chat_id:
        print(f"❌ TELEGRAM_CHAT_ID not set")
        return False
    
    print(f"✅ Environment variables OK")
    
    # Import and test
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from telegram_notifier import EnhancedTelegramNotifier
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        print(f"✅ Notifier initialized")
        
        print(f"📸 Testing basic photo send...")
        result = notifier.test_basic_photo_send()
        
        if result:
            print(f"✅ SUCCESS! Basic photo test passed")
            print(f"📱 Check your Telegram for the test image")
            return True
        else:
            print(f"❌ FAILED! Basic photo test failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    if success:
        print(f"\n🎉 QUICK TEST PASSED!")
    else:
        print(f"\n💥 QUICK TEST FAILED!")
