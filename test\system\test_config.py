#!/usr/bin/env python3
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🧪 Testing Signal Quality Filter Configuration...")

# Test configuration loading
SIGNAL_QUALITY_FILTER_ENABLED = bool(int(os.getenv("SIGNAL_QUALITY_FILTER_ENABLED", "1")))
MIN_CONFIDENCE_THRESHOLD = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.70"))
FIBONACCI_MIN_CONFIDENCE = float(os.getenv("FIBONACCI_MIN_CONFIDENCE", "0.70"))

print(f"Filter Enabled: {SIGNAL_QUALITY_FILTER_ENABLED}")
print(f"Min Threshold: {MIN_CONFIDENCE_THRESHOLD:.0%}")
print(f"Fi<PERSON><PERSON><PERSON> Threshold: {FIBONACCI_MIN_CONFIDENCE:.0%}")

# Test filtering logic
test_confidence = 0.65
should_send = test_confidence >= FIBONACCI_MIN_CONFIDENCE if SIGNAL_QUALITY_FILTER_ENABLED else True

print(f"Test Signal (65% confidence): {'✅ SEND' if should_send else '❌ SKIP'}")
print("✅ Configuration test complete!")
