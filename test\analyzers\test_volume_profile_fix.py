#!/usr/bin/env python3
"""
🧪 Test script to verify the Volume Profile analyzer always generates signals
"""

import pandas as pd
import numpy as np
import sys
import os

def create_test_data_scenarios():
    """Create different test data scenarios to stress-test the Volume Profile analyzer"""
    
    scenarios = []
    
    # Scenario 1: Normal data with good volume
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    base_price = 50000.0
    normal_data = []
    current_price = base_price
    
    for i in range(100):
        change = np.random.normal(0, 0.02)  # 2% volatility
        current_price *= (1 + change)
        volume = np.random.uniform(1000, 10000)
        normal_data.append({
            'timestamp': dates[i],
            'open': current_price * (1 + np.random.uniform(-0.01, 0.01)),
            'high': current_price * (1 + abs(np.random.normal(0, 0.01))),
            'low': current_price * (1 - abs(np.random.normal(0, 0.01))),
            'close': current_price,
            'volume': volume
        })
    
    scenarios.append(("Normal Data", pd.DataFrame(normal_data)))
    
    # Scenario 2: Zero volume data
    zero_volume_data = normal_data.copy()
    for row in zero_volume_data:
        row['volume'] = 0
    scenarios.append(("Zero Volume", pd.DataFrame(zero_volume_data)))
    
    # Scenario 3: Very low volume data
    low_volume_data = normal_data.copy()
    for row in low_volume_data:
        row['volume'] = np.random.uniform(0.1, 1.0)
    scenarios.append(("Low Volume", pd.DataFrame(low_volume_data)))
    
    # Scenario 4: Flat price data
    flat_price_data = []
    flat_price = 50000.0
    for i in range(100):
        volume = np.random.uniform(1000, 10000)
        flat_price_data.append({
            'timestamp': dates[i],
            'open': flat_price,
            'high': flat_price * 1.001,
            'low': flat_price * 0.999,
            'close': flat_price,
            'volume': volume
        })
    scenarios.append(("Flat Price", pd.DataFrame(flat_price_data)))
    
    # Scenario 5: Minimal data (edge case)
    minimal_data = normal_data[:10]  # Only 10 rows
    scenarios.append(("Minimal Data", pd.DataFrame(minimal_data)))
    
    return scenarios

def test_volume_profile_signal_generation():
    """Test Volume Profile signal generation across different scenarios"""
    print("🧪 Testing Volume Profile Signal Generation")
    print("=" * 60)
    
    try:
        # Add the current directory to Python path
        sys.path.insert(0, os.getcwd())
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        analyzer = VolumeProfileAnalyzer()
        scenarios = create_test_data_scenarios()
        
        all_passed = True
        results = []
        
        for scenario_name, df in scenarios:
            print(f"\n📊 Testing Scenario: {scenario_name}")
            print(f"  - Data rows: {len(df)}")
            print(f"  - Volume sum: {df['volume'].sum():,.0f}")
            print(f"  - Price range: {df['close'].min():.2f} - {df['close'].max():.2f}")
            
            try:
                # Run Volume Profile analysis
                result = analyzer.analyze_volume_profile(df)
                
                if result.get("status") == "success":
                    signals = result.get("signals", {})
                    signal = signals.get("primary_signal", "NONE")
                    confidence = signals.get("confidence", 0)
                    
                    print(f"  ✅ Analysis successful")
                    print(f"  📈 Signal: {signal}")
                    print(f"  📊 Confidence: {confidence:.3f} ({confidence:.1%})")
                    
                    # Check if signal is valid
                    if signal in ["BUY", "SELL"] and confidence > 0:
                        print(f"  ✅ PASS: Valid signal generated")
                        results.append((scenario_name, True, signal, confidence))
                    else:
                        print(f"  ❌ FAIL: Invalid signal or zero confidence")
                        results.append((scenario_name, False, signal, confidence))
                        all_passed = False
                        
                    # Show reasoning
                    reasoning = signals.get("reasoning", [])
                    if reasoning:
                        print(f"  📝 Reasoning: {reasoning[0]}")
                        
                else:
                    print(f"  ❌ FAIL: Analysis failed - {result.get('message', 'Unknown error')}")
                    results.append((scenario_name, False, "ERROR", 0))
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ FAIL: Exception occurred - {e}")
                results.append((scenario_name, False, "EXCEPTION", 0))
                all_passed = False
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 VOLUME PROFILE TEST RESULTS")
        print("=" * 60)
        
        for scenario_name, passed, signal, confidence in results:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"  {scenario_name:<15}: {status:<8} | Signal: {signal:<4} | Conf: {confidence:.3f}")
        
        print()
        if all_passed:
            print("🎉 ALL SCENARIOS PASSED!")
            print("✅ Volume Profile analyzer now generates signals in all test cases")
        else:
            print("❌ SOME SCENARIOS FAILED")
            print("Issues remain in Volume Profile signal generation")
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Failed to import VolumeProfileAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_signal_consistency():
    """Test that signals are consistent and reasonable"""
    print("\n🧪 Testing Signal Consistency")
    print("=" * 60)
    
    try:
        sys.path.insert(0, os.getcwd())
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        analyzer = VolumeProfileAnalyzer()
        
        # Create trending up data
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1H')
        trending_up_data = []
        price = 50000.0
        
        for i in range(50):
            price *= 1.01  # 1% increase each period
            volume = np.random.uniform(1000, 5000)
            trending_up_data.append({
                'timestamp': dates[i],
                'open': price * 0.999,
                'high': price * 1.002,
                'low': price * 0.998,
                'close': price,
                'volume': volume
            })
        
        df_up = pd.DataFrame(trending_up_data)
        
        # Create trending down data
        trending_down_data = []
        price = 50000.0
        
        for i in range(50):
            price *= 0.99  # 1% decrease each period
            volume = np.random.uniform(1000, 5000)
            trending_down_data.append({
                'timestamp': dates[i],
                'open': price * 1.001,
                'high': price * 1.002,
                'low': price * 0.998,
                'close': price,
                'volume': volume
            })
        
        df_down = pd.DataFrame(trending_down_data)
        
        # Test both scenarios
        test_cases = [
            ("Trending Up", df_up, ["BUY"]),  # Should favor BUY
            ("Trending Down", df_down, ["SELL"])  # Should favor SELL
        ]
        
        consistency_passed = True
        
        for case_name, df, expected_signals in test_cases:
            print(f"\n📈 Testing {case_name}:")
            print(f"  - Price change: {((df['close'].iloc[-1] / df['close'].iloc[0]) - 1) * 100:+.1f}%")
            
            result = analyzer.analyze_volume_profile(df)
            
            if result.get("status") == "success":
                signals = result.get("signals", {})
                signal = signals.get("primary_signal", "NONE")
                confidence = signals.get("confidence", 0)
                
                print(f"  📊 Generated signal: {signal} (confidence: {confidence:.1%})")
                
                if signal in expected_signals:
                    print(f"  ✅ PASS: Signal matches expectation")
                else:
                    print(f"  ⚠️ UNEXPECTED: Expected {expected_signals}, got {signal}")
                    # Don't fail for this - signals can vary based on volume profile
                    
                if signal in ["BUY", "SELL"] and confidence > 0:
                    print(f"  ✅ PASS: Valid signal with confidence")
                else:
                    print(f"  ❌ FAIL: Invalid signal or zero confidence")
                    consistency_passed = False
            else:
                print(f"  ❌ FAIL: Analysis failed")
                consistency_passed = False
        
        print(f"\nConsistency Test: {'✅ PASS' if consistency_passed else '❌ FAIL'}")
        return consistency_passed
        
    except Exception as e:
        print(f"❌ Consistency test error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 VOLUME PROFILE ANALYZER FIX VERIFICATION")
    print("=" * 70)
    
    # Test 1: Signal generation across scenarios
    test1_result = test_volume_profile_signal_generation()
    
    # Test 2: Signal consistency
    test2_result = test_signal_consistency()
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 FINAL RESULTS")
    print("=" * 70)
    
    print(f"  Signal Generation Test: {'✅ PASSED' if test1_result else '❌ FAILED'}")
    print(f"  Signal Consistency Test: {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    overall_success = test1_result and test2_result
    
    print()
    if overall_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Volume Profile analyzer now reliably generates signals")
        print("✅ Enhanced fallback logic is working correctly")
        print("✅ Emergency fallback ensures no NONE signals")
    else:
        print("❌ SOME TESTS FAILED")
        print("Volume Profile analyzer may still have issues")
    
    return overall_success

if __name__ == "__main__":
    main()
