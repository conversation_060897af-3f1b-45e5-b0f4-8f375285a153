#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌐 P&L WEB DASHBOARD
===================
Simple web dashboard to view trading performance statistics.

Features:
- Real-time P&L statistics
- Performance charts
- Signal breakdown analysis
- Active trades monitoring
- Export functionality

Author: Trading Bot System
Version: 1.0.0
"""

import json
import os
from datetime import datetime, timedelta
from flask import Flask, render_template_string, jsonify, request, send_file
from profit_loss_tracker import ProfitLossTracker
from trading_performance_integration import TradingPerformanceIntegration

# Initialize Flask app
app = Flask(__name__)

# Global P&L tracker instance
pnl_tracker = None
pnl_integration = None

def init_pnl_dashboard(db_path: str = "trading_performance.db"):
    """🚀 Initialize P&L dashboard with database."""
    global pnl_tracker, pnl_integration
    
    try:
        pnl_tracker = ProfitLossTracker(db_path)
        pnl_integration = TradingPerformanceIntegration(db_path)
        print(f"🌐 P&L Dashboard initialized with database: {db_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize P&L dashboard: {e}")
        return False

# HTML Template for the dashboard
DASHBOARD_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Trading P&L Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            text-align: center; 
            color: white; 
            margin-bottom: 30px; 
        }
        .header h1 { 
            font-size: 2.5em; 
            margin-bottom: 10px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .stat-card { 
            background: white; 
            border-radius: 15px; 
            padding: 25px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1); 
            transition: transform 0.3s ease;
        }
        .stat-card:hover { 
            transform: translateY(-5px); 
        }
        .stat-card h3 { 
            color: #667eea; 
            margin-bottom: 15px; 
            font-size: 1.2em;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        .stat-value { 
            font-size: 2em; 
            font-weight: bold; 
            margin: 10px 0; 
        }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
        .neutral { color: #34495e; }
        .trades-table { 
            background: white; 
            border-radius: 15px; 
            padding: 25px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
        }
        .trades-table h3 { 
            color: #667eea; 
            margin-bottom: 20px; 
            font-size: 1.3em;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
        }
        th { 
            background-color: #f8f9fa; 
            font-weight: 600;
            color: #667eea;
        }
        tr:hover { 
            background-color: #f8f9fa; 
        }
        .refresh-btn { 
            background: linear-gradient(45deg, #667eea, #764ba2); 
            color: white; 
            border: none; 
            padding: 12px 25px; 
            border-radius: 25px; 
            cursor: pointer; 
            font-size: 1em;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .refresh-btn:hover { 
            transform: scale(1.05); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .export-btn { 
            background: linear-gradient(45deg, #27ae60, #2ecc71); 
            color: white; 
            border: none; 
            padding: 12px 25px; 
            border-radius: 25px; 
            cursor: pointer; 
            font-size: 1em;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .export-btn:hover { 
            transform: scale(1.05); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .controls { 
            text-align: center; 
            margin: 20px 0; 
        }
        .loading { 
            text-align: center; 
            color: white; 
            font-size: 1.2em; 
        }
        .signal-breakdown { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 15px; 
            margin-top: 20px; 
        }
        .signal-card { 
            background: #f8f9fa; 
            border-radius: 10px; 
            padding: 15px; 
            border-left: 4px solid #667eea;
        }
        .signal-card h4 { 
            color: #667eea; 
            margin-bottom: 10px; 
        }
        .last-updated { 
            text-align: center; 
            color: white; 
            margin-top: 20px; 
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Trading P&L Dashboard</h1>
            <p>Real-time Performance Analytics</p>
        </div>
        
        <div class="controls">
            <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Data</button>
            <button class="export-btn" onclick="exportData()">📁 Export CSV</button>
            <button class="refresh-btn" onclick="showReport()">📋 Generate Report</button>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            Loading performance data...
        </div>
        
        <div id="dashboard-content">
            <!-- Content will be loaded here -->
        </div>
        
        <div class="last-updated" id="last-updated">
            <!-- Last updated time will be shown here -->
        </div>
    </div>

    <script>
        let dashboardData = {};
        
        async function loadDashboardData() {
            try {
                document.getElementById('loading').style.display = 'block';
                const response = await fetch('/api/dashboard');
                dashboardData = await response.json();
                renderDashboard();
                document.getElementById('loading').style.display = 'none';
                document.getElementById('last-updated').textContent = 
                    `Last updated: ${new Date().toLocaleString()}`;
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                document.getElementById('loading').textContent = 'Error loading data';
            }
        }
        
        function renderDashboard() {
            const content = document.getElementById('dashboard-content');
            const stats7d = dashboardData.performance_7d || {};
            const stats30d = dashboardData.performance_30d || {};
            const signalBreakdown = dashboardData.signal_breakdown || {};
            const activeTrades = dashboardData.active_trades || {};
            const recentTrades = dashboardData.recent_trades || [];
            
            content.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>📅 7-Day Performance</h3>
                        <div class="stat-value ${stats7d.total_profit_loss >= 0 ? 'positive' : 'negative'}">
                            ${stats7d.total_profit_loss || 0}%
                        </div>
                        <p>Total P&L</p>
                        <p>Win Rate: ${stats7d.win_rate || 0}% (${stats7d.winning_trades || 0}/${stats7d.total_trades || 0})</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>📊 30-Day Performance</h3>
                        <div class="stat-value ${stats30d.total_profit_loss >= 0 ? 'positive' : 'negative'}">
                            ${stats30d.total_profit_loss || 0}%
                        </div>
                        <p>Total P&L</p>
                        <p>Win Rate: ${stats30d.win_rate || 0}% (${stats30d.winning_trades || 0}/${stats30d.total_trades || 0})</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>🎯 Best Performance</h3>
                        <div class="stat-value positive">
                            ${stats30d.max_profit || 0}%
                        </div>
                        <p>Best Trade</p>
                        <p>Avg Win: ${stats30d.avg_win || 0}%</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>📉 Risk Metrics</h3>
                        <div class="stat-value negative">
                            ${stats30d.max_loss || 0}%
                        </div>
                        <p>Worst Trade</p>
                        <p>Avg Loss: ${stats30d.avg_loss || 0}%</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>⚖️ Risk/Reward</h3>
                        <div class="stat-value neutral">
                            ${stats30d.avg_risk_reward || 0}
                        </div>
                        <p>Average Ratio</p>
                        <p>Profit Factor: ${stats30d.profit_factor || 0}</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>🔄 Active Trades</h3>
                        <div class="stat-value neutral">
                            ${activeTrades.total_active || 0}
                        </div>
                        <p>Currently Open</p>
                        <p>Signals: ${Object.keys(activeTrades.by_signal_type || {}).length}</p>
                    </div>
                </div>
                
                <div class="trades-table">
                    <h3>🔍 Enhanced Algorithm Performance Breakdown</h3>
                    <div class="signal-breakdown">
                        ${Object.values(signalBreakdown).map(signal => `
                            <div class="signal-card">
                                <h4>🎯 ${signal.signal_type} - ${signal.signal_source}</h4>
                                <p><strong>📊 Trades:</strong> ${signal.total_trades}</p>
                                <p><strong>🏆 Win Rate:</strong> ${signal.win_rate}%</p>
                                <p><strong>💰 Total P&L:</strong> <span class="${signal.total_profit_loss >= 0 ? 'positive' : 'negative'}">${signal.total_profit_loss}%</span></p>
                                <p><strong>📈 Avg P&L:</strong> ${signal.avg_profit_loss}%</p>
                                <p><strong>⚖️ Risk/Reward:</strong> ${signal.avg_risk_reward || 'N/A'}</p>
                                <p><strong>🔄 Ultra Tracker:</strong> ${signal.ultra_tracker_signals || 0} signals</p>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="trades-table">
                    <h3>🚀 Ultra Tracker Integration Status</h3>
                    <div class="signal-breakdown">
                        <div class="signal-card">
                            <h4>🎯 Consensus Signals</h4>
                            <p><strong>📊 Active:</strong> ${dashboardData.ultra_tracker?.consensus?.active || 0}</p>
                            <p><strong>✅ Completed:</strong> ${dashboardData.ultra_tracker?.consensus?.completed || 0}</p>
                            <p><strong>📈 Success Rate:</strong> ${dashboardData.ultra_tracker?.consensus?.success_rate || 0}%</p>
                        </div>
                        <div class="signal-card">
                            <h4>🤖 AI Analysis</h4>
                            <p><strong>📊 Active:</strong> ${dashboardData.ultra_tracker?.ai_analysis?.active || 0}</p>
                            <p><strong>✅ Completed:</strong> ${dashboardData.ultra_tracker?.ai_analysis?.completed || 0}</p>
                            <p><strong>📈 Success Rate:</strong> ${dashboardData.ultra_tracker?.ai_analysis?.success_rate || 0}%</p>
                        </div>
                        <div class="signal-card">
                            <h4>🌀 Fibonacci Analysis</h4>
                            <p><strong>📊 Active:</strong> ${dashboardData.ultra_tracker?.fibonacci?.active || 0}</p>
                            <p><strong>✅ Completed:</strong> ${dashboardData.ultra_tracker?.fibonacci?.completed || 0}</p>
                            <p><strong>📈 Success Rate:</strong> ${dashboardData.ultra_tracker?.fibonacci?.success_rate || 0}%</p>
                        </div>
                        <div class="signal-card">
                            <h4>📊 Volume Profile</h4>
                            <p><strong>📊 Active:</strong> ${dashboardData.ultra_tracker?.volume_profile?.active || 0}</p>
                            <p><strong>✅ Completed:</strong> ${dashboardData.ultra_tracker?.volume_profile?.completed || 0}</p>
                            <p><strong>📈 Success Rate:</strong> ${dashboardData.ultra_tracker?.volume_profile?.success_rate || 0}%</p>
                        </div>
                        <div class="signal-card">
                            <h4>📋 Orderbook Analysis</h4>
                            <p><strong>📊 Active:</strong> ${dashboardData.ultra_tracker?.orderbook?.active || 0}</p>
                            <p><strong>✅ Completed:</strong> ${dashboardData.ultra_tracker?.orderbook?.completed || 0}</p>
                            <p><strong>📈 Success Rate:</strong> ${dashboardData.ultra_tracker?.orderbook?.success_rate || 0}%</p>
                        </div>
                        <div class="signal-card">
                            <h4>🎯 Overall Tracking</h4>
                            <p><strong>📊 Total Active:</strong> ${dashboardData.ultra_tracker?.total?.active || 0}</p>
                            <p><strong>✅ Total Completed:</strong> ${dashboardData.ultra_tracker?.total?.completed || 0}</p>
                            <p><strong>📈 Overall Success:</strong> ${dashboardData.ultra_tracker?.total?.success_rate || 0}%</p>
                        </div>
                    </div>
                </div>
                
                <div class="trades-table">
                    <h3>📋 Recent Trades</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Coin</th>
                                <th>Signal Type</th>
                                <th>Entry</th>
                                <th>P&L %</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${recentTrades.slice(0, 20).map(trade => `
                                <tr>
                                    <td>${trade.coin || 'N/A'}</td>
                                    <td>${trade.signal_type || 'N/A'}</td>
                                    <td>${trade.entry_signal || 'N/A'}</td>
                                    <td class="${(trade.profit_loss_pct || 0) >= 0 ? 'positive' : 'negative'}">
                                        ${(trade.profit_loss_pct || 0).toFixed(2)}%
                                    </td>
                                    <td>${trade.status || 'N/A'}</td>
                                    <td>${trade.entry_time ? new Date(trade.entry_time).toLocaleDateString() : 'N/A'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
        
        function refreshData() {
            loadDashboardData();
        }
        
        async function exportData() {
            try {
                const response = await fetch('/api/export');
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `trading_performance_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                alert('Error exporting data: ' + error.message);
            }
        }
        
        async function showReport() {
            try {
                const response = await fetch('/api/report');
                const report = await response.text();
                alert(report);
            } catch (error) {
                alert('Error generating report: ' + error.message);
            }
        }
        
        // Auto-refresh every 5 minutes
        setInterval(refreshData, 5 * 60 * 1000);
        
        // Load initial data
        loadDashboardData();
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """🏠 Main dashboard page."""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/dashboard')
def api_dashboard():
    """📊 API endpoint for dashboard data."""
    global pnl_integration
    
    if not pnl_integration:
        return jsonify({'error': 'P&L tracking not initialized'})
    
    try:
        dashboard_data = pnl_integration.get_performance_dashboard()
        return jsonify(dashboard_data)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/export')
def api_export():
    """📁 API endpoint for CSV export."""
    global pnl_tracker
    
    if not pnl_tracker:
        return jsonify({'error': 'P&L tracker not initialized'})
    
    try:
        filename = pnl_tracker.export_to_csv()
        if filename and os.path.exists(filename):
            return send_file(filename, as_attachment=True)
        else:
            return jsonify({'error': 'Failed to generate export file'})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/report')
def api_report():
    """📋 API endpoint for performance report."""
    global pnl_tracker
    
    if not pnl_tracker:
        return "P&L tracker not initialized"
    
    try:
        report = pnl_tracker.generate_performance_report(days=30)
        return report
    except Exception as e:
        return f"Error generating report: {e}"

def run_dashboard(host='127.0.0.1', port=2348, debug=False, db_path="trading_performance.db"):
    """🚀 Run the P&L web dashboard."""
    
    print(f"🌐 Starting P&L Web Dashboard...")
    print(f"   📊 Database: {db_path}")
    print(f"   🌍 Host: {host}:{port}")
    
    # Initialize P&L dashboard
    if not init_pnl_dashboard(db_path):
        print("❌ Failed to initialize P&L dashboard")
        return
    
    print(f"🚀 P&L Dashboard running at: http://{host}:{port}")
    print("   📊 View real-time trading performance")
    print("   📁 Export data to CSV")
    print("   📋 Generate detailed reports")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except Exception as e:
        print(f"❌ Error running dashboard: {e}")

if __name__ == "__main__":
    # Run dashboard with demo data
    run_dashboard(
        host='127.0.0.1',
        port=2348,
        debug=True,
        db_path="demo_trading_performance.db"
    )
