#!/usr/bin/env python3
"""
🔍 Simple Stability Test - Test đơn giản để monitor hệ thống
"""

import os
import time
import requests
import subprocess
from datetime import datetime, timed<PERSON><PERSON>

def send_telegram_message(message: str) -> bool:
    """📱 Send message to Telegram."""
    try:
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not bot_token or not chat_id:
            return False
        
        base_url = f"https://api.telegram.org/bot{bot_token}"
        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(f"{base_url}/sendMessage", data=data, timeout=10)
        return response.status_code == 200 and response.json().get('ok', False)
        
    except Exception as e:
        print(f"❌ Telegram error: {e}")
        return False

def simple_stability_test():
    """🔍 Run simple stability test."""
    print(f"🔍 SIMPLE STABILITY TEST")
    print(f"=" * 50)
    
    start_time = datetime.now()
    test_duration = 3  # 3 minutes for demo
    
    # Send start notification
    start_message = f"""🔍 <b>SIMPLE STABILITY TEST STARTED</b>

⏰ <b>Test Info:</b>
├ 🕐 Duration: <code>{test_duration} minutes</code>
├ 🎯 Purpose: <b>Monitor system stability</b>
└ 📊 Method: <b>Process monitoring</b>

🔍 <b>Monitoring:</b>
├ 🤖 Bot process health
├ 📁 Chart file generation
├ 🔄 System responsiveness
└ ❌ Error detection

<b>🚀 TEST STARTING...</b>"""
    
    if send_telegram_message(start_message):
        print(f"📱 Start notification sent")
    
    # Start bot process
    bot_process = None
    errors_found = 0
    charts_created = 0
    
    try:
        print(f"🚀 Starting bot process...")
        bot_process = subprocess.Popen([
            "python", "main_bot.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print(f"✅ Bot started with PID: {bot_process.pid}")
        
        # Monitor for specified duration
        end_time = start_time + timedelta(minutes=test_duration)
        last_chart_count = 0
        
        while datetime.now() < end_time:
            current_time = datetime.now()
            
            # Check if process is still running
            if bot_process.poll() is not None:
                print(f"❌ Bot process terminated!")
                errors_found += 1
                break
            
            # Check for new chart files
            try:
                if os.path.exists("charts"):
                    chart_files = [f for f in os.listdir("charts") if f.endswith('.png')]
                    current_chart_count = len(chart_files)
                    
                    if current_chart_count > last_chart_count:
                        new_charts = current_chart_count - last_chart_count
                        charts_created += new_charts
                        print(f"📊 {new_charts} new chart(s) created (total: {current_chart_count})")
                        last_chart_count = current_chart_count
            except Exception as e:
                print(f"⚠️ Error checking charts: {e}")
                errors_found += 1
            
            # Show progress
            runtime = current_time - start_time
            remaining = end_time - current_time
            
            print(f"🔍 Runtime: {int(runtime.total_seconds()//60)}:{int(runtime.total_seconds()%60):02d} | "
                  f"Remaining: {int(remaining.total_seconds()//60)}:{int(remaining.total_seconds()%60):02d} | "
                  f"Charts: {charts_created} | Errors: {errors_found}")
            
            time.sleep(10)  # Check every 10 seconds
        
        print(f"⏰ Test duration completed")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        errors_found += 1
    
    finally:
        # Stop bot process
        if bot_process:
            print(f"🛑 Stopping bot...")
            try:
                bot_process.terminate()
                bot_process.wait(timeout=5)
                print(f"✅ Bot stopped")
            except:
                try:
                    bot_process.kill()
                    print(f"⚠️ Bot force killed")
                except:
                    print(f"❌ Could not stop bot")
    
    # Generate results
    end_time = datetime.now()
    total_runtime = end_time - start_time
    
    # Assessment
    if errors_found == 0 and charts_created > 0:
        status = "🟢 EXCELLENT"
        assessment = "System is stable and active"
    elif errors_found == 0:
        status = "🟡 GOOD"
        assessment = "System is stable but low activity"
    elif errors_found < 3:
        status = "🟠 FAIR"
        assessment = "Some issues detected"
    else:
        status = "🔴 POOR"
        assessment = "Multiple issues detected"
    
    # Final report
    final_report = f"""🔍 <b>SIMPLE STABILITY TEST - RESULTS</b>

⏰ <b>TEST SUMMARY</b>
├ 🕐 Duration: <code>{str(total_runtime).split('.')[0]}</code>
├ 🤖 Bot Status: <b>{'STABLE' if errors_found == 0 else 'UNSTABLE'}</b>
├ 📊 Charts Created: <code>{charts_created}</code>
└ ❌ Errors Found: <code>{errors_found}</code>

🏥 <b>SYSTEM HEALTH</b>
├ 🎯 Status: <b>{status}</b>
├ 📊 Activity: <b>{'ACTIVE' if charts_created > 0 else 'IDLE'}</b>
└ 💡 Assessment: <b>{assessment}</b>

💡 <b>RECOMMENDATION</b>"""
    
    if errors_found == 0 and charts_created > 0:
        final_report += f"\n├ ✅ System is working perfectly"
        final_report += f"\n└ 🚀 Ready for production use"
    elif errors_found == 0:
        final_report += f"\n├ ✅ System is stable"
        final_report += f"\n└ 🔍 Consider longer test for activity"
    else:
        final_report += f"\n├ ⚠️ Issues detected need attention"
        final_report += f"\n└ 🔧 Review errors before production"
    
    final_report += f"\n\n🔍 <b>SIMPLE STABILITY TEST COMPLETED</b>"
    
    # Send final report
    if send_telegram_message(final_report):
        print(f"📱 Final report sent")
    
    # Console summary
    print(f"\n📊 TEST RESULTS:")
    print(f"  ⏰ Runtime: {str(total_runtime).split('.')[0]}")
    print(f"  🤖 Bot Status: {'STABLE' if errors_found == 0 else 'UNSTABLE'}")
    print(f"  📊 Charts: {charts_created}")
    print(f"  ❌ Errors: {errors_found}")
    print(f"  🎯 Status: {status}")
    print(f"  💡 Assessment: {assessment}")
    
    return errors_found == 0

def main():
    """🚀 Main function."""
    print(f"🔍 SIMPLE STABILITY TEST")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    print(f"")
    
    # Check environment
    if not os.getenv("TELEGRAM_BOT_TOKEN") or not os.getenv("TELEGRAM_CHAT_ID"):
        print(f"❌ Missing environment variables")
        return
    
    if not os.path.exists("main_bot.py"):
        print(f"❌ main_bot.py not found")
        return
    
    # Run test
    try:
        success = simple_stability_test()
        
        if success:
            print(f"\n🎉 TEST PASSED!")
            print(f"✅ System appears stable")
        else:
            print(f"\n⚠️ TEST FOUND ISSUES!")
            print(f"❌ Check Telegram for details")
            
    except Exception as e:
        print(f"❌ Fatal error: {e}")

if __name__ == "__main__":
    main()
