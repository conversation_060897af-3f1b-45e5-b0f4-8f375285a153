#!/usr/bin/env python3
"""
🧪 Final test để đảm bảo Volume Profile không trả về NONE và consensus threshold là 80%
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

def create_realistic_test_data():
    """Tạo dữ liệu test realistic cho WIF/USDT"""
    try:
        # Create 200 candles of realistic data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=200), 
                             end=datetime.now(), freq='1H')
        
        # Generate realistic WIF/USDT-like data
        base_price = 2.45  # WIF price around $2.45
        prices = []
        volumes = []
        
        for i in range(len(dates)):
            # Add some trend and volatility
            trend_factor = 1 + (i * 0.0001)  # Slight uptrend
            volatility = np.random.normal(0, 0.03)  # 3% volatility
            
            if i == 0:
                price = base_price
            else:
                price = prices[-1] * trend_factor * (1 + volatility)
            
            prices.append(max(0.1, price))  # Prevent negative prices
            
            # Volume with some spikes
            base_volume = np.random.uniform(500000, 2000000)
            if i % 12 == 0:  # Volume spike every 12 candles
                base_volume *= 3
            volumes.append(base_volume)
        
        # Create OHLCV data
        data = []
        for i, (date, price, volume) in enumerate(zip(dates, prices, volumes)):
            volatility = abs(np.random.normal(0, 0.02))
            
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            open_price = price * (1 + np.random.normal(0, 0.01))
            close_price = price * (1 + np.random.normal(0, 0.01))
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ Created realistic test data: {len(df)} candles")
        print(f"  📊 Price range: ${df['close'].min():.4f} - ${df['close'].max():.4f}")
        print(f"  📊 Volume range: {df['volume'].min():,.0f} - {df['volume'].max():,.0f}")
        print(f"  📊 Current price: ${df['close'].iloc[-1]:.6f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        return None

def test_volume_profile_no_none():
    """Test Volume Profile để đảm bảo không trả về NONE"""
    print("📊 TESTING VOLUME PROFILE - NO NONE SIGNALS")
    print("=" * 70)
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        # Create analyzer
        analyzer = VolumeProfileAnalyzer(
            price_bins=50,
            value_area_percentage=70.0,
            min_data_points=100
        )
        
        # Create test data
        df = create_realistic_test_data()
        if df is None:
            return False
        
        print(f"\n🧪 Testing Volume Profile analysis...")
        print(f"  📊 Data points: {len(df)}")
        print(f"  💰 Current price: ${df['close'].iloc[-1]:.6f}")
        
        # Test multiple scenarios
        test_scenarios = [
            ("Full data (200 candles)", df),
            ("Reduced data (150 candles)", df.tail(150)),
            ("Minimum data (100 candles)", df.tail(100)),
        ]
        
        all_passed = True
        
        for scenario_name, test_df in test_scenarios:
            print(f"\n🔍 Testing scenario: {scenario_name}")
            print(f"  📊 Candles: {len(test_df)}")
            
            try:
                # Run analysis
                result = analyzer.analyze_volume_profile(test_df, lookback_periods=min(200, len(test_df)))
                
                # Check result
                if result and result.get("status") == "success":
                    signals = result.get("signals", {})
                    primary_signal = signals.get("primary_signal", "NONE")
                    confidence = signals.get("confidence", 0)
                    
                    print(f"  📊 Analysis status: {result.get('status')}")
                    print(f"  🎯 Primary signal: {primary_signal}")
                    print(f"  💪 Confidence: {confidence:.3f}")
                    
                    # Check if signal is valid
                    if primary_signal in ["BUY", "SELL"]:
                        print(f"  ✅ PASSED: Valid signal generated")
                    else:
                        print(f"  ❌ FAILED: Invalid signal '{primary_signal}' - SHOULD NEVER BE NONE!")
                        all_passed = False
                        
                        # Debug information
                        print(f"    🔍 Debug info:")
                        print(f"      - Volume profile: {result.get('volume_profile', {}).get('total_volume', 0):,.0f}")
                        print(f"      - VPOC: {result.get('vpoc', {}).get('price', 0):.6f}")
                        print(f"      - Value area: {result.get('value_area', {})}")
                        
                elif result and result.get("status") == "error":
                    print(f"  ❌ FAILED: Analysis error - {result.get('message', 'Unknown error')}")
                    all_passed = False
                else:
                    print(f"  ❌ FAILED: No result returned")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ FAILED: Exception - {e}")
                all_passed = False
                import traceback
                traceback.print_exc()
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Cannot import VolumeProfileAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Volume Profile analyzer: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consensus_threshold():
    """Test consensus threshold để đảm bảo là 80%"""
    print("\n🎯 TESTING CONSENSUS THRESHOLD - SHOULD BE 80%")
    print("=" * 70)
    
    try:
        # Import main_bot to check threshold
        import main_bot
        
        threshold = main_bot.MIN_CONFIDENCE_THRESHOLD
        print(f"📊 Current MIN_CONFIDENCE_THRESHOLD: {threshold}")
        print(f"📊 Expected threshold: 0.80 (80%)")
        
        if threshold == 0.80:
            print(f"✅ PASSED: Consensus threshold is correctly set to 80%")
            return True
        else:
            print(f"❌ FAILED: Consensus threshold is {threshold*100:.1f}%, should be 80%")
            return False
            
    except ImportError as e:
        print(f"❌ Cannot import main_bot: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking consensus threshold: {e}")
        return False

def simulate_consensus_analysis():
    """Simulate consensus analysis với Volume Profile fix"""
    print("\n🔍 SIMULATING CONSENSUS ANALYSIS")
    print("=" * 70)
    
    try:
        # Simulate the exact scenario from user's log
        print(f"🎯 Simulating consensus for WIF/USDT...")
        
        # Mock consensus data similar to user's log
        mock_signals = {
            "AI": {"signal": "SELL", "confidence": 0.949, "weight": 0.2336448598130841},
            "Volume Profile": {"signal": "BUY", "confidence": 0.45, "weight": 0.20},  # Fixed: No longer NONE
            "Point & Figure": {"signal": "BUY", "confidence": 0.606, "weight": 0.16822429906542055},
            "Fibonacci": {"signal": "SELL", "confidence": 0.721, "weight": 0.22},
            "Fourier": {"signal": "BUY", "confidence": 0.653, "weight": 0.09345794392523364},
            "Orderbook": {"signal": "BUY", "confidence": 0.60, "weight": 0.04672897196261682}
        }
        
        print(f"📊 Mock consensus signals:")
        total_weight = 0
        contributing_signals = 0
        
        for analyzer, data in mock_signals.items():
            signal = data["signal"]
            confidence = data["confidence"]
            weight = data["weight"]
            
            if signal in ["BUY", "SELL"]:
                contributing_signals += 1
                total_weight += weight
                print(f"  ✅ {analyzer}: {signal} ({confidence:.1%}) - Weight: {weight:.3f}")
            else:
                print(f"  ❌ {analyzer}: No valid signal (signal={signal})")
        
        print(f"\n📊 Consensus Analysis Results:")
        print(f"  📊 Total contributing signals: {contributing_signals}/6")
        print(f"  ⚖️ Total weight: {total_weight:.3f}")
        
        # Calculate consensus
        buy_score = sum(data["weight"] for data in mock_signals.values() 
                       if data["signal"] == "BUY")
        sell_score = sum(data["weight"] for data in mock_signals.values() 
                        if data["signal"] == "SELL")
        
        if buy_score > sell_score:
            consensus_signal = "BUY"
            consensus_confidence = buy_score / total_weight if total_weight > 0 else 0
        else:
            consensus_signal = "SELL"
            consensus_confidence = sell_score / total_weight if total_weight > 0 else 0
        
        print(f"  🎯 Consensus: {consensus_signal}")
        print(f"  💪 Confidence: {consensus_confidence:.1%}")
        
        # Check against 80% threshold
        threshold = 0.80
        print(f"\n🎯 Quality Check:")
        print(f"  - Signal: {consensus_signal}")
        print(f"  - Confidence: {consensus_confidence:.1%}")
        print(f"  - Required Threshold: {threshold:.1%}")
        
        if consensus_confidence >= threshold:
            print(f"  ✅ Consensus signal meets quality threshold ({consensus_confidence:.1%} >= {threshold:.1%})")
            print(f"  🎉 SUCCESS: Strong consensus achieved!")
            return True
        else:
            print(f"  ❌ Consensus signal below quality threshold ({consensus_confidence:.1%} < {threshold:.1%})")
            return False
            
    except Exception as e:
        print(f"❌ Error in consensus simulation: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 VOLUME PROFILE NONE FIX & CONSENSUS 80% THRESHOLD TEST")
    print("=" * 80)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Volume Profile no NONE signals
    vp_result = test_volume_profile_no_none()
    
    # Test 2: Consensus threshold check
    threshold_result = test_consensus_threshold()
    
    # Test 3: Simulate consensus analysis
    consensus_result = simulate_consensus_analysis()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 80)
    
    tests = [
        ("Volume Profile No NONE", vp_result),
        ("Consensus Threshold 80%", threshold_result),
        ("Consensus Simulation", consensus_result)
    ]
    
    all_passed = True
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Volume Profile never returns NONE signals")
        print("✅ Consensus threshold correctly set to 80%")
        print("✅ Expected behavior:")
        print("  • Volume Profile: Always BUY or SELL (never NONE)")
        print("  • Consensus: 6/6 signals contributing")
        print("  • Quality threshold: 80% (easier to pass)")
        print("  • More trading opportunities!")
        
    else:
        print("❌ SOME TESTS FAILED")
        print("Check the specific failures above")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
