#!/usr/bin/env python3
"""
🚀 QUICK MAIN_BOT TEST
=====================

Quick test để kiểm tra main_bot.py có thể import và chạy được không.
"""

import os
import sys

def quick_test():
    """Quick test main_bot import"""
    print("🔧 Quick Main Bot Test")
    print("=" * 40)
    
    try:
        print("📦 Testing main_bot import...")
        import main_bot
        print("  ✅ main_bot imported successfully")
        
        print("🤖 Testing TradingBot class...")
        bot_class = main_bot.TradingBot
        print("  ✅ TradingBot class accessible")
        
        print("⚙️ Testing key configurations...")
        configs = [
            'DYNAMIC_TP_SL_ENABLED',
            'CHART_GENERATION_ENABLED', 
            'AI_ENSEMBLE_REPORTS_ENABLED'
        ]
        
        for config in configs:
            if hasattr(main_bot, config):
                value = getattr(main_bot, config)
                print(f"  ✅ {config}: {value}")
            else:
                print(f"  ❌ {config}: Not found")
        
        print("\n🎉 SUCCESS: Main bot can be imported and basic configurations are accessible!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
