#!/usr/bin/env python3
"""
🧪 TEST: Orderbook Spread Factor Fix
Test để kiểm tra fix cho lỗi 'spread_factor' referenced before assignment
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_orderbook_trading_levels_spread_factor():
    """Test orderbook trading levels calculation with spread_factor fix"""
    print("🧪 === TESTING ORDERBOOK SPREAD FACTOR FIX ===")
    
    try:
        from orderbook_analyzer import OrderbookAnalyzer
        
        # Initialize analyzer
        analyzer = OrderbookAnalyzer()
        
        # Create test data that would trigger the spread_factor error
        bid_levels = [
            {"price": 99.5, "volume": 1000},
            {"price": 99.0, "volume": 1500},
            {"price": 98.5, "volume": 2000}
        ]
        
        ask_levels = [
            {"price": 100.5, "volume": 1000},
            {"price": 101.0, "volume": 1500},
            {"price": 101.5, "volume": 2000}
        ]
        
        current_price = 100.0
        
        # Create imbalance analysis that might not trigger specific if/else blocks
        imbalance_analysis = {
            "primary_imbalance": 0.05,  # Small imbalance
            "trading_pressure": "balanced",
            "bid_ask_ratio": 1.0  # Exactly balanced - might not trigger specific conditions
        }
        
        # Create spread analysis
        spread_analysis = {
            "relative_spread_pct": 0.1,  # 0.1%
            "spread_quality": "fair",
            "absolute_spread": 1.0
        }
        
        print(f"📊 Test Data:")
        print(f"  Current Price: ${current_price}")
        print(f"  Bid/Ask Ratio: {imbalance_analysis['bid_ask_ratio']}")
        print(f"  Primary Imbalance: {imbalance_analysis['primary_imbalance']}")
        print(f"  Spread Quality: {spread_analysis['spread_quality']}")
        
        # Test the method that was causing the error
        print(f"\n🧪 Testing _calculate_orderbook_trading_levels...")
        
        result = analyzer._calculate_orderbook_trading_levels(
            bid_levels=bid_levels,
            ask_levels=ask_levels,
            current_price=current_price,
            imbalance_analysis=imbalance_analysis,
            spread_analysis=spread_analysis
        )
        
        if result.get("has_trading_levels", False):
            print(f"✅ Trading levels calculated successfully:")
            print(f"  Signal Type: {result.get('signal_type')}")
            print(f"  Entry Price: ${result.get('entry_price', 0):.8f}")
            print(f"  Take Profit: ${result.get('take_profit', 0):.8f}")
            print(f"  Stop Loss: ${result.get('stop_loss', 0):.8f}")
            print(f"  Risk/Reward: {result.get('risk_reward_ratio', 0):.2f}")
            
            # Validate TP levels
            tp_levels = result.get("tp_levels", {})
            if tp_levels:
                print(f"  TP1: ${tp_levels.get('tp1', 0):.8f}")
                print(f"  TP2: ${tp_levels.get('tp2', 0):.8f}")
                print(f"  TP3: ${tp_levels.get('tp3', 0):.8f}")
            
            return True
        else:
            print(f"❌ Trading levels calculation failed")
            print(f"  Result: {result}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases_spread_factor():
    """Test edge cases that might trigger spread_factor error"""
    print("\n🧪 === TESTING EDGE CASES FOR SPREAD FACTOR ===")
    
    try:
        from orderbook_analyzer import OrderbookAnalyzer
        
        analyzer = OrderbookAnalyzer()
        
        # Test case 1: Exactly balanced ratio (1.0)
        print(f"\n📊 Test Case 1: Exactly Balanced Ratio")
        
        result1 = analyzer._calculate_orderbook_trading_levels(
            bid_levels=[{"price": 100, "volume": 1000}],
            ask_levels=[{"price": 100.1, "volume": 1000}],
            current_price=100.05,
            imbalance_analysis={
                "primary_imbalance": 0.0,
                "trading_pressure": "balanced",
                "bid_ask_ratio": 1.0  # Exactly 1.0 - edge case
            },
            spread_analysis={
                "relative_spread_pct": 0.05,
                "spread_quality": "good"
            }
        )
        
        if result1.get("has_trading_levels"):
            print(f"✅ Case 1 passed: {result1.get('signal_type')} signal generated")
        else:
            print(f"❌ Case 1 failed")
            return False
        
        # Test case 2: Ratio in middle range (0.95)
        print(f"\n📊 Test Case 2: Middle Range Ratio")
        
        result2 = analyzer._calculate_orderbook_trading_levels(
            bid_levels=[{"price": 100, "volume": 950}],
            ask_levels=[{"price": 100.1, "volume": 1000}],
            current_price=100.05,
            imbalance_analysis={
                "primary_imbalance": -0.025,
                "trading_pressure": "slightly_bearish",
                "bid_ask_ratio": 0.95  # Middle range
            },
            spread_analysis={
                "relative_spread_pct": 0.08,
                "spread_quality": "fair"
            }
        )
        
        if result2.get("has_trading_levels"):
            print(f"✅ Case 2 passed: {result2.get('signal_type')} signal generated")
        else:
            print(f"❌ Case 2 failed")
            return False
        
        # Test case 3: Fallback scenario with good spread
        print(f"\n📊 Test Case 3: Fallback Scenario")
        
        result3 = analyzer._calculate_orderbook_trading_levels(
            bid_levels=[{"price": 100, "volume": 1000}],
            ask_levels=[{"price": 100.05, "volume": 1000}],
            current_price=100.025,
            imbalance_analysis={
                "primary_imbalance": 0.01,  # Very small imbalance
                "trading_pressure": "balanced",
                "bid_ask_ratio": 1.0
            },
            spread_analysis={
                "relative_spread_pct": 0.03,  # Good spread
                "spread_quality": "excellent"
            }
        )
        
        if result3.get("has_trading_levels"):
            print(f"✅ Case 3 passed: {result3.get('signal_type')} signal generated")
        else:
            print(f"❌ Case 3 failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Edge cases test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_spread_factor_initialization():
    """Test that spread_factor is properly initialized"""
    print("\n🧪 === TESTING SPREAD FACTOR INITIALIZATION ===")
    
    try:
        from orderbook_analyzer import OrderbookAnalyzer
        
        analyzer = OrderbookAnalyzer()
        
        # Test with missing spread data
        print(f"📊 Testing with minimal spread data...")
        
        result = analyzer._calculate_orderbook_trading_levels(
            bid_levels=[{"price": 100, "volume": 1000}],
            ask_levels=[{"price": 100.1, "volume": 1000}],
            current_price=100.05,
            imbalance_analysis={
                "primary_imbalance": 0.0,
                "trading_pressure": "balanced",
                "bid_ask_ratio": 1.0
            },
            spread_analysis={}  # Empty spread analysis
        )
        
        if result.get("has_trading_levels"):
            print(f"✅ Spread factor initialization working with empty spread data")
            
            # Check if spread_factor was used (should be default value)
            entry_price = result.get("entry_price", 0)
            stop_loss = result.get("stop_loss", 0)
            
            if entry_price > 0 and stop_loss > 0:
                print(f"  Entry: ${entry_price:.8f}")
                print(f"  Stop Loss: ${stop_loss:.8f}")
                print(f"  Spread factor successfully used in calculations")
                return True
            else:
                print(f"❌ Invalid entry or stop loss prices")
                return False
        else:
            print(f"❌ Trading levels not generated")
            return False
        
    except Exception as e:
        print(f"❌ Spread factor initialization test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all orderbook spread factor fix tests"""
    print("🧪 === ORDERBOOK SPREAD FACTOR FIX TEST ===")
    
    test1 = test_orderbook_trading_levels_spread_factor()
    test2 = test_edge_cases_spread_factor()
    test3 = test_spread_factor_initialization()
    
    if test1 and test2 and test3:
        print("\n🎉 SUCCESS: Orderbook spread factor fixes working!")
        print("✅ spread_factor properly initialized early")
        print("✅ No more 'referenced before assignment' errors")
        print("✅ All edge cases handled correctly")
        print("✅ Trading levels calculation working")
        print("✅ Ready for production deployment")
    else:
        print("\n❌ FAILED: Some spread factor fixes not working")
    
    return test1 and test2 and test3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
