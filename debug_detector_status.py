#!/usr/bin/env python3
"""
🔍 DEBUG DETECTOR STATUS
========================

Debug script để kiểm tra trạng thái thực tế của PUMP/DUMP detectors.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_detector_status():
    """Debug detector status in main_bot."""
    print("🔍 DEBUGGING DETECTOR STATUS")
    print("=" * 70)
    
    try:
        # Import main_bot modules
        print("📊 1. TESTING MODULE IMPORTS:")
        print("=" * 50)
        
        try:
            from dump_detector import UltraEarlyDumpDetector
            print("  ✅ UltraEarlyDumpDetector: Import successful")
        except ImportError as e:
            print(f"  ❌ UltraEarlyDumpDetector: Import failed - {e}")
            
        try:
            from volume_spike_detector import VolumeSpikeDetector
            print("  ✅ VolumeSpikeDetector: Import successful")
        except ImportError as e:
            print(f"  ❌ VolumeSpikeDetector: Import failed - {e}")
            
        try:
            from consensus_analyzer import ConsensusAnalyzer
            print("  ✅ ConsensusAnalyzer: Import successful")
        except ImportError as e:
            print(f"  ❌ ConsensusAnalyzer: Import failed - {e}")
            
        print("\n📊 2. TESTING DETECTOR INITIALIZATION:")
        print("=" * 50)
        
        # Test dump detector initialization
        try:
            dump_detector = UltraEarlyDumpDetector(
                sensitivity=0.7,
                min_volume_threshold=1.5,
                whale_threshold=50000
            )
            print(f"  ✅ Dump Detector: Initialized successfully")
            print(f"    - Type: {type(dump_detector)}")
            print(f"    - Has analyze method: {hasattr(dump_detector, 'analyze')}")
        except Exception as e:
            print(f"  ❌ Dump Detector: Initialization failed - {e}")
            dump_detector = None
            
        # Test volume detector (pump) initialization
        try:
            volume_detector = VolumeSpikeDetector(
                spike_threshold_multiplier=2.5,
                moving_avg_period=20,
                min_data_points=30
            )
            print(f"  ✅ Volume Detector (Pump): Initialized successfully")
            print(f"    - Type: {type(volume_detector)}")
            print(f"    - Has pump methods: {hasattr(volume_detector, '_analyze_pump_patterns')}")
            print(f"    - Has get_spike_details: {hasattr(volume_detector, 'get_spike_details')}")
        except Exception as e:
            print(f"  ❌ Volume Detector (Pump): Initialization failed - {e}")
            volume_detector = None
            
        print("\n📊 3. TESTING CONSENSUS ANALYZER WITH DETECTORS:")
        print("=" * 50)
        
        # Test consensus analyzer with detectors
        try:
            external_analyzers = {
                "volume_profile_analyzer": None,
                "point_figure_analyzer": None,
                "fourier_analyzer": None,
                "volume_pattern_analyzer": None,
                "volume_spike_detector": volume_detector,
                "ai_manager": None,
                "orderbook_analyzer": None,
                "dump_detector": dump_detector,
                "pump_detector": volume_detector  # Use volume_detector for pump
            }
            
            weight_config = {
                "ai_models": 0.25,
                "volume_profile": 0.20,
                "point_figure": 0.16,
                "zigzag_fibonacci": 0.16,
                "fourier": 0.06,
                "volume_patterns": 0.04,
                "dump_detector": 0.08,
                "pump_detector": 0.08
            }
            
            consensus_analyzer = ConsensusAnalyzer(
                min_consensus_score=0.55,
                weight_config=weight_config,
                confidence_threshold=0.65,
                external_analyzers=external_analyzers,
                enable_meta_learning=True,
                enable_adaptive_weights=True,
                enable_regime_detection=True,
                enable_cross_validation=True
            )
            
            print(f"  ✅ Consensus Analyzer: Initialized successfully")
            
            # Check analyzer connections
            connections = consensus_analyzer.analyzer_connections
            print(f"\n  📊 Analyzer Connections Status:")
            for name, analyzer in connections.items():
                status = "✅ Connected" if analyzer is not None else "❌ Not Connected"
                print(f"    - {name}: {status}")
                
            # Count connected analyzers
            connected_count = sum(1 for conn in connections.values() if conn is not None)
            print(f"\n  📊 Summary: {connected_count}/{len(connections)} analyzers connected")
            
            if connections.get('dump_detector') is not None:
                print(f"  ✅ Dump Detector: Successfully connected to consensus")
            else:
                print(f"  ❌ Dump Detector: NOT connected to consensus")
                
            if connections.get('pump_detector') is not None:
                print(f"  ✅ Pump Detector: Successfully connected to consensus")
            else:
                print(f"  ❌ Pump Detector: NOT connected to consensus")
                
        except Exception as e:
            print(f"  ❌ Consensus Analyzer: Initialization failed - {e}")
            import traceback
            traceback.print_exc()
            
        print("\n📊 4. TESTING SAMPLE ANALYSIS:")
        print("=" * 50)
        
        # Test sample analysis
        try:
            if 'consensus_analyzer' in locals():
                # Create sample analysis input
                sample_input = {
                    'coin': 'TESTCOIN/USDT',
                    'ai_prediction': {'ensemble_signal': 'BUY', 'ensemble_confidence': 0.7},
                    'volume_profile': {'signal': 'BUY', 'confidence': 0.6},
                    'point_figure': {'signal': 'SELL', 'confidence': 0.8},
                    'fibonacci': {'signal': 'SELL', 'confidence': 0.7},
                    'fourier': {'signal': 'SELL', 'confidence': 0.9},
                    'orderbook': {'signals': {'primary_signal': 'BUY', 'confidence': 0.45}},
                    'dump_analysis': {'dump_probability': 0.3, 'confidence': 0.3},
                    'pump_analysis': {'pump_probability': 0.4, 'confidence': 0.4}
                }
                
                print(f"  🧪 Running sample consensus analysis...")
                result = consensus_analyzer.analyze_consensus(sample_input)
                
                if result.get('status') == 'success':
                    print(f"  ✅ Sample Analysis: SUCCESS")
                    consensus = result.get('consensus', {})
                    print(f"    - Signal: {consensus.get('signal', 'UNKNOWN')}")
                    print(f"    - Confidence: {consensus.get('confidence', 0):.1%}")
                    print(f"    - Contributing Algorithms: {consensus.get('signal_count', 0)}")
                else:
                    print(f"  ❌ Sample Analysis: FAILED")
                    print(f"    - Error: {result.get('error', 'Unknown error')}")
                    
        except Exception as e:
            print(f"  ❌ Sample Analysis: Exception - {e}")
            
        print("\n🎯 DIAGNOSIS SUMMARY:")
        print("=" * 50)
        
        if dump_detector is not None and volume_detector is not None:
            print("✅ Both detectors can be initialized successfully")
            if 'consensus_analyzer' in locals():
                dump_connected = consensus_analyzer.analyzer_connections.get('dump_detector') is not None
                pump_connected = consensus_analyzer.analyzer_connections.get('pump_detector') is not None
                
                if dump_connected and pump_connected:
                    print("✅ Both detectors successfully connected to consensus")
                    print("🔍 Issue might be:")
                    print("   1. Detectors are None in actual main_bot runtime")
                    print("   2. Analysis input missing dump/pump analysis data")
                    print("   3. Detection thresholds too high")
                    print("   4. OHLCV data quality issues")
                else:
                    print("❌ Detectors not connected to consensus")
                    print("🔧 Check external_analyzers parameter in main_bot")
            else:
                print("❌ Consensus analyzer initialization failed")
        else:
            print("❌ Detector initialization failed")
            print("🔧 Check detector module imports and dependencies")
            
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run detector status debug."""
    print("🔍 DETECTOR STATUS DEBUG")
    print("=" * 70)
    
    success = debug_detector_status()
    
    if success:
        print(f"\n🎯 DEBUG COMPLETED SUCCESSFULLY!")
        print(f"Check the output above to identify the issue.")
    else:
        print(f"\n❌ DEBUG FAILED!")
        print(f"Check error messages above for troubleshooting.")

if __name__ == "__main__":
    main()
