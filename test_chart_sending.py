#!/usr/bin/env python3
"""
🧪 TEST CHART SENDING SYSTEM
============================

Test để kiểm tra xem hệ thống gửi chart từ các thuật toán có hoạt động không.
"""

import os
import sys
import time
import pandas as pd
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_sending():
    """Test chart sending system."""
    print("🧪 TESTING CHART SENDING SYSTEM")
    print("=" * 50)
    
    try:
        # Import main bot
        from main_bot import TradingBot
        
        print("✅ Main bot imported successfully")
        
        # Initialize bot
        print("🚀 Initializing trading bot...")
        bot = TradingBot()
        
        print("✅ Bot initialized successfully")
        
        # Check chart generator
        print(f"📊 Chart generator exists: {hasattr(bot, 'chart_generator')}")
        print(f"📊 Chart generator value: {getattr(bot, 'chart_generator', None)}")
        
        # Check telegram notifier
        print(f"📱 Telegram notifier exists: {hasattr(bot, 'notifier')}")
        print(f"📱 Telegram notifier value: {getattr(bot, 'notifier', None)}")
        
        # Check chart config
        print(f"⚙️ Chart config: {getattr(bot, 'chart_config', {})}")
        
        # Test chart generation for a sample coin
        test_coin = "BTCUSDT"
        print(f"\n🪙 Testing chart generation for {test_coin}...")
        
        # Create sample OHLCV data
        sample_data = pd.DataFrame({
            'timestamp': pd.date_range(start='2024-01-01', periods=100, freq='1H'),
            'open': [50000 + i * 10 for i in range(100)],
            'high': [50100 + i * 10 for i in range(100)],
            'low': [49900 + i * 10 for i in range(100)],
            'close': [50050 + i * 10 for i in range(100)],
            'volume': [1000 + i * 5 for i in range(100)]
        })
        
        current_price = 51000.0
        
        # Test Volume Profile chart generation
        if hasattr(bot, 'volume_profile_analyzer') and bot.volume_profile_analyzer:
            print("📊 Testing Volume Profile chart generation...")
            try:
                volume_data = {
                    "vpoc": {"price": current_price, "volume": 1000},
                    "signals": {"primary_signal": "BUY", "confidence": 0.75},
                    "value_area": {"high": current_price * 1.02, "low": current_price * 0.98},
                    "distribution_metrics": {"concentration_ratio": 0.8, "profile_quality": "HIGH"},
                    "analysis_quality": "HIGH"
                }
                
                success = bot._send_volume_profile_analysis_report(test_coin, volume_data, current_price)
                print(f"📊 Volume Profile chart test: {'✅ SUCCESS' if success else '❌ FAILED'}")
            except Exception as e:
                print(f"❌ Volume Profile chart test failed: {e}")
        
        # Test Point & Figure chart generation
        if hasattr(bot, 'point_figure_analyzer') and bot.point_figure_analyzer:
            print("📈 Testing Point & Figure chart generation...")
            try:
                pf_data = {
                    "signals": {"primary_signal": "BUY", "confidence": 0.8},
                    "trend_analysis": {"trend": "UPTREND", "trend_strength": 0.9},
                    "pattern_recognition": {"pattern_type": "BULLISH", "pattern_strength": 0.85},
                    "price_objectives": {"upside_target": current_price * 1.05, "downside_target": current_price * 0.95},
                    "box_size": current_price * 0.01,
                    "reversal_amount": 3,
                    "current_column_type": "X",
                    "analysis_quality": "HIGH"
                }
                
                success = bot._send_point_figure_analysis_report(test_coin, pf_data, current_price)
                print(f"📈 Point & Figure chart test: {'✅ SUCCESS' if success else '❌ FAILED'}")
            except Exception as e:
                print(f"❌ Point & Figure chart test failed: {e}")
        
        # Test Fibonacci chart generation
        print("🌀 Testing Fibonacci chart generation...")
        try:
            fibonacci_data = {
                "status": "success",
                "retracement_levels": [
                    {"ratio": 0.236, "price": current_price * 0.976},
                    {"ratio": 0.382, "price": current_price * 0.962},
                    {"ratio": 0.618, "price": current_price * 0.938}
                ],
                "extension_levels": [
                    {"ratio": 1.618, "price": current_price * 1.062},
                    {"ratio": 2.618, "price": current_price * 1.124}
                ],
                "confluence_zones": [],
                "pivot_high": current_price * 1.05,
                "pivot_low": current_price * 0.95,
                "trend_direction": "UPTREND",
                "calculation_method": "enhanced_automatic",
                "confidence": 0.8,
                "signal_strength": "STRONG",
                "signals": {"overall_signal": "BUY", "confidence": 0.8}
            }
            
            success = bot._send_fibonacci_analysis_report(test_coin, fibonacci_data, current_price)
            print(f"🌀 Fibonacci chart test: {'✅ SUCCESS' if success else '❌ FAILED'}")
        except Exception as e:
            print(f"❌ Fibonacci chart test failed: {e}")
        
        # Test Fourier chart generation
        if hasattr(bot, 'fourier_analyzer') and bot.fourier_analyzer:
            print("🌊 Testing Fourier chart generation...")
            try:
                fourier_data = {
                    "status": "success",
                    "price_cycles": [{"frequency": 0.1, "amplitude": 100}],
                    "volume_cycles": [{"frequency": 0.2, "amplitude": 50}],
                    "dominant_cycle": 10.5,
                    "signals": {"overall_signal": "BUY", "confidence": 0.75},
                    "trend_component": 0.05,
                    "seasonal_strength": 0.7,
                    "cycle_amplitude": 0.02,
                    "market_regime": {"regime_type": "trending"},
                    "analysis_metadata": {"analysis_quality": "high"}
                }
                
                success = bot._send_fourier_analysis_report(test_coin, fourier_data, current_price)
                print(f"🌊 Fourier chart test: {'✅ SUCCESS' if success else '❌ FAILED'}")
            except Exception as e:
                print(f"❌ Fourier chart test failed: {e}")
        
        # Test AI analysis chart generation
        if hasattr(bot, 'ai_manager') and bot.ai_manager:
            print("🤖 Testing AI analysis chart generation...")
            try:
                ai_data = {
                    "ensemble_signal": "BUY",
                    "ensemble_confidence": 0.85,
                    "model_results": {
                        "XGBoost": {"prediction": "BUY", "confidence": 0.8},
                        "RandomForest": {"prediction": "BUY", "confidence": 0.9},
                        "LSTM": {"prediction": "BUY", "confidence": 0.7}
                    },
                    "working_models": 3,
                    "total_models": 11,
                    "prediction_quality": "HIGH"
                }
                
                success = bot._send_ai_analysis_report(test_coin, ai_data, current_price)
                print(f"🤖 AI analysis chart test: {'✅ SUCCESS' if success else '❌ FAILED'}")
            except Exception as e:
                print(f"❌ AI analysis chart test failed: {e}")
        
        print("\n🎯 CHART SENDING TEST COMPLETED")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Chart sending test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_chart_sending()
