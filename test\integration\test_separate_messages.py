#!/usr/bin/env python3
"""
📱 Test Separate Messages - Test chart + detailed report as separate messages
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def test_separate_messages():
    """📱 Test chart + detailed report as separate messages."""
    print(f"📱 SEPARATE MESSAGES TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 5000, 100)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Test 1: Fourier Analysis with Separate Messages
        print(f"\n1️⃣ Testing Fourier Analysis with Separate Messages...")
        
        fourier_data = {
            'price_cycles': [
                {'period': 40.0, 'confidence': 0.89, 'cycle_type': 'medium'},
                {'period': 18.2, 'confidence': 0.79, 'cycle_type': 'short'}
            ],
            'volume_cycles': [
                {'period': 10.7, 'confidence': 1.00}
            ],
            'signals': {
                'overall_signal': 'BULLISH',
                'signal_strength': 0.7,
                'confidence': 0.693,
                'trading_levels': {
                    'entry_price': current_price * 0.99,
                    'take_profit': current_price * 1.05,
                    'stop_loss': current_price * 0.98,
                    'risk_reward_ratio': 5.0
                }
            },
            'dominant_cycle': 40.0,
            'trend_component': 0.009,
            'seasonal_strength': 0.658,
            'market_regime': {
                'regime_type': 'stable_ranging',
                'volatility_level': 'medium',
                'trend_direction': 'neutral'
            },
            'analysis_metadata': {
                'confidence_level': 0.824,
                'analysis_quality': 'high'
            }
        }
        
        try:
            fourier_success = notifier.send_fourier_analysis_report(
                "BTC/USDT", fourier_data, current_price, use_html=True,
                ohlcv_data=ohlcv_data, chart_generator=chart_gen
            )
            
            if fourier_success:
                print(f"  ✅ Fourier analysis with separate messages sent successfully")
            else:
                print(f"  ❌ Fourier analysis with separate messages failed")
                
        except Exception as fourier_error:
            print(f"  ❌ Fourier test error: {fourier_error}")
            import traceback
            traceback.print_exc()
            fourier_success = False
        
        # Summary
        print(f"\n📱 SEPARATE MESSAGES TEST RESULTS:")
        print(f"  1️⃣ Fourier Analysis: {'✅ PASS' if fourier_success else '❌ FAIL'}")
        
        if fourier_success:
            print(f"\n🎉 SEPARATE MESSAGES SOLUTION WORKING!")
            print(f"✅ Chart sent with basic caption")
            print(f"✅ Detailed report sent as separate message")
            print(f"✅ No caption length limit issues")
            print(f"📱 Check your Telegram for chart + detailed report")
        else:
            print(f"\n⚠️ SEPARATE MESSAGES SOLUTION NEEDS WORK!")
        
        return fourier_success
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_separate_messages()
    if success:
        print(f"\n🎉 SEPARATE MESSAGES TEST PASSED!")
        print(f"📱 Chart + Detailed Report solution working perfectly!")
    else:
        print(f"\n💥 SEPARATE MESSAGES TEST FAILED!")
