#!/usr/bin/env python3
"""
🧪 Enhanced Trading Bot System Test Runner V6.0
==================================================

This script provides comprehensive testing capabilities for the Enhanced Trading Bot.
It can run various types of tests and generate detailed reports.

Usage:
    python run_tests.py                    # Run comprehensive system test
    python run_tests.py --quick           # Run quick health check only
    python run_tests.py --component core  # Test specific component
    python run_tests.py --report          # Generate detailed test report
    python run_tests.py --fix             # Attempt to fix common issues

Author: Enhanced Trading Bot Team
Version: 6.0
"""

import sys
import os
import argparse
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_test_environment():
    """🔧 Setup test environment."""
    print("🔧 Setting up test environment...")
    
    # Create test directories
    test_dirs = ["test_results", "test_logs", "test_backups"]
    for test_dir in test_dirs:
        os.makedirs(test_dir, exist_ok=True)
    
    print("✅ Test environment ready")

def run_comprehensive_test() -> Dict[str, Any]:
    """🧪 Run comprehensive system test."""
    try:
        print("\n🧪 === COMPREHENSIVE SYSTEM TEST V6.0 ===")
        print("🚀 Initializing Enhanced Trading Bot for testing...")
        
        # Import and initialize bot
        from main_bot import TradingBot
        
        print("📊 Creating bot instance...")
        bot = TradingBot()
        
        print("🔍 Running comprehensive system test...")
        test_results = bot.perform_comprehensive_system_test()
        
        # Save test results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results/comprehensive_test_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(test_results, f, indent=2, default=str)
        
        print(f"💾 Test results saved to: {results_file}")
        
        return test_results
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        import traceback
        traceback.print_exc()
        return {
            "overall_status": "error",
            "overall_health_score": 0.0,
            "error": str(e)
        }

def run_quick_health_check() -> Dict[str, Any]:
    """🏥 Run quick health check."""
    try:
        print("\n🏥 === QUICK HEALTH CHECK V6.0 ===")
        print("⚡ Running quick system validation...")
        
        # Import and initialize bot
        from main_bot import TradingBot
        
        print("📊 Creating bot instance...")
        bot = TradingBot()
        
        print("🔍 Performing quick health check...")
        health_results = bot.perform_quick_health_check()
        
        # Display results
        health_score = health_results.get("health_score", 0.0)
        print(f"\n📊 QUICK HEALTH CHECK RESULTS:")
        print(f"  🎯 Health Score: {health_score*100:.1f}%")
        print(f"  🔧 Core Systems: {health_results.get('core_systems_ready', 0)}/{health_results.get('total_core_systems', 0)}")
        print(f"  📱 Communication: {'✅' if health_results.get('communication_ready', False) else '❌'}")
        print(f"  🧠 AI System: {'✅' if health_results.get('ai_ready', False) else '❌'}")
        print(f"  💾 Backup System: {'✅' if health_results.get('backup_ready', False) else '❌'}")
        print(f"  📝 Logging System: {'✅' if health_results.get('logging_ready', False) else '❌'}")
        
        # Status determination
        if health_score >= 0.8:
            print("\n🏆 EXCELLENT: System is ready for production!")
        elif health_score >= 0.6:
            print("\n✅ GOOD: System is operational with minor issues")
        elif health_score >= 0.4:
            print("\n⚠️ WARNING: System has significant issues")
        else:
            print("\n🔴 CRITICAL: System requires immediate attention")
        
        return health_results
        
    except Exception as e:
        print(f"❌ Quick health check failed: {e}")
        import traceback
        traceback.print_exc()
        return {
            "health_score": 0.0,
            "error": str(e)
        }

def test_specific_component(component: str) -> Dict[str, Any]:
    """🔍 Test specific component."""
    try:
        print(f"\n🔍 === COMPONENT TEST: {component.upper()} ===")
        
        # Import and initialize bot
        from main_bot import TradingBot
        bot = TradingBot()
        
        # Map component names to test methods
        component_tests = {
            "core": bot.test_core_modules,
            "ai": bot.test_ai_system,
            "communication": bot.test_communication_system,
            "chart": bot.test_chart_system,
            "backup": bot.test_backup_logging_system,
            "admin": bot.test_admin_system,
            "performance": bot.test_performance_metrics
        }
        
        if component not in component_tests:
            print(f"❌ Unknown component: {component}")
            print(f"Available components: {', '.join(component_tests.keys())}")
            return {"status": "error", "error": f"Unknown component: {component}"}
        
        print(f"🧪 Testing {component} component...")
        test_result = component_tests[component]()
        
        # Display results
        status = test_result.get("status", "unknown")
        health_score = test_result.get("health_score", 0.0)
        
        print(f"\n📊 {component.upper()} TEST RESULTS:")
        print(f"  🎯 Status: {status.upper()}")
        print(f"  📈 Health Score: {health_score*100:.1f}%")
        print(f"  🧪 Tests Run: {test_result.get('tests_run', 0)}")
        print(f"  ✅ Tests Passed: {test_result.get('tests_passed', 0)}")
        
        # Show issues if any
        issues = test_result.get("issues", [])
        if issues:
            print(f"  ❌ Issues Found:")
            for issue in issues[:3]:
                print(f"    - {issue}")
            if len(issues) > 3:
                print(f"    ... and {len(issues) - 3} more issues")
        
        # Show details
        details = test_result.get("details", {})
        if details:
            print(f"  🔍 Details:")
            for detail_name, detail_value in details.items():
                print(f"    - {detail_name}: {detail_value}")
        
        return test_result
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "health_score": 0.0,
            "error": str(e)
        }

def generate_test_report(test_results: Dict[str, Any]):
    """📊 Generate detailed test report."""
    try:
        print("\n📊 === GENERATING TEST REPORT ===")
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        report_file = f"test_results/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w') as f:
            f.write(f"# Enhanced Trading Bot Test Report\n\n")
            f.write(f"**Generated:** {timestamp}\n\n")
            
            # Overall status
            overall_status = test_results.get("overall_status", "unknown")
            overall_health = test_results.get("overall_health_score", 0.0)
            
            f.write(f"## Overall Status\n\n")
            f.write(f"- **Status:** {overall_status.upper()}\n")
            f.write(f"- **Health Score:** {overall_health*100:.1f}%\n\n")
            
            # Test summary
            test_summary = test_results.get("test_summary", {})
            f.write(f"## Test Summary\n\n")
            f.write(f"- **Total Tests:** {test_summary.get('total_tests', 0)}\n")
            f.write(f"- **Passed:** {test_summary.get('passed_tests', 0)}\n")
            f.write(f"- **Warnings:** {test_summary.get('warning_tests', 0)}\n")
            f.write(f"- **Failed:** {test_summary.get('failed_tests', 0)}\n\n")
            
            # Component tests
            component_tests = test_results.get("component_tests", {})
            if component_tests:
                f.write(f"## Component Test Results\n\n")
                for component, result in component_tests.items():
                    status = result.get("status", "unknown")
                    health = result.get("health_score", 0.0)
                    f.write(f"### {component.replace('_', ' ').title()}\n\n")
                    f.write(f"- **Status:** {status.upper()}\n")
                    f.write(f"- **Health Score:** {health*100:.1f}%\n")
                    
                    # Details
                    details = result.get("details", {})
                    if details:
                        f.write(f"- **Details:**\n")
                        for detail_name, detail_value in details.items():
                            f.write(f"  - {detail_name}: {detail_value}\n")
                    f.write(f"\n")
            
            # Critical issues
            critical_issues = test_results.get("critical_issues", [])
            if critical_issues:
                f.write(f"## Critical Issues\n\n")
                for issue in critical_issues:
                    f.write(f"- ❌ {issue}\n")
                f.write(f"\n")
            
            # Recommendations
            recommendations = test_results.get("recommendations", [])
            if recommendations:
                f.write(f"## Recommendations\n\n")
                for rec in recommendations:
                    f.write(f"- 💡 {rec}\n")
                f.write(f"\n")
            
            # Performance metrics
            performance_metrics = test_results.get("performance_metrics", {})
            if performance_metrics:
                f.write(f"## Performance Metrics\n\n")
                for metric, value in performance_metrics.items():
                    f.write(f"- **{metric.replace('_', ' ').title()}:** {value}\n")
                f.write(f"\n")
        
        print(f"📄 Test report generated: {report_file}")
        return report_file
        
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        return None

def attempt_common_fixes():
    """🔧 Attempt to fix common issues."""
    print("\n🔧 === ATTEMPTING COMMON FIXES ===")
    
    fixes_applied = []
    
    try:
        # Fix 1: Install missing dependencies
        print("🔧 Checking and installing missing dependencies...")
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            fixes_applied.append("✅ Dependencies updated successfully")
        else:
            fixes_applied.append("⚠️ Dependency update had issues")
    except Exception as e:
        fixes_applied.append(f"❌ Dependency fix failed: {e}")
    
    # Fix 2: Create missing directories
    print("🔧 Creating missing directories...")
    try:
        directories = ["backup", "charts", "logs", "data", "models"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        fixes_applied.append("✅ Required directories created")
    except Exception as e:
        fixes_applied.append(f"❌ Directory creation failed: {e}")
    
    # Fix 3: Check file permissions
    print("🔧 Checking file permissions...")
    try:
        # Basic permission check
        test_file = "permission_test.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        fixes_applied.append("✅ File permissions are working")
    except Exception as e:
        fixes_applied.append(f"❌ File permission issue: {e}")
    
    # Display results
    print("\n🔧 FIXES APPLIED:")
    for fix in fixes_applied:
        print(f"  {fix}")
    
    return fixes_applied

def main():
    """🚀 Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Enhanced Trading Bot System Test Runner V6.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run comprehensive test
  python run_tests.py --quick           # Quick health check
  python run_tests.py --component core  # Test core component
  python run_tests.py --report          # Generate report only
  python run_tests.py --fix             # Fix common issues
        """
    )
    
    parser.add_argument("--quick", action="store_true", 
                       help="Run quick health check only")
    parser.add_argument("--component", type=str, 
                       help="Test specific component (core, ai, communication, chart, backup, admin, performance)")
    parser.add_argument("--report", action="store_true", 
                       help="Generate test report from last results")
    parser.add_argument("--fix", action="store_true", 
                       help="Attempt to fix common issues")
    parser.add_argument("--no-setup", action="store_true", 
                       help="Skip test environment setup")
    
    args = parser.parse_args()
    
    try:
        print("🧪 Enhanced Trading Bot System Test Runner V6.0")
        print("=" * 60)
        
        # Setup test environment
        if not args.no_setup:
            setup_test_environment()
        
        # Run based on arguments
        if args.fix:
            attempt_common_fixes()
            return 0
        
        elif args.quick:
            results = run_quick_health_check()
            health_score = results.get("health_score", 0.0)
            return 0 if health_score >= 0.5 else 1
        
        elif args.component:
            results = test_specific_component(args.component)
            status = results.get("status", "error")
            return 0 if status in ["pass", "warning"] else 1
        
        elif args.report:
            # Try to load last test results
            try:
                import glob
                result_files = glob.glob("test_results/comprehensive_test_*.json")
                if result_files:
                    latest_file = max(result_files, key=os.path.getctime)
                    with open(latest_file, 'r') as f:
                        results = json.load(f)
                    generate_test_report(results)
                    return 0
                else:
                    print("❌ No test results found. Run comprehensive test first.")
                    return 1
            except Exception as e:
                print(f"❌ Report generation failed: {e}")
                return 1
        
        else:
            # Run comprehensive test
            results = run_comprehensive_test()
            
            # Generate report
            generate_test_report(results)
            
            # Determine exit code
            overall_status = results.get("overall_status", "error")
            critical_issues = results.get("critical_issues", [])
            
            if overall_status == "excellent":
                return 0
            elif overall_status == "good":
                return 0
            elif overall_status == "acceptable":
                return 1 if critical_issues else 0
            elif overall_status == "poor":
                return 2
            else:
                return 3
    
    except KeyboardInterrupt:
        print("\n\n⏹️ Test runner stopped by user (Ctrl+C)")
        return 130
    
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
