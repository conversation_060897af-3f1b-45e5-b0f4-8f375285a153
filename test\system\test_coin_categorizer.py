#!/usr/bin/env python3
"""
🧪 Test script để kiểm tra CoinCategorizer functionality
"""

import os
import time
from datetime import datetime

def test_coin_categorizer_basic():
    """Test basic functionality của CoinCategorizer"""
    print("🔧 TESTING COIN CATEGORIZER BASIC FUNCTIONALITY")
    print("=" * 60)
    
    try:
        from coin_categorizer import CoinCategorizer
        
        # Initialize categorizer
        categorizer = CoinCategorizer(
            cache_file="test_coin_categories_cache.json",
            auto_update=False  # Disable auto-update for testing
        )
        
        print(f"✅ CoinCategorizer initialized successfully")
        print(f"  📊 Known coins: {len(categorizer.known_coins)}")
        print(f"  🏷️ Categories: {len(categorizer.categories)}")
        print(f"  🔄 Auto-update: {categorizer.auto_update}")
        
        return categorizer
        
    except ImportError as e:
        print(f"❌ Cannot import CoinCategorizer: {e}")
        return None
    except Exception as e:
        print(f"❌ Error initializing CoinCategorizer: {e}")
        return None

def test_known_coins_categorization(categorizer):
    """Test categorization của known coins"""
    print("\n📊 TESTING KNOWN COINS CATEGORIZATION")
    print("=" * 60)
    
    if not categorizer:
        print("❌ No categorizer available")
        return False
    
    # Test cases for known coins
    test_coins = [
        # Layer 1
        ("BTC/USDT", "LAYER1"),
        ("ETH/USDC", "LAYER1"),
        ("SOL/USDT", "LAYER1"),
        ("ADA/USDT", "LAYER1"),
        ("DOT/USDT", "LAYER1"),
        
        # DeFi
        ("UNI/USDT", "DEFI"),
        ("AAVE/USDT", "DEFI"),
        ("SUSHI/USDT", "DEFI"),
        ("CRV/USDT", "DEFI"),
        
        # Meme coins
        ("DOGE/USDT", "MEME"),
        ("SHIB/USDT", "MEME"),
        ("PEPE/USDT", "MEME"),
        
        # Stablecoins
        ("USDT/USD", "STABLECOIN"),
        ("USDC/USDT", "STABLECOIN"),
        ("DAI/USDT", "STABLECOIN"),
        
        # AI tokens
        ("FET/USDT", "AI"),
        ("AGIX/USDT", "AI"),
        ("OCEAN/USDT", "AI"),
        
        # Gaming
        ("AXS/USDT", "GAMEFI"),
        ("SAND/USDT", "GAMEFI"),
        ("MANA/USDT", "GAMEFI"),
    ]
    
    correct_predictions = 0
    total_tests = len(test_coins)
    
    print(f"🧪 Testing {total_tests} known coins...")
    
    for symbol, expected_category in test_coins:
        predicted_category = categorizer.get_coin_category(symbol)
        is_correct = predicted_category == expected_category
        
        if is_correct:
            correct_predictions += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"  {status} {symbol:<12} → Expected: {expected_category:<15} | Got: {predicted_category:<15}")
    
    accuracy = (correct_predictions / total_tests) * 100
    print(f"\n📊 Results:")
    print(f"  ✅ Correct: {correct_predictions}/{total_tests}")
    print(f"  📊 Accuracy: {accuracy:.1f}%")
    
    return accuracy >= 80  # 80% accuracy threshold

def test_pattern_based_detection(categorizer):
    """Test pattern-based detection"""
    print("\n🔍 TESTING PATTERN-BASED DETECTION")
    print("=" * 60)
    
    if not categorizer:
        print("❌ No categorizer available")
        return False
    
    # Test cases for pattern detection
    pattern_tests = [
        # AI patterns
        ("CHATGPT/USDT", "AI"),
        ("NEURALAI/USDT", "AI"),
        ("ROBOTCOIN/USDT", "AI"),
        
        # Meme patterns
        ("BABYDOGE/USDT", "MEME"),
        ("SAFEMOON/USDT", "MEME"),
        ("ELONMUSK/USDT", "MEME"),
        
        # DeFi patterns
        ("NEWSWAP/USDT", "DEFI"),
        ("YIELDTOKEN/USDT", "DEFI"),
        ("FARMCOIN/USDT", "DEFI"),
        
        # Gaming patterns
        ("GAMETOKEN/USDT", "GAMEFI"),
        ("PLAYCOIN/USDT", "GAMEFI"),
        ("METAVERSE/USDT", "GAMEFI"),
        
        # Stablecoin patterns
        ("NEWUSD/USDT", "STABLECOIN"),
        ("STABLECOIN/USDT", "STABLECOIN"),
    ]
    
    correct_patterns = 0
    total_pattern_tests = len(pattern_tests)
    
    print(f"🧪 Testing {total_pattern_tests} pattern-based detections...")
    
    for symbol, expected_category in pattern_tests:
        predicted_category = categorizer.get_coin_category(symbol)
        is_correct = predicted_category == expected_category
        
        if is_correct:
            correct_patterns += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"  {status} {symbol:<15} → Expected: {expected_category:<15} | Got: {predicted_category:<15}")
    
    pattern_accuracy = (correct_patterns / total_pattern_tests) * 100
    print(f"\n📊 Pattern Detection Results:")
    print(f"  ✅ Correct: {correct_patterns}/{total_pattern_tests}")
    print(f"  📊 Accuracy: {pattern_accuracy:.1f}%")
    
    return pattern_accuracy >= 60  # 60% accuracy threshold for patterns

def test_cache_functionality(categorizer):
    """Test caching functionality"""
    print("\n💾 TESTING CACHE FUNCTIONALITY")
    print("=" * 60)
    
    if not categorizer:
        print("❌ No categorizer available")
        return False
    
    test_symbol = "BTC/USDT"
    
    # First call - should cache result
    start_time = time.time()
    category1 = categorizer.get_coin_category(test_symbol)
    first_call_time = time.time() - start_time
    
    # Second call - should use cache
    start_time = time.time()
    category2 = categorizer.get_coin_category(test_symbol)
    second_call_time = time.time() - start_time
    
    # Check consistency
    is_consistent = category1 == category2
    is_faster = second_call_time < first_call_time
    
    print(f"🧪 Cache test for {test_symbol}:")
    print(f"  📊 First call: {category1} ({first_call_time:.4f}s)")
    print(f"  📊 Second call: {category2} ({second_call_time:.4f}s)")
    print(f"  ✅ Consistent: {'YES' if is_consistent else 'NO'}")
    print(f"  ⚡ Faster: {'YES' if is_faster else 'NO'}")
    
    # Check cache size
    cache_size = len(categorizer.category_cache)
    print(f"  💾 Cache size: {cache_size} entries")
    
    return is_consistent and cache_size > 0

def test_bulk_categorization(categorizer):
    """Test bulk categorization"""
    print("\n📦 TESTING BULK CATEGORIZATION")
    print("=" * 60)
    
    if not categorizer:
        print("❌ No categorizer available")
        return False
    
    # Test symbols
    test_symbols = [
        "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
        "DOGE/USDT", "SHIB/USDT", "UNI/USDT", "AAVE/USDT", "LINK/USDT"
    ]
    
    print(f"🧪 Bulk categorizing {len(test_symbols)} symbols...")
    
    start_time = time.time()
    results = categorizer.bulk_categorize(test_symbols)
    bulk_time = time.time() - start_time
    
    print(f"📊 Bulk categorization results:")
    for symbol, category in results.items():
        print(f"  📊 {symbol:<12} → {category}")
    
    print(f"\n⏱️ Performance:")
    print(f"  📊 Total time: {bulk_time:.2f}s")
    print(f"  📊 Average per coin: {bulk_time/len(test_symbols):.3f}s")
    
    # Check if all symbols were categorized
    all_categorized = len(results) == len(test_symbols)
    no_errors = all(category != "ERROR" for category in results.values())
    
    return all_categorized and no_errors

def test_category_stats(categorizer):
    """Test category statistics"""
    print("\n📊 TESTING CATEGORY STATISTICS")
    print("=" * 60)
    
    if not categorizer:
        print("❌ No categorizer available")
        return False
    
    # Get all categories
    all_categories = categorizer.get_all_categories()
    print(f"📋 Available categories ({len(all_categories)}):")
    for code, description in all_categories.items():
        print(f"  📊 {code:<20} → {description}")
    
    # Get category stats
    stats = categorizer.get_category_stats()
    print(f"\n📊 Current categorization stats:")
    for category, count in sorted(stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  📊 {category:<20}: {count} coins")
    
    # Get update stats
    update_stats = categorizer.get_update_stats()
    print(f"\n🔄 Update statistics:")
    for key, value in update_stats.items():
        print(f"  📊 {key}: {value}")
    
    return len(all_categories) > 0

def cleanup_test_files():
    """Cleanup test files"""
    try:
        test_files = [
            "test_coin_categories_cache.json",
            "known_coins_db.json"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)
                print(f"🧹 Cleaned up: {file}")
    except Exception as e:
        print(f"⚠️ Error cleaning up: {e}")

def main():
    """Main test function"""
    print("🧪 COIN CATEGORIZER FUNCTIONALITY TEST")
    print("=" * 70)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Basic functionality
    categorizer = test_coin_categorizer_basic()
    
    if not categorizer:
        print("❌ Cannot proceed without working categorizer")
        return
    
    # Test 2: Known coins categorization
    known_coins_result = test_known_coins_categorization(categorizer)
    
    # Test 3: Pattern-based detection
    pattern_result = test_pattern_based_detection(categorizer)
    
    # Test 4: Cache functionality
    cache_result = test_cache_functionality(categorizer)
    
    # Test 5: Bulk categorization
    bulk_result = test_bulk_categorization(categorizer)
    
    # Test 6: Category statistics
    stats_result = test_category_stats(categorizer)
    
    # Cleanup
    cleanup_test_files()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 COIN CATEGORIZER TEST RESULTS")
    print("=" * 70)
    
    tests = [
        ("Known Coins Categorization", known_coins_result),
        ("Pattern-Based Detection", pattern_result),
        ("Cache Functionality", cache_result),
        ("Bulk Categorization", bulk_result),
        ("Category Statistics", stats_result)
    ]
    
    all_passed = True
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL COIN CATEGORIZER TESTS PASSED!")
        print("✅ CoinCategorizer is working correctly")
        print("✅ Known coins categorization accurate")
        print("✅ Pattern detection functional")
        print("✅ Caching system operational")
        print("✅ Bulk operations working")
        print("✅ Statistics and reporting functional")
        
        print(f"\n🔧 Ready for integration with main bot:")
        print(f"  • Import CoinCategorizer in main_bot.py")
        print(f"  • Initialize in TradingBot.__init__()")
        print(f"  • Use categorizer.get_coin_category() for each coin")
        print(f"  • Replace 'UNKNOWN' with actual categories")
    else:
        print("❌ SOME TESTS FAILED")
        print("CoinCategorizer may need attention before integration")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
