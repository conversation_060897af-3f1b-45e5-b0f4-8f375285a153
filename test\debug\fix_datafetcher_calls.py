import re

def fix_datafetcher_method_calls():
    """Fix all incorrect DataFetcher method calls in main_bot.py"""
    
    # Read the file
    with open('main_bot.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Dictionary of incorrect -> correct method names
    fixes = {
        r'\.get_ohlcv_data\(': '.fetch_ohlcv(',
        r'\.get_data\(': '.fetch_ohlcv(',
        r'\.fetch_data\(': '.fetch_ohlcv(',
        r'\.get_historical_ohlcv\(': '.fetch_ohlcv(',
        r'\.get_candles\(': '.fetch_ohlcv(',
    }
    
    # Apply fixes
    for wrong_pattern, correct_replacement in fixes.items():
        content = re.sub(wrong_pattern, correct_replacement, content)
        
    # Write back the fixed content
    with open('main_bot.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed all DataFetcher method calls!")

if __name__ == "__main__":
    fix_datafetcher_method_calls()