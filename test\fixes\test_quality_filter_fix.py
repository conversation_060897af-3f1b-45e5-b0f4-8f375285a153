#!/usr/bin/env python3
"""
🧪 Test Quality Filter Fix - Verify all analyses run even when some don't meet threshold
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_quality_filter_fix():
    """🎯 Test that all analyses run even when quality filter is enabled."""
    print("🧪 Testing Quality Filter Fix...")
    print("=" * 70)
    
    # Check current configuration
    print("📊 Current Quality Filter Configuration:")
    
    filter_enabled = bool(int(os.getenv("SIGNAL_QUALITY_FILTER_ENABLED", "1")))
    min_threshold = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.70"))
    fibonacci_threshold = float(os.getenv("FIBONACCI_MIN_CONFIDENCE", "0.70"))
    point_figure_threshold = float(os.getenv("POINT_FIGURE_MIN_CONFIDENCE", "0.70"))
    volume_profile_threshold = float(os.getenv("VOLUME_PROFILE_MIN_CONFIDENCE", "0.70"))
    orderbook_threshold = float(os.getenv("ORDERBOOK_MIN_CONFIDENCE", "0.70"))
    fourier_threshold = float(os.getenv("FOURIER_MIN_CONFIDENCE", "0.70"))
    
    print(f"  🎯 Filter Enabled: {'✅ YES' if filter_enabled else '❌ NO'}")
    print(f"  📊 Global Threshold: {min_threshold:.0%}")
    print(f"  🌀 Fibonacci: {fibonacci_threshold:.0%}")
    print(f"  📈 Point & Figure: {point_figure_threshold:.0%}")
    print(f"  📊 Volume Profile: {volume_profile_threshold:.0%}")
    print(f"  📋 Orderbook: {orderbook_threshold:.0%}")
    print(f"  🌊 Fourier: {fourier_threshold:.0%}")
    
    print("\n🔍 Expected Behavior After Fix:")
    if filter_enabled:
        print(f"  ✅ ALL analyses should RUN regardless of individual confidence")
        print(f"  ✅ Only REPORTING should be filtered based on confidence")
        print(f"  ✅ Consensus analysis should ALWAYS run (uses all data)")
        print(f"  ✅ AI analysis should ALWAYS run (uses all features)")
        print(f"  ❌ Only low-confidence REPORTS should be skipped")
    else:
        print(f"  ✅ ALL analyses and reports should run")
    
    print("\n🧪 Analysis Flow Test:")
    
    # Test scenarios with different confidence levels
    test_scenarios = [
        {
            "name": "High Confidence Scenario",
            "fibonacci_conf": 0.85,
            "point_figure_conf": 0.78,
            "volume_profile_conf": 0.82,
            "fourier_conf": 0.75,
            "ai_conf": 0.88,
            "expected_reports": "All reports should be sent"
        },
        {
            "name": "Mixed Confidence Scenario", 
            "fibonacci_conf": 0.45,  # Below threshold
            "point_figure_conf": 0.78,  # Above threshold
            "volume_profile_conf": 0.55,  # Below threshold
            "fourier_conf": 0.75,  # Above threshold
            "ai_conf": 0.65,  # Below threshold
            "expected_reports": "Only P&F and Fourier reports should be sent"
        },
        {
            "name": "Low Confidence Scenario",
            "fibonacci_conf": 0.35,
            "point_figure_conf": 0.42,
            "volume_profile_conf": 0.38,
            "fourier_conf": 0.45,
            "ai_conf": 0.55,
            "expected_reports": "No individual reports, but consensus should still run"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Test {i}: {scenario['name']} ---")
        print(f"📊 Confidence Levels:")
        print(f"  🌀 Fibonacci: {scenario['fibonacci_conf']:.0%}")
        print(f"  📈 Point & Figure: {scenario['point_figure_conf']:.0%}")
        print(f"  📊 Volume Profile: {scenario['volume_profile_conf']:.0%}")
        print(f"  🌊 Fourier: {scenario['fourier_conf']:.0%}")
        print(f"  🤖 AI: {scenario['ai_conf']:.0%}")
        
        print(f"🎯 Expected Behavior:")
        print(f"  📈 Analysis Execution:")
        print(f"    ✅ Fibonacci analysis: SHOULD RUN")
        print(f"    ✅ Point & Figure analysis: SHOULD RUN")
        print(f"    ✅ Volume Profile analysis: SHOULD RUN")
        print(f"    ✅ Fourier analysis: SHOULD RUN")
        print(f"    ✅ AI analysis: SHOULD RUN")
        print(f"    ✅ Consensus analysis: SHOULD RUN")
        
        print(f"  📤 Report Sending:")
        if filter_enabled:
            fib_send = "✅ SEND" if scenario['fibonacci_conf'] >= fibonacci_threshold else "❌ SKIP"
            pf_send = "✅ SEND" if scenario['point_figure_conf'] >= point_figure_threshold else "❌ SKIP"
            vp_send = "✅ SEND" if scenario['volume_profile_conf'] >= volume_profile_threshold else "❌ SKIP"
            fourier_send = "✅ SEND" if scenario['fourier_conf'] >= fourier_threshold else "❌ SKIP"
            ai_send = "✅ SEND" if scenario['ai_conf'] >= min_threshold else "❌ SKIP"
            
            print(f"    🌀 Fibonacci report: {fib_send}")
            print(f"    📈 Point & Figure report: {pf_send}")
            print(f"    📊 Volume Profile report: {vp_send}")
            print(f"    🌊 Fourier report: {fourier_send}")
            print(f"    🤖 AI report: {ai_send}")
            print(f"    🎯 Consensus report: ✅ SHOULD ALWAYS RUN")
        else:
            print(f"    ✅ ALL reports should be sent (filter disabled)")
        
        print(f"  📝 Expected Result: {scenario['expected_reports']}")
    
    print("\n🔧 Key Fixes Applied:")
    print("  ✅ Removed 'continue' statements that skipped entire coins")
    print("  ✅ AI analysis runs but only sends reports if confidence ≥ 70%")
    print("  ✅ Fibonacci analysis runs but only sends reports if confidence ≥ 70%")
    print("  ✅ All other analyses continue to run regardless of individual results")
    print("  ✅ Consensus analysis always runs with all available data")
    print("  ✅ Quality filter only affects REPORTING, not ANALYSIS execution")
    
    print("\n📊 What to Look For in Bot Logs:")
    print("  ✅ '🌀 Running ENHANCED Fibonacci analysis...' - Should ALWAYS appear")
    print("  ✅ '🌊 Running ENHANCED Fourier analysis...' - Should ALWAYS appear")
    print("  ✅ '📊 Running ENHANCED Volume Profile analysis...' - Should ALWAYS appear")
    print("  ✅ '📈 Running ENHANCED Point & Figure analysis...' - Should ALWAYS appear")
    print("  ✅ '🤖 FORCING AI Analysis...' - Should ALWAYS appear")
    print("  ✅ '🎯 Running ENHANCED consensus analysis...' - Should ALWAYS appear")
    print("  ❌ 'Quality Check:' messages followed by 'SKIPPING' for low confidence")
    print("  ✅ 'Quality Check:' messages followed by 'SENDING' for high confidence")
    
    print("\n🎯 Critical Success Criteria:")
    print("  1. ✅ ALL analysis methods should execute for every coin")
    print("  2. ✅ Consensus analysis should NEVER be skipped")
    print("  3. ✅ AI analysis should NEVER be skipped")
    print("  4. ❌ Only REPORTS should be filtered based on confidence")
    print("  5. ✅ Bot should process ALL coins in the list")
    print("  6. ✅ No 'continue' statements should skip entire coins")
    
    print("\n🚨 Red Flags to Watch For:")
    print("  ❌ 'Skipping AI analysis due to quality filter' followed by coin skip")
    print("  ❌ 'Fibonacci signal below quality threshold' followed by coin skip")
    print("  ❌ Consensus analysis not running for coins with low individual confidence")
    print("  ❌ Fewer coins being processed than expected")
    print("  ❌ Analysis methods not executing due to quality filter")
    
    print("\n🔍 Debugging Commands:")
    print("  📊 Check if all analyses run:")
    print("    grep 'Running ENHANCED' bot_logs.txt")
    print("  📤 Check quality filter decisions:")
    print("    grep 'Quality Check:' bot_logs.txt")
    print("  ⏭️ Check for skipped analyses:")
    print("    grep 'SKIPPING\\|skipped due to' bot_logs.txt")
    print("  🎯 Check consensus execution:")
    print("    grep 'consensus analysis' bot_logs.txt")
    
    print("\n" + "=" * 70)
    print("✅ Quality Filter Fix Test Complete!")
    print("Now run the bot and verify that:")
    print("1. All analyses execute for every coin")
    print("2. Only reports are filtered based on confidence")
    print("3. Consensus and AI always run with all available data")

if __name__ == "__main__":
    test_quality_filter_fix()
