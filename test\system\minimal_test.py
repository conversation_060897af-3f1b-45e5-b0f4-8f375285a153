#!/usr/bin/env python3
"""
🔍 Minimal Test - Test basic photo sending with minimal setup
"""

import os
import requests
from datetime import datetime

def minimal_test():
    """🔍 Minimal test."""
    print(f"🔍 MINIMAL TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token:
        print(f"❌ TELEGRAM_BOT_TOKEN not set")
        return False
        
    if not chat_id:
        print(f"❌ TELEGRAM_CHAT_ID not set")
        return False
    
    print(f"✅ Environment variables OK")
    print(f"🎯 Chat ID: {chat_id}")
    print(f"🔑 Bot token length: {len(bot_token)}")
    
    # Create a simple test image
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        print(f"📊 Creating test image...")
        fig, ax = plt.subplots(figsize=(8, 6))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, 'b-', linewidth=2)
        ax.set_title('Minimal Test Chart')
        ax.grid(True)
        
        test_image_path = "minimal_test.png"
        plt.savefig(test_image_path, dpi=100, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Test image created: {test_image_path}")
        
        # Check file
        if not os.path.exists(test_image_path):
            print(f"❌ Test image file not found")
            return False
        
        file_size = os.path.getsize(test_image_path)
        print(f"📊 File size: {file_size} bytes ({file_size/1024:.1f} KB)")
        
        # Test Telegram API directly
        print(f"📤 Testing Telegram API directly...")
        
        url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"
        
        # Simple caption
        caption = f"🔍 Minimal Test - {datetime.now().strftime('%H:%M:%S')}"
        
        print(f"📝 Caption: {caption}")
        print(f"🌐 URL: {url}")
        
        # Prepare data
        with open(test_image_path, 'rb') as photo_file:
            files = {
                'photo': ('minimal_test.png', photo_file, 'image/png')
            }
            
            data = {
                'chat_id': chat_id,
                'caption': caption
            }
            
            print(f"📤 Sending request...")
            response = requests.post(url, data=data, files=files, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok', False):
                print(f"✅ SUCCESS! Photo sent successfully")
                print(f"📱 Check your Telegram for the test image")
                success = True
            else:
                print(f"❌ API error: {result}")
                success = False
        else:
            print(f"❌ HTTP error {response.status_code}")
            try:
                error_data = response.json()
                print(f"❌ Error details: {error_data}")
            except:
                print(f"❌ Error text: {response.text}")
            success = False
        
        # Cleanup
        try:
            os.remove(test_image_path)
            print(f"🧹 Test image cleaned up")
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = minimal_test()
    if success:
        print(f"\n🎉 MINIMAL TEST PASSED!")
    else:
        print(f"\n💥 MINIMAL TEST FAILED!")
