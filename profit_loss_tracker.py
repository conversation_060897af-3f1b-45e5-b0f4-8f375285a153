#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📊 PROFIT & LOSS TRACKING SYSTEM
=====================================
Comprehensive P&L tracking for trading signals with detailed analytics.

Features:
- Real-time P&L tracking per signal type
- Win/Loss ratio analysis
- Risk-reward ratio calculations
- Performance metrics by algorithm
- Historical performance analysis
- Export capabilities for detailed reports

Author: Trading Bot System
Version: 1.0.0
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import sqlite3
import threading

# ✅ FIX: Handle pandas import gracefully
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ Pandas not available - some advanced statistics will be disabled")

@dataclass
class TradeRecord:
    """📈 Enhanced trade record structure for comprehensive tracking."""
    trade_id: str
    coin: str
    signal_type: str  # 'consensus', 'ai_analysis', 'fibonacci', 'volume_profile', 'orderbook', etc.
    signal_source: str  # Which algorithm generated the signal
    entry_signal: str  # 'BUY' or 'SELL'
    entry_price: float
    entry_time: datetime
    entry_confidence: float

    # Exit information (filled when trade closes)
    exit_price: Optional[float] = None
    exit_time: Optional[datetime] = None
    exit_reason: Optional[str] = None  # 'TP', 'SL', 'MANUAL', 'TIMEOUT'

    # P&L calculations
    profit_loss_pct: Optional[float] = None
    profit_loss_amount: Optional[float] = None

    # Risk management
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None
    risk_reward_ratio: Optional[float] = None

    # ✅ ENHANCED: Algorithm-specific data
    algorithm_data: Optional[Dict[str, Any]] = None  # Store algorithm-specific information
    consensus_score: Optional[float] = None  # For consensus signals
    ai_models_used: Optional[List[str]] = None  # For AI analysis
    fibonacci_levels: Optional[Dict[str, float]] = None  # For Fibonacci analysis
    volume_profile_data: Optional[Dict[str, Any]] = None  # For Volume Profile
    orderbook_data: Optional[Dict[str, Any]] = None  # For Orderbook analysis

    # ✅ ENHANCED: Tracking integration
    ultra_tracker_id: Optional[str] = None  # Link to Ultra Tracker
    tracking_status: str = 'ACTIVE'  # 'ACTIVE', 'MONITORED', 'COMPLETED'

    # Additional metadata
    market_conditions: Optional[str] = None
    volume_at_entry: Optional[float] = None
    notes: Optional[str] = None

    # Status tracking
    status: str = 'OPEN'  # 'OPEN', 'CLOSED', 'CANCELLED'

class ProfitLossTracker:
    """📊 Comprehensive Profit & Loss tracking system."""
    
    def __init__(self, db_path: str = "trading_performance.db"):
        """Initialize P&L tracker with SQLite database."""
        self.db_path = db_path
        self.lock = threading.Lock()
        self.active_trades: Dict[str, TradeRecord] = {}
        
        # Performance metrics cache
        self.performance_cache = {}
        self.cache_expiry = datetime.now()
        
        # Initialize database
        self._init_database()
        
        # Load active trades
        self._load_active_trades()
        
        print(f"📊 P&L Tracker initialized with database: {db_path}")
    
    def _init_database(self):
        """🗄️ Initialize SQLite database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create enhanced trades table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trades (
                        trade_id TEXT PRIMARY KEY,
                        coin TEXT NOT NULL,
                        signal_type TEXT NOT NULL,
                        signal_source TEXT NOT NULL,
                        entry_signal TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        entry_time TEXT NOT NULL,
                        entry_confidence REAL NOT NULL,
                        exit_price REAL,
                        exit_time TEXT,
                        exit_reason TEXT,
                        profit_loss_pct REAL,
                        profit_loss_amount REAL,
                        stop_loss_price REAL,
                        take_profit_price REAL,
                        risk_reward_ratio REAL,
                        algorithm_data TEXT,
                        consensus_score REAL,
                        ai_models_used TEXT,
                        fibonacci_levels TEXT,
                        volume_profile_data TEXT,
                        orderbook_data TEXT,
                        ultra_tracker_id TEXT,
                        tracking_status TEXT DEFAULT 'ACTIVE',
                        market_conditions TEXT,
                        volume_at_entry REAL,
                        notes TEXT,
                        status TEXT DEFAULT 'OPEN',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create performance summary table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_summary (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        signal_type TEXT NOT NULL,
                        signal_source TEXT NOT NULL,
                        date TEXT NOT NULL,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        losing_trades INTEGER DEFAULT 0,
                        total_profit_loss REAL DEFAULT 0.0,
                        avg_profit_loss REAL DEFAULT 0.0,
                        win_rate REAL DEFAULT 0.0,
                        avg_risk_reward REAL DEFAULT 0.0,
                        max_drawdown REAL DEFAULT 0.0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(signal_type, signal_source, date)
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_coin ON trades(coin)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_signal_type ON trades(signal_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_signal_source ON trades(signal_source)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_entry_time ON trades(entry_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status)')
                
                conn.commit()
                print("✅ Database tables initialized successfully")
                
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            raise
    
    def _load_active_trades(self):
        """📂 Load active trades from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM trades WHERE status = 'OPEN'")
                
                for row in cursor.fetchall():
                    trade_record = self._row_to_trade_record(row)
                    self.active_trades[trade_record.trade_id] = trade_record
                
                print(f"📂 Loaded {len(self.active_trades)} active trades")
                
        except Exception as e:
            print(f"❌ Error loading active trades: {e}")
    
    def _row_to_trade_record(self, row) -> TradeRecord:
        """🔄 Convert database row to TradeRecord object."""
        return TradeRecord(
            trade_id=row[0],
            coin=row[1],
            signal_type=row[2],
            signal_source=row[3],
            entry_signal=row[4],
            entry_price=row[5],
            entry_time=datetime.fromisoformat(row[6]),
            entry_confidence=row[7],
            exit_price=row[8],
            exit_time=datetime.fromisoformat(row[9]) if row[9] else None,
            exit_reason=row[10],
            profit_loss_pct=row[11],
            profit_loss_amount=row[12],
            stop_loss_price=row[13],
            take_profit_price=row[14],
            risk_reward_ratio=row[15],
            market_conditions=row[16],
            volume_at_entry=row[17],
            notes=row[18],
            status=row[19]
        )
    
    def record_signal_entry(self, 
                          coin: str,
                          signal_type: str,
                          signal_source: str,
                          entry_signal: str,
                          entry_price: float,
                          entry_confidence: float,
                          stop_loss_price: Optional[float] = None,
                          take_profit_price: Optional[float] = None,
                          market_conditions: Optional[str] = None,
                          volume_at_entry: Optional[float] = None,
                          notes: Optional[str] = None) -> str:
        """📈 Record a new signal entry for tracking."""
        
        with self.lock:
            # Generate unique trade ID
            trade_id = f"{signal_type}_{coin}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Calculate risk-reward ratio if both SL and TP are provided
            risk_reward_ratio = None
            if stop_loss_price and take_profit_price:
                if entry_signal == 'BUY':
                    risk = abs(entry_price - stop_loss_price)
                    reward = abs(take_profit_price - entry_price)
                else:  # SELL
                    risk = abs(stop_loss_price - entry_price)
                    reward = abs(entry_price - take_profit_price)
                
                risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # Create trade record
            trade_record = TradeRecord(
                trade_id=trade_id,
                coin=coin,
                signal_type=signal_type,
                signal_source=signal_source,
                entry_signal=entry_signal,
                entry_price=entry_price,
                entry_time=datetime.now(),
                entry_confidence=entry_confidence,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                risk_reward_ratio=risk_reward_ratio,
                market_conditions=market_conditions,
                volume_at_entry=volume_at_entry,
                notes=notes,
                status='OPEN'
            )
            
            # Save to database
            self._save_trade_to_db(trade_record)
            
            # Add to active trades
            self.active_trades[trade_id] = trade_record
            
            print(f"📈 Recorded new signal entry: {trade_id}")
            print(f"   🪙 {coin} | 📊 {signal_type} | 🎯 {entry_signal} | 💰 ${entry_price:.8f}")
            
            return trade_id
    
    def record_signal_exit(self,
                         trade_id: str,
                         exit_price: float,
                         exit_reason: str,
                         notes: Optional[str] = None) -> bool:
        """📉 Record signal exit and calculate P&L."""
        
        with self.lock:
            if trade_id not in self.active_trades:
                print(f"❌ Trade ID not found in active trades: {trade_id}")
                return False
            
            trade = self.active_trades[trade_id]
            
            # Calculate P&L
            if trade.entry_signal == 'BUY':
                profit_loss_pct = ((exit_price - trade.entry_price) / trade.entry_price) * 100
            else:  # SELL
                profit_loss_pct = ((trade.entry_price - exit_price) / trade.entry_price) * 100
            
            # Assume $1000 position size for amount calculation (can be made configurable)
            position_size = 1000
            profit_loss_amount = (profit_loss_pct / 100) * position_size
            
            # Update trade record
            trade.exit_price = exit_price
            trade.exit_time = datetime.now()
            trade.exit_reason = exit_reason
            trade.profit_loss_pct = profit_loss_pct
            trade.profit_loss_amount = profit_loss_amount
            trade.status = 'CLOSED'
            
            if notes:
                trade.notes = f"{trade.notes or ''} | Exit: {notes}".strip(' |')
            
            # Update in database
            self._update_trade_in_db(trade)
            
            # Remove from active trades
            del self.active_trades[trade_id]
            
            # Update performance summary
            self._update_performance_summary(trade)
            
            print(f"📉 Recorded signal exit: {trade_id}")
            print(f"   💰 P&L: {profit_loss_pct:.2f}% (${profit_loss_amount:.2f})")
            print(f"   🎯 Exit: {exit_reason} at ${exit_price:.8f}")
            
            return True

    def _save_trade_to_db(self, trade: TradeRecord):
        """💾 Save trade record to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO trades (
                        trade_id, coin, signal_type, signal_source, entry_signal,
                        entry_price, entry_time, entry_confidence, exit_price, exit_time,
                        exit_reason, profit_loss_pct, profit_loss_amount, stop_loss_price,
                        take_profit_price, risk_reward_ratio, market_conditions,
                        volume_at_entry, notes, status, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    trade.trade_id, trade.coin, trade.signal_type, trade.signal_source,
                    trade.entry_signal, trade.entry_price, trade.entry_time.isoformat(),
                    trade.entry_confidence, trade.exit_price,
                    trade.exit_time.isoformat() if trade.exit_time else None,
                    trade.exit_reason, trade.profit_loss_pct, trade.profit_loss_amount,
                    trade.stop_loss_price, trade.take_profit_price, trade.risk_reward_ratio,
                    trade.market_conditions, trade.volume_at_entry, trade.notes,
                    trade.status, datetime.now().isoformat()
                ))
                conn.commit()
        except Exception as e:
            print(f"❌ Error saving trade to database: {e}")

    def _update_trade_in_db(self, trade: TradeRecord):
        """🔄 Update existing trade record in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE trades SET
                        exit_price = ?, exit_time = ?, exit_reason = ?,
                        profit_loss_pct = ?, profit_loss_amount = ?, notes = ?,
                        status = ?, updated_at = ?
                    WHERE trade_id = ?
                ''', (
                    trade.exit_price, trade.exit_time.isoformat() if trade.exit_time else None,
                    trade.exit_reason, trade.profit_loss_pct, trade.profit_loss_amount,
                    trade.notes, trade.status, datetime.now().isoformat(), trade.trade_id
                ))
                conn.commit()
        except Exception as e:
            print(f"❌ Error updating trade in database: {e}")

    def _update_performance_summary(self, trade: TradeRecord):
        """📊 Update daily performance summary."""
        try:
            date_str = trade.entry_time.strftime('%Y-%m-%d')

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get existing summary
                cursor.execute('''
                    SELECT total_trades, winning_trades, losing_trades, total_profit_loss
                    FROM performance_summary
                    WHERE signal_type = ? AND signal_source = ? AND date = ?
                ''', (trade.signal_type, trade.signal_source, date_str))

                result = cursor.fetchone()

                if result:
                    total_trades, winning_trades, losing_trades, total_profit_loss = result
                    total_trades += 1
                    total_profit_loss += trade.profit_loss_pct or 0

                    if trade.profit_loss_pct and trade.profit_loss_pct > 0:
                        winning_trades += 1
                    elif trade.profit_loss_pct and trade.profit_loss_pct < 0:
                        losing_trades += 1
                else:
                    total_trades = 1
                    total_profit_loss = trade.profit_loss_pct or 0
                    winning_trades = 1 if trade.profit_loss_pct and trade.profit_loss_pct > 0 else 0
                    losing_trades = 1 if trade.profit_loss_pct and trade.profit_loss_pct < 0 else 0

                # Calculate metrics
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                avg_profit_loss = total_profit_loss / total_trades if total_trades > 0 else 0

                # Insert or update summary
                cursor.execute('''
                    INSERT OR REPLACE INTO performance_summary (
                        signal_type, signal_source, date, total_trades, winning_trades,
                        losing_trades, total_profit_loss, avg_profit_loss, win_rate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    trade.signal_type, trade.signal_source, date_str, total_trades,
                    winning_trades, losing_trades, total_profit_loss, avg_profit_loss, win_rate
                ))

                conn.commit()

        except Exception as e:
            print(f"❌ Error updating performance summary: {e}")

    def get_performance_stats(self,
                            signal_type: Optional[str] = None,
                            signal_source: Optional[str] = None,
                            days: int = 30) -> Dict[str, Any]:
        """📊 Get comprehensive performance statistics."""

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Build query conditions
                conditions = ["status = 'CLOSED'"]
                params = []

                if signal_type:
                    conditions.append("signal_type = ?")
                    params.append(signal_type)

                if signal_source:
                    conditions.append("signal_source = ?")
                    params.append(signal_source)

                # Add date filter
                start_date = (datetime.now() - timedelta(days=days)).isoformat()
                conditions.append("entry_time >= ?")
                params.append(start_date)

                where_clause = " AND ".join(conditions)

                # Get basic stats
                cursor.execute(f'''
                    SELECT
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN profit_loss_pct > 0 THEN 1 ELSE 0 END) as winning_trades,
                        SUM(CASE WHEN profit_loss_pct < 0 THEN 1 ELSE 0 END) as losing_trades,
                        AVG(profit_loss_pct) as avg_profit_loss,
                        SUM(profit_loss_pct) as total_profit_loss,
                        MAX(profit_loss_pct) as max_profit,
                        MIN(profit_loss_pct) as max_loss,
                        AVG(risk_reward_ratio) as avg_risk_reward
                    FROM trades
                    WHERE {where_clause}
                ''', params)

                stats = cursor.fetchone()

                if not stats or stats[0] == 0:
                    return {
                        'total_trades': 0,
                        'winning_trades': 0,
                        'losing_trades': 0,
                        'win_rate': 0.0,
                        'avg_profit_loss': 0.0,
                        'total_profit_loss': 0.0,
                        'max_profit': 0.0,
                        'max_loss': 0.0,
                        'avg_risk_reward': 0.0,
                        'profit_factor': 0.0,
                        'sharpe_ratio': 0.0
                    }

                total_trades, winning_trades, losing_trades, avg_profit_loss, total_profit_loss, max_profit, max_loss, avg_risk_reward = stats

                # Calculate additional metrics
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

                # Get winning and losing trade averages
                cursor.execute(f'''
                    SELECT AVG(profit_loss_pct) FROM trades
                    WHERE {where_clause} AND profit_loss_pct > 0
                ''', params)
                avg_win = cursor.fetchone()[0] or 0

                cursor.execute(f'''
                    SELECT AVG(profit_loss_pct) FROM trades
                    WHERE {where_clause} AND profit_loss_pct < 0
                ''', params)
                avg_loss = cursor.fetchone()[0] or 0

                # Calculate profit factor
                total_wins = winning_trades * avg_win if avg_win > 0 else 0
                total_losses = abs(losing_trades * avg_loss) if avg_loss < 0 else 0
                profit_factor = total_wins / total_losses if total_losses > 0 else float('inf') if total_wins > 0 else 0

                # Calculate Sharpe ratio (simplified)
                cursor.execute(f'''
                    SELECT profit_loss_pct FROM trades
                    WHERE {where_clause} AND profit_loss_pct IS NOT NULL
                ''', params)
                returns = [row[0] for row in cursor.fetchall()]

                if len(returns) > 1 and PANDAS_AVAILABLE:
                    returns_std = pd.Series(returns).std()
                    sharpe_ratio = (avg_profit_loss / returns_std) if returns_std > 0 else 0
                elif len(returns) > 1:
                    # ✅ FIX: Calculate standard deviation manually if pandas not available
                    mean_return = sum(returns) / len(returns)
                    variance = sum((x - mean_return) ** 2 for x in returns) / len(returns)
                    returns_std = variance ** 0.5
                    sharpe_ratio = (avg_profit_loss / returns_std) if returns_std > 0 else 0
                else:
                    sharpe_ratio = 0

                return {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'win_rate': round(win_rate, 2),
                    'avg_profit_loss': round(avg_profit_loss or 0, 2),
                    'total_profit_loss': round(total_profit_loss or 0, 2),
                    'max_profit': round(max_profit or 0, 2),
                    'max_loss': round(max_loss or 0, 2),
                    'avg_win': round(avg_win, 2),
                    'avg_loss': round(avg_loss, 2),
                    'avg_risk_reward': round(avg_risk_reward or 0, 2),
                    'profit_factor': round(profit_factor, 2),
                    'sharpe_ratio': round(sharpe_ratio, 2)
                }

        except Exception as e:
            print(f"❌ Error getting performance stats: {e}")
            return {}

    def get_signal_performance_breakdown(self, days: int = 30) -> Dict[str, Dict[str, Any]]:
        """📈 Get performance breakdown by signal type and source."""

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                start_date = (datetime.now() - timedelta(days=days)).isoformat()

                cursor.execute('''
                    SELECT
                        signal_type,
                        signal_source,
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN profit_loss_pct > 0 THEN 1 ELSE 0 END) as winning_trades,
                        AVG(profit_loss_pct) as avg_profit_loss,
                        SUM(profit_loss_pct) as total_profit_loss,
                        MAX(profit_loss_pct) as max_profit,
                        MIN(profit_loss_pct) as max_loss
                    FROM trades
                    WHERE status = 'CLOSED' AND entry_time >= ?
                    GROUP BY signal_type, signal_source
                    ORDER BY total_profit_loss DESC
                ''', (start_date,))

                breakdown = {}

                for row in cursor.fetchall():
                    signal_type, signal_source, total_trades, winning_trades, avg_profit_loss, total_profit_loss, max_profit, max_loss = row

                    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

                    key = f"{signal_type}_{signal_source}"
                    breakdown[key] = {
                        'signal_type': signal_type,
                        'signal_source': signal_source,
                        'total_trades': total_trades,
                        'winning_trades': winning_trades,
                        'win_rate': round(win_rate, 2),
                        'avg_profit_loss': round(avg_profit_loss or 0, 2),
                        'total_profit_loss': round(total_profit_loss or 0, 2),
                        'max_profit': round(max_profit or 0, 2),
                        'max_loss': round(max_loss or 0, 2)
                    }

                return breakdown

        except Exception as e:
            print(f"❌ Error getting signal performance breakdown: {e}")
            return {}

    def get_recent_trades(self, limit: int = 50) -> List[Dict[str, Any]]:
        """📋 Get recent trades with full details."""

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM trades
                    ORDER BY entry_time DESC
                    LIMIT ?
                ''', (limit,))

                trades = []
                for row in cursor.fetchall():
                    trade = self._row_to_trade_record(row)
                    # ✅ FIX: Convert datetime objects to strings for JSON serialization
                    trade_dict = asdict(trade)
                    if isinstance(trade_dict.get('entry_time'), datetime):
                        trade_dict['entry_time'] = trade_dict['entry_time'].isoformat()
                    if isinstance(trade_dict.get('exit_time'), datetime):
                        trade_dict['exit_time'] = trade_dict['exit_time'].isoformat()
                    trades.append(trade_dict)

                return trades

        except Exception as e:
            print(f"❌ Error getting recent trades: {e}")
            return []

    def generate_performance_report(self, days: int = 30) -> str:
        """📊 Generate comprehensive performance report."""

        try:
            # Get overall stats
            overall_stats = self.get_performance_stats(days=days)

            # Get signal breakdown
            signal_breakdown = self.get_signal_performance_breakdown(days=days)

            # Get recent trades
            recent_trades = self.get_recent_trades(limit=10)

            # Generate report
            report = f"""
📊 TRADING PERFORMANCE REPORT
{'='*50}
📅 Period: Last {days} days
⏰ Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 OVERALL PERFORMANCE
{'─'*30}
📈 Total Trades: {overall_stats.get('total_trades', 0)}
✅ Winning Trades: {overall_stats.get('winning_trades', 0)}
❌ Losing Trades: {overall_stats.get('losing_trades', 0)}
🎯 Win Rate: {overall_stats.get('win_rate', 0):.2f}%
💰 Total P&L: {overall_stats.get('total_profit_loss', 0):.2f}%
📊 Average P&L: {overall_stats.get('avg_profit_loss', 0):.2f}%
🚀 Max Profit: {overall_stats.get('max_profit', 0):.2f}%
📉 Max Loss: {overall_stats.get('max_loss', 0):.2f}%
⚖️ Avg Risk/Reward: {overall_stats.get('avg_risk_reward', 0):.2f}
💎 Profit Factor: {overall_stats.get('profit_factor', 0):.2f}
📈 Sharpe Ratio: {overall_stats.get('sharpe_ratio', 0):.2f}

🔍 PERFORMANCE BY SIGNAL TYPE
{'─'*40}"""

            # Add signal breakdown
            for key, stats in signal_breakdown.items():
                report += f"""
📊 {stats['signal_type']} - {stats['signal_source']}:
   📈 Trades: {stats['total_trades']} | Win Rate: {stats['win_rate']:.1f}%
   💰 Total P&L: {stats['total_profit_loss']:.2f}% | Avg: {stats['avg_profit_loss']:.2f}%
   🚀 Best: {stats['max_profit']:.2f}% | 📉 Worst: {stats['max_loss']:.2f}%"""

            # Add recent trades summary
            if recent_trades:
                report += f"""

📋 RECENT TRADES (Last 10)
{'─'*35}"""

                for i, trade in enumerate(recent_trades[:10], 1):
                    status_emoji = "✅" if trade.get('profit_loss_pct', 0) > 0 else "❌" if trade.get('profit_loss_pct', 0) < 0 else "⏳"
                    pnl = trade.get('profit_loss_pct', 0)

                    # ✅ FIX: Handle datetime object properly
                    entry_time_str = trade.get('entry_time', 'N/A')
                    if isinstance(entry_time_str, datetime):
                        entry_time_str = entry_time_str.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(entry_time_str, str) and len(entry_time_str) > 19:
                        entry_time_str = entry_time_str[:19]

                    report += f"""
{i:2d}. {status_emoji} {trade.get('coin', 'N/A')} | {trade.get('signal_type', 'N/A')} | {trade.get('entry_signal', 'N/A')}
    💰 Entry: ${trade.get('entry_price', 0):.8f} | P&L: {pnl:.2f}%
    📅 {entry_time_str} | Status: {trade.get('status', 'N/A')}"""

            report += f"""

📊 ACTIVE TRADES: {len(self.active_trades)}
{'─'*25}"""

            for trade_id, trade in list(self.active_trades.items())[:5]:
                report += f"""
🔄 {trade.coin} | {trade.signal_type} | {trade.entry_signal}
   💰 Entry: ${trade.entry_price:.8f} | Confidence: {trade.entry_confidence:.1%}
   📅 {trade.entry_time.strftime('%Y-%m-%d %H:%M:%S')}"""

            if len(self.active_trades) > 5:
                report += f"\n   ... and {len(self.active_trades) - 5} more active trades"

            report += f"""

{'='*50}
📊 Report generated by P&L Tracking System
"""

            return report

        except Exception as e:
            print(f"❌ Error generating performance report: {e}")
            return f"❌ Error generating report: {e}"

    def export_to_csv(self, filename: Optional[str] = None, days: int = 30) -> str:
        """📁 Export trading data to CSV file."""

        try:
            if not filename:
                filename = f"trading_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            with sqlite3.connect(self.db_path) as conn:
                start_date = (datetime.now() - timedelta(days=days)).isoformat()

                query = '''
                    SELECT * FROM trades
                    WHERE entry_time >= ?
                    ORDER BY entry_time DESC
                '''

                if PANDAS_AVAILABLE:
                    # Use pandas if available
                    df = pd.read_sql_query(query, conn, params=(start_date,))
                    df.to_csv(filename, index=False)
                else:
                    # ✅ FIX: Manual CSV export if pandas not available
                    import csv
                    cursor = conn.cursor()
                    cursor.execute(query, (start_date,))

                    # Get column names
                    columns = [description[0] for description in cursor.description]

                    # Write CSV file
                    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                        writer = csv.writer(csvfile)
                        writer.writerow(columns)  # Header
                        writer.writerows(cursor.fetchall())  # Data

                print(f"📁 Trading data exported to: {filename}")
                return filename

        except Exception as e:
            print(f"❌ Error exporting to CSV: {e}")
            return ""

    def get_active_trades_summary(self) -> Dict[str, Any]:
        """🔄 Get summary of currently active trades."""

        if not self.active_trades:
            return {
                'total_active': 0,
                'by_signal_type': {},
                'by_coin': {},
                'oldest_trade': None,
                'newest_trade': None
            }

        # Group by signal type
        by_signal_type = defaultdict(int)
        by_coin = defaultdict(int)

        oldest_time = None
        newest_time = None

        for trade in self.active_trades.values():
            by_signal_type[trade.signal_type] += 1
            by_coin[trade.coin] += 1

            if oldest_time is None or trade.entry_time < oldest_time:
                oldest_time = trade.entry_time

            if newest_time is None or trade.entry_time > newest_time:
                newest_time = trade.entry_time

        return {
            'total_active': len(self.active_trades),
            'by_signal_type': dict(by_signal_type),
            'by_coin': dict(by_coin),
            'oldest_trade': oldest_time.isoformat() if oldest_time else None,
            'newest_trade': newest_time.isoformat() if newest_time else None
        }

    def cleanup_old_data(self, days_to_keep: int = 90):
        """🧹 Clean up old trade data to keep database size manageable."""

        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).isoformat()

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Count records to be deleted
                cursor.execute("SELECT COUNT(*) FROM trades WHERE entry_time < ? AND status = 'CLOSED'", (cutoff_date,))
                count_to_delete = cursor.fetchone()[0]

                if count_to_delete > 0:
                    # Delete old closed trades
                    cursor.execute("DELETE FROM trades WHERE entry_time < ? AND status = 'CLOSED'", (cutoff_date,))

                    # Delete old performance summaries
                    cursor.execute("DELETE FROM performance_summary WHERE date < ?", (cutoff_date[:10],))

                    conn.commit()

                    print(f"🧹 Cleaned up {count_to_delete} old trade records")
                else:
                    print("🧹 No old records to clean up")

        except Exception as e:
            print(f"❌ Error cleaning up old data: {e}")


# ============================================================================
# 🧪 TESTING AND DEMO FUNCTIONS
# ============================================================================

def demo_profit_loss_tracker():
    """🧪 Demo function to test P&L tracker functionality."""

    print("🧪 Starting P&L Tracker Demo...")

    # Initialize tracker
    tracker = ProfitLossTracker("demo_trading_performance.db")

    # Simulate some trades
    demo_trades = [
        {
            'coin': 'BTC/USDT',
            'signal_type': 'consensus',
            'signal_source': 'enhanced_consensus_v4',
            'entry_signal': 'BUY',
            'entry_price': 45000.0,
            'entry_confidence': 0.85,
            'stop_loss_price': 43000.0,
            'take_profit_price': 48000.0,
            'exit_price': 47500.0,
            'exit_reason': 'TP'
        },
        {
            'coin': 'ETH/USDT',
            'signal_type': 'ai_prediction',
            'signal_source': 'ensemble_ai',
            'entry_signal': 'SELL',
            'entry_price': 3200.0,
            'entry_confidence': 0.75,
            'stop_loss_price': 3350.0,
            'take_profit_price': 3000.0,
            'exit_price': 3100.0,
            'exit_reason': 'TP'
        },
        {
            'coin': 'ADA/USDT',
            'signal_type': 'fibonacci',
            'signal_source': 'fibonacci_analyzer',
            'entry_signal': 'BUY',
            'entry_price': 0.5,
            'entry_confidence': 0.65,
            'stop_loss_price': 0.45,
            'take_profit_price': 0.6,
            'exit_price': 0.47,
            'exit_reason': 'SL'
        }
    ]

    # Record demo trades
    for trade_data in demo_trades:
        trade_id = tracker.record_signal_entry(
            coin=trade_data['coin'],
            signal_type=trade_data['signal_type'],
            signal_source=trade_data['signal_source'],
            entry_signal=trade_data['entry_signal'],
            entry_price=trade_data['entry_price'],
            entry_confidence=trade_data['entry_confidence'],
            stop_loss_price=trade_data['stop_loss_price'],
            take_profit_price=trade_data['take_profit_price']
        )

        # Simulate exit
        tracker.record_signal_exit(
            trade_id=trade_id,
            exit_price=trade_data['exit_price'],
            exit_reason=trade_data['exit_reason']
        )

    # Generate and print report
    report = tracker.generate_performance_report(days=1)
    print(report)

    # Export to CSV
    csv_file = tracker.export_to_csv("demo_trades.csv", days=1)
    print(f"\n📁 Demo data exported to: {csv_file}")

    print("\n🎉 P&L Tracker Demo completed!")


if __name__ == "__main__":
    demo_profit_loss_tracker()
