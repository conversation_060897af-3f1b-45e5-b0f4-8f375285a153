#!/usr/bin/env python3
"""
🤖 Bot Stability Test - Chạy bot và monitor lỗi trong thời gian dài
"""

import os
import sys
import time
import threading
import requests
import subprocess
from datetime import datetime, timedelta
from collections import defaultdict

class BotStabilityTester:
    """🤖 Test stability của bot trong thời gian dài."""
    
    def __init__(self):
        self.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        # Test settings
        self.test_duration_minutes = 30  # Test for 30 minutes
        self.report_interval_minutes = 5  # Report every 5 minutes
        
        # Statistics
        self.start_time = datetime.now()
        self.errors_detected = []
        self.analysis_count = defaultdict(int)
        self.bot_process = None
        
        print(f"🤖 BOT STABILITY TESTER INITIALIZED")
        print(f"⏰ Start time: {self.start_time.strftime('%H:%M:%S %d/%m/%Y')}")
        print(f"🕐 Test duration: {self.test_duration_minutes} minutes")
    
    def send_telegram_message(self, message: str) -> bool:
        """📱 Send message to Telegram."""
        try:
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            response = requests.post(f"{self.base_url}/sendMessage", data=data, timeout=10)
            return response.status_code == 200 and response.json().get('ok', False)
        except Exception as e:
            print(f"❌ Error sending Telegram message: {e}")
            return False
    
    def start_bot_process(self) -> bool:
        """🚀 Start main bot process."""
        try:
            print(f"🚀 Starting main bot process...")
            
            self.bot_process = subprocess.Popen([
                sys.executable, "main_bot.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
               text=True, bufsize=1, universal_newlines=True)
            
            print(f"✅ Bot process started with PID: {self.bot_process.pid}")
            return True
            
        except Exception as e:
            print(f"❌ Error starting bot process: {e}")
            return False
    
    def monitor_bot_output(self):
        """🔍 Monitor bot output for errors."""
        if not self.bot_process:
            return
        
        error_patterns = [
            'Error',
            'Exception',
            'Traceback',
            'Failed',
            'UnboundLocalError',
            'NoneType',
            'Unknown analysis type',
            'Connection error',
            'Timeout'
        ]
        
        try:
            while self.bot_process.poll() is None:
                line = self.bot_process.stdout.readline()
                if line:
                    line = line.strip()
                    print(f"BOT: {line}")
                    
                    # Check for errors
                    for pattern in error_patterns:
                        if pattern.lower() in line.lower():
                            error_info = {
                                'timestamp': datetime.now(),
                                'pattern': pattern,
                                'message': line
                            }
                            self.errors_detected.append(error_info)
                            print(f"🚨 ERROR DETECTED: {pattern} - {line}")
                    
                    # Count analysis types
                    if 'analysis' in line.lower():
                        for analysis_type in ['fibonacci', 'volume', 'ai', 'pump', 'dump', 'consensus', 'orderbook', 'point']:
                            if analysis_type in line.lower():
                                self.analysis_count[analysis_type] += 1
                
        except Exception as e:
            print(f"❌ Error monitoring bot output: {e}")
    
    def generate_status_report(self) -> str:
        """📊 Generate status report."""
        current_time = datetime.now()
        runtime = current_time - self.start_time
        
        report = f"""🤖 <b>BOT STABILITY TEST - STATUS REPORT</b>

⏰ <b>RUNTIME INFORMATION</b>
├ 🕐 Start: <code>{self.start_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 Current: <code>{current_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ ⏱️ Runtime: <code>{str(runtime).split('.')[0]}</code>
└ 🎯 Target: <code>{self.test_duration_minutes} minutes</code>

🤖 <b>BOT STATUS</b>
├ 🔄 Process Status: <b>{'RUNNING' if self.bot_process and self.bot_process.poll() is None else 'STOPPED'}</b>
├ 🆔 Process PID: <code>{self.bot_process.pid if self.bot_process else 'N/A'}</code>
└ ❌ Errors Detected: <code>{len(self.errors_detected)}</code>

📊 <b>ANALYSIS ACTIVITY</b>"""
        
        if self.analysis_count:
            for analysis_type, count in self.analysis_count.items():
                report += f"\n├ {analysis_type.title()}: <code>{count}</code>"
        else:
            report += f"\n└ No analysis activity detected yet"
        
        if self.errors_detected:
            report += f"\n\n❌ <b>RECENT ERRORS (Last 3)</b>"
            for error in self.errors_detected[-3:]:
                timestamp = error['timestamp'].strftime('%H:%M:%S')
                pattern = error['pattern']
                message = error['message'][:80] + "..." if len(error['message']) > 80 else error['message']
                report += f"\n├ <code>{timestamp}</code> [{pattern}]: {message}"
        
        report += f"\n\n🔍 <i>Continuous monitoring active...</i>"
        
        return report
    
    def stop_bot_process(self):
        """🛑 Stop bot process."""
        if self.bot_process:
            print(f"🛑 Stopping bot process...")
            try:
                self.bot_process.terminate()
                self.bot_process.wait(timeout=10)
                print(f"✅ Bot process stopped gracefully")
            except subprocess.TimeoutExpired:
                print(f"⚠️ Force killing bot process...")
                self.bot_process.kill()
                self.bot_process.wait()
                print(f"✅ Bot process killed")
    
    def run_stability_test(self):
        """🚀 Run complete stability test."""
        print(f"🚀 Starting bot stability test...")
        
        # Send initial notification
        initial_message = f"""🤖 <b>BOT STABILITY TEST STARTED</b>

⏰ <b>Test Configuration:</b>
├ 🕐 Duration: <code>{self.test_duration_minutes} minutes</code>
├ 📊 Report Interval: <code>{self.report_interval_minutes} minutes</code>
└ 🎯 Purpose: <b>Monitor bot for errors and stability</b>

🔍 <b>Monitoring for:</b>
├ ❌ Runtime errors and exceptions
├ 📊 Analysis activity and performance
├ 🔄 Process stability and responsiveness
└ 📱 Telegram communication issues

<b>🚀 BOT STABILITY TEST STARTED</b>"""
        
        self.send_telegram_message(initial_message)
        
        # Start bot process
        if not self.start_bot_process():
            print(f"❌ Failed to start bot process")
            return
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_bot_output, daemon=True)
        monitor_thread.start()
        
        # Main monitoring loop
        end_time = self.start_time + timedelta(minutes=self.test_duration_minutes)
        last_report_time = self.start_time
        
        try:
            while datetime.now() < end_time:
                current_time = datetime.now()
                
                # Check if bot process is still running
                if self.bot_process.poll() is not None:
                    print(f"❌ Bot process terminated unexpectedly!")
                    error_info = {
                        'timestamp': current_time,
                        'pattern': 'PROCESS_TERMINATED',
                        'message': 'Bot process terminated unexpectedly'
                    }
                    self.errors_detected.append(error_info)
                    break
                
                # Send periodic reports
                if current_time - last_report_time >= timedelta(minutes=self.report_interval_minutes):
                    report = self.generate_status_report()
                    self.send_telegram_message(report)
                    last_report_time = current_time
                    print(f"📊 Status report sent at {current_time.strftime('%H:%M:%S')}")
                
                # Wait and show progress
                time.sleep(30)  # Check every 30 seconds
                runtime = current_time - self.start_time
                remaining = end_time - current_time
                print(f"🔍 Runtime: {str(runtime).split('.')[0]} | Remaining: {str(remaining).split('.')[0]} | Errors: {len(self.errors_detected)}")
        
        except KeyboardInterrupt:
            print(f"\n🛑 Test stopped by user")
        
        finally:
            # Stop bot process
            self.stop_bot_process()
            
            # Send final report
            final_report = self.generate_final_report()
            self.send_telegram_message(final_report)
            print(f"📊 Final report sent")
    
    def generate_final_report(self) -> str:
        """📊 Generate final comprehensive report."""
        end_time = datetime.now()
        total_runtime = end_time - self.start_time
        
        report = f"""🎯 <b>BOT STABILITY TEST - FINAL REPORT</b>

⏰ <b>TEST SUMMARY</b>
├ 🕐 Start: <code>{self.start_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 End: <code>{end_time.strftime('%H:%M:%S %d/%m/%Y')}</code>
├ ⏱️ Total Runtime: <code>{str(total_runtime).split('.')[0]}</code>
└ 🎯 Target: <code>{self.test_duration_minutes} minutes</code>

📊 <b>FINAL STATISTICS</b>
├ ❌ Total Errors: <code>{len(self.errors_detected)}</code>
├ 📊 Total Analysis: <code>{sum(self.analysis_count.values())}</code>
└ 📈 Error Rate: <code>{(len(self.errors_detected) / max(1, total_runtime.total_seconds() / 60)):.2f} errors/min</code>"""

        # Analysis breakdown
        if self.analysis_count:
            report += f"\n\n📊 <b>ANALYSIS BREAKDOWN</b>"
            for analysis_type, count in sorted(self.analysis_count.items()):
                report += f"\n├ {analysis_type.title()}: <code>{count}</code>"
        
        # Stability assessment
        if len(self.errors_detected) == 0:
            stability = "🟢 EXCELLENT - No errors detected"
        elif len(self.errors_detected) < 5:
            stability = "🟡 GOOD - Few minor errors"
        elif len(self.errors_detected) < 15:
            stability = "🟠 FAIR - Some errors detected"
        else:
            stability = "🔴 POOR - Many errors detected"
        
        report += f"\n\n🏥 <b>STABILITY ASSESSMENT</b>"
        report += f"\n└ 🎯 Rating: <b>{stability}</b>"
        
        # Error summary
        if self.errors_detected:
            error_types = defaultdict(int)
            for error in self.errors_detected:
                error_types[error['pattern']] += 1
            
            report += f"\n\n❌ <b>ERROR SUMMARY</b>"
            for error_type, count in sorted(error_types.items()):
                report += f"\n├ {error_type}: <code>{count}</code>"
        
        # Recommendations
        report += f"\n\n💡 <b>RECOMMENDATIONS</b>"
        if len(self.errors_detected) == 0:
            report += f"\n├ ✅ Bot is stable and ready for production"
            report += f"\n└ 🚀 No issues detected during test period"
        else:
            report += f"\n├ 🔧 Review and fix detected errors"
            report += f"\n└ 🔍 Consider longer testing period"
        
        report += f"\n\n🎯 <b>BOT STABILITY TEST COMPLETED</b>"
        
        return report

def main():
    """🚀 Main function."""
    print(f"🤖 BOT STABILITY TEST")
    print(f"=" * 60)
    
    # Check environment variables
    if not os.getenv("TELEGRAM_BOT_TOKEN") or not os.getenv("TELEGRAM_CHAT_ID"):
        print(f"❌ Missing environment variables")
        print(f"Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID")
        return
    
    # Check if main_bot.py exists
    if not os.path.exists("main_bot.py"):
        print(f"❌ main_bot.py not found in current directory")
        return
    
    # Create and run tester
    tester = BotStabilityTester()
    
    try:
        tester.run_stability_test()
    except Exception as e:
        print(f"❌ Fatal error in stability test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"🎯 Bot stability test completed")

if __name__ == "__main__":
    main()
