
# QR Code integration for telegram_member_manager.py

def update_donation_info_with_qr(self):
    """Cập nhật donation info với QR code"""
    try:
        from qr_code_generator import DonationQRGenerator
        
        # Generate QR codes
        qr_gen = DonationQRGenerator()
        qr_files = qr_gen.generate_all_formats()
        
        # Update donation info
        self.donation_info.update({
            "qr_code_basic": qr_files.get("basic", ""),
            "qr_code_enhanced": qr_files.get("enhanced", ""),
            "qr_code_svg": qr_files.get("svg", ""),
            "qr_code_telegram": qr_files.get("telegram", ""),
            "qr_generator": qr_gen
        })
        
        print("✅ QR codes integrated into donation info")
        return True
        
    except Exception as e:
        print(f"❌ Error integrating QR codes: {e}")
        return False

def get_donation_message_with_qr(self) -> str:
    """Lấy donation message với QR code"""
    qr_info = ""
    if self.donation_info.get("qr_code_telegram"):
        qr_info = f"\n📱 <b>QR Code:</b> Scan để donation nhanh"
    
    return f"""
💰 <b>HỖ TRỢ PHÁT TRIỂN BOT</b> 💰

🙏 <b>Cảm ơn bạn đã sử dụng Trading Bot AI!</b>

💝 <b>THÔNG TIN DONATION:</b>
├ 🏦 Địa chỉ ví: <code>{self.donation_info['wallet_address']}</code>
├ 🌐 Mạng: {self.donation_info['network']}
├ 💰 Loại coin: {self.donation_info['currency']}
└ 📱 Scan QR code để donation nhanh{qr_info}

🎯 <b>DONATION GIÚP:</b>
├ 🔧 Duy trì và phát triển bot
├ 📊 Cải thiện thuật toán AI
├ 🚀 Thêm tính năng mới
├ 💡 Nâng cao chất lượng tín hiệu
└ 🤖 Hỗ trợ 24/7 cho người dùng

⚡ <b>SAU KHI DONATION:</b>
├ 📞 Liên hệ admin với proof of payment
├ ⏰ Được gia hạn thành viên ngay lập tức
├ 🎁 Nhận các tính năng premium
└ 🏆 Trở thành thành viên VIP

<b>Mọi đóng góp đều được trân trọng! 🙏</b>

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💝 <i>Ví USDT (BEP20): {self.donation_info['wallet_address']}</i>
    """

def send_qr_code(self, chat_id: str, qr_type: str = "telegram"):
    """Gửi QR code qua Telegram"""
    try:
        qr_path_key = f"qr_code_{qr_type}"
        qr_path = self.donation_info.get(qr_path_key)
        
        if qr_path and os.path.exists(qr_path):
            # Gửi QR code image
            with open(qr_path, 'rb') as qr_file:
                self.telegram_notifier.send_photo(
                    photo=qr_file,
                    chat_id=chat_id,
                    caption=f"""
📱 <b>QR CODE DONATION</b>

🏦 <b>Wallet:</b> <code>{self.donation_info['wallet_address']}</code>
🌐 <b>Network:</b> {self.donation_info['network']}
💰 <b>Currency:</b> {self.donation_info['currency']}

📱 <b>Cách sử dụng:</b>
1. Mở ví crypto của bạn
2. Scan QR code này
3. Nhập số tiền muốn donation
4. Xác nhận giao dịch
5. Liên hệ admin với proof

🙏 <b>Cảm ơn sự ủng hộ!</b>
                    """,
                    parse_mode="HTML"
                )
                
                print(f"✅ QR code sent to chat {chat_id}")
                return True
        else:
            print(f"❌ QR code not found: {qr_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending QR code: {e}")
        return False
