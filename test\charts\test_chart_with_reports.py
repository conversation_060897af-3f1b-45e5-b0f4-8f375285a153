#!/usr/bin/env python3
"""
📊 Test Chart with Reports - Test existing detailed reports with charts
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def test_chart_with_reports():
    """📊 Test existing detailed reports with charts."""
    print(f"📊 CHART WITH REPORTS TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 5000, 100)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Test 1: Fourier Analysis with Chart
        print(f"\n1️⃣ Testing Fourier Analysis with Chart...")
        
        fourier_data = {
            'price_cycles': [
                {'period': 40.0, 'confidence': 0.89, 'cycle_type': 'medium'},
                {'period': 18.2, 'confidence': 0.79, 'cycle_type': 'short'},
                {'period': 9.1, 'confidence': 0.65, 'cycle_type': 'short'}
            ],
            'volume_cycles': [
                {'period': 10.7, 'confidence': 1.00},
                {'period': 5.5, 'confidence': 1.00},
                {'period': 18.8, 'confidence': 0.94}
            ],
            'signals': {
                'overall_signal': 'BULLISH',
                'signal_strength': 0.7,
                'confidence': 0.693,
                'trading_levels': {
                    'entry_price': current_price * 0.99,
                    'take_profit': current_price * 1.05,
                    'stop_loss': current_price * 0.98,
                    'risk_reward_ratio': 5.0,
                    'tp_levels': {
                        'tp1': current_price * 1.03,
                        'tp2': current_price * 1.05,
                        'tp3': current_price * 1.08
                    }
                },
                'timing_signals': [
                    {
                        'type': 'multi_cycle_synchronization',
                        'action': 'STRONG_SELL',
                        'confidence': 1.0,
                        'cycles_involved': 3,
                        'synchronization_type': 'peak_alignment'
                    }
                ]
            },
            'dominant_cycle': 40.0,
            'trend_component': 0.009,
            'seasonal_strength': 0.658,
            'market_regime': {
                'regime_type': 'stable_ranging',
                'volatility_level': 'medium',
                'trend_direction': 'neutral'
            },
            'analysis_metadata': {
                'confidence_level': 0.824,
                'analysis_quality': 'high'
            }
        }
        
        try:
            fourier_success = notifier.send_fourier_analysis_report(
                "BTC/USDT", fourier_data, current_price, use_html=True,
                ohlcv_data=ohlcv_data, chart_generator=chart_gen
            )
            
            if fourier_success:
                print(f"  ✅ Fourier analysis with chart sent successfully")
            else:
                print(f"  ❌ Fourier analysis with chart failed")
                
        except Exception as fourier_error:
            print(f"  ❌ Fourier test error: {fourier_error}")
            fourier_success = False
        
        # Wait between sends to avoid rate limiting
        import time
        time.sleep(5)
        
        # Test 2: AI Analysis with Chart
        print(f"\n2️⃣ Testing AI Analysis with Chart...")
        
        ai_data = {
            'ensemble_signal': 'BUY',
            'ensemble_confidence': 0.85,
            'model_results': {
                'XGBoost': {'prediction': 'BUY', 'confidence': 0.9},
                'RandomForest': {'prediction': 'BUY', 'confidence': 0.8},
                'LSTM': {'prediction': 'SELL', 'confidence': 0.7},
                'Transformer': {'prediction': 'BUY', 'confidence': 0.95}
            },
            'prediction_quality': 'HIGH',
            'technical_analysis': {
                'momentum': 0.6,
                'volatility': 0.02,
                'trend_strength': 0.8
            },
            'market_sentiment': 'BULLISH',
            'recommendation': 'BUY',
            'risk_assessment': 'MEDIUM',
            'trading_levels': {
                'entry_price': current_price,
                'take_profit': current_price * 1.05,
                'stop_loss': current_price * 0.98,
                'risk_reward_ratio': 2.5,
                'tp_levels': {
                    'tp1': current_price * 1.03,
                    'tp2': current_price * 1.05,
                    'tp3': current_price * 1.08
                },
                'confidence_metrics': {
                    'overall_confidence': 0.85
                }
            }
        }
        
        try:
            ai_success = notifier.send_ai_analysis_report(
                "BTC/USDT", ai_data, current_price, use_html=True,
                ohlcv_data=ohlcv_data, chart_generator=chart_gen
            )
            
            if ai_success:
                print(f"  ✅ AI analysis with chart sent successfully")
            else:
                print(f"  ❌ AI analysis with chart failed")
                
        except Exception as ai_error:
            print(f"  ❌ AI test error: {ai_error}")
            ai_success = False
        
        # Summary
        print(f"\n📊 TEST RESULTS:")
        print(f"  1️⃣ Fourier Analysis with Chart: {'✅ PASS' if fourier_success else '❌ FAIL'}")
        print(f"  2️⃣ AI Analysis with Chart: {'✅ PASS' if ai_success else '❌ FAIL'}")
        
        overall_success = fourier_success and ai_success
        
        if overall_success:
            print(f"\n🎉 ALL TESTS PASSED!")
            print(f"📱 You should see detailed reports with charts in Telegram")
            print(f"🎯 Charts show basic info, captions show detailed analysis")
        else:
            print(f"\n⚠️ SOME TESTS FAILED!")
            print(f"📱 Check Telegram for any successful reports")
        
        return overall_success
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chart_with_reports()
    if success:
        print(f"\n🎉 CHART WITH REPORTS TEST PASSED!")
        print(f"📱 Check your Telegram for detailed reports with charts")
    else:
        print(f"\n💥 CHART WITH REPORTS TEST FAILED!")
