#!/usr/bin/env python3
"""
🧪 TEST: Dynamic Sectors System
Test để kiểm tra hệ thống dynamic sector classification
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_dynamic_sector_initialization():
    """Test dynamic sector initialization"""
    print("\n🧪 === TESTING DYNAMIC SECTOR INITIALIZATION ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        # Initialize analyzer with dynamic sectors
        print("🔄 Initializing MoneyFlowAnalyzer with dynamic sectors...")
        analyzer = MoneyFlowAnalyzer()
        
        # Check if sectors were initialized
        sector_info = analyzer.get_sector_info()
        
        print(f"\n📊 DYNAMIC SECTOR INFORMATION:")
        print(f"  📈 Total sectors: {sector_info['total_sectors']}")
        print(f"  🪙 Total coins: {sector_info['total_coins']}")
        print(f"  💾 Cache size: {sector_info['cache_size']}")
        
        # Display sectors
        print(f"\n🏷️ DYNAMIC SECTORS:")
        for sector, coins in sector_info['sectors'].items():
            print(f"  📊 {sector}: {len(coins)} coins")
            if coins:
                # Show first few coins
                sample_coins = [coin.replace('USDT', '') for coin in coins[:5]]
                print(f"      Sample: {', '.join(sample_coins)}")
        
        # Verify minimum requirements
        if sector_info['total_sectors'] >= 5:
            print(f"\n✅ SUCCESS: Found {sector_info['total_sectors']} sectors")
        else:
            print(f"\n⚠️ WARNING: Only {sector_info['total_sectors']} sectors found")
        
        if sector_info['total_coins'] >= 20:
            print(f"✅ SUCCESS: Found {sector_info['total_coins']} coins")
        else:
            print(f"⚠️ WARNING: Only {sector_info['total_coins']} coins found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing dynamic sectors: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coin_classification():
    """Test individual coin classification"""
    print("\n🧪 === TESTING COIN CLASSIFICATION ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        analyzer = MoneyFlowAnalyzer()
        
        # Test known coins
        test_coins = [
            ('BTC', 'Layer1'),
            ('ETH', 'Layer1'),
            ('DOGE', 'Meme'),
            ('SHIB', 'Meme'),
            ('LINK', 'Oracle'),
            ('UNI', 'DeFi'),
            ('MATIC', 'Layer2'),
            ('AXS', 'Gaming'),
            ('FET', 'AI')
        ]
        
        print("🔍 TESTING COIN CLASSIFICATION:")
        correct_classifications = 0
        
        for coin_symbol, expected_sector in test_coins:
            pair = f"{coin_symbol}USDT"
            classified_sector = analyzer._classify_coin_by_keywords(coin_symbol.lower(), pair)
            
            if classified_sector == expected_sector:
                print(f"  ✅ {coin_symbol}: {classified_sector} (correct)")
                correct_classifications += 1
            else:
                print(f"  ⚠️ {coin_symbol}: {classified_sector} (expected: {expected_sector})")
        
        accuracy = correct_classifications / len(test_coins) * 100
        print(f"\n📊 CLASSIFICATION ACCURACY: {accuracy:.1f}% ({correct_classifications}/{len(test_coins)})")
        
        if accuracy >= 70:
            print("✅ Classification accuracy is good")
            return True
        else:
            print("⚠️ Classification accuracy needs improvement")
            return False
        
    except Exception as e:
        print(f"❌ Error testing coin classification: {e}")
        return False

def test_sector_update():
    """Test sector update functionality"""
    print("\n🧪 === TESTING SECTOR UPDATE ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        analyzer = MoneyFlowAnalyzer()
        
        # Get initial sector info
        initial_info = analyzer.get_sector_info()
        print(f"📊 Initial sectors: {initial_info['total_sectors']}")
        print(f"🪙 Initial coins: {initial_info['total_coins']}")
        
        # Test update (this might take a moment)
        print("\n🔄 Testing sector update...")
        analyzer.update_dynamic_sectors()
        
        # Get updated sector info
        updated_info = analyzer.get_sector_info()
        print(f"📊 Updated sectors: {updated_info['total_sectors']}")
        print(f"🪙 Updated coins: {updated_info['total_coins']}")
        
        # Check if update worked
        if updated_info['last_updated'] > initial_info['last_updated']:
            print("✅ Sector update successful")
            return True
        else:
            print("⚠️ Sector update may not have worked")
            return False
        
    except Exception as e:
        print(f"❌ Error testing sector update: {e}")
        return False

def test_fallback_sectors():
    """Test fallback sector system"""
    print("\n🧪 === TESTING FALLBACK SECTORS ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        analyzer = MoneyFlowAnalyzer()
        
        # Test fallback
        print("🔄 Testing fallback sectors...")
        analyzer._use_fallback_sectors()
        
        fallback_info = analyzer.get_sector_info()
        
        print(f"📊 Fallback sectors: {fallback_info['total_sectors']}")
        print(f"🪙 Fallback coins: {fallback_info['total_coins']}")
        
        # Check fallback sectors
        expected_fallback_sectors = ['Layer1', 'DeFi', 'Layer2', 'Gaming', 'AI', 'Meme', 'Infrastructure']
        
        missing_sectors = []
        for sector in expected_fallback_sectors:
            if sector not in fallback_info['sectors']:
                missing_sectors.append(sector)
        
        if not missing_sectors:
            print("✅ All expected fallback sectors present")
            return True
        else:
            print(f"❌ Missing fallback sectors: {missing_sectors}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing fallback sectors: {e}")
        return False

def main():
    """Run all dynamic sector tests"""
    print("🧪 === DYNAMIC SECTORS SYSTEM TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Dynamic Sector Initialization", test_dynamic_sector_initialization),
        ("Coin Classification", test_coin_classification),
        ("Sector Update", test_sector_update),
        ("Fallback Sectors", test_fallback_sectors)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append({
            'name': test_name,
            'success': success,
            'duration': end_time - start_time
        })
        
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"\n{test_name}: {status} ({end_time - start_time:.2f}s)")
    
    # Summary
    print(f"\n{'='*60}")
    print("🧪 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['name']}: {result['duration']:.2f}s")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Dynamic sectors system is working!")
        print("\n📋 DYNAMIC FEATURES CONFIRMED:")
        print("✅ Automatic coin discovery from Binance API")
        print("✅ Dynamic sector classification")
        print("✅ Keyword-based categorization")
        print("✅ Manual classification for known coins")
        print("✅ Fallback system for reliability")
        print("✅ Cache system for performance")
        print("✅ Periodic updates capability")
        
        print("\n🌊 BENEFITS:")
        print("✅ Always up-to-date with market")
        print("✅ Automatic new coin detection")
        print("✅ No manual maintenance required")
        print("✅ Adapts to market trends")
        print("✅ Scalable to any number of coins")
        
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
