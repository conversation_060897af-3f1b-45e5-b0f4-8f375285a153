#!/usr/bin/env python3
"""
🔍 PUMP/DUMP DETECTION TEST
Test PUMP/DUMP detection in consensus analyzer
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pump_dump_detection():
    """Test PUMP/DUMP detection functionality"""
    print("🚀 STARTING PUMP/DUMP DETECTION TEST")
    print("=" * 60)
    
    try:
        # Test 1: Import dump detector
        print("\n🔍 Testing Dump Detector Import...")
        try:
            from dump_detector import UltraEarlyDumpDetector
            print("  ✅ UltraEarlyDumpDetector imported successfully")
            
            # Initialize dump detector with correct parameters
            dump_detector = UltraEarlyDumpDetector(
                ultra_early_sensitivity=0.7,
                whale_threshold=50000,
                pre_dump_lookback=60,
                min_confidence=0.70
            )
            print("  ✅ Dump detector initialized")
            
        except Exception as e:
            print(f"  ❌ Dump detector import failed: {e}")
            dump_detector = None
        
        # Test 2: Import volume detector (for pump detection)
        print("\n🔍 Testing Volume Detector Import...")
        try:
            from volume_spike_detector import VolumeSpikeDetector
            print("  ✅ VolumeSpikeDetector imported successfully")
            
            # Initialize volume detector with correct parameters
            volume_detector = VolumeSpikeDetector(
                spike_threshold_multiplier=2.5,
                moving_avg_period=20,
                min_data_points=50,
                enable_ml_detection=True
            )
            print("  ✅ Volume detector initialized")
            
        except Exception as e:
            print(f"  ❌ Volume detector import failed: {e}")
            volume_detector = None
        
        # Test 3: Create sample market data
        print("\n🔍 Creating Sample Market Data...")
        
        # Create sample OHLCV data
        dates = pd.date_range(start=datetime.now() - timedelta(days=7), periods=168, freq='H')
        np.random.seed(42)
        
        # Normal market data
        base_price = 50000
        price_changes = np.random.normal(0, 0.02, 168)  # 2% volatility
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV data
        ohlcv_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': [np.random.uniform(1000000, 5000000) for _ in prices]
        })
        
        print(f"  ✅ Sample OHLCV data created: {len(ohlcv_data)} rows")
        print(f"  📊 Price range: {ohlcv_data['close'].min():.2f} - {ohlcv_data['close'].max():.2f}")
        
        # Test 4: Test dump detection
        print("\n🔍 Testing Dump Detection...")
        if dump_detector:
            try:
                current_price = ohlcv_data['close'].iloc[-1]
                dump_market_data = {
                    "ohlcv_data": ohlcv_data,
                    "current_price": current_price,
                    "orderbook_data": None,
                    "whale_transactions": []
                }
                
                print(f"  📊 Testing with price: {current_price:.2f}")
                dump_alert = dump_detector.analyze_dump_probability("BTC/USDT", dump_market_data)
                
                if dump_alert:
                    print(f"  ✅ Dump analysis completed")
                    print(f"    🔍 Dump probability: {dump_alert.dump_probability:.1%}")
                    print(f"    🔍 Risk level: {getattr(dump_alert, 'risk_level', 'UNKNOWN')}")
                    print(f"    🔍 Stage: {getattr(dump_alert, 'stage', 'UNKNOWN')}")
                    
                    # Test consensus input format
                    dump_analysis = {
                        "probability": dump_alert.dump_probability,
                        "stage": getattr(dump_alert, 'stage', 'ACTIVE_DUMP'),
                        "confidence": dump_alert.dump_probability,
                        "severity": getattr(dump_alert, 'risk_level', 'MEDIUM')
                    }
                    print(f"  ✅ Dump analysis formatted for consensus: {dump_analysis}")
                else:
                    print(f"  ⚠️ No dump alert generated")
                    dump_analysis = {"probability": 0.0, "stage": "NONE", "confidence": 0.0, "severity": "LOW"}
                    
            except Exception as e:
                print(f"  ❌ Dump detection test failed: {e}")
                dump_analysis = {"probability": 0.0, "stage": "ERROR", "confidence": 0.0, "severity": "LOW"}
        else:
            print(f"  ⚠️ Dump detector not available")
            dump_analysis = {"probability": 0.0, "stage": "UNAVAILABLE", "confidence": 0.0, "severity": "LOW"}
        
        # Test 5: Test pump detection
        print("\n🔍 Testing Pump Detection...")
        if volume_detector:
            try:
                # Create pump scenario data
                pump_ohlcv = ohlcv_data.copy()
                # Simulate volume spike in last few candles
                pump_ohlcv.loc[pump_ohlcv.index[-5:], 'volume'] *= 15  # 15x volume spike
                pump_ohlcv.loc[pump_ohlcv.index[-3:], 'close'] *= 1.08  # 8% price increase
                
                current_price = pump_ohlcv['close'].iloc[-1]
                print(f"  📊 Testing with pump scenario, price: {current_price:.2f}")
                
                spike_details = volume_detector.get_spike_details(
                    pump_ohlcv,
                    orderbook_data=None,
                    current_price=current_price
                )
                
                if spike_details and spike_details.get("is_spike"):
                    print(f"  ✅ Volume spike detected")
                    print(f"    🔍 Spike factor: {spike_details.get('spike_factor', 0):.2f}x")
                    
                    pump_analysis_data = spike_details.get("pump_analysis", {})
                    if pump_analysis_data:
                        print(f"    🔍 Pump probability: {pump_analysis_data.get('pump_probability', 0):.1%}")
                        print(f"    🔍 Intensity: {pump_analysis_data.get('intensity', 0):.2f}")
                        
                        # Test consensus input format
                        pump_analysis = {
                            "probability": pump_analysis_data.get("pump_probability", 0.0),
                            "stage": pump_analysis_data.get("stage", "ACTIVE_PUMP" if pump_analysis_data.get("pump_probability", 0) > 0.3 else "NONE"),
                            "confidence": pump_analysis_data.get("pump_probability", 0.0),
                            "severity": pump_analysis_data.get("risk_level", "MEDIUM")
                        }
                        print(f"  ✅ Pump analysis formatted for consensus: {pump_analysis}")
                    else:
                        print(f"  ⚠️ No pump analysis in spike details")
                        pump_analysis = {"probability": 0.0, "stage": "NONE", "confidence": 0.0, "severity": "LOW"}
                else:
                    print(f"  ⚠️ No volume spike detected")
                    pump_analysis = {"probability": 0.0, "stage": "NONE", "confidence": 0.0, "severity": "LOW"}
                    
            except Exception as e:
                print(f"  ❌ Pump detection test failed: {e}")
                pump_analysis = {"probability": 0.0, "stage": "ERROR", "confidence": 0.0, "severity": "LOW"}
        else:
            print(f"  ⚠️ Volume detector not available")
            pump_analysis = {"probability": 0.0, "stage": "UNAVAILABLE", "confidence": 0.0, "severity": "LOW"}
        
        # Test 6: Test consensus analyzer with PUMP/DUMP data
        print("\n🔍 Testing Consensus Analyzer with PUMP/DUMP...")
        try:
            from consensus_analyzer import ConsensusAnalyzer
            print("  ✅ ConsensusAnalyzer imported successfully")
            
            # Initialize consensus analyzer
            consensus_analyzer = ConsensusAnalyzer(
                min_consensus_score=0.55,
                weight_config={
                    "ai_models": 0.20,
                    "volume_profile": 0.15,
                    "point_figure": 0.15,
                    "zigzag_fibonacci": 0.15,
                    "fourier": 0.05,
                    "volume_patterns": 0.05,
                    "dump_detector": 0.12,  # Higher weight for testing
                    "pump_detector": 0.13   # Higher weight for testing
                }
            )
            print("  ✅ Consensus analyzer initialized")
            
            # Create test consensus input
            consensus_input = {
                "coin": "BTC/USDT",
                "symbol": "BTC/USDT",
                "ohlcv_data": ohlcv_data,
                "processed_features": {},
                
                # Basic signals (low confidence to test PUMP/DUMP override)
                "volume_profile": {"signal": "BUY", "confidence": 0.3},
                "point_figure": {"signal": "BUY", "confidence": 0.4},
                "fibonacci": {"signal": "SELL", "confidence": 0.2},
                "fourier": {"signal": "NEUTRAL", "confidence": 0.1},
                "orderbook": {"signals": {"primary_signal": "BUY", "confidence": 0.3}},
                
                # PUMP/DUMP analysis
                "dump_analysis": dump_analysis,
                "pump_analysis": pump_analysis,
                
                # Additional data
                "volume_pattern_analysis": {},
                "volume_spike_info": {},
                "pump_detection_results": {}
            }
            
            print(f"  📊 Testing consensus with:")
            print(f"    🚨 Dump: prob={dump_analysis['probability']:.1%}, stage={dump_analysis['stage']}")
            print(f"    🚀 Pump: prob={pump_analysis['probability']:.1%}, stage={pump_analysis['stage']}")
            
            # Run consensus analysis
            consensus_result = consensus_analyzer.analyze_consensus(consensus_input)
            
            if consensus_result and consensus_result.get('status') == 'success':
                consensus = consensus_result.get('consensus', {})
                print(f"  ✅ Consensus analysis completed")
                print(f"    🎯 Signal: {consensus.get('signal', 'NONE')}")
                print(f"    🎯 Confidence: {consensus.get('confidence', 0):.1%}")
                print(f"    🎯 Score: {consensus.get('consensus_score', 0):.1%}")
                print(f"    📊 Contributing signals: {consensus.get('signal_count', 0)}")

                # Check if PUMP/DUMP was detected in contributing algorithms
                contributing_algorithms = consensus.get('contributing_algorithms', [])
                pump_dump_signals = [s for s in contributing_algorithms if 'pump' in s.get('name', '').lower() or 'dump' in s.get('name', '').lower()]

                if pump_dump_signals:
                    print(f"  🎉 PUMP/DUMP signals detected in consensus:")
                    for signal in pump_dump_signals:
                        print(f"    - {signal.get('name')}: {signal.get('signal')} ({signal.get('confidence', 0):.1%})")
                else:
                    print(f"  ⚠️ No PUMP/DUMP signals found in consensus result")

                # Check if consensus override worked
                if consensus.get('signal') == 'BUY' and consensus.get('confidence', 0) > 0.4:
                    print(f"  🎉 SUCCESS: PUMP signal detected and processed in consensus!")
                elif consensus.get('signal') == 'SELL' and consensus.get('confidence', 0) > 0.4:
                    print(f"  🎉 SUCCESS: DUMP signal detected and processed in consensus!")
                else:
                    print(f"  ⚠️ PUMP/DUMP signal not properly processed in consensus")
                    
            else:
                print(f"  ❌ Consensus analysis failed")
                
        except Exception as e:
            print(f"  ❌ Consensus analyzer test failed: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("🎯 PUMP/DUMP DETECTION TEST COMPLETED")
        
        # Summary
        print(f"\n📊 TEST SUMMARY:")
        print(f"  🚨 Dump Detection: {'✅ Working' if dump_detector else '❌ Failed'}")
        print(f"  🚀 Pump Detection: {'✅ Working' if volume_detector else '❌ Failed'}")
        print(f"  🎯 Consensus Integration: {'✅ Working' if 'consensus_analyzer' in locals() else '❌ Failed'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pump_dump_detection()
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
