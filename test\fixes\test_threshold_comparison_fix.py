#!/usr/bin/env python3
"""
🧪 Test script to verify the threshold comparison logic fix
Tests both the intelligent TP/SL algorithm support and the threshold comparison display
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_threshold_comparison_logic():
    """🔍 Test threshold comparison logic with various confidence values."""
    print("🧪 Testing Threshold Comparison Logic Fix...")
    print("=" * 60)
    
    # Load threshold from environment
    MIN_CONFIDENCE_THRESHOLD = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.80"))
    
    print(f"📊 Testing with MIN_CONFIDENCE_THRESHOLD = {MIN_CONFIDENCE_THRESHOLD}")
    print()
    
    # Test cases that previously showed the bug
    test_cases = [
        {"confidence": 0.811, "expected_pass": True, "description": "Original bug case"},
        {"confidence": 0.8, "expected_pass": True, "description": "Exact threshold"},
        {"confidence": 0.799, "expected_pass": False, "description": "Just below threshold"},
        {"confidence": 0.85, "expected_pass": True, "description": "Well above threshold"},
        {"confidence": 0.75, "expected_pass": False, "description": "Well below threshold"},
        {"confidence": 0.801, "expected_pass": True, "description": "Just above threshold"},
    ]
    
    print("🔍 Testing threshold comparison logic:")
    print(f"{'Confidence':<12} {'Threshold':<12} {'Pass':<6} {'Expected':<10} {'Status':<10} {'Display Format'}")
    print("-" * 80)
    
    all_passed = True
    
    for case in test_cases:
        confidence = case["confidence"]
        expected_pass = case["expected_pass"]
        description = case["description"]
        
        # Test the actual logic
        actual_pass = confidence >= MIN_CONFIDENCE_THRESHOLD
        
        # Test display formatting (this was the bug)
        display_format = f"conf: {confidence:.3f} < {MIN_CONFIDENCE_THRESHOLD:.3f}"
        
        # Check if test passes
        test_passed = actual_pass == expected_pass
        status = "✅ PASS" if test_passed else "❌ FAIL"
        
        if not test_passed:
            all_passed = False
        
        print(f"{confidence:<12.3f} {MIN_CONFIDENCE_THRESHOLD:<12.3f} {actual_pass!s:<6} {expected_pass!s:<10} {status:<10} {display_format}")
    
    print("-" * 80)
    print(f"Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    print()
    
    return all_passed

def test_intelligent_tp_sl_algorithms():
    """🎯 Test intelligent TP/SL algorithm support documentation."""
    print("🎯 Testing Intelligent TP/SL Algorithm Support...")
    print("=" * 60)
    
    # Import the analyzer to test its methods
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        # Initialize analyzer
        analyzer = IntelligentTPSLAnalyzer()
        
        # Expected 12 algorithms
        expected_algorithms = [
            "atr_dynamic",
            "fibonacci_confluence", 
            "volume_profile",
            "point_figure",
            "sr_confluence",
            "volatility_bands",
            "momentum_based",
            "statistical_risk",
            "fourier_harmonic",
            "orderbook_levels",
            "volume_spike",
            "volume_pattern"
        ]
        
        print(f"📊 Expected Algorithms: {len(expected_algorithms)}")
        print()
        
        # Check if methods exist
        available_methods = []
        missing_methods = []
        
        for algorithm in expected_algorithms:
            method_name = f"_calculate_{algorithm}_tp_sl"
            if hasattr(analyzer, method_name):
                available_methods.append(algorithm)
                print(f"✅ {algorithm:<20} - Method: {method_name}")
            else:
                missing_methods.append(algorithm)
                print(f"❌ {algorithm:<20} - Method: {method_name} (MISSING)")
        
        print()
        print(f"📊 Algorithm Support Summary:")
        print(f"  Available: {len(available_methods)}/12")
        print(f"  Missing: {len(missing_methods)}/12")
        
        if missing_methods:
            print(f"  Missing algorithms: {', '.join(missing_methods)}")
        
        # Test market regime detection
        print()
        print("🔍 Testing Market Regime Detection:")
        if hasattr(analyzer, '_detect_comprehensive_market_regime'):
            print("✅ Market regime detection available")
        else:
            print("❌ Market regime detection missing")
        
        # Test ensemble methodology
        print()
        print("🔍 Testing Ensemble Methodology:")
        if hasattr(analyzer, '_calculate_enhanced_ensemble_tp_sl'):
            print("✅ Enhanced ensemble methodology available")
        else:
            print("❌ Enhanced ensemble methodology missing")
        
        success = len(missing_methods) == 0
        print()
        print(f"Overall Algorithm Support: {'✅ COMPLETE' if success else '❌ INCOMPLETE'}")
        
        return success
        
    except ImportError as e:
        print(f"❌ Failed to import IntelligentTPSLAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing algorithms: {e}")
        return False

def test_configuration_consistency():
    """⚙️ Test configuration consistency across the system."""
    print("⚙️ Testing Configuration Consistency...")
    print("=" * 60)
    
    # Load all threshold configurations
    config_vars = {
        "SIGNAL_QUALITY_FILTER_ENABLED": bool(int(os.getenv("SIGNAL_QUALITY_FILTER_ENABLED", "1"))),
        "MIN_CONFIDENCE_THRESHOLD": float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.80")),
        "FIBONACCI_MIN_CONFIDENCE": float(os.getenv("FIBONACCI_MIN_CONFIDENCE", "0.85")),
        "POINT_FIGURE_MIN_CONFIDENCE": float(os.getenv("POINT_FIGURE_MIN_CONFIDENCE", "0.85")),
        "VOLUME_PROFILE_MIN_CONFIDENCE": float(os.getenv("VOLUME_PROFILE_MIN_CONFIDENCE", "0.40")),
        "ORDERBOOK_MIN_CONFIDENCE": float(os.getenv("ORDERBOOK_MIN_CONFIDENCE", "0.85")),
        "FOURIER_MIN_CONFIDENCE": float(os.getenv("FOURIER_MIN_CONFIDENCE", "0.85")),
    }
    
    print("📊 Current Configuration:")
    for var, value in config_vars.items():
        if isinstance(value, float):
            print(f"  {var:<30}: {value:.3f} ({value:.1%})")
        else:
            print(f"  {var:<30}: {value}")
    
    print()
    
    # Check for consistency issues
    issues = []
    
    # Check if all algorithm thresholds are reasonable
    algorithm_thresholds = {
        "Fibonacci": config_vars["FIBONACCI_MIN_CONFIDENCE"],
        "Point & Figure": config_vars["POINT_FIGURE_MIN_CONFIDENCE"], 
        "Volume Profile": config_vars["VOLUME_PROFILE_MIN_CONFIDENCE"],
        "Orderbook": config_vars["ORDERBOOK_MIN_CONFIDENCE"],
        "Fourier": config_vars["FOURIER_MIN_CONFIDENCE"],
    }
    
    for algo, threshold in algorithm_thresholds.items():
        if threshold < 0.3 or threshold > 0.95:
            issues.append(f"{algo} threshold ({threshold:.1%}) is outside reasonable range (30%-95%)")
    
    # Check if global threshold is consistent
    global_threshold = config_vars["MIN_CONFIDENCE_THRESHOLD"]
    if global_threshold < 0.5 or global_threshold > 0.9:
        issues.append(f"Global threshold ({global_threshold:.1%}) is outside recommended range (50%-90%)")
    
    print("🔍 Configuration Analysis:")
    if issues:
        for issue in issues:
            print(f"  ⚠️ {issue}")
    else:
        print("  ✅ All configurations are within reasonable ranges")
    
    print()
    print(f"Configuration Status: {'✅ GOOD' if not issues else '⚠️ HAS ISSUES'}")
    
    return len(issues) == 0

def main():
    """🚀 Main test function."""
    print("🧪 THRESHOLD COMPARISON & ALGORITHM SUPPORT TEST SUITE")
    print("=" * 70)
    print(f"⏰ Test started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    test_results = []
    
    # Test 1: Threshold comparison logic
    print("TEST 1: Threshold Comparison Logic")
    result1 = test_threshold_comparison_logic()
    test_results.append(("Threshold Comparison", result1))
    
    print()
    
    # Test 2: Algorithm support
    print("TEST 2: Intelligent TP/SL Algorithm Support")
    result2 = test_intelligent_tp_sl_algorithms()
    test_results.append(("Algorithm Support", result2))
    
    print()
    
    # Test 3: Configuration consistency
    print("TEST 3: Configuration Consistency")
    result3 = test_configuration_consistency()
    test_results.append(("Configuration", result3))
    
    # Final summary
    print()
    print("=" * 70)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 70)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    print(f"Overall Status: {'🎉 ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    print(f"⏰ Test completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
