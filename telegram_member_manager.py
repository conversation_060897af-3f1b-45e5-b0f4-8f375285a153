#!/usr/bin/env python3
"""
👥 ENHANCED TELEGRAM MEMBER MANAGER V2.0 - PRODUCTION READY
===========================================================

Advanced Telegram Member Management System:
- 👥 Comprehensive member lifecycle management with automation
- ⏰ Advanced trial period tracking with smart notifications
- 🔐 Role-based access control and permission management
- 📊 Real-time analytics and member behavior tracking
- 💰 Integrated donation system with QR code generation
- 🚀 Performance optimized for large member bases
- 🛡️ Comprehensive error handling and data protection

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import json
import time
import threading
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import sqlite3
import os

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import pandas as pd
    AVAILABLE_MODULES['pandas'] = True
    print("✅ pandas imported successfully - Advanced data analysis available")
except ImportError:
    AVAILABLE_MODULES['pandas'] = False
    print("⚠️ pandas not available - Using basic data processing")

try:
    from cryptography.fernet import Fernet
    AVAILABLE_MODULES['cryptography'] = True
    print("✅ cryptography imported successfully - Data encryption available")
except ImportError:
    AVAILABLE_MODULES['cryptography'] = False
    print("⚠️ cryptography not available - No data encryption")

print(f"👥 Telegram Member Manager V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class TelegramMemberManager:
    """
    👥 ENHANCED TELEGRAM MEMBER MANAGER V2.0 - PRODUCTION READY
    ===========================================================

    Advanced Telegram Member Management System:
    - 👥 Comprehensive member lifecycle management with automation
    - ⏰ Advanced trial period tracking with smart notifications
    - 🔐 Role-based access control and permission management
    - 📊 Real-time analytics and member behavior tracking
    - 💰 Integrated donation system with QR code generation
    - 🚀 Performance optimized for large member bases
    """

    def __init__(self, telegram_notifier=None, enable_analytics=True,
                 enable_auto_expiration=True, trial_period_days=60,
                 enable_csv_export=True, enable_data_encryption=False):
        """
        Initialize Enhanced Telegram Member Manager V2.0.

        Args:
            telegram_notifier: Telegram notification service
            enable_analytics: Enable member analytics and tracking
            enable_auto_expiration: Enable automatic member expiration
            trial_period_days: Default trial period in days (60)
            enable_csv_export: Enable CSV export functionality
            enable_data_encryption: Enable data encryption (requires cryptography)
        """
        print("👥 Initializing Enhanced Telegram Member Manager V2.0...")

        self.telegram_notifier = telegram_notifier
        self.db_path = "telegram_members.db"

        # Enhanced features
        self.enable_analytics = enable_analytics
        self.enable_auto_expiration = enable_auto_expiration
        self.trial_period_days = max(7, min(365, trial_period_days))  # 7-365 days
        self.enable_csv_export = enable_csv_export and AVAILABLE_MODULES.get('pandas', False)
        self.enable_data_encryption = enable_data_encryption and AVAILABLE_MODULES.get('cryptography', False)

        # Enhanced managed groups configuration
        self.managed_groups = {
            "-1002301937119": {
                "name": "Trading Signals Group",
                "description": "Nhóm tín hiệu trading AI",
                "trial_days": self.trial_period_days,
                "max_members": 1000,
                "permissions": ["view_signals", "receive_alerts"]
            },
            "-1002395637657": {
                "name": "Premium Analysis Group",
                "description": "Nhóm phân tích chuyên sâu",
                "trial_days": self.trial_period_days,
                "max_members": 500,
                "permissions": ["view_analysis", "receive_reports", "advanced_features"]
            }
        }

        # Performance tracking
        self.management_stats = {
            "total_members_managed": 0,
            "active_members": 0,
            "expired_members": 0,
            "warnings_sent": 0,
            "successful_operations": 0,
            "failed_operations": 0
        }
        
        # Thông tin donation
        self.donation_info = {
            "wallet_address": "******************************************",
            "network": "BNB Smart Chain (BEP20)",
            "currency": "USDT",
            "qr_code_basic": "",
            "qr_code_enhanced": "",
            "qr_code_svg": "",
            "qr_code_telegram": "",
            "qr_generator": None
        }
        
        # Khởi tạo database
        self.init_database()

        # Khởi tạo QR codes
        self.init_qr_codes()

        # Khởi tạo CSV exporter
        self.init_csv_exporter()

        # Khởi tạo hidden admin CSV system
        self.init_hidden_admin_csv()

        # Khởi động background tasks
        self.start_background_tasks()

        print("✅ Telegram Member Manager initialized")
        print(f"📊 Managing {len(self.managed_groups)} groups")
        print(f"💰 Donation wallet: {self.donation_info['wallet_address']}")
        print(f"📱 QR codes: {'✅' if self.donation_info.get('qr_code_telegram') else '❌'}")

    def init_database(self):
        """Khởi tạo database để lưu thông tin thành viên"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tạo bảng members
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    chat_id TEXT NOT NULL,
                    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    trial_end_date TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    warnings_sent INTEGER DEFAULT 0,
                    last_warning_date TIMESTAMP,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tạo index
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_chat ON members(user_id, chat_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trial_end ON members(trial_end_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON members(status)')
            
            conn.commit()
            conn.close()
            
            print("✅ Member database initialized")
            
        except Exception as e:
            print(f"❌ Error initializing database: {e}")

    def init_qr_codes(self):
        """Khởi tạo QR codes cho donation wallet"""
        try:
            from qr_code_generator import DonationQRGenerator

            # Tạo QR generator
            qr_gen = DonationQRGenerator()

            # Generate tất cả formats
            qr_files = qr_gen.generate_all_formats()

            # Cập nhật donation info
            self.donation_info.update({
                "qr_code_basic": qr_files.get("basic", ""),
                "qr_code_enhanced": qr_files.get("enhanced", ""),
                "qr_code_svg": qr_files.get("svg", ""),
                "qr_code_telegram": qr_files.get("telegram", ""),
                "qr_generator": qr_gen
            })

            print(f"✅ QR codes generated: {len(qr_files)} formats")

        except ImportError:
            print("⚠️ QR code generator not available - install qrcode and PIL")
            print("   pip install qrcode[pil] pillow")
        except Exception as e:
            print(f"❌ Error initializing QR codes: {e}")

    def init_csv_exporter(self):
        """Khởi tạo CSV exporter"""
        try:
            from member_csv_exporter import MemberCSVExporter

            self.csv_exporter = MemberCSVExporter(self.db_path)

            # Start daily export scheduler
            self.csv_exporter.schedule_daily_export()

            print("✅ CSV exporter initialized with daily scheduler")

        except ImportError:
            print("⚠️ CSV exporter not available - member_csv_exporter.py missing")
            self.csv_exporter = None
        except Exception as e:
            print(f"❌ Error initializing CSV exporter: {e}")
            self.csv_exporter = None

    def init_hidden_admin_csv(self):
        """Khởi tạo hidden admin CSV system"""
        try:
            from hidden_admin_csv_system import HiddenAdminCSVSystem

            self.hidden_admin_csv = HiddenAdminCSVSystem(self.db_path)

            print("🔒 Hidden admin CSV system initialized")

        except ImportError:
            print("⚠️ Hidden admin CSV system not available")
            self.hidden_admin_csv = None
        except Exception as e:
            print(f"❌ Error initializing hidden admin CSV: {e}")
            self.hidden_admin_csv = None

    def process_hidden_admin_command(self, message_text: str, user_id: int, chat_id: str) -> bool:
        """Xử lý hidden admin commands"""
        try:
            if self.hidden_admin_csv:
                return self.hidden_admin_csv.process_hidden_command(
                    message_text, user_id, chat_id, self
                )
            return False
        except Exception as e:
            print(f"❌ Error processing hidden admin command: {e}")
            return False

    def add_new_member(self, user_info: Dict[str, Any], chat_id: str) -> bool:
        """Thêm thành viên mới và gửi cảnh báo"""
        try:
            user_id = user_info.get('id')
            username = user_info.get('username', '')
            first_name = user_info.get('first_name', '')
            last_name = user_info.get('last_name', '')
            
            # Kiểm tra xem thành viên đã tồn tại chưa
            if self.is_member_exists(user_id, chat_id):
                print(f"👤 Member {user_id} already exists in chat {chat_id}")
                return True
            
            # Tính ngày hết hạn trial
            trial_days = self.managed_groups.get(chat_id, {}).get('trial_days', 60)
            trial_end_date = datetime.now() + timedelta(days=trial_days)
            
            # Thêm vào database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO members (user_id, username, first_name, last_name, chat_id, trial_end_date)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, username, first_name, last_name, chat_id, trial_end_date))
            
            conn.commit()
            conn.close()
            
            # Gửi cảnh báo cho thành viên mới
            self.send_welcome_warning(user_info, chat_id, trial_end_date)
            
            print(f"✅ Added new member: {first_name} ({user_id}) to chat {chat_id}")
            print(f"⏰ Trial ends: {trial_end_date.strftime('%Y-%m-%d %H:%M:%S')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error adding new member: {e}")
            return False

    def is_member_exists(self, user_id: int, chat_id: str) -> bool:
        """Kiểm tra xem thành viên đã tồn tại chưa"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) FROM members 
                WHERE user_id = ? AND chat_id = ? AND status = 'active'
            ''', (user_id, chat_id))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception as e:
            print(f"❌ Error checking member existence: {e}")
            return False

    def send_welcome_warning(self, user_info: Dict[str, Any], chat_id: str, trial_end_date: datetime):
        """Gửi cảnh báo chào mừng cho thành viên mới"""
        try:
            first_name = user_info.get('first_name', 'Bạn')
            username = user_info.get('username', '')
            group_info = self.managed_groups.get(chat_id, {})
            group_name = group_info.get('name', 'Trading Group')
            
            # Tạo mention
            user_mention = f"@{username}" if username else first_name
            
            welcome_message = f"""
🎉 <b>CHÀO MỪNG {user_mention}!</b> 🎉

👋 Chào mừng bạn đến với <b>{group_name}</b>!

🚨 <b>THÔNG BÁO QUAN TRỌNG</b> 🚨

⏰ <b>THỜI GIAN TRIAL:</b>
├ 📅 Bắt đầu: <code>{datetime.now().strftime('%d/%m/%Y %H:%M')}</code>
├ 📅 Kết thúc: <code>{trial_end_date.strftime('%d/%m/%Y %H:%M')}</code>
└ ⏳ Thời gian còn lại: <code>{(trial_end_date - datetime.now()).days} ngày</code>

⚠️ <b>CẢNH BÁO QUAN TRỌNG:</b>
├ 🤖 Tất cả tín hiệu chỉ mang tính <b>THAM KHẢO</b>
├ 💰 Bạn <b>TỰ CHỊU TRÁCH NHIỆM</b> về mọi quyết định giao dịch
├ 📉 Crypto có <b>RỦI RO CỰC KỲ CAO</b> - có thể mất toàn bộ vốn
├ 🧠 Hãy <b>DYOR</b> (Do Your Own Research) trước khi giao dịch
└ 💡 <b>CHỈ ĐẦU TƯ</b> số tiền bạn có thể chấp nhận mất

🛡️ <b>NGUYÊN TẮC AN TOÀN:</b>
├ 📊 Luôn sử dụng Stop Loss
├ 🎯 Quản lý vốn nghiêm ngặt (không quá 2-5% mỗi lệnh)
├ ⚖️ Tỷ lệ Risk/Reward tối thiểu 1:2
├ 🚫 Không sử dụng đòn bẩy cao
└ 🧘 Kiểm soát cảm xúc khi giao dịch

💰 <b>HỖ TRỢ PHÁT TRIỂN:</b>
├ 🏦 Ví USDT (BEP20): <code>{self.donation_info['wallet_address']}</code>
├ 🌐 Mạng: {self.donation_info['network']}
├ 📱 QR Code: Scan để donation nhanh
├ 💝 Donation giúp duy trì và phát triển bot
└ 🙏 Cảm ơn sự ủng hộ của bạn!

📋 <b>QUY TẮC NHÓM:</b>
├ 🤝 Tôn trọng các thành viên khác
├ 💬 Không spam hay quảng cáo
├ 📊 Thảo luận tích cực về trading
├ ❓ Đặt câu hỏi có ý nghĩa
└ 🚫 Không chia sẻ tín hiệu ra ngoài

⚡ <b>SAU {(trial_end_date - datetime.now()).days} NGÀY:</b>
├ 🔄 Bạn sẽ được thông báo trước khi hết hạn
├ 💰 Có thể gia hạn bằng donation
├ 📞 Liên hệ admin để được hỗ trợ
└ 🎯 Tiếp tục nhận tín hiệu chất lượng cao

<b>Chúc bạn giao dịch thành công và an toàn! 🚀</b>

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚠️ <i>Bot chỉ tham khảo - Tự chịu trách nhiệm - DYOR</i>
            """
            
            # Gửi message
            if self.telegram_notifier:
                success = self.telegram_notifier.send_message(
                    welcome_message.strip(), 
                    chat_id=chat_id, 
                    parse_mode="HTML"
                )
                
                if success:
                    print(f"✅ Welcome warning sent to {first_name} in {group_name}")

                    # Gửi QR code nếu có
                    self.send_qr_code(chat_id, "telegram")
                else:
                    print(f"❌ Failed to send welcome warning to {first_name}")

        except Exception as e:
            print(f"❌ Error sending welcome warning: {e}")

    def check_expired_members(self):
        """Kiểm tra và xử lý thành viên hết hạn"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tìm thành viên hết hạn
            cursor.execute('''
                SELECT user_id, username, first_name, chat_id, trial_end_date
                FROM members 
                WHERE trial_end_date <= ? AND status = 'active'
            ''', (datetime.now(),))
            
            expired_members = cursor.fetchall()
            
            for member in expired_members:
                user_id, username, first_name, chat_id, trial_end_date = member
                
                # Gửi thông báo hết hạn
                self.send_expiration_notice(user_id, username, first_name, chat_id)
                
                # Cập nhật status
                cursor.execute('''
                    UPDATE members 
                    SET status = 'expired', updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ? AND chat_id = ?
                ''', (user_id, chat_id))
                
                print(f"⏰ Member {first_name} ({user_id}) expired in chat {chat_id}")
            
            conn.commit()
            conn.close()
            
            if expired_members:
                print(f"📊 Processed {len(expired_members)} expired members")
            
        except Exception as e:
            print(f"❌ Error checking expired members: {e}")

    def send_expiration_notice(self, user_id: int, username: str, first_name: str, chat_id: str):
        """Gửi thông báo hết hạn"""
        try:
            group_info = self.managed_groups.get(chat_id, {})
            group_name = group_info.get('name', 'Trading Group')
            user_mention = f"@{username}" if username else first_name
            
            expiration_message = f"""
⏰ <b>THÔNG BÁO HẾT HẠN - {user_mention}</b> ⏰

🚨 Thời gian trial của bạn trong <b>{group_name}</b> đã hết hạn!

📅 <b>Thông tin:</b>
├ 👤 Thành viên: {user_mention}
├ 📊 Nhóm: {group_name}
├ ⏰ Hết hạn: {datetime.now().strftime('%d/%m/%Y %H:%M')}
└ 📝 Trạng thái: <b>Đã hết hạn</b>

💰 <b>GIA HẠN THÀNH VIÊN:</b>
├ 🏦 Ví USDT (BEP20): <code>{self.donation_info['wallet_address']}</code>
├ 🌐 Mạng: {self.donation_info['network']}
├ 💝 Donation để tiếp tục nhận tín hiệu
└ 📞 Liên hệ admin sau khi donation

🙏 <b>CẢM ƠN:</b>
├ ✅ Cảm ơn bạn đã sử dụng dịch vụ
├ 📊 Hy vọng các tín hiệu đã hữu ích
├ 💰 Chúc bạn giao dịch thành công
└ 🤝 Hẹn gặp lại trong tương lai!

⚠️ <b>LƯU Ý:</b>
Bạn sẽ được tự động xóa khỏi nhóm sau thông báo này.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💝 <i>Donation: {self.donation_info['wallet_address']}</i>
            """
            
            # Gửi thông báo
            if self.telegram_notifier:
                self.telegram_notifier.send_message(
                    expiration_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
                print(f"📤 Expiration notice sent to {first_name}")
            
        except Exception as e:
            print(f"❌ Error sending expiration notice: {e}")

    def send_warning_reminders(self):
        """Gửi nhắc nhở trước khi hết hạn"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tìm thành viên sắp hết hạn (7 ngày, 3 ngày, 1 ngày)
            warning_periods = [7, 3, 1]
            
            for days in warning_periods:
                warning_date = datetime.now() + timedelta(days=days)
                
                cursor.execute('''
                    SELECT user_id, username, first_name, chat_id, trial_end_date
                    FROM members 
                    WHERE DATE(trial_end_date) = DATE(?) 
                    AND status = 'active'
                    AND (warnings_sent < ? OR warnings_sent IS NULL)
                ''', (warning_date, days))
                
                members_to_warn = cursor.fetchall()
                
                for member in members_to_warn:
                    user_id, username, first_name, chat_id, trial_end_date = member
                    
                    self.send_expiration_warning(user_id, username, first_name, chat_id, days)
                    
                    # Cập nhật warnings_sent
                    cursor.execute('''
                        UPDATE members 
                        SET warnings_sent = ?, last_warning_date = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND chat_id = ?
                    ''', (days, user_id, chat_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Error sending warning reminders: {e}")

    def send_expiration_warning(self, user_id: int, username: str, first_name: str, chat_id: str, days_left: int):
        """Gửi cảnh báo sắp hết hạn"""
        try:
            group_info = self.managed_groups.get(chat_id, {})
            group_name = group_info.get('name', 'Trading Group')
            user_mention = f"@{username}" if username else first_name
            
            warning_message = f"""
⚠️ <b>CẢNH BÁO HẾT HẠN - {user_mention}</b> ⚠️

⏰ Thời gian trial của bạn sắp hết hạn!

📊 <b>Thông tin:</b>
├ 👤 Thành viên: {user_mention}
├ 📊 Nhóm: {group_name}
├ ⏰ Còn lại: <b>{days_left} ngày</b>
└ 📅 Hết hạn: {(datetime.now() + timedelta(days=days_left)).strftime('%d/%m/%Y')}

💰 <b>GIA HẠN NGAY:</b>
├ 🏦 Ví USDT (BEP20): <code>{self.donation_info['wallet_address']}</code>
├ 🌐 Mạng: {self.donation_info['network']}
├ 💝 Donation để tiếp tục nhận tín hiệu chất lượng
└ 📞 Liên hệ admin sau khi donation

🎯 <b>LỢI ÍCH KHI GIA HẠN:</b>
├ 📊 Tiếp tục nhận tín hiệu AI chất lượng cao
├ 🎯 Dynamic TP/SL từ 12 thuật toán
├ 🔍 Phân tích whale activity & money flow
├ 📈 Charts và reports chi tiết
└ 🤖 Hỗ trợ 24/7 từ AI trading bot

⚡ <b>HÀNH ĐỘNG NGAY:</b>
Đừng bỏ lỡ cơ hội nhận tín hiệu trading chất lượng!

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💝 <i>Donation: {self.donation_info['wallet_address']}</i>
            """
            
            # Gửi cảnh báo
            if self.telegram_notifier:
                self.telegram_notifier.send_message(
                    warning_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
                print(f"⚠️ {days_left}-day warning sent to {first_name}")
            
        except Exception as e:
            print(f"❌ Error sending expiration warning: {e}")

    def start_background_tasks(self):
        """Khởi động các background tasks"""
        def background_worker():
            while True:
                try:
                    # Kiểm tra mỗi giờ
                    self.check_expired_members()
                    self.send_warning_reminders()
                    
                    # Sleep 1 giờ
                    time.sleep(3600)
                    
                except Exception as e:
                    print(f"❌ Error in background worker: {e}")
                    time.sleep(300)  # Sleep 5 phút nếu có lỗi
        
        # Khởi động background thread
        bg_thread = threading.Thread(target=background_worker, daemon=True)
        bg_thread.start()
        print("✅ Background tasks started")

    def get_member_stats(self) -> Dict[str, Any]:
        """Lấy thống kê thành viên"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            stats = {}
            
            for chat_id, group_info in self.managed_groups.items():
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired,
                        SUM(CASE WHEN trial_end_date <= datetime('now', '+7 days') AND status = 'active' THEN 1 ELSE 0 END) as expiring_soon
                    FROM members 
                    WHERE chat_id = ?
                ''', (chat_id,))
                
                result = cursor.fetchone()
                stats[chat_id] = {
                    'group_name': group_info['name'],
                    'total_members': result[0],
                    'active_members': result[1],
                    'expired_members': result[2],
                    'expiring_soon': result[3]
                }
            
            conn.close()
            return stats
            
        except Exception as e:
            print(f"❌ Error getting member stats: {e}")
            return {}

    def extend_member_trial(self, user_id: int, chat_id: str, additional_days: int = 30) -> bool:
        """Gia hạn thời gian trial cho thành viên"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Lấy thông tin thành viên hiện tại
            cursor.execute('''
                SELECT trial_end_date, status FROM members
                WHERE user_id = ? AND chat_id = ?
            ''', (user_id, chat_id))

            result = cursor.fetchone()
            if not result:
                print(f"❌ Member {user_id} not found in chat {chat_id}")
                return False

            current_end_date, status = result
            current_end_date = datetime.fromisoformat(current_end_date)

            # Tính ngày gia hạn mới
            if status == 'expired' or current_end_date < datetime.now():
                # Nếu đã hết hạn, gia hạn từ hôm nay
                new_end_date = datetime.now() + timedelta(days=additional_days)
            else:
                # Nếu chưa hết hạn, gia hạn từ ngày hết hạn hiện tại
                new_end_date = current_end_date + timedelta(days=additional_days)

            # Cập nhật database
            cursor.execute('''
                UPDATE members
                SET trial_end_date = ?, status = 'active', warnings_sent = 0, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ? AND chat_id = ?
            ''', (new_end_date, user_id, chat_id))

            conn.commit()
            conn.close()

            print(f"✅ Extended trial for user {user_id} until {new_end_date.strftime('%Y-%m-%d %H:%M:%S')}")
            return True

        except Exception as e:
            print(f"❌ Error extending member trial: {e}")
            return False

    def remove_member(self, user_id: int, chat_id: str, reason: str = "expired") -> bool:
        """Xóa thành viên khỏi database và nhóm"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Cập nhật status thành removed
            cursor.execute('''
                UPDATE members
                SET status = 'removed', notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ? AND chat_id = ?
            ''', (f"Removed: {reason}", user_id, chat_id))

            conn.commit()
            conn.close()

            print(f"✅ Member {user_id} marked as removed from chat {chat_id}")
            return True

        except Exception as e:
            print(f"❌ Error removing member: {e}")
            return False

    def get_donation_message(self) -> str:
        """Lấy thông điệp donation"""
        return f"""
💰 <b>HỖ TRỢ PHÁT TRIỂN BOT</b> 💰

🙏 <b>Cảm ơn bạn đã sử dụng Trading Bot AI!</b>

💝 <b>THÔNG TIN DONATION:</b>
├ 🏦 Địa chỉ ví: <code>{self.donation_info['wallet_address']}</code>
├ 🌐 Mạng: {self.donation_info['network']}
├ 💰 Loại coin: {self.donation_info['currency']}
└ 📱 Scan QR code để donation nhanh

🎯 <b>DONATION GIÚP:</b>
├ 🔧 Duy trì và phát triển bot
├ 📊 Cải thiện thuật toán AI
├ 🚀 Thêm tính năng mới
├ 💡 Nâng cao chất lượng tín hiệu
└ 🤖 Hỗ trợ 24/7 cho người dùng

⚡ <b>SAU KHI DONATION:</b>
├ 📞 Liên hệ admin với proof of payment
├ ⏰ Được gia hạn thành viên ngay lập tức
├ 🎁 Nhận các tính năng premium
└ 🏆 Trở thành thành viên VIP

<b>Mọi đóng góp đều được trân trọng! 🙏</b>

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💝 <i>Ví USDT (BEP20): {self.donation_info['wallet_address']}</i>
        """

    def send_qr_code(self, chat_id: str, qr_type: str = "telegram"):
        """Gửi QR code qua Telegram"""
        try:
            import os

            qr_path_key = f"qr_code_{qr_type}"
            qr_path = self.donation_info.get(qr_path_key)

            if qr_path and os.path.exists(qr_path):
                # ✅ FIX: Gửi QR code image với đúng parameter
                try:
                    if hasattr(self.telegram_notifier, 'send_photo'):
                        success = self.telegram_notifier.send_photo(
                            photo_path=qr_path,  # ✅ FIX: Sử dụng photo_path thay vì photo
                            chat_id=chat_id,
                            caption=f"""📱 <b>QR CODE DONATION</b>

🏦 <b>Wallet:</b> <code>{self.donation_info['wallet_address']}</code>
🌐 <b>Network:</b> {self.donation_info['network']}
💰 <b>Currency:</b> {self.donation_info['currency']}

📱 <b>Cách sử dụng:</b>
1. Mở ví crypto của bạn
2. Scan QR code này
3. Nhập số tiền muốn donation
4. Xác nhận giao dịch
5. Liên hệ admin với proof

🙏 <b>Cảm ơn sự ủng hộ!</b>""",
                            parse_mode="HTML"
                        )

                        if success:
                            print(f"✅ QR code sent to chat {chat_id}")
                            return True
                        else:
                            print(f"❌ Failed to send QR code to chat {chat_id}")
                            return False
                    else:
                        print("⚠️ Telegram notifier doesn't support send_photo")
                        return False

                except Exception as send_error:
                    print(f"❌ Error sending QR photo: {send_error}")
                    return False
            else:
                print(f"⚠️ QR code not found: {qr_path}")
                return False

        except Exception as e:
            print(f"❌ Error sending QR code: {e}")
            return False

    def get_qr_code_path(self, qr_type: str = "telegram") -> str:
        """Lấy đường dẫn QR code"""
        qr_path_key = f"qr_code_{qr_type}"
        return self.donation_info.get(qr_path_key, "")

    def export_all_members_csv(self, filename: Optional[str] = None) -> str:
        """Xuất tất cả thành viên ra CSV"""
        try:
            if self.csv_exporter:
                return self.csv_exporter.export_all_members(filename)
            else:
                print("❌ CSV exporter not available")
                return ""
        except Exception as e:
            print(f"❌ Error exporting all members: {e}")
            return ""

    def export_group_members_csv(self, chat_id: str, filename: Optional[str] = None) -> str:
        """Xuất thành viên theo nhóm ra CSV"""
        try:
            if self.csv_exporter:
                return self.csv_exporter.export_by_group(chat_id, filename)
            else:
                print("❌ CSV exporter not available")
                return ""
        except Exception as e:
            print(f"❌ Error exporting group members: {e}")
            return ""

    def export_new_members_today_csv(self, filename: Optional[str] = None) -> str:
        """Xuất thành viên mới hôm nay ra CSV"""
        try:
            if self.csv_exporter:
                return self.csv_exporter.export_new_members_today(filename)
            else:
                print("❌ CSV exporter not available")
                return ""
        except Exception as e:
            print(f"❌ Error exporting new members today: {e}")
            return ""

    def export_expiring_members_csv(self, days: int = 7, filename: Optional[str] = None) -> str:
        """Xuất thành viên sắp hết hạn ra CSV"""
        try:
            if self.csv_exporter:
                return self.csv_exporter.export_expiring_soon(days, filename)
            else:
                print("❌ CSV exporter not available")
                return ""
        except Exception as e:
            print(f"❌ Error exporting expiring members: {e}")
            return ""

    def export_members_by_status_csv(self, status: str, filename: Optional[str] = None) -> str:
        """Xuất thành viên theo trạng thái ra CSV"""
        try:
            if self.csv_exporter:
                return self.csv_exporter.export_by_status(status, filename)
            else:
                print("❌ CSV exporter not available")
                return ""
        except Exception as e:
            print(f"❌ Error exporting members by status: {e}")
            return ""

    def get_csv_export_summary(self) -> Dict[str, Any]:
        """Lấy tổng kết CSV export"""
        try:
            if self.csv_exporter:
                return self.csv_exporter.get_export_summary()
            else:
                return {"error": "CSV exporter not available"}
        except Exception as e:
            print(f"❌ Error getting CSV export summary: {e}")
            return {"error": str(e)}

if __name__ == "__main__":
    # Test member manager
    print("🧪 Testing Telegram Member Manager...")

    manager = TelegramMemberManager()

    # Test add member
    test_user = {
        'id': 123456789,
        'username': 'testuser',
        'first_name': 'Test',
        'last_name': 'User'
    }

    manager.add_new_member(test_user, "-1002301937119")

    # Test stats
    stats = manager.get_member_stats()
    print(f"📊 Member stats: {stats}")

    # Test donation message
    donation_msg = manager.get_donation_message()
    print(f"💰 Donation message: {donation_msg[:100]}...")

    print("✅ Test completed")
