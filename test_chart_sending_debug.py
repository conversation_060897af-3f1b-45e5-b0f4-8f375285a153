#!/usr/bin/env python3
"""
🧪 TEST CHART SENDING DEBUG
===========================

Test để debug tại sao charts không được gửi kèm signals.
"""

import os
import sys
import traceback
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_sending_debug():
    """Test chart sending debug."""
    print("🧪 TESTING CHART SENDING DEBUG")
    print("=" * 50)
    
    try:
        # Test chart generation directly
        print("📊 Testing chart generation directly...")
        import chart_generator
        
        # Initialize chart generator
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=False,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=100
        )
        
        # Create sample data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=100), periods=100, freq='1H')
        sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': [50000 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'high': [50100 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'low': [49900 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'close': [50050 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'volume': [1000 + i * 5 + np.random.randn() * 100 for i in range(100)]
        })
        sample_data.set_index('timestamp', inplace=True)
        
        coin = "BTCUSDT"
        current_price = 51000.0
        
        # Test Fibonacci chart
        print("🌀 Testing Fibonacci chart generation...")
        fibonacci_data = {
            "status": "success",
            "retracement_levels": [
                {"ratio": 0.236, "price": current_price * 0.976},
                {"ratio": 0.382, "price": current_price * 0.962},
                {"ratio": 0.618, "price": current_price * 0.938}
            ],
            "extension_levels": [
                {"ratio": 1.618, "price": current_price * 1.062},
                {"ratio": 2.618, "price": current_price * 1.124}
            ],
            "signals": {"overall_signal": "SELL", "confidence": 0.8}
        }
        
        chart_path = chart_gen.generate_fibonacci_chart(coin, fibonacci_data, sample_data, current_price)
        print(f"📊 Fibonacci chart result: {'✅ SUCCESS' if chart_path else '❌ FAILED'}")
        if chart_path:
            print(f"    📁 Chart path: {chart_path}")
            print(f"    📁 File exists: {os.path.exists(chart_path)}")
            print(f"    📁 File size: {os.path.getsize(chart_path) if os.path.exists(chart_path) else 0} bytes")
        
        # Test Volume Profile chart
        print("\n📊 Testing Volume Profile chart generation...")
        volume_data = {
            "vpoc": {"price": current_price, "volume": 1000},
            "signals": {"primary_signal": "SELL", "confidence": 0.75},
            "value_area": {"high": current_price * 1.02, "low": current_price * 0.98},
            "distribution_metrics": {"concentration_ratio": 0.8, "profile_quality": "HIGH"},
            "analysis_quality": "HIGH"
        }
        
        chart_path2 = chart_gen.generate_volume_profile_chart(coin, volume_data, sample_data, current_price)
        print(f"📊 Volume Profile chart result: {'✅ SUCCESS' if chart_path2 else '❌ FAILED'}")
        if chart_path2:
            print(f"    📁 Chart path: {chart_path2}")
            print(f"    📁 File exists: {os.path.exists(chart_path2)}")
            print(f"    📁 File size: {os.path.getsize(chart_path2) if os.path.exists(chart_path2) else 0} bytes")
        
        # Test Point & Figure chart
        print("\n📈 Testing Point & Figure chart generation...")
        pf_data = {
            "signals": {"primary_signal": "SELL", "confidence": 0.8},
            "trend_analysis": {"trend": "DOWNTREND", "trend_strength": 0.9},
            "pattern_recognition": {"pattern_type": "BEARISH", "pattern_strength": 0.85},
            "price_objectives": {"upside_target": current_price * 1.05, "downside_target": current_price * 0.95},
            "box_size": current_price * 0.01,
            "reversal_amount": 3,
            "current_column_type": "O",
            "analysis_quality": "HIGH"
        }
        
        chart_path3 = chart_gen.generate_point_figure_chart(coin, pf_data, sample_data, current_price)
        print(f"📈 Point & Figure chart result: {'✅ SUCCESS' if chart_path3 else '❌ FAILED'}")
        if chart_path3:
            print(f"    📁 Chart path: {chart_path3}")
            print(f"    📁 File exists: {os.path.exists(chart_path3)}")
            print(f"    📁 File size: {os.path.getsize(chart_path3) if os.path.exists(chart_path3) else 0} bytes")
        
        # Test AI Analysis chart
        print("\n🤖 Testing AI Analysis chart generation...")
        ai_data = {
            "ensemble_signal": "SELL",
            "ensemble_confidence": 0.85,
            "model_results": {
                "XGBoost": {"prediction": "SELL", "confidence": 0.8},
                "RandomForest": {"prediction": "SELL", "confidence": 0.9},
                "LSTM": {"prediction": "SELL", "confidence": 0.7}
            },
            "working_models": 3,
            "total_models": 11,
            "prediction_quality": "HIGH"
        }
        
        chart_path4 = chart_gen.generate_ai_analysis_chart(coin, ai_data, sample_data, current_price)
        print(f"🤖 AI Analysis chart result: {'✅ SUCCESS' if chart_path4 else '❌ FAILED'}")
        if chart_path4:
            print(f"    📁 Chart path: {chart_path4}")
            print(f"    📁 File exists: {os.path.exists(chart_path4)}")
            print(f"    📁 File size: {os.path.getsize(chart_path4) if os.path.exists(chart_path4) else 0} bytes")
        
        # Test Fourier chart
        print("\n🌊 Testing Fourier chart generation...")
        fourier_data = {
            "status": "success",
            "price_cycles": [{"frequency": 0.1, "amplitude": 100}],
            "volume_cycles": [{"frequency": 0.2, "amplitude": 50}],
            "dominant_cycle": 10.5,
            "signals": {"overall_signal": "SELL", "confidence": 0.75},
            "trend_component": 0.05,
            "seasonal_strength": 0.7,
            "cycle_amplitude": 0.02,
            "market_regime": {"regime_type": "trending"},
            "analysis_metadata": {"analysis_quality": "high"}
        }
        
        chart_path5 = chart_gen.generate_fourier_chart(coin, fourier_data, sample_data, current_price)
        print(f"🌊 Fourier chart result: {'✅ SUCCESS' if chart_path5 else '❌ FAILED'}")
        if chart_path5:
            print(f"    📁 Chart path: {chart_path5}")
            print(f"    📁 File exists: {os.path.exists(chart_path5)}")
            print(f"    📁 File size: {os.path.getsize(chart_path5) if os.path.exists(chart_path5) else 0} bytes")
        
        # Summary
        charts_generated = sum([
            bool(chart_path), bool(chart_path2), bool(chart_path3), 
            bool(chart_path4), bool(chart_path5)
        ])
        
        print(f"\n🎯 CHART GENERATION SUMMARY:")
        print(f"📊 Charts generated: {charts_generated}/5")
        print(f"📊 Success rate: {charts_generated/5*100:.1f}%")
        
        if charts_generated == 5:
            print("✅ ALL CHARTS GENERATED SUCCESSFULLY!")
            print("The issue is likely with signal sending or duplicate detection, not chart generation.")
        else:
            print("❌ SOME CHARTS FAILED TO GENERATE!")
            print("The issue is with chart generation itself.")
        
        return charts_generated == 5
        
    except Exception as e:
        print(f"❌ Chart sending debug failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_chart_sending_debug()
