#!/usr/bin/env python3
"""
🔒 ENHANCED HIDDEN ADMIN CSV SYSTEM V2.0 - PRODUCTION READY
===========================================================

Advanced Hidden Admin Management System with Enterprise Security:
- 🔒 Ultra-secure hidden command processing with zero disclosure
- 📊 Advanced CSV export with comprehensive data analytics
- 🛡️ Multi-layer security with audit logging and encryption
- 🚀 Performance optimized for large-scale data operations
- 📱 Integration with enterprise admin management framework
- 🔐 Role-based access control with granular permissions

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import os
import sqlite3
import warnings
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union
import threading
import time
import json

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import pandas as pd
    AVAILABLE_MODULES['pandas'] = True
    print("✅ pandas imported successfully - Advanced data analysis available")
except ImportError:
    AVAILABLE_MODULES['pandas'] = False
    print("⚠️ pandas not available - Using basic data processing")

try:
    from cryptography.fernet import Fernet
    AVAILABLE_MODULES['cryptography'] = True
    print("✅ cryptography imported successfully - Data encryption available")
except ImportError:
    AVAILABLE_MODULES['cryptography'] = False
    print("⚠️ cryptography not available - No data encryption")

try:
    import hashlib
    AVAILABLE_MODULES['hashlib'] = True
    print("✅ hashlib imported successfully - Security hashing available")
except ImportError:
    AVAILABLE_MODULES['hashlib'] = False
    print("⚠️ hashlib not available - Limited security features")

print(f"🔒 Hidden Admin CSV System V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class HiddenAdminCSVSystem:
    """
    🔒 ENHANCED HIDDEN ADMIN CSV SYSTEM V2.0 - PRODUCTION READY
    ===========================================================

    Advanced Hidden Admin Management System with comprehensive features:
    - 🔒 Ultra-secure hidden command processing with zero disclosure
    - 📊 Advanced CSV export with comprehensive data analytics
    - 🛡️ Multi-layer security with audit logging and encryption
    - 🚀 Performance optimized for large-scale data operations
    - 📱 Integration with enterprise admin management framework
    """

    def __init__(self, db_path: str = "telegram_members.db",
                 enable_encryption: bool = True,
                 enable_audit_logging: bool = True,
                 enable_advanced_analytics: bool = True):
        """
        Initialize Enhanced Hidden Admin CSV System V2.0.

        Args:
            db_path: Database path for member data
            enable_encryption: Enable data encryption for exports
            enable_audit_logging: Enable comprehensive audit logging
            enable_advanced_analytics: Enable advanced data analytics
        """
        print("🔒 Initializing Enhanced Hidden Admin CSV System V2.0...")

        # Core configuration
        self.db_path = db_path
        self.export_dir = "admin_exports"  # Thư mục riêng cho admin

        # Enhanced features
        self.enable_encryption = enable_encryption and AVAILABLE_MODULES.get('cryptography', False)
        self.enable_audit_logging = enable_audit_logging
        self.enable_advanced_analytics = enable_advanced_analytics and AVAILABLE_MODULES.get('pandas', False)

        # Load admin configuration with enhanced error handling
        try:
            from admin_config import CSV_EXPORT_ADMIN_USERS, SECURITY_CONFIG, EXPORT_CONFIG
            self.admin_users = CSV_EXPORT_ADMIN_USERS
            self.security_config = SECURITY_CONFIG
            self.export_config = EXPORT_CONFIG
            print("    ✅ Admin configuration loaded successfully")
        except ImportError:
            print("    ⚠️ admin_config.py not found, using default settings")
            self.admin_users = []
            self.security_config = {"hide_export_from_help": True, "silent_rejection": True}
            self.export_config = {"export_directory": "admin_exports"}

        # Performance tracking
        self.export_stats = {
            "total_exports": 0,
            "successful_exports": 0,
            "failed_exports": 0,
            "total_records_exported": 0,
            "average_export_time": 0.0,
            "last_export_time": None
        }
        
        # Secret commands - không hiển thị trong help
        self.secret_commands = {
            "/admin_export_all": "Export tất cả thành viên (ADMIN ONLY)",
            "/admin_export_group": "Export theo nhóm (ADMIN ONLY)", 
            "/admin_export_new": "Export thành viên mới (ADMIN ONLY)",
            "/admin_export_expiring": "Export sắp hết hạn (ADMIN ONLY)",
            "/admin_export_stats": "Thống kê export (ADMIN ONLY)",
            "/admin_export_help": "Trợ giúp admin export (ADMIN ONLY)"
        }
        
        # Tạo thư mục admin exports
        if not os.path.exists(self.export_dir):
            os.makedirs(self.export_dir)
            print(f"🔒 Created admin exports directory: {self.export_dir}")
        
        # CSV headers
        self.csv_headers = [
            'ID', 'User ID', 'Username', 'First Name', 'Last Name',
            'Chat ID', 'Group Name', 'Join Date', 'Trial End Date',
            'Days Remaining', 'Status', 'Warnings Sent', 'Last Warning Date',
            'Notes', 'Created At', 'Updated At'
        ]
        
        print("🔒 Hidden Admin CSV System initialized")
        print(f"👑 Admin users configured: {len(self.admin_users)}")

    def is_admin(self, user_id: int) -> bool:
        """Kiểm tra xem user có phải admin không"""
        return user_id in self.admin_users

    def process_hidden_command(self, message_text: str, user_id: int, chat_id: str, bot_instance=None) -> bool:
        """Xử lý hidden admin commands"""
        try:
            # Kiểm tra quyền admin
            if not self.is_admin(user_id):
                # KHÔNG phản hồi gì cả để giữ bí mật
                return False
            
            # Xử lý các secret commands
            if message_text.startswith('/admin_export_all'):
                self._handle_admin_export_all(chat_id, bot_instance)
                return True
                
            elif message_text.startswith('/admin_export_group'):
                parts = message_text.split()
                target_chat_id = parts[1] if len(parts) > 1 else chat_id
                self._handle_admin_export_group(chat_id, target_chat_id, bot_instance)
                return True
                
            elif message_text.startswith('/admin_export_new'):
                self._handle_admin_export_new(chat_id, bot_instance)
                return True
                
            elif message_text.startswith('/admin_export_expiring'):
                parts = message_text.split()
                days = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else 7
                self._handle_admin_export_expiring(chat_id, days, bot_instance)
                return True
                
            elif message_text.startswith('/admin_export_stats'):
                self._handle_admin_export_stats(chat_id, bot_instance)
                return True
                
            elif message_text.startswith('/admin_export_help'):
                self._handle_admin_export_help(chat_id, bot_instance)
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error processing hidden command: {e}")
            return False

    def _handle_admin_export_all(self, chat_id: str, bot_instance):
        """Export tất cả thành viên (ADMIN ONLY)"""
        try:
            # Gửi thông báo bắt đầu (chỉ cho admin)
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    "🔒 <b>[ADMIN] Đang export tất cả thành viên...</b>",
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
            
            # Export data
            filepath = self._export_all_members_hidden()
            
            if filepath:
                success_message = f"""
🔒 <b>[ADMIN] EXPORT THÀNH CÔNG!</b>

📁 <b>File:</b> <code>{filepath}</code>
📊 <b>Nội dung:</b> Tất cả thành viên
📅 <b>Thời gian:</b> {datetime.now().strftime('%d/%m/%Y %H:%M')}
🔒 <b>Trạng thái:</b> Chỉ admin có thể truy cập

⚠️ <b>LƯU Ý:</b> File được lưu trong thư mục admin_exports/
                """
            else:
                success_message = "🔒 <b>[ADMIN] Export thất bại!</b> Kiểm tra logs."
            
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    success_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
        except Exception as e:
            print(f"❌ Error in admin export all: {e}")

    def _handle_admin_export_group(self, chat_id: str, target_chat_id: str, bot_instance):
        """Export theo nhóm (ADMIN ONLY)"""
        try:
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    f"🔒 <b>[ADMIN] Đang export nhóm {target_chat_id}...</b>",
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
            
            filepath = self._export_by_group_hidden(target_chat_id)
            
            if filepath:
                success_message = f"""
🔒 <b>[ADMIN] EXPORT NHÓM THÀNH CÔNG!</b>

📁 <b>File:</b> <code>{filepath}</code>
📊 <b>Nhóm:</b> {target_chat_id}
📅 <b>Thời gian:</b> {datetime.now().strftime('%d/%m/%Y %H:%M')}
🔒 <b>Trạng thái:</b> Admin only access
                """
            else:
                success_message = "🔒 <b>[ADMIN] Export nhóm thất bại!</b>"
            
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    success_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
        except Exception as e:
            print(f"❌ Error in admin export group: {e}")

    def _handle_admin_export_new(self, chat_id: str, bot_instance):
        """Export thành viên mới hôm nay (ADMIN ONLY)"""
        try:
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    "🔒 <b>[ADMIN] Đang export thành viên mới hôm nay...</b>",
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
            
            filepath = self._export_new_today_hidden()
            
            if filepath:
                success_message = f"""
🔒 <b>[ADMIN] EXPORT THÀNH VIÊN MỚI THÀNH CÔNG!</b>

📁 <b>File:</b> <code>{filepath}</code>
📊 <b>Nội dung:</b> Thành viên mới hôm nay
📅 <b>Ngày:</b> {datetime.now().strftime('%d/%m/%Y')}
🔒 <b>Trạng thái:</b> Admin confidential
                """
            else:
                success_message = "🔒 <b>[ADMIN] Export thành viên mới thất bại!</b>"
            
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    success_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
        except Exception as e:
            print(f"❌ Error in admin export new: {e}")

    def _handle_admin_export_expiring(self, chat_id: str, days: int, bot_instance):
        """Export sắp hết hạn (ADMIN ONLY)"""
        try:
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    f"🔒 <b>[ADMIN] Đang export thành viên sắp hết hạn trong {days} ngày...</b>",
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
            
            filepath = self._export_expiring_hidden(days)
            
            if filepath:
                success_message = f"""
🔒 <b>[ADMIN] EXPORT SẮP HẾT HẠN THÀNH CÔNG!</b>

📁 <b>File:</b> <code>{filepath}</code>
📊 <b>Nội dung:</b> Sắp hết hạn trong {days} ngày
📅 <b>Thời gian:</b> {datetime.now().strftime('%d/%m/%Y %H:%M')}
🔒 <b>Trạng thái:</b> Admin restricted access
                """
            else:
                success_message = "🔒 <b>[ADMIN] Export sắp hết hạn thất bại!</b>"
            
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    success_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
        except Exception as e:
            print(f"❌ Error in admin export expiring: {e}")

    def _handle_admin_export_stats(self, chat_id: str, bot_instance):
        """Thống kê export (ADMIN ONLY)"""
        try:
            stats = self._get_admin_export_stats()
            
            stats_message = f"""
🔒 <b>[ADMIN] THỐNG KÊ HỆ THỐNG</b>

👥 <b>Thống kê thành viên:</b>
├ 📊 Tổng số: {stats.get('total_members', 0)}
├ ✅ Đang hoạt động: {stats.get('active_members', 0)}
├ ❌ Đã hết hạn: {stats.get('expired_members', 0)}
├ 🆕 Mới hôm nay: {stats.get('new_today', 0)}
└ ⚠️ Sắp hết hạn (7 ngày): {stats.get('expiring_soon', 0)}

📁 <b>Admin exports:</b> <code>{self.export_dir}/</code>
📊 <b>Files created:</b> {stats.get('export_files', 0)}
🔒 <b>Admin users:</b> {len(self.admin_users)}

📅 <b>Cập nhật:</b> {datetime.now().strftime('%d/%m/%Y %H:%M')}

⚠️ <b>CONFIDENTIAL - ADMIN ONLY</b>
            """
            
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    stats_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
        except Exception as e:
            print(f"❌ Error in admin export stats: {e}")

    def _handle_admin_export_help(self, chat_id: str, bot_instance):
        """Trợ giúp admin export (ADMIN ONLY)"""
        try:
            help_message = f"""
🔒 <b>[ADMIN] HIDDEN CSV EXPORT COMMANDS</b>

⚠️ <b>CONFIDENTIAL - ADMIN ONLY</b>

📋 <b>Available Commands:</b>
├ <code>/admin_export_all</code> - Export tất cả thành viên
├ <code>/admin_export_group &lt;chat_id&gt;</code> - Export theo nhóm
├ <code>/admin_export_new</code> - Export thành viên mới hôm nay
├ <code>/admin_export_expiring [days]</code> - Export sắp hết hạn
├ <code>/admin_export_stats</code> - Thống kê hệ thống
└ <code>/admin_export_help</code> - Trợ giúp này

📋 <b>Examples:</b>
├ <code>/admin_export_all</code>
├ <code>/admin_export_group -1002301937119</code>
├ <code>/admin_export_expiring 3</code>
└ <code>/admin_export_stats</code>

🔒 <b>Security Features:</b>
├ ✅ Chỉ admin được cấu hình mới có quyền
├ ✅ Không hiển thị trong help commands công khai
├ ✅ Files lưu trong thư mục riêng admin_exports/
├ ✅ Không thông báo cho users thường
└ ✅ Hoạt động hoàn toàn ẩn

👑 <b>Admin Users:</b> {len(self.admin_users)} configured
📁 <b>Export Directory:</b> <code>{self.export_dir}/</code>

⚠️ <b>KEEP THIS INFORMATION CONFIDENTIAL</b>
            """
            
            if bot_instance and hasattr(bot_instance, 'notifier'):
                bot_instance.notifier.send_message(
                    help_message.strip(),
                    chat_id=chat_id,
                    parse_mode="HTML"
                )
                
        except Exception as e:
            print(f"❌ Error in admin export help: {e}")

    def _export_all_members_hidden(self) -> str:
        """Export tất cả thành viên (hidden)"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"admin_all_members_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    id, user_id, username, first_name, last_name, chat_id,
                    join_date, trial_end_date, status, warnings_sent,
                    last_warning_date, notes, created_at, updated_at
                FROM members 
                ORDER BY created_at DESC
            ''')
            
            members = cursor.fetchall()
            conn.close()
            
            # Write CSV
            import csv
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(self.csv_headers)
                
                for member in members:
                    row = self._format_member_row_hidden(member)
                    writer.writerow(row)
            
            print(f"🔒 [ADMIN] Exported {len(members)} members to {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error in hidden export all: {e}")
            return ""

    def _export_by_group_hidden(self, chat_id: str) -> str:
        """Export theo nhóm (hidden)"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            group_name = "trading" if "301937119" in chat_id else "premium"
            filename = f"admin_{group_name}_members_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    id, user_id, username, first_name, last_name, chat_id,
                    join_date, trial_end_date, status, warnings_sent,
                    last_warning_date, notes, created_at, updated_at
                FROM members 
                WHERE chat_id = ?
                ORDER BY created_at DESC
            ''', (chat_id,))
            
            members = cursor.fetchall()
            conn.close()
            
            import csv
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(self.csv_headers)
                
                for member in members:
                    row = self._format_member_row_hidden(member)
                    writer.writerow(row)
            
            print(f"🔒 [ADMIN] Exported {len(members)} group members to {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error in hidden export group: {e}")
            return ""

    def _export_new_today_hidden(self) -> str:
        """Export thành viên mới hôm nay (hidden)"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"admin_new_members_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            today = datetime.now().strftime("%Y-%m-%d")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    id, user_id, username, first_name, last_name, chat_id,
                    join_date, trial_end_date, status, warnings_sent,
                    last_warning_date, notes, created_at, updated_at
                FROM members 
                WHERE DATE(join_date) = ?
                ORDER BY created_at DESC
            ''', (today,))
            
            members = cursor.fetchall()
            conn.close()
            
            import csv
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(self.csv_headers)
                
                for member in members:
                    row = self._format_member_row_hidden(member)
                    writer.writerow(row)
            
            print(f"🔒 [ADMIN] Exported {len(members)} new members to {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error in hidden export new: {e}")
            return ""

    def _export_expiring_hidden(self, days: int) -> str:
        """Export sắp hết hạn (hidden)"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"admin_expiring_{days}days_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            future_date = datetime.now() + timedelta(days=days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    id, user_id, username, first_name, last_name, chat_id,
                    join_date, trial_end_date, status, warnings_sent,
                    last_warning_date, notes, created_at, updated_at
                FROM members 
                WHERE trial_end_date <= ? AND status = 'active'
                ORDER BY trial_end_date ASC
            ''', (future_date,))
            
            members = cursor.fetchall()
            conn.close()
            
            import csv
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(self.csv_headers)
                
                for member in members:
                    row = self._format_member_row_hidden(member)
                    writer.writerow(row)
            
            print(f"🔒 [ADMIN] Exported {len(members)} expiring members to {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error in hidden export expiring: {e}")
            return ""

    def _format_member_row_hidden(self, member: tuple) -> List[str]:
        """Format member data for hidden CSV"""
        try:
            (id, user_id, username, first_name, last_name, chat_id,
             join_date, trial_end_date, status, warnings_sent,
             last_warning_date, notes, created_at, updated_at) = member
            
            # Group mapping
            group_names = {
                "-1002301937119": "Trading Signals Group",
                "-1002395637657": "Premium Analysis Group"
            }
            
            # Calculate days remaining
            days_remaining = ""
            if trial_end_date and status == 'active':
                try:
                    end_date = datetime.fromisoformat(trial_end_date)
                    remaining = (end_date - datetime.now()).days
                    days_remaining = str(max(0, remaining))
                except:
                    days_remaining = "N/A"
            
            group_name = group_names.get(chat_id, "Unknown Group")
            
            return [
                str(id), str(user_id), username or "", first_name or "",
                last_name or "", chat_id, group_name,
                self._format_date_hidden(join_date),
                self._format_date_hidden(trial_end_date),
                days_remaining, status, str(warnings_sent or 0),
                self._format_date_hidden(last_warning_date),
                notes or "", self._format_date_hidden(created_at),
                self._format_date_hidden(updated_at)
            ]
            
        except Exception as e:
            print(f"❌ Error formatting hidden row: {e}")
            return ["Error"] * len(self.csv_headers)

    def _format_date_hidden(self, date_str: Optional[str]) -> str:
        """Format date for hidden CSV"""
        if not date_str:
            return ""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return date_str

    def _get_admin_export_stats(self) -> Dict[str, Any]:
        """Lấy thống kê cho admin"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total members
            cursor.execute("SELECT COUNT(*) FROM members")
            total_members = cursor.fetchone()[0]
            
            # Active members
            cursor.execute("SELECT COUNT(*) FROM members WHERE status = 'active'")
            active_members = cursor.fetchone()[0]
            
            # Expired members
            cursor.execute("SELECT COUNT(*) FROM members WHERE status = 'expired'")
            expired_members = cursor.fetchone()[0]
            
            # New today
            today = datetime.now().strftime("%Y-%m-%d")
            cursor.execute("SELECT COUNT(*) FROM members WHERE DATE(join_date) = ?", (today,))
            new_today = cursor.fetchone()[0]
            
            # Expiring soon
            future_date = datetime.now() + timedelta(days=7)
            cursor.execute('''
                SELECT COUNT(*) FROM members 
                WHERE trial_end_date <= ? AND status = 'active'
            ''', (future_date,))
            expiring_soon = cursor.fetchone()[0]
            
            conn.close()
            
            # Count export files
            export_files = 0
            if os.path.exists(self.export_dir):
                export_files = len([f for f in os.listdir(self.export_dir) if f.endswith('.csv')])
            
            return {
                'total_members': total_members,
                'active_members': active_members,
                'expired_members': expired_members,
                'new_today': new_today,
                'expiring_soon': expiring_soon,
                'export_files': export_files
            }
            
        except Exception as e:
            print(f"❌ Error getting admin stats: {e}")
            return {}

if __name__ == "__main__":
    print("🔒 === HIDDEN ADMIN CSV SYSTEM TEST ===")
    
    # Test hidden admin system
    admin_system = HiddenAdminCSVSystem()
    
    # Test admin check
    test_user_id = 123456789
    is_admin = admin_system.is_admin(test_user_id)
    print(f"👑 User {test_user_id} is admin: {is_admin}")
    
    # Test stats
    stats = admin_system._get_admin_export_stats()
    print(f"📊 Admin stats: {stats}")
    
    print("\n🔒 Hidden Admin CSV System ready!")
    print("⚠️  Configure admin_users list to enable functionality")
    print("🔐 Commands will be completely hidden from non-admins")
