#!/usr/bin/env python3
"""
🧪 TEST TELEGRAM ADDRESS FIX
Test để kiểm tra hệ thống có đọc đúng địa chỉ Telegram từ .env file không
"""

import sys
import os
import re
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_telegram_address_configuration():
    """Test Telegram address configuration from .env"""
    print("🧪 === TESTING TELEGRAM ADDRESS CONFIGURATION ===")
    print(f"⏰ Test started at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Test 1: Check .env file configuration
    print(f"\n🧪 TEST 1: Checking .env file configuration")
    
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Expected Telegram addresses from .env
        expected_addresses = {
            "TELEGRAM_CHAT_ID": "-1002301937119",
            "TELEGRAM_CONSENSUS_CHAT_ID": "-1002301937119", 
            "TELEGRAM_FIBONACCI_ZIGZAG_FOURIER": "-1002395637657",
            "TELEGRAM_VOLUME_PROFILE_POINT_FIGURE": "-1002395637657",
            "TELEGRAM_AI_ANALYSIS": "-1002395637657",
            "TELEGRAM_PUMP_DETECTION": "-1002301937119",
            "TELEGRAM_DUMP_DETECTION": "-1002301937119",
            "TELEGRAM_ORDERBOOK_ANALYSIS": "-1002395637657",
            "TELEGRAM_CONSENSUS_SIGNALS": "-1002301937119",
            "TELEGRAM_MONEY_FLOW": "-1002301937119",
            "TELEGRAM_WHALE_DETECTION": "-1002301937119",
            "TELEGRAM_MANIPULATION_DETECTION": "-1002301937119",
            "TELEGRAM_CROSS_ASSET": "-1002301937119"
        }
        
        print(f"  📋 Checking {len(expected_addresses)} Telegram addresses...")
        
        correct_addresses = 0
        for env_var, expected_value in expected_addresses.items():
            actual_value = os.getenv(env_var)
            if actual_value == expected_value:
                print(f"  ✅ {env_var}: {actual_value}")
                correct_addresses += 1
            else:
                print(f"  ❌ {env_var}: Expected {expected_value}, got {actual_value}")
        
        if correct_addresses == len(expected_addresses):
            print(f"  ✅ All {correct_addresses} addresses configured correctly")
        else:
            print(f"  ⚠️ {correct_addresses}/{len(expected_addresses)} addresses correct")
        
    except Exception as e:
        print(f"❌ Error checking .env configuration: {e}")
        return False
    
    # Test 2: Check for hardcoded addresses in main_bot.py
    print(f"\n🧪 TEST 2: Checking for hardcoded addresses in main_bot.py")
    
    try:
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            main_bot_content = f.read()
        
        # Look for hardcoded addresses (but exclude the correct fallback values)
        hardcoded_pattern = r'-100[0-9]{10,}'
        hardcoded_matches = re.findall(hardcoded_pattern, main_bot_content)
        
        # Filter out acceptable fallback values in os.getenv() calls
        acceptable_fallbacks = ["-1002301937119", "-1002395637657"]
        
        # Check each match to see if it's in an acceptable context
        problematic_addresses = []
        for match in hardcoded_matches:
            # Check if this address is used as a fallback in os.getenv()
            if match not in acceptable_fallbacks:
                problematic_addresses.append(match)
            else:
                # Check if it's used outside of os.getenv() context
                lines_with_match = [line for line in main_bot_content.split('\n') if match in line]
                for line in lines_with_match:
                    if 'os.getenv' not in line and 'TELEGRAM_SPECIALIZED_CHATS.get' not in line:
                        problematic_addresses.append(match)
        
        if not problematic_addresses:
            print(f"  ✅ No problematic hardcoded addresses found in main_bot.py")
        else:
            print(f"  ❌ Found {len(problematic_addresses)} problematic hardcoded addresses:")
            for addr in set(problematic_addresses):
                print(f"    - {addr}")
        
    except Exception as e:
        print(f"❌ Error checking main_bot.py: {e}")
        return False
    
    # Test 3: Check for hardcoded addresses in chart_generator.py
    print(f"\n🧪 TEST 3: Checking for hardcoded addresses in chart_generator.py")
    
    try:
        with open('chart_generator.py', 'r', encoding='utf-8') as f:
            chart_generator_content = f.read()
        
        # Look for hardcoded addresses
        hardcoded_matches = re.findall(hardcoded_pattern, chart_generator_content)
        
        # Check each match to see if it's in an acceptable context (os.getenv fallback)
        problematic_addresses = []
        for match in hardcoded_matches:
            lines_with_match = [line for line in chart_generator_content.split('\n') if match in line]
            for line in lines_with_match:
                if 'os.getenv' not in line:
                    problematic_addresses.append(match)
        
        if not problematic_addresses:
            print(f"  ✅ No problematic hardcoded addresses found in chart_generator.py")
        else:
            print(f"  ❌ Found {len(problematic_addresses)} problematic hardcoded addresses:")
            for addr in set(problematic_addresses):
                print(f"    - {addr}")
        
    except Exception as e:
        print(f"❌ Error checking chart_generator.py: {e}")
        return False
    
    # Test 4: Check telegram_notifier.py configuration
    print(f"\n🧪 TEST 4: Checking telegram_notifier.py configuration")
    
    try:
        with open('telegram_notifier.py', 'r', encoding='utf-8') as f:
            notifier_content = f.read()
        
        # Check if it reads from .env properly
        env_usage_checks = [
            'os.getenv("TELEGRAM_FIBONACCI_ZIGZAG_FOURIER"',
            'os.getenv("TELEGRAM_VOLUME_PROFILE_POINT_FIGURE"',
            'os.getenv("TELEGRAM_AI_ANALYSIS"',
            'os.getenv("TELEGRAM_PUMP_DETECTION"',
            'os.getenv("TELEGRAM_DUMP_DETECTION"'
        ]
        
        found_env_usage = 0
        for check in env_usage_checks:
            if check in notifier_content:
                found_env_usage += 1
                print(f"  ✅ Found: {check}")
            else:
                print(f"  ❌ Missing: {check}")
        
        if found_env_usage >= 3:
            print(f"  ✅ telegram_notifier.py properly reads from .env ({found_env_usage}/{len(env_usage_checks)})")
        else:
            print(f"  ⚠️ telegram_notifier.py may not read all addresses from .env ({found_env_usage}/{len(env_usage_checks)})")
        
    except Exception as e:
        print(f"❌ Error checking telegram_notifier.py: {e}")
        return False
    
    # Test 5: Summary and recommendations
    print(f"\n📊 TELEGRAM ADDRESS FIX SUMMARY:")
    
    total_score = 0
    max_score = 4
    
    if correct_addresses == len(expected_addresses):
        total_score += 1
        print(f"  ✅ .env configuration: PERFECT")
    else:
        print(f"  ⚠️ .env configuration: NEEDS ATTENTION")
    
    if not problematic_addresses:
        total_score += 1
        print(f"  ✅ main_bot.py: NO HARDCODED ADDRESSES")
    else:
        print(f"  ❌ main_bot.py: HAS HARDCODED ADDRESSES")
    
    if not problematic_addresses:  # Same check for chart_generator
        total_score += 1
        print(f"  ✅ chart_generator.py: NO HARDCODED ADDRESSES")
    else:
        print(f"  ❌ chart_generator.py: HAS HARDCODED ADDRESSES")
    
    if found_env_usage >= 3:
        total_score += 1
        print(f"  ✅ telegram_notifier.py: READS FROM .ENV")
    else:
        print(f"  ⚠️ telegram_notifier.py: PARTIAL .ENV USAGE")
    
    print(f"\n🎯 OVERALL SCORE: {total_score}/{max_score}")
    
    if total_score == max_score:
        print(f"  🎉 EXCELLENT: All Telegram addresses properly configured!")
        print(f"  ✅ System will send to correct addresses from .env file")
    elif total_score >= 3:
        print(f"  ✅ GOOD: Most addresses properly configured")
        print(f"  🔧 Minor improvements may be needed")
    else:
        print(f"  ❌ NEEDS WORK: Significant address configuration issues")
        print(f"  🚨 System may send to wrong addresses")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"  1. ✅ All addresses should read from .env file")
    print(f"  2. ✅ No hardcoded addresses outside of fallback values")
    print(f"  3. ✅ Use os.getenv() with proper fallback values")
    print(f"  4. 📊 Monitor logs to ensure messages go to correct chats")
    
    print(f"\n✅ Telegram address test completed!")
    print(f"⏰ Test finished at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    return total_score >= 3

if __name__ == "__main__":
    success = test_telegram_address_configuration()
    if success:
        print(f"\n🎉 TELEGRAM ADDRESS TEST PASSED!")
    else:
        print(f"\n❌ TELEGRAM ADDRESS TEST FAILED!")
    
    sys.exit(0 if success else 1)
