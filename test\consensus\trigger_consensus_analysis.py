#!/usr/bin/env python3
"""
🎯 Script to trigger consensus analysis and see the improvements in action
"""

import sys
import os
import time
from datetime import datetime

def trigger_consensus_for_coin(coin_symbol="SHIB/USDT"):
    """Trigger consensus analysis for a specific coin to see the improvements"""
    print(f"🎯 TRIGGERING CONSENSUS ANALYSIS FOR {coin_symbol}")
    print("=" * 60)
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Import the main bot
        from main_bot import EnhancedTradingBot
        
        print("✅ Successfully imported EnhancedTradingBot")
        
        # Initialize the bot
        print("🔧 Initializing Enhanced Trading Bot...")
        bot = EnhancedTradingBot()
        print("✅ Bot initialized successfully")
        
        # Get the coin data
        print(f"📊 Fetching data for {coin_symbol}...")
        
        # Get current price
        current_price = bot.fetcher.get_current_price(coin_symbol)
        if not current_price:
            print(f"❌ Could not fetch current price for {coin_symbol}")
            return False
        
        print(f"💰 Current price: {current_price:.8f}")
        
        # Get OHLCV data
        primary_ohlcv_data = bot.fetcher.fetch_ohlcv(coin_symbol, "1h", limit=200)
        if primary_ohlcv_data is None or primary_ohlcv_data.empty:
            print(f"❌ Could not fetch OHLCV data for {coin_symbol}")
            return False
        
        print(f"📈 OHLCV data: {len(primary_ohlcv_data)} bars")
        
        # Get orderbook data
        orderbook_data = bot.fetcher.get_orderbook(coin_symbol, limit=100)
        if not orderbook_data:
            print(f"⚠️ Could not fetch orderbook data for {coin_symbol}")
            orderbook_data = None
        else:
            print(f"📋 Orderbook data: {len(orderbook_data.get('bids', []))} bids, {len(orderbook_data.get('asks', []))} asks")
        
        # Process the data to get features
        print("🔄 Processing data with enhanced algorithms...")
        processed_features = bot.processor.process_data(primary_ohlcv_data.copy())
        
        if not processed_features:
            print("❌ Failed to process data")
            return False
        
        print("✅ Data processed successfully")
        
        # Prepare coin features
        coin_features = {
            "symbol": coin_symbol,
            "coin_category": "MEME",
            f"ohlcv_1h": primary_ohlcv_data
        }
        
        # Run Volume Profile analysis
        print("\n📊 Running Volume Profile Analysis...")
        try:
            volume_profile_result = bot.volume_profile_analyzer.analyze_volume_profile(primary_ohlcv_data)
            if volume_profile_result.get("status") == "success":
                signals = volume_profile_result.get("signals", {})
                vp_signal = signals.get("primary_signal", "NONE")
                vp_confidence = signals.get("confidence", 0)
                print(f"  ✅ Volume Profile: {vp_signal} ({vp_confidence:.1%})")
                coin_features["volume_profile_analysis"] = volume_profile_result
            else:
                print(f"  ❌ Volume Profile failed: {volume_profile_result.get('message', 'Unknown error')}")
                coin_features["volume_profile_analysis"] = {"status": "error"}
        except Exception as vp_error:
            print(f"  ❌ Volume Profile error: {vp_error}")
            coin_features["volume_profile_analysis"] = {"status": "error"}
        
        # Run Orderbook analysis
        print("\n📋 Running Orderbook Analysis...")
        if orderbook_data:
            try:
                orderbook_result = bot.orderbook_analyzer.analyze_orderbook(
                    coin_symbol, orderbook_data, current_price, 1000000  # Mock volume
                )
                if orderbook_result.get("status") == "success":
                    ob_signals = orderbook_result.get("signals", {})
                    ob_signal = ob_signals.get("primary_signal", "NONE")
                    ob_confidence = ob_signals.get("confidence", 0)
                    print(f"  ✅ Orderbook: {ob_signal} ({ob_confidence:.1%})")
                    coin_features["orderbook_analysis"] = orderbook_result
                else:
                    print(f"  ❌ Orderbook failed: {orderbook_result.get('message', 'Unknown error')}")
                    coin_features["orderbook_analysis"] = {"status": "error"}
            except Exception as ob_error:
                print(f"  ❌ Orderbook error: {ob_error}")
                coin_features["orderbook_analysis"] = {"status": "error"}
        else:
            print("  ⚠️ No orderbook data available")
            coin_features["orderbook_analysis"] = {"status": "unavailable"}
        
        # Prepare consensus input
        print("\n🎯 Preparing Consensus Input...")
        consensus_input = bot._prepare_enhanced_consensus_input_with_pump(
            processed_features, coin_features, processed_features, 
            primary_ohlcv_data, None  # No pump detection for this test
        )
        
        # Add mock AI prediction
        consensus_input["ai_prediction"] = {
            "ensemble_signal": "BUY",
            "ensemble_confidence": 0.75
        }
        
        print("✅ Consensus input prepared")
        
        # Run the consensus analysis
        print("\n🔍 Running ENHANCED consensus analysis V3.0...")
        print(f"    🎯 Analyzing consensus for {coin_symbol}...")
        
        try:
            consensus_result = bot.consensus_analyzer.analyze_consensus(consensus_input)
            
            if consensus_result.get("status") == "success":
                consensus_data = consensus_result.get("consensus", {})
                consensus_signal = consensus_data.get("signal", "NONE")
                consensus_score = consensus_data.get("consensus_score", 0)
                consensus_confidence = consensus_data.get("confidence", 0)
                
                # Get contributing algorithms
                contributing = consensus_result.get("contributing_algorithms", [])
                
                print(f"    📊 Total contributing signals: {len(contributing)}")
                print(f"    ⚖️ Total weight: {consensus_data.get('total_weight', 0):.3f}")
                
                # Show each contributing signal
                for contrib in contributing:
                    name = contrib.get("name", "Unknown")
                    signal = contrib.get("signal", "NONE")
                    confidence = contrib.get("confidence", 0)
                    weight = contrib.get("weight", 0)
                    print(f"      ✅ {name}: {signal} ({confidence:.1%}) - Weight: {weight:.3f}")
                
                print(f"    🎯 Consensus: {consensus_signal} (score: {consensus_score:.3f}, conf: {consensus_confidence:.3f})")
                
                # Check quality requirements
                min_confidence = 0.80  # ✅ Lowered to 80% threshold
                if consensus_signal in ["BUY", "SELL"]:
                    if consensus_confidence >= min_confidence:
                        print(f"    ✅ Consensus signal meets quality threshold ({consensus_confidence:.1%} >= {min_confidence:.0%})")
                        print(f"    🎉 SUCCESS: Strong consensus achieved!")
                    else:
                        print(f"    ⚠️ Consensus signal below quality threshold ({consensus_confidence:.1%} < {min_confidence:.0%})")
                        print(f"    📈 IMPROVEMENT: More signals contributing than before!")
                else:
                    print(f"    ℹ️ No actionable signal: {consensus_signal}")
                
                return True
                
            else:
                print(f"    ❌ Consensus analysis failed: {consensus_result.get('message', 'Unknown error')}")
                return False
                
        except Exception as consensus_error:
            print(f"    ❌ Error in consensus analysis: {consensus_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except ImportError as e:
        print(f"❌ Failed to import bot components: {e}")
        print("Make sure you're running this from the bot directory")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🚀 CONSENSUS ANALYSIS TRIGGER TOOL")
    print("=" * 70)
    print("This tool will trigger a consensus analysis to demonstrate the fixes")
    print()
    
    # Test with different coins
    test_coins = ["SHIB/USDT", "DOGE/USDT", "PEPE/USDT"]
    
    for coin in test_coins:
        print(f"\n{'='*20} TESTING {coin} {'='*20}")
        success = trigger_consensus_for_coin(coin)
        
        if success:
            print(f"✅ {coin}: Consensus analysis completed successfully")
        else:
            print(f"❌ {coin}: Consensus analysis failed")
        
        print(f"{'='*50}")
        
        # Small delay between coins
        time.sleep(2)
    
    print("\n🎯 CONSENSUS ANALYSIS TESTING COMPLETE")
    print("=" * 70)
    print("🔧 Key improvements you should see:")
    print("  ✅ Volume Profile now generates signals (not NONE)")
    print("  ✅ Orderbook now generates signals (not NONE)")
    print("  ✅ More contributing signals (5-6 instead of 3-4)")
    print("  ✅ Higher total weight (0.8+ instead of 0.6)")
    print("  ✅ Better consensus success rate")

if __name__ == "__main__":
    main()
