#!/usr/bin/env python3
"""
🔧 CHAT ID FIX TEST
Test that chat IDs are properly handled and invalid formats are fixed
"""

import sys
import os
from unittest.mock import Mock, patch

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chat_id_fix():
    """Test chat ID validation and fixing"""
    print("🔧 TESTING CHAT ID FIX")
    print("=" * 50)
    
    try:
        # Test 1: Chat ID Validation
        print("\n🔍 TEST 1: Chat ID Validation")
        
        # Mock environment variables with problematic chat IDs
        test_env = {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'TELEGRAM_CHAT_ID': '-1002301937119',  # Good format
            'TELEGRAM_VOLUME_PROFILE_POINT_FIGURE': '-1002395637657',  # Good format
            'TELEGRAM_FIBONACCI_ZIGZAG_FOURIER': '-1002395637657',  # Good format
            'TELEGRAM_MONEY_FLOW': '-1002301937119',  # Good format
        }
        
        with patch.dict(os.environ, test_env):
            from telegram_notifier import TelegramNotifier
            
            # Create notifier
            notifier = TelegramNotifier()
            
            print(f"📊 Main chat ID: {notifier.chat_id}")
            print(f"📊 Specialized chats: {len(notifier.specialized_chats)}")
            
            # Check that chat IDs are valid (no underscores)
            valid_main_chat = '_' not in str(notifier.chat_id)
            print(f"✅ Main chat ID valid: {'YES' if valid_main_chat else 'NO'}")
            
            valid_specialized_chats = all('_' not in str(chat_id) for chat_id in notifier.specialized_chats.values())
            print(f"✅ Specialized chat IDs valid: {'YES' if valid_specialized_chats else 'NO'}")
            
            if valid_main_chat and valid_specialized_chats:
                print("✅ TEST 1 PASSED: Chat ID validation working")
            else:
                print("❌ TEST 1 FAILED: Invalid chat ID formats detected")
                return False
        
        # Test 2: Environment Variable Usage
        print("\n🔍 TEST 2: Environment Variable Usage")
        
        # Check that hardcoded chat IDs are removed
        with open('telegram_notifier.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for problematic hardcoded chat IDs
        problematic_patterns = [
            '-1002608968097_621',
            '-1002608968097_619',
            'hardcode',
            'Hardcode'
        ]
        
        found_issues = []
        for pattern in problematic_patterns:
            if pattern in content:
                found_issues.append(pattern)
        
        if found_issues:
            print(f"❌ TEST 2 FAILED: Found hardcoded chat IDs: {found_issues}")
            return False
        else:
            print("✅ TEST 2 PASSED: No hardcoded chat IDs found")
        
        # Test 3: Environment Variable Fallback
        print("\n🔍 TEST 3: Environment Variable Fallback")
        
        # Check that os.getenv() is used properly
        env_usage_patterns = [
            "os.getenv('TELEGRAM_VOLUME_PROFILE_POINT_FIGURE'",
            "os.getenv('TELEGRAM_FIBONACCI_ZIGZAG_FOURIER'",
            "os.getenv('TELEGRAM_MONEY_FLOW'"
        ]
        
        env_usage_found = []
        for pattern in env_usage_patterns:
            if pattern in content:
                env_usage_found.append(pattern)
        
        if len(env_usage_found) >= 3:
            print(f"✅ TEST 3 PASSED: Environment variable usage found ({len(env_usage_found)} patterns)")
        else:
            print(f"❌ TEST 3 FAILED: Insufficient environment variable usage ({len(env_usage_found)} patterns)")
            return False
        
        # Test 4: Chat Availability Tracking
        print("\n🔍 TEST 4: Chat Availability Tracking")
        
        # Test unavailable chat tracking
        test_env_2 = {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'TELEGRAM_CHAT_ID': '-1002301937119',
        }
        
        with patch.dict(os.environ, test_env_2):
            notifier = TelegramNotifier()
            
            # Test adding unavailable chat
            test_chat = '-1002608968097'  # The problematic chat (without suffix)
            notifier.unavailable_chats.add(test_chat)
            
            # Check if chat is marked as unavailable
            is_unavailable = test_chat in notifier.unavailable_chats
            print(f"✅ Unavailable chat tracking: {'WORKING' if is_unavailable else 'NOT WORKING'}")
            
            if is_unavailable:
                print("✅ TEST 4 PASSED: Chat availability tracking working")
            else:
                print("❌ TEST 4 FAILED: Chat availability tracking not working")
                return False
        
        # Test 5: Chat ID Format Validation
        print("\n🔍 TEST 5: Chat ID Format Validation")
        
        # Test the validation method directly
        test_notifier = TelegramNotifier()
        
        # Simulate problematic chat ID
        test_notifier.chat_id = '-1002301937119_621'
        test_notifier.specialized_chats['test'] = '-1002395637657_619'
        
        # Run validation
        test_notifier._validate_chat_ids()
        
        # Check if IDs were fixed
        main_fixed = '_' not in str(test_notifier.chat_id)
        specialized_fixed = '_' not in str(test_notifier.specialized_chats.get('test', ''))
        
        print(f"✅ Main chat ID fixed: {'YES' if main_fixed else 'NO'}")
        print(f"✅ Specialized chat ID fixed: {'YES' if specialized_fixed else 'NO'}")
        
        if main_fixed and specialized_fixed:
            print("✅ TEST 5 PASSED: Chat ID format validation working")
        else:
            print("❌ TEST 5 FAILED: Chat ID format validation not working")
            return False
        
        print("\n" + "=" * 50)
        print("🎯 CHAT ID FIX TEST SUMMARY")
        print("=" * 50)
        print("✅ All tests passed - Chat ID handling fixed!")
        print("\n🔧 Fix Summary:")
        print("  ✅ Hardcoded chat IDs removed")
        print("  ✅ Environment variable usage implemented")
        print("  ✅ Chat ID validation added")
        print("  ✅ Invalid format detection and fixing")
        print("  ✅ Unavailable chat tracking working")
        print("\n📊 Expected Behavior:")
        print("  - No more 403 errors from invalid chat IDs")
        print("  - Proper fallback to environment variables")
        print("  - Automatic fixing of malformed chat IDs")
        print("  - Tracking of unavailable chats to prevent spam")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 STARTING CHAT ID FIX VERIFICATION")
    print("=" * 60)
    
    success = test_chat_id_fix()
    
    print(f"\n🎯 FINAL RESULTS: {'SUCCESS' if success else 'FAILED'}")
    
    if success:
        print("🎉 CHAT ID HANDLING FIXED!")
        print("\n✅ Production ready:")
        print("  🔧 No hardcoded chat IDs")
        print("  📊 Environment variable usage")
        print("  🔍 Chat ID validation and fixing")
        print("  🚫 Unavailable chat tracking")
        print("  ✅ Proper error handling for kicked bot")
    else:
        print("❌ Chat ID handling needs attention")
    
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
