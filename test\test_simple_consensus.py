#!/usr/bin/env python3
"""
🧪 SIMPLE TEST: Consensus Analyzer
Test đơn giản để kiểm tra consensus analyzer
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_simple_consensus():
    """Test simple consensus analyzer"""
    print("🧠 Testing Consensus Analyzer...")
    
    try:
        from consensus_analyzer import ConsensusAnalyzer
        
        print("  🔧 Initializing...")
        analyzer = ConsensusAnalyzer()
        print("  ✅ Initialized successfully")
        
        print("  🧪 Testing basic functionality...")
        sample_input = {
            'coin': 'BTCUSDT',
            'ai_prediction': {
                'ensemble_signal': 'BUY',
                'ensemble_confidence': 0.75
            }
        }
        
        result = analyzer.analyze_consensus(sample_input)
        print(f"  ✅ Analysis completed: {result.get('status', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Run simple test"""
    print("🧪 === SIMPLE CONSENSUS TEST ===")
    
    success = test_simple_consensus()
    
    if success:
        print("\n🎉 SUCCESS: Consensus Analyzer is working!")
        print("✅ The dict vs float comparison bug has been fixed!")
    else:
        print("\n❌ FAILED: Consensus Analyzer still has issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
