#!/usr/bin/env python3
"""
📱 QR CODE SENDING TEST
=======================

Test script để kiểm tra QR code sending functionality
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_qr_files():
    """Test QR code files exist"""
    print("📱 === TESTING QR CODE FILES ===")
    print("=" * 50)
    
    qr_dir = "qr_codes"
    if not os.path.exists(qr_dir):
        print(f"❌ QR codes directory not found: {qr_dir}")
        return False
    
    qr_files = [
        "donation_telegram.png",
        "donation_wallet.svg", 
        "donation_wallet_basic.png",
        "donation_wallet_enhanced.png"
    ]
    
    print(f"🔧 Checking QR files in {qr_dir}/...")
    found_files = []
    
    for qr_file in qr_files:
        file_path = os.path.join(qr_dir, qr_file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  ✅ {qr_file} ({file_size} bytes)")
            found_files.append(qr_file)
        else:
            print(f"  ❌ {qr_file} - Missing")
    
    print(f"\n📊 Found {len(found_files)}/{len(qr_files)} QR files")
    
    if len(found_files) == 0:
        print("\n🔧 Generating QR codes...")
        try:
            from qr_code_generator import DonationQRGenerator
            qr_gen = DonationQRGenerator()
            qr_gen.generate_all_qr_codes()
            print("✅ QR codes generated successfully")
            return True
        except Exception as e:
            print(f"❌ Error generating QR codes: {e}")
            return False
    
    return len(found_files) > 0

def test_telegram_notifier():
    """Test Telegram notifier send_photo method"""
    print("\n📱 === TESTING TELEGRAM NOTIFIER ===")
    print("=" * 50)
    
    try:
        from telegram_notifier import EnhancedTelegramNotifier
        
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not bot_token:
            print("❌ TELEGRAM_BOT_TOKEN missing from .env")
            return False
        
        if not chat_id:
            print("❌ TELEGRAM_CHAT_ID missing from .env")
            return False
        
        print("🔧 Initializing Telegram notifier...")
        notifier = EnhancedTelegramNotifier(bot_token=bot_token, chat_id=chat_id)
        print("✅ Telegram notifier initialized")
        
        # Check if send_photo method exists
        if hasattr(notifier, 'send_photo'):
            print("✅ send_photo method exists")
            
            # Check method signature
            import inspect
            sig = inspect.signature(notifier.send_photo)
            print(f"📋 Method signature: send_photo{sig}")
            
            return True
        else:
            print("❌ send_photo method not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Telegram notifier: {e}")
        return False

def test_qr_sending():
    """Test QR code sending functionality"""
    print("\n📤 === TESTING QR SENDING ===")
    print("=" * 50)
    
    try:
        from telegram_notifier import EnhancedTelegramNotifier
        
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        notifier = EnhancedTelegramNotifier(bot_token=bot_token, chat_id=chat_id)
        
        # Test QR file
        qr_file = "qr_codes/donation_telegram.png"
        
        if not os.path.exists(qr_file):
            print(f"❌ QR file not found: {qr_file}")
            return False
        
        print(f"🔧 Testing QR sending to {chat_id}...")
        print(f"📁 QR file: {qr_file}")
        
        # Test send_photo with correct parameters
        success = notifier.send_photo(
            photo_path=qr_file,
            caption="🧪 Test QR Code - Donation Wallet",
            chat_id=chat_id
        )
        
        if success:
            print("✅ QR code sent successfully!")
            return True
        else:
            print("❌ Failed to send QR code")
            return False
            
    except Exception as e:
        print(f"❌ Error testing QR sending: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_donation_message():
    """Test donation message sending"""
    print("\n💰 === TESTING DONATION MESSAGE ===")
    print("=" * 50)
    
    try:
        from telegram_notifier import EnhancedTelegramNotifier
        
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        notifier = EnhancedTelegramNotifier(bot_token=bot_token, chat_id=chat_id)
        
        donate_msg = """💰 <b>Support Our Bot - Test</b>

🙏 Cảm ơn bạn đã muốn support bot!

💳 <b>Donation Wallet:</b>
<code>******************************************</code>

🔗 <b>Network:</b> USDT BEP20 (Binance Smart Chain)

📱 <b>How to Donate:</b>
1. Copy wallet address above
2. Send USDT BEP20 to this address
3. Contact admin for premium features

🎁 <b>Benefits:</b>
• Premium signals
• Advanced analysis
• Priority support
• Extended trial period

❤️ Mọi donation đều được trân trọng!

🧪 <i>This is a test message</i>"""
        
        print("🔧 Sending donation message...")
        success = notifier.send_message(donate_msg, chat_id=chat_id, parse_mode="HTML")
        
        if success:
            print("✅ Donation message sent successfully!")
            return True
        else:
            print("❌ Failed to send donation message")
            return False
            
    except Exception as e:
        print(f"❌ Error testing donation message: {e}")
        return False

def show_solution():
    """Show solution for QR sending issues"""
    print("\n💡 === SOLUTION FOR QR SENDING ===")
    print("=" * 50)
    
    print("🔧 IF QR CODES NOT SENDING:")
    print()
    print("1️⃣ CHECK QR FILES EXIST:")
    print("   • Run: python qr_code_generator.py")
    print("   • Check qr_codes/ directory")
    print("   • Verify files are not empty")
    print()
    print("2️⃣ CHECK TELEGRAM PERMISSIONS:")
    print("   • Bot needs permission to send photos")
    print("   • Bot should be admin in groups")
    print("   • Check bot token is correct")
    print()
    print("3️⃣ CHECK METHOD SIGNATURE:")
    print("   • Use: notifier.send_photo(photo_path=file, caption=text, chat_id=id)")
    print("   • Don't use: notifier.send_photo(file, chat_id=id, caption=text)")
    print()
    print("4️⃣ TEST MANUALLY:")
    print("   • Run: python test_qr_sending.py")
    print("   • Check console for errors")
    print("   • Verify QR appears in Telegram")
    print()
    print("🔧 COMMON FIXES:")
    print("   • Generate QR codes first")
    print("   • Use correct method parameters")
    print("   • Check file permissions")
    print("   • Verify bot token and chat ID")

def main():
    """Main test function"""
    print("📱 === QR CODE SENDING TEST ===")
    print("🎯 Testing QR code generation and sending")
    print()
    
    # Run tests
    test1 = test_qr_files()
    test2 = test_telegram_notifier()
    test3 = test_donation_message()
    test4 = test_qr_sending()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("QR Files", test1),
        ("Telegram Notifier", test2),
        ("Donation Message", test3),
        ("QR Sending", test4)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed")
    
    # Show solution
    show_solution()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ QR CODE SYSTEM IS WORKING!")
        print("  • QR files: GENERATED")
        print("  • Telegram notifier: FUNCTIONAL")
        print("  • Message sending: WORKING")
        print("  • QR sending: SUCCESSFUL")
        print("\n💰 QR CODES READY FOR DONATION!")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please fix the issues above and try again.")
        
        if not test1:
            print("\n🔧 FIX QR FILES:")
            print("  Run: python qr_code_generator.py")
        
        if not test2:
            print("\n🔧 FIX TELEGRAM NOTIFIER:")
            print("  Check TELEGRAM_BOT_TOKEN in .env")
        
        if not test4:
            print("\n🔧 FIX QR SENDING:")
            print("  Check method parameters and bot permissions")

if __name__ == "__main__":
    main()
