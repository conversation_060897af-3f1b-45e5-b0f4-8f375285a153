#!/usr/bin/env python3
"""
🔍 ANALYZER CONNECTION CHECKER V2.0
=====================================

Comprehensive analyzer connection verification system:
- 🔗 Check all analyzer module imports
- 🧠 Verify class and method availability
- 📊 Test analyzer initialization
- 🎯 Generate detailed connection report
- 🚀 Validate consensus integration

Author: Trading Bot Team
Version: 2.0 - Production Ready
"""

import sys
import os
import importlib
import traceback
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class AnalyzerConnectionChecker:
    """Comprehensive analyzer connection verification system."""
    
    def __init__(self):
        """Initialize the analyzer connection checker."""
        self.results = {
            "core_modules": {},
            "analyzer_modules": {},
            "advanced_modules": {},
            "communication_modules": {},
            "utility_modules": {},
            "consensus_integration": {},
            "summary": {}
        }
        
        # Define expected analyzers and their key methods
        self.expected_analyzers = {
            "volume_profile_analyzer": {
                "class": "VolumeProfileAnalyzer",
                "methods": ["analyze_volume_profile", "get_poc_levels", "get_value_area"]
            },
            "point_figure_analyzer": {
                "class": "PointFigureAnalyzer", 
                "methods": ["analyze_point_figure", "get_price_objectives", "detect_patterns"]
            },
            "fourier_analyzer": {
                "class": "FourierAnalyzer",
                "methods": ["analyze_fourier", "detect_cycles", "predict_trends"]
            },
            "orderbook_analyzer": {
                "class": "OrderbookAnalyzer",
                "methods": ["analyze_orderbook", "detect_walls", "calculate_imbalance"]
            },
            "volume_pattern_analyzer": {
                "class": "VolumePatternAnalyzer",
                "methods": ["analyze_volume_patterns", "detect_spikes", "predict_volume"]
            },
            "volume_spike_detector": {
                "class": "VolumeSpikeDetector",
                "methods": ["get_spike_details", "analyze_pump_patterns", "detect_volume_anomalies"]
            },
            "ai_model_manager": {
                "class": "AIModelManager",
                "methods": ["predict", "get_ensemble_prediction", "update_models"]
            },
            "consensus_analyzer": {
                "class": "ConsensusAnalyzer",
                "methods": ["analyze_consensus", "calculate_agreement", "generate_signal"]
            },
            "dump_detector": {
                "class": "DumpDetector",
                "methods": ["detect_dump", "analyze_dump_patterns", "get_dump_probability"]
            },
            "whale_activity_tracker": {
                "class": "WhaleActivityTracker",
                "methods": ["analyze_whale_activity", "detect_whale_movements", "track_large_orders"]
            },
            "market_manipulation_detector": {
                "class": "MarketManipulationDetector",
                "methods": ["detect_manipulation", "analyze_wash_trading", "detect_spoofing"]
            }
        }
        
    def check_module_import(self, module_name: str) -> dict:
        """Check if a module can be imported successfully."""
        result = {
            "module_name": module_name,
            "import_success": False,
            "module_object": None,
            "error": None,
            "classes_found": [],
            "methods_found": {}
        }
        
        try:
            # Try to import the module
            module = importlib.import_module(module_name)
            result["import_success"] = True
            result["module_object"] = module
            
            # Check for expected classes and methods
            if module_name in self.expected_analyzers:
                expected = self.expected_analyzers[module_name]
                expected_class = expected["class"]
                expected_methods = expected["methods"]
                
                # Check if expected class exists
                if hasattr(module, expected_class):
                    result["classes_found"].append(expected_class)
                    
                    # Get the class object
                    class_obj = getattr(module, expected_class)
                    
                    # Check for expected methods
                    for method_name in expected_methods:
                        if hasattr(class_obj, method_name):
                            result["methods_found"][method_name] = True
                        else:
                            result["methods_found"][method_name] = False
                            
            print(f"✅ {module_name}: Import successful")
            
        except ImportError as e:
            result["error"] = f"ImportError: {str(e)}"
            print(f"❌ {module_name}: Import failed - {str(e)}")
            
        except Exception as e:
            result["error"] = f"Error: {str(e)}"
            print(f"❌ {module_name}: Unexpected error - {str(e)}")
            
        return result
    
    def test_analyzer_initialization(self, module_name: str, module_result: dict) -> dict:
        """Test if analyzer can be initialized successfully."""
        init_result = {
            "initialization_success": False,
            "instance_created": False,
            "error": None,
            "available_methods": []
        }
        
        if not module_result["import_success"]:
            init_result["error"] = "Module import failed"
            return init_result
            
        try:
            if module_name in self.expected_analyzers:
                expected = self.expected_analyzers[module_name]
                expected_class = expected["class"]
                
                module = module_result["module_object"]
                if hasattr(module, expected_class):
                    # Try to create instance
                    class_obj = getattr(module, expected_class)
                    instance = class_obj()
                    
                    init_result["initialization_success"] = True
                    init_result["instance_created"] = True
                    
                    # Get available methods
                    for attr_name in dir(instance):
                        if not attr_name.startswith('_') and callable(getattr(instance, attr_name)):
                            init_result["available_methods"].append(attr_name)
                    
                    print(f"✅ {module_name}: Initialization successful")
                else:
                    init_result["error"] = f"Class {expected_class} not found"
                    
        except Exception as e:
            init_result["error"] = f"Initialization error: {str(e)}"
            print(f"❌ {module_name}: Initialization failed - {str(e)}")
            
        return init_result
    
    def check_consensus_integration(self) -> dict:
        """Check consensus analyzer integration with other analyzers."""
        consensus_result = {
            "consensus_available": False,
            "external_analyzers_connected": 0,
            "total_expected_analyzers": 0,
            "missing_analyzers": [],
            "connection_details": {}
        }
        
        try:
            # Try to import and initialize consensus analyzer
            import consensus_analyzer
            consensus = consensus_analyzer.ConsensusAnalyzer()
            
            consensus_result["consensus_available"] = True
            
            # Check external analyzer connections
            if hasattr(consensus, 'external_analyzers'):
                external_analyzers = consensus.external_analyzers
                consensus_result["total_expected_analyzers"] = len(self.expected_analyzers) - 1  # Exclude consensus itself
                
                for analyzer_name in self.expected_analyzers:
                    if analyzer_name != "consensus_analyzer":
                        if analyzer_name in external_analyzers and external_analyzers[analyzer_name] is not None:
                            consensus_result["external_analyzers_connected"] += 1
                            consensus_result["connection_details"][analyzer_name] = "Connected"
                        else:
                            consensus_result["missing_analyzers"].append(analyzer_name)
                            consensus_result["connection_details"][analyzer_name] = "Not Connected"
            
            print(f"✅ Consensus Integration: {consensus_result['external_analyzers_connected']}/{consensus_result['total_expected_analyzers']} analyzers connected")
            
        except Exception as e:
            consensus_result["error"] = f"Consensus integration error: {str(e)}"
            print(f"❌ Consensus Integration: Failed - {str(e)}")
            
        return consensus_result
    
    def generate_report(self) -> str:
        """Generate comprehensive connection report."""
        report = []
        report.append("🔍 ANALYZER CONNECTION REPORT")
        report.append("=" * 50)
        report.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Summary statistics
        total_modules = len(self.expected_analyzers)
        successful_imports = sum(1 for result in self.results["analyzer_modules"].values() if result.get("import_success", False))
        successful_inits = sum(1 for result in self.results["analyzer_modules"].values() if result.get("init_result", {}).get("initialization_success", False))
        
        report.append("📊 SUMMARY STATISTICS")
        report.append("-" * 30)
        report.append(f"📦 Total Expected Modules: {total_modules}")
        report.append(f"✅ Successful Imports: {successful_imports}")
        report.append(f"🚀 Successful Initializations: {successful_inits}")
        report.append(f"📈 Import Success Rate: {(successful_imports/total_modules)*100:.1f}%")
        report.append(f"🎯 Initialization Success Rate: {(successful_inits/total_modules)*100:.1f}%")
        report.append("")
        
        # Detailed module results
        report.append("📋 DETAILED MODULE RESULTS")
        report.append("-" * 40)
        
        for module_name, result in self.results["analyzer_modules"].items():
            status = "✅" if result.get("import_success", False) else "❌"
            init_status = "🚀" if result.get("init_result", {}).get("initialization_success", False) else "❌"
            
            report.append(f"{status} {module_name}")
            report.append(f"   Import: {'Success' if result.get('import_success', False) else 'Failed'}")
            report.append(f"   Init: {init_status} {'Success' if result.get('init_result', {}).get('initialization_success', False) else 'Failed'}")
            
            if result.get("error"):
                report.append(f"   Error: {result['error']}")
                
            # Show method availability
            methods_found = result.get("methods_found", {})
            if methods_found:
                available_methods = sum(1 for available in methods_found.values() if available)
                total_methods = len(methods_found)
                report.append(f"   Methods: {available_methods}/{total_methods} available")
                
            report.append("")
        
        # Consensus integration results
        consensus_result = self.results.get("consensus_integration", {})
        if consensus_result:
            report.append("🎯 CONSENSUS INTEGRATION")
            report.append("-" * 30)
            report.append(f"📊 Connected Analyzers: {consensus_result.get('external_analyzers_connected', 0)}/{consensus_result.get('total_expected_analyzers', 0)}")
            
            missing = consensus_result.get("missing_analyzers", [])
            if missing:
                report.append(f"❌ Missing Analyzers: {', '.join(missing)}")
            else:
                report.append("✅ All expected analyzers connected")
                
            report.append("")
        
        # Recommendations
        report.append("💡 RECOMMENDATIONS")
        report.append("-" * 25)
        
        if successful_imports < total_modules:
            report.append("🔧 Install missing analyzer modules")
            
        if successful_inits < successful_imports:
            report.append("🔧 Fix analyzer initialization issues")
            
        if consensus_result.get("external_analyzers_connected", 0) < consensus_result.get("total_expected_analyzers", 0):
            report.append("🔧 Improve consensus analyzer integration")
            
        if successful_imports == total_modules and successful_inits == successful_imports:
            report.append("🎉 All analyzers working perfectly!")
            
        return "\n".join(report)
    
    def run_full_check(self) -> dict:
        """Run comprehensive analyzer connection check."""
        print("🚀 STARTING ANALYZER CONNECTION CHECK")
        print("=" * 50)
        
        # Check all expected analyzers
        print("\n📦 CHECKING ANALYZER MODULES...")
        for module_name in self.expected_analyzers:
            print(f"\n🔍 Checking {module_name}...")
            
            # Check import
            import_result = self.check_module_import(module_name)
            
            # Check initialization
            init_result = self.test_analyzer_initialization(module_name, import_result)
            import_result["init_result"] = init_result
            
            self.results["analyzer_modules"][module_name] = import_result
        
        # Check consensus integration
        print("\n🎯 CHECKING CONSENSUS INTEGRATION...")
        consensus_result = self.check_consensus_integration()
        self.results["consensus_integration"] = consensus_result
        
        # Generate summary
        total_modules = len(self.expected_analyzers)
        successful_imports = sum(1 for result in self.results["analyzer_modules"].values() if result.get("import_success", False))
        successful_inits = sum(1 for result in self.results["analyzer_modules"].values() if result.get("init_result", {}).get("initialization_success", False))
        
        self.results["summary"] = {
            "total_modules": total_modules,
            "successful_imports": successful_imports,
            "successful_initializations": successful_inits,
            "import_success_rate": (successful_imports/total_modules)*100,
            "init_success_rate": (successful_inits/total_modules)*100,
            "overall_health": "EXCELLENT" if successful_inits == total_modules else "GOOD" if successful_inits >= total_modules * 0.8 else "NEEDS_ATTENTION"
        }
        
        print(f"\n🎯 CHECK COMPLETE!")
        print(f"📊 Results: {successful_imports}/{total_modules} imports, {successful_inits}/{total_modules} initializations")
        print(f"🏥 System Health: {self.results['summary']['overall_health']}")
        
        return self.results

if __name__ == "__main__":
    print("🔍 ANALYZER CONNECTION CHECKER V2.0")
    print("=" * 60)
    
    # Create checker and run full check
    checker = AnalyzerConnectionChecker()
    results = checker.run_full_check()
    
    # Generate and display report
    print("\n" + "=" * 60)
    report = checker.generate_report()
    print(report)
    
    # Save report to file
    try:
        with open("analyzer_connection_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print(f"\n💾 Report saved to: analyzer_connection_report.txt")
    except Exception as e:
        print(f"\n❌ Failed to save report: {e}")
    
    # Exit with appropriate code
    overall_health = results["summary"]["overall_health"]
    if overall_health == "EXCELLENT":
        print(f"\n🎉 ALL ANALYZERS WORKING PERFECTLY!")
        sys.exit(0)
    elif overall_health == "GOOD":
        print(f"\n✅ MOST ANALYZERS WORKING - MINOR ISSUES")
        sys.exit(0)
    else:
        print(f"\n⚠️ ANALYZER ISSUES DETECTED - NEEDS ATTENTION")
        sys.exit(1)
