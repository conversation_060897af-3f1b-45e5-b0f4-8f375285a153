#!/usr/bin/env python3
"""
🚨 DUMP DETECTOR INITIALIZATION FIX TEST
Test dump detector initialization fix for ultra_early_sensitivity parameter
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dump_detector_initialization():
    """Test dump detector initialization with correct parameters"""
    print("🚨 TESTING DUMP DETECTOR INITIALIZATION FIX")
    print("=" * 60)
    
    try:
        # Test 1: Import DumpDetector
        print("\n🔍 TEST 1: Import DumpDetector")
        from dump_detector import DumpDetector
        print("✅ DumpDetector imported successfully")
        
        # Test 2: Initialize with correct parameters (no ultra_early_sensitivity)
        print("\n🔍 TEST 2: Initialize DumpDetector with correct parameters")
        dump_detector = DumpDetector(
            sensitivity=0.75,
            min_volume_threshold=2.0,
            whale_threshold=50000,
            lookback_period=60,
            enable_ml_detection=True,
            enable_pattern_recognition=True,
            enable_real_time_monitoring=True
        )
        print("✅ DumpDetector initialized successfully with correct parameters")
        
        # Test 3: Check if ultra_detector is properly initialized
        print("\n🔍 TEST 3: Check internal ultra_detector")
        if hasattr(dump_detector, 'ultra_detector'):
            print("✅ ultra_detector attribute exists")
            if dump_detector.ultra_detector is not None:
                print("✅ ultra_detector is properly initialized")
            else:
                print("❌ ultra_detector is None")
                return False
        else:
            print("❌ ultra_detector attribute missing")
            return False
        
        # Test 4: Test basic functionality
        print("\n🔍 TEST 4: Test basic functionality")
        if hasattr(dump_detector, 'analyze'):
            print("✅ analyze method exists")
        else:
            print("❌ analyze method missing")
            return False
        
        # Test 5: Import UltraEarlyDumpDetector
        print("\n🔍 TEST 5: Import UltraEarlyDumpDetector")
        from dump_detector import UltraEarlyDumpDetector
        print("✅ UltraEarlyDumpDetector imported successfully")
        
        # Test 6: Initialize UltraEarlyDumpDetector with ultra_early_sensitivity
        print("\n🔍 TEST 6: Initialize UltraEarlyDumpDetector with ultra_early_sensitivity")
        ultra_detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=0.75,
            pre_dump_lookback=60,
            whale_threshold=100000,
            min_confidence=0.70
        )
        print("✅ UltraEarlyDumpDetector initialized successfully with ultra_early_sensitivity")
        
        print("\n" + "=" * 60)
        print("🎯 DUMP DETECTOR FIX TEST SUMMARY")
        print("=" * 60)
        print("✅ All tests passed - Dump detector initialization fix working correctly!")
        print("\n🔧 Fix Summary:")
        print("  ✅ DumpDetector class: Uses correct parameter names")
        print("  ✅ UltraEarlyDumpDetector class: Uses ultra_early_sensitivity")
        print("  ✅ Internal initialization: Fixed to use UltraEarlyDumpDetector")
        print("  ✅ Main bot compatibility: Maintained")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_bot_integration():
    """Test main bot integration"""
    print("\n🔍 TESTING MAIN BOT INTEGRATION")
    print("=" * 60)
    
    try:
        # Test importing main bot modules
        print("\n🔍 Testing main bot dump detector integration...")
        
        # Simulate main bot initialization parameters
        DUMP_ALERT_THRESHOLD = 0.6
        DUMP_VOLUME_THRESHOLD = 2.0
        
        from dump_detector import DumpDetector
        
        # Test initialization with main bot parameters
        dump_detector = DumpDetector(
            sensitivity=DUMP_ALERT_THRESHOLD,
            min_volume_threshold=DUMP_VOLUME_THRESHOLD,
            whale_threshold=50000,
            lookback_period=60,
            enable_ml_detection=True,
            enable_pattern_recognition=True,
            enable_real_time_monitoring=True
        )
        
        print("✅ Main bot integration test passed")
        print(f"  📊 Sensitivity: {DUMP_ALERT_THRESHOLD}")
        print(f"  📈 Volume threshold: {DUMP_VOLUME_THRESHOLD}")
        print(f"  🐋 Whale threshold: 50000")
        print(f"  🔍 Lookback period: 60")
        
        return True
        
    except Exception as e:
        print(f"❌ Main bot integration test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 STARTING DUMP DETECTOR FIX VERIFICATION")
    print("=" * 70)
    
    test1_success = test_dump_detector_initialization()
    test2_success = test_main_bot_integration()
    
    total_tests = 2
    passed_tests = sum([test1_success, test2_success])
    
    print(f"\n🎯 FINAL RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - Dump detector fix is working correctly!")
        print("\n✅ Ready for production:")
        print("  🚨 DumpDetector initialization fixed")
        print("  🔧 Parameter compatibility maintained")
        print("  🎯 Main bot integration working")
        success = True
    else:
        print(f"❌ {total_tests - passed_tests} tests failed - Fix needs attention")
        success = False
    
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
