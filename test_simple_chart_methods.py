#!/usr/bin/env python3
"""
🧪 TEST SIMPLE CHART METHODS
============================

Simple test để kiểm tra chart generation methods có tồn tại không.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_methods_exist():
    """Test if chart generation methods exist."""
    print("🧪 TESTING CHART METHODS EXISTENCE")
    print("=" * 50)
    
    try:
        import chart_generator
        
        # Test chart generator initialization
        print("📊 Testing chart generator initialization...")
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=False,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=100
        )
        print("  ✅ Chart generator initialized successfully")
        
        # Test required methods
        required_methods = [
            'generate_fibonacci_chart',
            'generate_pump_alert_chart',
            'generate_dump_alert_chart',
            'generate_consensus_chart'
        ]
        
        all_methods_exist = True
        for method in required_methods:
            has_method = hasattr(chart_gen, method)
            print(f"  📊 {method}: {'✅' if has_method else '❌'}")
            if not has_method:
                all_methods_exist = False
        
        return all_methods_exist
        
    except Exception as e:
        print(f"❌ Chart generator test failed: {e}")
        return False

def test_signal_integration_methods():
    """Test signal integration methods."""
    print("\n🧪 TESTING SIGNAL INTEGRATION METHODS")
    print("=" * 50)
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Test initialization
        integration = SignalManagerIntegration(
            telegram_notifier=None,
            data_fetcher=None,
            trade_tracker=None
        )
        print("  ✅ Signal integration initialized successfully")
        
        # Test required methods
        required_methods = [
            'send_fibonacci_analysis_with_tracking',
            'send_pump_alert_with_tracking',
            'send_dump_alert_with_tracking'
        ]
        
        all_methods_exist = True
        for method in required_methods:
            has_method = hasattr(integration, method)
            print(f"  📡 {method}: {'✅' if has_method else '❌'}")
            if not has_method:
                all_methods_exist = False
        
        return all_methods_exist
        
    except Exception as e:
        print(f"❌ Signal integration test failed: {e}")
        return False

def test_telegram_notifier_methods():
    """Test telegram notifier methods."""
    print("\n🧪 TESTING TELEGRAM NOTIFIER METHODS")
    print("=" * 50)
    
    try:
        import telegram_notifier
        
        # Test required methods exist in class
        required_methods = [
            'send_pump_alert',
            'send_dump_alert',
            'format_pump_dump_alert'
        ]
        
        all_methods_exist = True
        for method in required_methods:
            has_method = hasattr(telegram_notifier.TelegramNotifier, method)
            print(f"  📱 {method}: {'✅' if has_method else '❌'}")
            if not has_method:
                all_methods_exist = False
        
        return all_methods_exist
        
    except Exception as e:
        print(f"❌ Telegram notifier test failed: {e}")
        return False

def test_early_warning_logic():
    """Test early warning logic."""
    print("\n🧪 TESTING EARLY WARNING LOGIC")
    print("=" * 50)
    
    # Test warning data structure
    current_price = 50000.0
    
    # Test PUMP warning
    pump_warning = {
        "type": "EARLY_PUMP_WARNING",
        "coin": "TESTCOIN/USDT",
        "probability": 0.75,
        "confidence": 0.68,
        "risk_level": "MEDIUM",
        "indicators": ["Volume pre-spike pattern detected"]
    }
    
    # Add current_price and predicted_price (like in main_bot.py fix)
    pump_warning["current_price"] = current_price
    if "PUMP" in pump_warning.get("type", ""):
        pump_warning["predicted_price"] = current_price * 1.03
        pump_warning["targets"] = [current_price * 1.03, current_price * 1.05, current_price * 1.08]
    
    print(f"🚀 PUMP Warning Test:")
    print(f"  - Has current_price: {'✅' if 'current_price' in pump_warning else '❌'}")
    print(f"  - Has predicted_price: {'✅' if 'predicted_price' in pump_warning else '❌'}")
    print(f"  - Has targets: {'✅' if 'targets' in pump_warning else '❌'}")
    
    # Test DUMP warning
    dump_warning = {
        "type": "EARLY_DUMP_WARNING",
        "coin": "TESTCOIN/USDT",
        "probability": 0.70,
        "confidence": 0.65,
        "risk_level": "HIGH",
        "indicators": ["Selling pressure detected"]
    }
    
    # Add current_price and predicted_price (like in main_bot.py fix)
    dump_warning["current_price"] = current_price
    if "DUMP" in dump_warning.get("type", ""):
        dump_warning["predicted_price"] = current_price * 0.97
        dump_warning["support_levels"] = [current_price * 0.97, current_price * 0.94, current_price * 0.91]
    
    print(f"\n📉 DUMP Warning Test:")
    print(f"  - Has current_price: {'✅' if 'current_price' in dump_warning else '❌'}")
    print(f"  - Has predicted_price: {'✅' if 'predicted_price' in dump_warning else '❌'}")
    print(f"  - Has support_levels: {'✅' if 'support_levels' in dump_warning else '❌'}")
    
    # Calculate price changes
    pump_change = ((pump_warning['predicted_price'] - pump_warning['current_price']) / pump_warning['current_price']) * 100
    dump_change = ((dump_warning['predicted_price'] - dump_warning['current_price']) / dump_warning['current_price']) * 100
    
    print(f"\n📊 Price Change Calculations:")
    print(f"  🚀 PUMP: {pump_change:+.1f}% (should be positive)")
    print(f"  📉 DUMP: {dump_change:+.1f}% (should be negative)")
    
    pump_correct = 'current_price' in pump_warning and 'predicted_price' in pump_warning and 'targets' in pump_warning
    dump_correct = 'current_price' in dump_warning and 'predicted_price' in dump_warning and 'support_levels' in dump_warning
    
    return pump_correct and dump_correct

def main():
    """Run all simple tests."""
    print("🧪 TESTING SIMPLE CHART METHODS")
    print("=" * 70)
    
    # Run tests
    test1 = test_chart_methods_exist()
    test2 = test_signal_integration_methods()
    test3 = test_telegram_notifier_methods()
    test4 = test_early_warning_logic()
    
    # Summary
    print(f"\n🎯 SIMPLE TEST SUMMARY:")
    print("=" * 50)
    print(f"📊 Chart Methods Exist: {'✅ YES' if test1 else '❌ NO'}")
    print(f"📡 Signal Integration Methods: {'✅ YES' if test2 else '❌ NO'}")
    print(f"📱 Telegram Notifier Methods: {'✅ YES' if test3 else '❌ NO'}")
    print(f"⚡ Early Warning Logic: {'✅ CORRECT' if test4 else '❌ BROKEN'}")
    
    total_passed = sum([test1, test2, test3, test4])
    print(f"\n🏆 TOTAL TESTS PASSED: {total_passed}/4")
    
    if total_passed == 4:
        print(f"\n🎉 ALL BASIC METHODS WORKING!")
        print(f"\n📋 LIKELY ISSUES IN PRODUCTION:")
        print(f"1. 🔍 Methods exist but chart generation fails due to:")
        print(f"   - Invalid OHLCV data format")
        print(f"   - Missing required data fields")
        print(f"   - File system permissions")
        print(f"   - Chart library errors")
        print(f"2. 🔍 Early warnings missing current/predicted price because:")
        print(f"   - Warning data not enhanced before sending")
        print(f"   - Fallback message used instead of enhanced format")
        print(f"3. 🔍 Charts not sent because:")
        print(f"   - Chart generation returns None")
        print(f"   - Telegram sending fails")
        print(f"   - Quality filters block signals")
    else:
        print(f"\n⚠️ {4-total_passed} basic method issues found.")
        print(f"Please check the failed tests above.")

if __name__ == "__main__":
    main()
