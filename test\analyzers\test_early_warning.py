#!/usr/bin/env python3
"""
🧪 Test Early Warning System
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime
from typing import Dict, Any

# Mock data generator
def generate_mock_ohlcv_data(bars: int = 100, trend: str = "neutral") -> pd.DataFrame:
    """Generate mock OHLCV data for testing."""
    np.random.seed(42)
    
    # Base price
    base_price = 1.0
    
    # Generate price series based on trend
    if trend == "pump_building":
        # Gradual increase with acceleration
        price_changes = np.random.normal(0.001, 0.005, bars)  # Small positive bias
        price_changes[-20:] += np.linspace(0, 0.01, 20)  # Acceleration at end
    elif trend == "dump_building":
        # Gradual decrease with acceleration
        price_changes = np.random.normal(-0.001, 0.005, bars)  # Small negative bias
        price_changes[-20:] -= np.linspace(0, 0.01, 20)  # Acceleration at end
    else:
        # Neutral trend
        price_changes = np.random.normal(0, 0.005, bars)
    
    # Generate prices
    prices = [base_price]
    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(max(0.01, new_price))  # Prevent negative prices
    
    prices = prices[1:]  # Remove initial price
    
    # Generate OHLC from close prices
    data = []
    for i, close in enumerate(prices):
        if i == 0:
            open_price = base_price
        else:
            open_price = prices[i-1]
        
        # Generate high and low
        high = close * np.random.uniform(1.0, 1.02)
        low = close * np.random.uniform(0.98, 1.0)
        
        # Ensure OHLC consistency
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # Generate volume (higher volume during trend acceleration)
        if trend == "pump_building" and i >= bars - 20:
            volume = np.random.uniform(1000, 5000) * (1 + (i - (bars - 20)) * 0.2)
        elif trend == "dump_building" and i >= bars - 20:
            volume = np.random.uniform(1000, 4000) * (1 + (i - (bars - 20)) * 0.15)
        else:
            volume = np.random.uniform(500, 2000)
        
        data.append({
            'timestamp': time.time() - (bars - i) * 3600,  # 1 hour intervals
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def generate_mock_orderbook(imbalance_type: str = "neutral") -> Dict[str, Any]:
    """Generate mock orderbook data."""
    current_price = 1.0
    
    # Generate bids and asks
    bids = []
    asks = []
    
    for i in range(20):
        bid_price = current_price - (i + 1) * 0.001
        ask_price = current_price + (i + 1) * 0.001
        
        if imbalance_type == "pump_prep":
            # More buying pressure
            bid_volume = np.random.uniform(1000, 5000) * (1.5 if i < 5 else 1.0)
            ask_volume = np.random.uniform(500, 2000)
        elif imbalance_type == "dump_prep":
            # More selling pressure
            bid_volume = np.random.uniform(500, 2000)
            ask_volume = np.random.uniform(1000, 5000) * (1.5 if i < 5 else 1.0)
        else:
            # Balanced
            bid_volume = np.random.uniform(1000, 3000)
            ask_volume = np.random.uniform(1000, 3000)
        
        bids.append([bid_price, bid_volume])
        asks.append([ask_price, ask_volume])
    
    return {
        "bids": bids,
        "asks": asks
    }

def test_early_warning_system():
    """🧪 Test Early Warning System with different scenarios."""
    print("🧪 Testing Early Warning System...")
    print("=" * 60)
    
    # Import Early Warning System
    try:
        import early_warning_system
        print("✅ Early Warning System module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Early Warning System: {e}")
        return False
    
    # Initialize Early Warning System
    print("\n🚨 Initializing Early Warning System...")
    ews = early_warning_system.EarlyWarningSystem(
        pump_threshold=0.4,
        dump_threshold=0.4,
        volume_threshold=3.0,
        price_momentum_threshold=0.02,
        orderbook_imbalance_threshold=0.3,
        cooldown_minutes=15
    )
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Neutral Market",
            "trend": "neutral",
            "orderbook": "neutral",
            "expected": "No warnings"
        },
        {
            "name": "Pump Building",
            "trend": "pump_building",
            "orderbook": "pump_prep",
            "expected": "Pump warning"
        },
        {
            "name": "Dump Building",
            "trend": "dump_building", 
            "orderbook": "dump_prep",
            "expected": "Dump warning"
        }
    ]
    
    print(f"\n🔍 Testing {len(test_scenarios)} scenarios...")
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Test {i}: {scenario['name']} ---")
        
        # Generate test data
        ohlcv_data = generate_mock_ohlcv_data(100, scenario['trend'])
        orderbook_data = generate_mock_orderbook(scenario['orderbook'])
        current_price = ohlcv_data['close'].iloc[-1]
        
        print(f"📊 Generated data:")
        print(f"  - OHLCV bars: {len(ohlcv_data)}")
        print(f"  - Current price: {current_price:.6f}")
        print(f"  - Trend: {scenario['trend']}")
        print(f"  - Orderbook: {scenario['orderbook']}")
        
        # Run early warning analysis
        try:
            analysis = ews.analyze_early_signals(
                coin="TEST/USDT",
                ohlcv_data=ohlcv_data,
                current_price=current_price,
                orderbook_data=orderbook_data
            )
            
            print(f"📈 Analysis results:")
            print(f"  - Status: {analysis.get('status', 'unknown')}")
            print(f"  - Risk Level: {analysis.get('risk_level', 'unknown')}")
            print(f"  - Confidence: {analysis.get('confidence', 0):.1%}")
            print(f"  - Total Score: {analysis.get('total_score', 0):.2f}")
            
            warnings = analysis.get('warnings', [])
            print(f"  - Warnings: {len(warnings)}")
            
            for warning in warnings:
                warning_type = warning.get('type', 'UNKNOWN')
                probability = warning.get('probability', 0)
                print(f"    ⚠️ {warning_type}: {probability:.1%} probability")
            
            # Check if result matches expectation
            if scenario['expected'] == "No warnings" and len(warnings) == 0:
                print(f"  ✅ Result matches expectation: {scenario['expected']}")
            elif "Pump" in scenario['expected'] and any("PUMP" in w.get('type', '') for w in warnings):
                print(f"  ✅ Result matches expectation: {scenario['expected']}")
            elif "Dump" in scenario['expected'] and any("DUMP" in w.get('type', '') for w in warnings):
                print(f"  ✅ Result matches expectation: {scenario['expected']}")
            else:
                print(f"  ⚠️ Result differs from expectation: {scenario['expected']}")
            
        except Exception as e:
            print(f"  ❌ Analysis failed: {e}")
    
    # Test cooldown functionality
    print(f"\n🕐 Testing cooldown functionality...")
    
    # Generate pump scenario
    pump_data = generate_mock_ohlcv_data(100, "pump_building")
    pump_orderbook = generate_mock_orderbook("pump_prep")
    pump_price = pump_data['close'].iloc[-1]
    
    # First analysis
    analysis1 = ews.analyze_early_signals("COOLDOWN/USDT", pump_data, pump_price, pump_orderbook)
    warnings1 = analysis1.get('warnings', [])
    print(f"  First analysis: {len(warnings1)} warnings")
    
    # Immediate second analysis (should be in cooldown)
    analysis2 = ews.analyze_early_signals("COOLDOWN/USDT", pump_data, pump_price, pump_orderbook)
    status2 = analysis2.get('status', 'unknown')
    print(f"  Second analysis: {status2}")
    
    if status2 == "cooldown":
        print(f"  ✅ Cooldown working correctly")
    else:
        print(f"  ⚠️ Cooldown may not be working")
    
    print("\n" + "=" * 60)
    print("✅ Early Warning System Test Complete!")
    
    return True

if __name__ == "__main__":
    test_early_warning_system()
