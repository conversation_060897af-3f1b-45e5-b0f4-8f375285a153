#!/usr/bin/env python3
"""
📱 ENHANCED TELEGRAM MESSAGE HANDLER V2.0 - PRODUCTION READY
============================================================

Advanced Telegram Message Processing System with Comprehensive Features:
- 📱 Intelligent message routing and command processing
- 🔄 Real-time message handling with async capabilities
- 🎯 Advanced user interaction management
- 🛡️ Comprehensive security and rate limiting
- 🚀 Performance optimized for high-volume messaging
- 📊 Message analytics and monitoring capabilities

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import os
import json
import time
import threading
import warnings
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
import requests
from dotenv import load_dotenv

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import asyncio
    import aiohttp
    AVAILABLE_MODULES['async'] = True
    print("✅ asyncio/aiohttp imported successfully - Async message handling available")
except ImportError:
    AVAILABLE_MODULES['async'] = False
    print("⚠️ asyncio/aiohttp not available - Sync message handling only")

try:
    from telegram import Bot, Update
    from telegram.ext import Application, MessageHandler, CommandHandler, filters
    AVAILABLE_MODULES['python_telegram_bot'] = True
    print("✅ python-telegram-bot imported successfully - Advanced bot features available")
except ImportError:
    AVAILABLE_MODULES['python_telegram_bot'] = False
    print("⚠️ python-telegram-bot not available - Basic API calls only")

# Load environment variables
load_dotenv()

print(f"📱 Telegram Message Handler V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class TelegramMessageHandler:
    """
    📱 ENHANCED TELEGRAM MESSAGE HANDLER V2.0 - PRODUCTION READY
    ============================================================

    Advanced Telegram Message Processing System with comprehensive features:
    - 📱 Intelligent message routing and command processing
    - 🔄 Real-time message handling with async capabilities
    - 🎯 Advanced user interaction management
    - 🛡️ Comprehensive security and rate limiting
    - 🚀 Performance optimized for high-volume messaging
    """

    def __init__(self, bot_instance,
                 enable_async_processing: bool = True,
                 enable_rate_limiting: bool = True,
                 enable_message_analytics: bool = True,
                 max_concurrent_messages: int = 100):
        """
        Initialize Enhanced Telegram Message Handler V2.0.

        Args:
            bot_instance: Main bot instance
            enable_async_processing: Enable async message processing
            enable_rate_limiting: Enable rate limiting protection
            enable_message_analytics: Enable message analytics
            max_concurrent_messages: Maximum concurrent messages (100)
        """
        print("📱 Initializing Enhanced Telegram Message Handler V2.0...")

        # Core configuration
        self.bot = bot_instance
        self.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.api_url = f"https://api.telegram.org/bot{self.bot_token}"

        # Enhanced features
        self.enable_async_processing = enable_async_processing and AVAILABLE_MODULES.get('async', False)
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_message_analytics = enable_message_analytics
        self.max_concurrent_messages = max(10, min(500, max_concurrent_messages))

        # Managed groups with validation
        self.managed_groups = [
            os.getenv("TELEGRAM_CHAT_ID", "-1002301937119"),
            os.getenv("TELEGRAM_FIBONACCI_ZIGZAG_FOURIER", "-1002395637657")
        ]

        # Performance tracking
        self.message_stats = {
            "total_messages": 0,
            "processed_messages": 0,
            "failed_messages": 0,
            "commands_processed": 0,
            "average_processing_time": 0.0,
            "rate_limited_messages": 0
        }

        # Polling settings
        self.polling_active = False
        self.polling_thread = None
        self.last_update_id = 0

        # Rate limiting
        self.rate_limits = {}
        self.rate_limit_window = 60  # seconds
        self.max_messages_per_user = 10

        print(f"    🤖 Bot Token: {'✅ SET' if self.bot_token else '❌ MISSING'}")
        print(f"    👥 Managed Groups: {len(self.managed_groups)}")
        print(f"    🔄 Async Processing: {'Enabled' if self.enable_async_processing else 'Disabled'}")
        print(f"    🛡️ Rate Limiting: {'Enabled' if self.enable_rate_limiting else 'Disabled'}")
        print(f"    📊 Message Analytics: {'Enabled' if self.enable_message_analytics else 'Disabled'}")
    
    def start_polling(self):
        """Start polling for Telegram messages"""
        if self.polling_active:
            print("⚠️ Polling already active")
            return
        
        if not self.bot_token:
            print("❌ Cannot start polling: Bot token missing")
            return
        
        print("🚀 Starting Telegram message polling...")
        self.polling_active = True
        self.polling_thread = threading.Thread(target=self._polling_loop, daemon=True)
        self.polling_thread.start()
        print("✅ Telegram message polling started")
    
    def stop_polling(self):
        """Stop polling for Telegram messages"""
        if not self.polling_active:
            return
        
        print("🛑 Stopping Telegram message polling...")
        self.polling_active = False
        if self.polling_thread:
            self.polling_thread.join(timeout=5)
        print("✅ Telegram message polling stopped")
    
    def _polling_loop(self):
        """Main polling loop"""
        while self.polling_active:
            try:
                # Get updates from Telegram
                updates = self._get_updates()
                
                if updates:
                    for update in updates:
                        self._process_update(update)
                        self.last_update_id = update.get('update_id', 0) + 1
                
                # Sleep between polls
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error in polling loop: {e}")
                time.sleep(5)  # Wait longer on error
    
    def _get_updates(self) -> list:
        """Get updates from Telegram API"""
        try:
            params = {
                'offset': self.last_update_id,
                'limit': 100,
                'timeout': 10
            }
            
            response = requests.get(f"{self.api_url}/getUpdates", params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    return data.get('result', [])
            
            return []
            
        except Exception as e:
            print(f"❌ Error getting updates: {e}")
            return []
    
    def _process_update(self, update: dict):
        """Process a single update from Telegram"""
        try:
            # Handle regular messages
            if 'message' in update:
                message = update['message']
                self._process_message(message)
            
            # Handle new chat members
            elif 'new_chat_member' in update:
                self._handle_new_member(update)
            
            # Handle left chat members
            elif 'left_chat_member' in update:
                self._handle_left_member(update)
                
        except Exception as e:
            print(f"❌ Error processing update: {e}")
    
    def _process_message(self, message: dict):
        """Process incoming message"""
        try:
            chat_id = str(message.get('chat', {}).get('id', ''))
            user_id = message.get('from', {}).get('id')
            text = message.get('text', '')
            user_info = message.get('from', {})
            
            # Log message for debugging
            username = user_info.get('username', 'Unknown')
            first_name = user_info.get('first_name', 'Unknown')
            print(f"📱 Message from {first_name} (@{username}) in {chat_id}: {text[:50]}...")
            
            # Handle commands
            if text.startswith('/'):
                self._handle_command(text, user_id, chat_id, user_info)
            
            # Handle new member joins (alternative method)
            if 'new_chat_members' in message:
                for new_member in message['new_chat_members']:
                    if hasattr(self.bot, 'handle_new_member_join'):
                        self.bot.handle_new_member_join(new_member, chat_id)
            
            # Handle member leaves (alternative method)
            if 'left_chat_member' in message:
                left_member = message['left_chat_member']
                if hasattr(self.bot, 'handle_member_leave'):
                    self.bot.handle_member_leave(left_member, chat_id)
                    
        except Exception as e:
            print(f"❌ Error processing message: {e}")
    
    def _handle_command(self, command: str, user_id: int, chat_id: str, user_info: dict):
        """Handle command messages"""
        try:
            print(f"🔧 Processing command: {command} from user {user_id}")
            
            # Try admin commands first
            if hasattr(self.bot, 'admin_commands'):
                admin_handled = self.bot.admin_commands.process_admin_command(command, user_id, chat_id)
                if admin_handled:
                    print(f"✅ Admin command handled: {command}")
                    return
            
            # Try hidden admin CSV commands
            if hasattr(self.bot, 'hidden_admin_csv'):
                csv_handled = self.bot.hidden_admin_csv.process_hidden_command(command, user_id, chat_id, self.bot)
                if csv_handled:
                    print(f"✅ Hidden admin command handled: {command}")
                    return
            
            # Handle basic bot commands
            self._handle_basic_commands(command, user_id, chat_id, user_info)
            
        except Exception as e:
            print(f"❌ Error handling command: {e}")
    
    def _handle_basic_commands(self, command: str, user_id: int, chat_id: str, user_info: dict):
        """Handle basic bot commands"""
        try:
            if command == '/start':
                welcome_msg = """🤖 <b>AI Trading Bot</b>

👋 Chào mừng bạn đến với AI Trading Bot!

🎯 <b>Bot Features:</b>
• 📊 AI Trading Signals
• 📈 Technical Analysis
• 🔍 Market Analysis
• 💰 Pump/Dump Detection

💡 <b>Để sử dụng bot:</b>
• Join group để nhận signals
• Theo dõi analysis reports
• Sử dụng thông tin để trade

⚠️ <b>Disclaimer:</b>
Bot chỉ cung cấp thông tin tham khảo.
Bạn chịu trách nhiệm cho quyết định đầu tư của mình.

💰 <b>Support Bot:</b>
Donate qua USDT BEP20: <code>******************************************</code>"""
                
                self._send_message(chat_id, welcome_msg)
                
                # Send QR code if available
                if hasattr(self.bot, 'send_donation_info'):
                    self.bot.send_donation_info(chat_id)
            
            elif command == '/help':
                help_msg = """📚 <b>Bot Help</b>

🤖 <b>Available Commands:</b>
• /start - Welcome message
• /help - This help message
• /status - Bot status
• /donate - Donation information

📊 <b>Bot Features:</b>
• AI Trading Analysis
• Technical Indicators
• Market Signals
• Pump/Dump Alerts

💡 <b>How to Use:</b>
1. Join our trading groups
2. Receive real-time signals
3. Follow analysis reports
4. Make informed decisions

⚠️ <b>Important:</b>
All information is for reference only.
Always do your own research before trading."""
                
                self._send_message(chat_id, help_msg)
            
            elif command == '/status':
                status_msg = f"""📊 <b>Bot Status</b>

🤖 <b>System Status:</b>
• Bot: ✅ Online
• Analysis: ✅ Running
• Signals: ✅ Active
• Time: <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>

📈 <b>Active Features:</b>
• AI Analysis: ✅
• Technical Analysis: ✅
• Volume Analysis: ✅
• Pump/Dump Detection: ✅

👥 <b>Member Management:</b>
• Auto Welcome: ✅
• Trial System: ✅
• QR Donation: ✅"""
                
                self._send_message(chat_id, status_msg)
            
            elif command == '/donate':
                if hasattr(self.bot, 'send_donation_info'):
                    self.bot.send_donation_info(chat_id)
                else:
                    donate_msg = """💰 <b>Support Our Bot</b>

🙏 Cảm ơn bạn đã muốn support bot!

💳 <b>Donation Wallet:</b>
<code>******************************************</code>

🔗 <b>Network:</b> USDT BEP20 (Binance Smart Chain)

📱 <b>How to Donate:</b>
1. Copy wallet address above
2. Send USDT BEP20 to this address
3. Contact admin for premium features

🎁 <b>Benefits:</b>
• Premium signals
• Advanced analysis
• Priority support
• Extended trial period

❤️ Mọi donation đều được trân trọng!"""
                    
                    self._send_message(chat_id, donate_msg)
            
        except Exception as e:
            print(f"❌ Error handling basic command: {e}")
    
    def _handle_new_member(self, update: dict):
        """Handle new member joining"""
        try:
            message = update.get('message', {})
            chat_id = str(message.get('chat', {}).get('id', ''))
            
            if 'new_chat_members' in message:
                for new_member in message['new_chat_members']:
                    if hasattr(self.bot, 'handle_new_member_join'):
                        self.bot.handle_new_member_join(new_member, chat_id)
                        
        except Exception as e:
            print(f"❌ Error handling new member: {e}")
    
    def _handle_left_member(self, update: dict):
        """Handle member leaving"""
        try:
            message = update.get('message', {})
            chat_id = str(message.get('chat', {}).get('id', ''))
            
            if 'left_chat_member' in message:
                left_member = message['left_chat_member']
                if hasattr(self.bot, 'handle_member_leave'):
                    self.bot.handle_member_leave(left_member, chat_id)
                    
        except Exception as e:
            print(f"❌ Error handling left member: {e}")
    
    def _send_message(self, chat_id: str, text: str, parse_mode: str = "HTML"):
        """Send message to Telegram"""
        try:
            if hasattr(self.bot, 'notifier'):
                return self.bot.notifier.send_message(text, chat_id=chat_id, parse_mode=parse_mode)
            else:
                # Fallback direct API call
                data = {
                    'chat_id': chat_id,
                    'text': text,
                    'parse_mode': parse_mode
                }
                
                response = requests.post(f"{self.api_url}/sendMessage", json=data, timeout=10)
                return response.status_code == 200
                
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return False
    
    def get_bot_info(self) -> dict:
        """Get bot information"""
        try:
            response = requests.get(f"{self.api_url}/getMe", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    return data.get('result', {})
            return {}
        except Exception as e:
            print(f"❌ Error getting bot info: {e}")
            return {}
    
    def test_connection(self) -> bool:
        """Test Telegram API connection"""
        try:
            bot_info = self.get_bot_info()
            if bot_info:
                print(f"✅ Bot connection test successful:")
                print(f"  🤖 Bot Name: {bot_info.get('first_name', 'Unknown')}")
                print(f"  👤 Username: @{bot_info.get('username', 'Unknown')}")
                print(f"  🆔 Bot ID: {bot_info.get('id', 'Unknown')}")
                return True
            else:
                print("❌ Bot connection test failed")
                return False
        except Exception as e:
            print(f"❌ Error testing connection: {e}")
            return False
