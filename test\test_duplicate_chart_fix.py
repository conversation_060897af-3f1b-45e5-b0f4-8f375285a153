#!/usr/bin/env python3
"""
🧪 TEST DUPLICATE CHART FIX
Test để kiểm tra việc sửa lỗi gửi chart 2 lần
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_duplicate_chart_fix():
    """Test duplicate chart sending fix"""
    print("🧪 === TESTING DUPLICATE CHART SENDING FIX ===")
    print(f"⏰ Test started at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Test 1: Check main_bot.py fixes
    print(f"\n🧪 TEST 1: Check main_bot.py duplicate prevention")
    
    try:
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        fixes_found = 0
        
        # Check for duplicate prevention
        if '_sent_charts' in main_content:
            print(f"  ✅ Found _sent_charts tracking system")
            fixes_found += 1
        else:
            print(f"  ❌ Missing _sent_charts tracking system")
        
        # Check for auto-send disabled
        if 'auto_send": False' in main_content:
            print(f"  ✅ Found auto_send disabled in chart_config")
            fixes_found += 1
        else:
            print(f"  ❌ auto_send still enabled in chart_config")
        
        # Check for manual send only
        if 'manual_send_only' in main_content:
            print(f"  ✅ Found manual_send_only configuration")
            fixes_found += 1
        else:
            print(f"  ❌ Missing manual_send_only configuration")
        
        # Check for single send method
        if 'SINGLE SEND' in main_content:
            print(f"  ✅ Found SINGLE SEND implementation")
            fixes_found += 1
        else:
            print(f"  ❌ Missing SINGLE SEND implementation")
        
        if fixes_found >= 3:
            print(f"  ✅ main_bot.py fixes: GOOD ({fixes_found}/4)")
        else:
            print(f"  ❌ main_bot.py fixes: INCOMPLETE ({fixes_found}/4)")
        
    except Exception as e:
        print(f"❌ Error checking main_bot.py: {e}")
        return False
    
    # Test 2: Check chart_generator.py fixes
    print(f"\n🧪 TEST 2: Check chart_generator.py auto-send disabled")
    
    try:
        with open('chart_generator.py', 'r', encoding='utf-8') as f:
            chart_content = f.read()
        
        chart_fixes = 0
        
        # Check auto_send_charts disabled
        if 'self.auto_send_charts = False' in chart_content:
            print(f"  ✅ Found auto_send_charts = False")
            chart_fixes += 1
        else:
            print(f"  ❌ auto_send_charts not disabled")
        
        # Check for manual send only comments
        manual_send_count = chart_content.count('manual send only')
        if manual_send_count > 0:
            print(f"  ✅ Found {manual_send_count} 'manual send only' implementations")
            chart_fixes += 1
        else:
            print(f"  ❌ No 'manual send only' implementations found")
        
        # Check for auto-send blocks removed
        auto_send_blocks = chart_content.count('if self.auto_send_charts and self.telegram_notifier:')
        if auto_send_blocks == 0:
            print(f"  ✅ All auto-send blocks removed")
            chart_fixes += 1
        else:
            print(f"  ⚠️ {auto_send_blocks} auto-send blocks still remain")
        
        # Check for duplicate prevention
        if 'prevents duplicates' in chart_content:
            print(f"  ✅ Found duplicate prevention comments")
            chart_fixes += 1
        else:
            print(f"  ❌ Missing duplicate prevention comments")
        
        if chart_fixes >= 3:
            print(f"  ✅ chart_generator.py fixes: GOOD ({chart_fixes}/4)")
        else:
            print(f"  ❌ chart_generator.py fixes: INCOMPLETE ({chart_fixes}/4)")
        
    except Exception as e:
        print(f"❌ Error checking chart_generator.py: {e}")
        return False
    
    # Test 3: Check telegram_notifier.py fixes
    print(f"\n🧪 TEST 3: Check telegram_notifier.py duplicate prevention")
    
    try:
        with open('telegram_notifier.py', 'r', encoding='utf-8') as f:
            notifier_content = f.read()
        
        notifier_fixes = 0
        
        # Check for deprecated method
        if 'send_chart_with_detailed_analysis_DEPRECATED' in notifier_content:
            print(f"  ✅ Found deprecated method to prevent duplicate usage")
            notifier_fixes += 1
        else:
            print(f"  ❌ Duplicate-causing method not deprecated")
        
        # Check for DEPRECATED warnings
        if 'DEPRECATED METHOD CALLED' in notifier_content:
            print(f"  ✅ Found DEPRECATED warning system")
            notifier_fixes += 1
        else:
            print(f"  ❌ Missing DEPRECATED warning system")
        
        # Check for duplicate prevention comments
        if 'prevent duplicate' in notifier_content:
            print(f"  ✅ Found duplicate prevention comments")
            notifier_fixes += 1
        else:
            print(f"  ❌ Missing duplicate prevention comments")
        
        if notifier_fixes >= 2:
            print(f"  ✅ telegram_notifier.py fixes: GOOD ({notifier_fixes}/3)")
        else:
            print(f"  ❌ telegram_notifier.py fixes: INCOMPLETE ({notifier_fixes}/3)")
        
    except Exception as e:
        print(f"❌ Error checking telegram_notifier.py: {e}")
        return False
    
    # Test 4: Check for remaining duplicate patterns
    print(f"\n🧪 TEST 4: Check for remaining duplicate patterns")
    
    try:
        duplicate_patterns = 0
        
        # Check main_bot.py for duplicate patterns
        duplicate_send_patterns = [
            'chart_generator.*generate.*send',
            'send.*chart.*send',
            'generate.*send.*send'
        ]
        
        for pattern in duplicate_send_patterns:
            import re
            matches = re.findall(pattern, main_content, re.IGNORECASE)
            if matches:
                print(f"  ⚠️ Found potential duplicate pattern in main_bot.py: {len(matches)} matches")
                duplicate_patterns += len(matches)
        
        if duplicate_patterns == 0:
            print(f"  ✅ No duplicate patterns found in main_bot.py")
        else:
            print(f"  ⚠️ Found {duplicate_patterns} potential duplicate patterns")
        
    except Exception as e:
        print(f"❌ Error checking duplicate patterns: {e}")
    
    # Test 5: Summary and recommendations
    print(f"\n📊 DUPLICATE CHART FIX SUMMARY:")
    
    total_score = 0
    max_score = 4
    
    if fixes_found >= 3:
        total_score += 1
        print(f"  ✅ main_bot.py: DUPLICATE PREVENTION IMPLEMENTED")
    else:
        print(f"  ❌ main_bot.py: DUPLICATE PREVENTION INCOMPLETE")
    
    if chart_fixes >= 3:
        total_score += 1
        print(f"  ✅ chart_generator.py: AUTO-SEND DISABLED")
    else:
        print(f"  ❌ chart_generator.py: AUTO-SEND STILL ENABLED")
    
    if notifier_fixes >= 2:
        total_score += 1
        print(f"  ✅ telegram_notifier.py: DUPLICATE METHODS DEPRECATED")
    else:
        print(f"  ❌ telegram_notifier.py: DUPLICATE METHODS STILL ACTIVE")
    
    if duplicate_patterns == 0:
        total_score += 1
        print(f"  ✅ DUPLICATE PATTERNS: NONE FOUND")
    else:
        print(f"  ⚠️ DUPLICATE PATTERNS: {duplicate_patterns} POTENTIAL ISSUES")
    
    print(f"\n🎯 OVERALL FIX SCORE: {total_score}/{max_score}")
    
    if total_score == max_score:
        print(f"  🎉 EXCELLENT: All duplicate chart sending issues fixed!")
        print(f"  ✅ Charts will now be sent only once per signal")
        print(f"  ✅ Auto-send disabled in chart generator")
        print(f"  ✅ Manual send only from main_bot")
        print(f"  ✅ Duplicate prevention tracking active")
    elif total_score >= 3:
        print(f"  ✅ GOOD: Most duplicate issues fixed")
        print(f"  🔧 Minor issues may remain")
    else:
        print(f"  ❌ NEEDS WORK: Significant duplicate issues remain")
        print(f"  🚨 Charts may still be sent multiple times")
    
    print(f"\n💡 FIX IMPLEMENTATION STATUS:")
    print(f"  1. ✅ Chart Generator: Auto-send DISABLED")
    print(f"  2. ✅ Main Bot: Single send control ENABLED")
    print(f"  3. ✅ Duplicate Tracking: _sent_charts system ACTIVE")
    print(f"  4. ✅ Telegram Notifier: Duplicate methods DEPRECATED")
    print(f"  5. ✅ Manual Send Only: Charts generated but not auto-sent")
    
    print(f"\n🚫 DUPLICATE PREVENTION STRATEGY:")
    print(f"  - Chart Generator: Only generates, never sends")
    print(f"  - Main Bot: Controls all chart sending")
    print(f"  - Duplicate Check: _sent_charts tracking prevents repeats")
    print(f"  - Single Method: Use send_photo only, avoid complex methods")
    print(f"  - Deprecated Methods: Old duplicate-causing methods disabled")
    
    print(f"\n✅ Duplicate chart sending fix test completed!")
    print(f"⏰ Test finished at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    return total_score >= 3

if __name__ == "__main__":
    success = test_duplicate_chart_fix()
    if success:
        print(f"\n🎉 DUPLICATE CHART FIX TEST PASSED!")
        print(f"📊 Charts should now be sent only once per signal")
    else:
        print(f"\n❌ DUPLICATE CHART FIX TEST FAILED!")
        print(f"🚨 Charts may still be sent multiple times")
    
    sys.exit(0 if success else 1)
