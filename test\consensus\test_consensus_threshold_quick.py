#!/usr/bin/env python3
import os
from dotenv import load_dotenv

# Load environment
load_dotenv()

# Test consensus threshold
try:
    import main_bot
    import consensus_analyzer
    
    print("🔍 CONSENSUS THRESHOLD TEST")
    print("=" * 40)
    
    # Check main_bot threshold
    threshold = main_bot.MIN_CONFIDENCE_THRESHOLD
    print(f"main_bot.MIN_CONFIDENCE_THRESHOLD = {threshold}")
    print(f"As percentage: {threshold:.1%}")
    
    # Test consensus analyzer
    analyzer = consensus_analyzer.ConsensusAnalyzer(
        min_consensus_score=0.6,
        confidence_threshold=threshold
    )
    
    print(f"ConsensusAnalyzer.confidence_threshold = {analyzer.confidence_threshold}")
    
    # Test comparison
    test_confidence = 0.807
    meets_threshold = test_confidence >= threshold
    
    print(f"\nTest comparison:")
    print(f"  Test confidence: {test_confidence:.3f} ({test_confidence:.1%})")
    print(f"  Threshold: {threshold:.3f} ({threshold:.1%})")
    print(f"  Result: {test_confidence:.1%} {'≥' if meets_threshold else '<'} {threshold:.1%}")
    print(f"  Meets threshold: {'✅ YES' if meets_threshold else '❌ NO'}")
    
    if meets_threshold:
        print("\n✅ SUCCESS: 80.7% confidence SHOULD PASS 80% threshold!")
    else:
        print("\n❌ FAILED: 80.7% confidence should pass 80% threshold!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
