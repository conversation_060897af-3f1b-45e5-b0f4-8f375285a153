#!/usr/bin/env python3
"""
🎨 ENHANCED CHART GENERATOR V2.0 - PRODUCTION READY
===================================================

Advanced Chart Generation System with Professional Visualization:
- 🎨 Professional chart generation with zero white space optimization
- 📊 Advanced technical indicators with intelligent plotting
- 🎯 Multi-chart support with adaptive layouts
- 📱 Telegram-optimized image generation and auto-management
- 🚀 Performance optimized for high-frequency chart generation
- 🛡️ Comprehensive error handling and resource management

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
import matplotlib.patches as patches
import matplotlib.gridspec as gridspec
import pandas as pd
import numpy as np
import os
import time
import warnings
from datetime import datetime, timedelta
import matplotlib.colors as mcolors
from typing import Dict, Any, List, Optional, Tuple
import mplfinance as mpf
import copy
import traceback

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import seaborn as sns
    AVAILABLE_MODULES['seaborn'] = True
    print("✅ seaborn imported successfully - Enhanced styling available")
except ImportError:
    AVAILABLE_MODULES['seaborn'] = False
    print("⚠️ seaborn not available - Using basic styling")

try:
    from PIL import Image, ImageDraw, ImageFont
    AVAILABLE_MODULES['PIL'] = True
    print("✅ PIL imported successfully - Image processing available")
except ImportError:
    AVAILABLE_MODULES['PIL'] = False
    print("⚠️ PIL not available - Limited image processing")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    AVAILABLE_MODULES['plotly'] = True
    print("✅ plotly imported successfully - Interactive charts available")
except ImportError:
    AVAILABLE_MODULES['plotly'] = False
    print("⚠️ plotly not available - Static charts only")

print(f"🎨 Chart Generator V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class EnhancedChartGenerator:
    """
    🎨 ENHANCED CHART GENERATOR V2.0 - PRODUCTION READY
    ===================================================

    Advanced Chart Generation System with comprehensive features:
    - 🎨 Professional chart generation with zero white space optimization
    - 📊 Advanced technical indicators with intelligent plotting
    - 🎯 Multi-chart support with adaptive layouts
    - 📱 Telegram-optimized image generation and auto-management
    - 🚀 Performance optimized for high-frequency chart generation
    - 🛡️ Comprehensive error handling and resource management
    """

    def __init__(self, output_dir="charts", telegram_notifier=None,
                 enable_auto_delete=True, enable_advanced_styling=True,
                 enable_interactive_charts=False, max_storage_mb=500):
        """
        Initialize Enhanced Chart Generator V2.0.

        Args:
            output_dir: Directory for chart output
            telegram_notifier: Telegram notification service
            enable_auto_delete: Enable automatic chart cleanup
            enable_advanced_styling: Enable advanced styling features
            enable_interactive_charts: Enable interactive chart generation
            max_storage_mb: Maximum storage limit in MB
        """
        print("🎨 Initializing Enhanced Chart Generator V2.0...")

        # Core configuration
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Services
        self.telegram_notifier = telegram_notifier

        # Enhanced features
        self.enable_auto_delete = enable_auto_delete
        self.enable_advanced_styling = enable_advanced_styling and AVAILABLE_MODULES.get('seaborn', False)
        self.enable_interactive_charts = enable_interactive_charts and AVAILABLE_MODULES.get('plotly', False)
        self.max_storage_mb = max(100, min(2000, max_storage_mb))  # 100MB-2GB

        # Legacy compatibility
        self.auto_send_charts = False  # Always False to prevent duplicates
        self.auto_delete_after_send = enable_auto_delete
        self.auto_delete_failed_charts = enable_auto_delete
        self.keep_charts_minutes = 30
        self.max_chart_storage_mb = max_storage_mb

        # Performance tracking
        self.generation_stats = {
            "total_charts_generated": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "charts_sent": 0,
            "charts_deleted": 0,
            "average_generation_time": 0.0
        }
        
        # 📊 CHART TRACKING với Auto-Delete
        self.sent_charts = set()  # Track charts đã gửi thành công
        self.failed_charts = set()  # Track charts gửi thất bại
        self.chart_creation_times = {}  # Track thời gian tạo chart
        self.deleted_charts_count = 0  # Đếm số chart đã xóa
        
        # 🎨 ENHANCED: Beautiful modern color schemes for stunning Telegram charts
        self.color_schemes = {
            'fibonacci': {
                'primary': '#FF6B6B',      # Coral Red - Eye-catching
                'secondary': '#4ECDC4',    # Turquoise - Calming
                'tertiary': '#45B7D1',     # Sky Blue - Professional
                'background': '#1A1A2E',   # Dark Navy - Modern
                'text': '#FFFFFF',         # Pure White - High contrast
                'grid': '#16213E',         # Darker Navy - Subtle
                'accent': '#FFD93D',       # Golden Yellow - Highlight
                'success': '#6BCF7F',      # Mint Green - Positive
                'danger': '#FF6B6B',       # Coral Red - Alert
                'gradient_start': '#FF6B6B',
                'gradient_end': '#4ECDC4',
                'shadow': '#00000040'      # Stronger shadow for dark theme
            },
            'volume_profile': {
                'primary': '#667EEA',      # Purple Blue - Elegant
                'secondary': '#764BA2',    # Deep Purple - Rich
                'tertiary': '#F093FB',     # Pink Purple - Vibrant
                'background': '#0F0F23',   # Deep Dark - Premium
                'text': '#FFFFFF',         # Pure White - Clear
                'grid': '#1A1A2E',         # Dark Navy - Minimal
                'accent': '#4FACFE',       # Light Blue - Fresh
                'success': '#00D4AA',      # Teal Green - Growth
                'danger': '#FF5E5B'        # Bright Red - Warning
            },
            'ai_analysis': {
                'primary': '#A8EDEA',      # Mint Cyan - AI Theme
                'secondary': '#FED6E3',    # Soft Pink - Gentle
                'tertiary': '#D299C2',     # Lavender - Sophisticated
                'background': '#0D1421',   # Midnight Blue - Tech
                'text': '#FFFFFF',         # Pure White - Readable
                'grid': '#1A202C',         # Dark Gray - Clean
                'accent': '#68D391',       # Green Mint - AI Success
                'success': '#48BB78',      # Forest Green - Confidence
                'danger': '#F56565'        # Soft Red - Caution
            },
            'pump_dump': {
                'primary': '#FF6B9D',      # Hot Pink - Attention
                'secondary': '#C44569',    # Deep Pink - Intensity
                'tertiary': '#F8B500',     # Amber - Energy
                'background': '#2C2C54',   # Dark Purple - Dramatic
                'text': '#FFFFFF',         # Pure White - Bold
                'grid': '#40407A',         # Medium Purple - Subtle
                'accent': '#00D2D3',       # Cyan - Electric
                'success': '#5F27CD',      # Purple - Pump
                'danger': '#FF3838'        # Bright Red - Dump
            },
            'consensus': {
                'primary': '#00D2D3',      # Electric Cyan - Modern
                'secondary': '#FF9FF3',    # Bright Pink - Dynamic
                'tertiary': '#54A0FF',     # Bright Blue - Trust
                'background': '#1E1E30',   # Dark Purple - Professional
                'text': '#FFFFFF',         # Pure White - Clear
                'grid': '#2F2F42',         # Medium Dark - Clean
                'accent': '#FFDD59',       # Bright Yellow - Signal
                'success': '#26DE81',      # Neon Green - Success
                'danger': '#FC5C65'        # Coral Red - Alert
            },
            'minimal': {
                'primary': '#667EEA',      # Clean Blue - Simple
                'secondary': '#764BA2',    # Purple - Elegant
                'tertiary': '#F093FB',     # Light Purple - Soft
                'background': '#FFFFFF',   # Pure White - Clean
                'text': '#2D3748',         # Dark Gray - Readable
                'grid': '#E2E8F0',         # Light Gray - Minimal
                'accent': '#4299E1',       # Blue - Accent
                'success': '#48BB78',      # Green - Positive
                'danger': '#F56565'        # Red - Negative
            }
        }
        
        # 📊 ENHANCED CHART STATISTICS với Auto-Delete metrics
        self.chart_stats = {
            'total_generated': 0,
            'total_sent': 0,
            'total_deleted': 0,  # ✅ NEW
            'total_failed_sends': 0,  # ✅ NEW
            'by_type': {},
            'by_coin': {},
            'failures': [],
            'telegram_sends': {
                'success': 0,
                'failed': 0,
                'last_send': None
            },
            'auto_delete': {  # ✅ NEW Auto-Delete metrics
                'deleted_after_send': 0,
                'deleted_failed_charts': 0,
                'deleted_expired_charts': 0,
                'space_saved_mb': 0,
                'last_cleanup': None
            }
        }
        
        # ✅ NEW V6.0: Zero White Space Configuration
        self.zero_whitespace_config = {
            'figure_margins': {'left': 0.02, 'right': 0.98, 'top': 0.96, 'bottom': 0.04},
            'subplot_spacing': {'hspace': 0.05, 'wspace': 0.02},
            'tight_layout_pad': 0.5,
            'bbox_inches_pad': 0.02,
            'axis_margins': {'x': 0.01, 'y': 0.02},
            'title_spacing': 0.02,
            'legend_spacing': 0.01,
            'annotation_spacing': 0.01
        }

        print(f"🎨 Enhanced Chart Generator V6.0 initialized với ZERO WHITE SPACE")
        print(f"  📱 Telegram Integration: {'❌ DISABLED' if not self.auto_send_charts else '✅ ENABLED'} (prevents duplicates)")
        print(f"  🚫 Auto-Send Charts: {'❌ DISABLED' if not self.auto_send_charts else '✅ ENABLED'} (manual send only)")
        print(f"  🗑️ Auto-Delete After Send: {'✅ ENABLED' if self.auto_delete_after_send else '❌ DISABLED'}")
        print(f"  🧹 Auto-Delete Failed Charts: {'✅ ENABLED' if self.auto_delete_failed_charts else '❌ DISABLED'}")
        print(f"  ⏱️ Keep Charts Minutes: {self.keep_charts_minutes}")
        print(f"  💾 Max Storage: {self.max_chart_storage_mb} MB")
        print(f"  🎨 Color Schemes: {len(self.color_schemes)} available")
        print(f"  🚀 Zero White Space: ✅ ENABLED (V6.0 Feature)")
        print(f"  📐 Space Optimization: Maximum visual density")
        print(f"  🔧 Duplicate Prevention: Charts generated but not auto-sent")

    # ============================================================================
    # 🎨 BEAUTIFUL CHART DESIGN SYSTEM - V7.0
    # ============================================================================

    def _create_beautiful_chart_base(self, figsize: Tuple[int, int] = (12, 8),
                                   color_scheme: str = 'minimal',
                                   title: str = None) -> Tuple[plt.Figure, Dict[str, str]]:
        """🎨 Create beautiful chart base with modern design."""
        try:
            # Get color scheme
            colors = self.color_schemes.get(color_scheme, self.color_schemes['minimal'])

            # Create figure with high DPI for crisp images
            fig = plt.figure(figsize=figsize, dpi=150, facecolor=colors['background'])

            # Set modern style
            plt.style.use('default')  # Reset to clean style

            # Configure matplotlib for beautiful charts
            plt.rcParams.update({
                'font.family': 'sans-serif',
                'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
                'font.size': 10,
                'axes.linewidth': 1.2,
                'axes.spines.left': True,
                'axes.spines.bottom': True,
                'axes.spines.top': False,
                'axes.spines.right': False,
                'xtick.bottom': True,
                'xtick.top': False,
                'ytick.left': True,
                'ytick.right': False,
                'axes.grid': True,
                'grid.alpha': 0.3,
                'grid.linewidth': 0.8,
                'figure.autolayout': True
            })

            # Add minimal title if provided
            if title:
                fig.suptitle(title, fontsize=16, fontweight='bold',
                           color=colors['text'], y=0.95, ha='center')

            return fig, colors

        except Exception as e:
            print(f"❌ Error creating beautiful chart base: {e}")
            # Fallback to simple figure
            fig = plt.figure(figsize=figsize, dpi=150)
            colors = self.color_schemes['minimal']
            return fig, colors

    def _style_beautiful_axis(self, ax, colors: Dict[str, str],
                            minimal_info: bool = True) -> None:
        """🎨 Style axis for beautiful appearance with minimal information."""
        try:
            # Set background
            ax.set_facecolor(colors['background'])

            # Style spines
            for spine in ax.spines.values():
                spine.set_visible(False)

            # Show only left and bottom spines with custom color
            ax.spines['left'].set_visible(True)
            ax.spines['bottom'].set_visible(True)
            ax.spines['left'].set_color(colors['grid'])
            ax.spines['bottom'].set_color(colors['grid'])
            ax.spines['left'].set_linewidth(1)
            ax.spines['bottom'].set_linewidth(1)

            # Style ticks
            ax.tick_params(
                axis='both',
                which='major',
                labelsize=9,
                labelcolor=colors['text'],
                color=colors['grid'],
                length=4,
                width=1
            )

            # Minimal grid
            ax.grid(True, alpha=0.2, linewidth=0.5, color=colors['grid'])

            # Reduce number of ticks for cleaner look
            if minimal_info:
                ax.locator_params(axis='x', nbins=6)
                ax.locator_params(axis='y', nbins=6)

            # Style labels
            ax.xaxis.label.set_color(colors['text'])
            ax.yaxis.label.set_color(colors['text'])

        except Exception as e:
            print(f"❌ Error styling beautiful axis: {e}")

    def _add_minimal_price_info(self, ax, coin: str, current_price: float,
                              colors: Dict[str, str], position: str = 'top-right') -> None:
        """💰 Add minimal price information to chart."""
        try:
            # Create minimal price text
            price_text = f"{coin}\n${current_price:.6f}"

            # Position mapping
            positions = {
                'top-right': (0.98, 0.95),
                'top-left': (0.02, 0.95),
                'bottom-right': (0.98, 0.05),
                'bottom-left': (0.02, 0.05)
            }

            x, y = positions.get(position, positions['top-right'])
            ha = 'right' if 'right' in position else 'left'
            va = 'top' if 'top' in position else 'bottom'

            # Add text with beautiful styling
            ax.text(x, y, price_text, transform=ax.transAxes,
                   fontsize=12, fontweight='bold', color=colors['text'],
                   ha=ha, va=va,
                   bbox=dict(boxstyle='round,pad=0.5',
                           facecolor=colors['accent'],
                           alpha=0.8,
                           edgecolor='none'))

        except Exception as e:
            print(f"❌ Error adding minimal price info: {e}")

    def _draw_beautiful_candlesticks(self, ax, ohlcv_data: pd.DataFrame,
                                   colors: Dict[str, str], minimal: bool = True) -> None:
        """🕯️ Draw beautiful minimal candlesticks."""
        try:
            # Limit data for cleaner look if minimal
            if minimal and len(ohlcv_data) > 100:
                ohlcv_data = ohlcv_data.tail(100)  # Show only last 100 candles

            # Calculate colors for candles
            up_color = colors.get('success', '#26DE81')
            down_color = colors.get('danger', '#FC5C65')

            # Draw candlesticks with modern style
            for i, (idx, row) in enumerate(ohlcv_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # Determine color
                color = up_color if close_price >= open_price else down_color

                # Draw high-low line (wick)
                ax.plot([i, i], [low_price, high_price],
                       color=color, linewidth=1, alpha=0.8)

                # Draw open-close rectangle (body)
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                # Make body slightly transparent for modern look
                rect = plt.Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                   facecolor=color, alpha=0.8, edgecolor=color)
                ax.add_patch(rect)

            # Set x-axis limits
            ax.set_xlim(-1, len(ohlcv_data))

            # Minimal x-axis labels
            if minimal:
                # Show only a few time labels
                step = max(1, len(ohlcv_data) // 5)
                x_ticks = range(0, len(ohlcv_data), step)
                x_labels = [ohlcv_data.index[i].strftime('%H:%M') if i < len(ohlcv_data) else ''
                           for i in x_ticks]
                ax.set_xticks(x_ticks)
                ax.set_xticklabels(x_labels, rotation=0, ha='center')

        except Exception as e:
            print(f"❌ Error drawing beautiful candlesticks: {e}")

    def _add_beautiful_indicators(self, ax, ohlcv_data: pd.DataFrame,
                                colors: Dict[str, str], indicator_type: str = 'minimal') -> None:
        """📊 Add beautiful minimal indicators."""
        try:
            if indicator_type == 'minimal':
                # Add only essential indicators

                # Simple moving average
                if len(ohlcv_data) >= 20:
                    sma_20 = ohlcv_data['close'].rolling(window=20).mean()
                    ax.plot(range(len(sma_20)), sma_20,
                           color=colors['primary'], linewidth=2, alpha=0.7,
                           label='SMA 20')

                # Support/Resistance levels (simple)
                recent_high = ohlcv_data['high'].tail(50).max()
                recent_low = ohlcv_data['low'].tail(50).min()

                ax.axhline(y=recent_high, color=colors['danger'],
                          linewidth=1, alpha=0.5, linestyle='--')
                ax.axhline(y=recent_low, color=colors['success'],
                          linewidth=1, alpha=0.5, linestyle='--')

        except Exception as e:
            print(f"❌ Error adding beautiful indicators: {e}")

    def _create_fallback_chart(self, coin: str, chart_type: str, colors: Dict[str, str]) -> Optional[str]:
        """
        ✅ FIX: Create fallback chart instead of returning None
        Always provides a basic chart when main generation fails
        """
        try:
            print(f"    🔧 Creating fallback chart for {coin} ({chart_type})")

            # Create simple fallback chart
            fig, ax = plt.subplots(figsize=(10, 6), dpi=100, facecolor=colors['background'])

            # Set background
            ax.set_facecolor(colors['background'])

            # Create simple placeholder data
            x = np.linspace(0, 10, 50)
            y = np.sin(x) * 100 + 50000  # Simple sine wave around 50k

            # Plot simple line
            ax.plot(x, y, color=colors['primary'], linewidth=2, alpha=0.8)

            # Add title and labels
            ax.set_title(f"{coin} - {chart_type.upper()} (Fallback Chart)",
                        color=colors['text'], fontsize=14, fontweight='bold')
            ax.set_xlabel("Time", color=colors['text'])
            ax.set_ylabel("Price", color=colors['text'])

            # Style the chart
            ax.tick_params(colors=colors['text'])
            ax.grid(True, alpha=0.3, color=colors['grid'])

            # Remove spines
            for spine in ax.spines.values():
                spine.set_visible(False)

            # Add fallback message
            ax.text(0.5, 0.5, f"Fallback Chart\n{coin}\nChart generation failed\nUsing emergency fallback",
                   transform=ax.transAxes, ha='center', va='center',
                   fontsize=12, color=colors['text'], alpha=0.7,
                   bbox=dict(boxstyle='round,pad=0.5', facecolor=colors['accent'], alpha=0.3))

            # Generate filename
            timestamp = int(time.time())
            filename = f"fallback_{chart_type}_{coin}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # Save chart
            plt.tight_layout()
            plt.savefig(filepath, dpi=100, bbox_inches='tight',
                       facecolor=colors['background'], edgecolor='none')
            plt.close(fig)

            print(f"    ✅ Fallback chart created: {filename}")
            return filepath

        except Exception as e:
            print(f"    ❌ Critical error creating fallback chart: {e}")
            plt.close('all')  # Clean up any open figures
            return None

    def _save_beautiful_chart(self, fig, filepath: str, colors: Dict[str, str]) -> bool:
        """💾 Save beautiful chart with optimal settings."""
        try:
            # Final styling
            fig.patch.set_facecolor(colors['background'])

            # Tight layout for minimal whitespace
            plt.tight_layout(pad=1.0)

            # Save with high quality
            plt.savefig(
                filepath,
                dpi=150,  # High quality but not too large for Telegram
                bbox_inches='tight',
                pad_inches=0.1,
                facecolor=colors['background'],
                edgecolor='none',
                format='png'
            )

            plt.close(fig)
            print(f"      💾 Beautiful chart saved: {os.path.basename(filepath)}")
            return True

        except Exception as e:
            print(f"      ❌ Error saving beautiful chart: {e}")
            plt.close(fig)
            return False

    # ============================================================================
    # 🚀 ZERO WHITE SPACE OPTIMIZATION SYSTEM - V6.0
    # ============================================================================

    def _apply_zero_whitespace_layout(self, fig, gs=None, title_text: str = None) -> None:
        """🚀 Apply zero white space optimization to figure."""
        try:
            config = self.zero_whitespace_config

            # 1. Set figure margins to minimum
            fig.subplots_adjust(
                left=config['figure_margins']['left'],
                right=config['figure_margins']['right'],
                top=config['figure_margins']['top'],
                bottom=config['figure_margins']['bottom'],
                hspace=config['subplot_spacing']['hspace'],
                wspace=config['subplot_spacing']['wspace']
            )

            # 2. Apply tight layout with minimal padding
            plt.tight_layout(pad=config['tight_layout_pad'])

            # 3. Optimize individual axes
            for ax in fig.get_axes():
                self._optimize_axis_spacing(ax)

            # 4. Add title with minimal spacing if provided
            if title_text:
                fig.suptitle(title_text, fontsize=14, fontweight='bold',
                           y=1-config['title_spacing'], ha='center')

            print(f"      🚀 Zero white space optimization applied")

        except Exception as e:
            print(f"      ⚠️ Error applying zero white space layout: {e}")

    def _optimize_axis_spacing(self, ax) -> None:
        """📐 Optimize individual axis spacing."""
        try:
            config = self.zero_whitespace_config

            # Set axis margins to minimum
            ax.margins(x=config['axis_margins']['x'], y=config['axis_margins']['y'])

            # Optimize tick parameters
            ax.tick_params(axis='both', which='major', labelsize=8, pad=2)
            ax.tick_params(axis='both', which='minor', labelsize=7, pad=1)

            # Optimize spine positioning
            for spine in ax.spines.values():
                spine.set_linewidth(0.8)

            # Optimize grid
            if ax.grid:
                ax.grid(True, alpha=0.3, linewidth=0.5)

        except Exception as e:
            print(f"      ⚠️ Error optimizing axis spacing: {e}")

    def _create_optimized_gridspec(self, nrows: int, ncols: int,
                                 height_ratios: List[float] = None,
                                 width_ratios: List[float] = None) -> gridspec.GridSpec:
        """📊 Create optimized GridSpec with minimal spacing."""
        try:
            config = self.zero_whitespace_config

            gs = gridspec.GridSpec(
                nrows, ncols,
                height_ratios=height_ratios,
                width_ratios=width_ratios,
                hspace=config['subplot_spacing']['hspace'],
                wspace=config['subplot_spacing']['wspace'],
                left=config['figure_margins']['left'],
                right=config['figure_margins']['right'],
                top=config['figure_margins']['top'] - 0.05,  # Space for title
                bottom=config['figure_margins']['bottom']
            )

            return gs

        except Exception as e:
            print(f"      ❌ Error creating optimized GridSpec: {e}")
            # Fallback to standard GridSpec
            return gridspec.GridSpec(nrows, ncols, height_ratios=height_ratios, width_ratios=width_ratios)

    def _save_optimized_chart(self, fig, filepath: str, dpi: int = 200) -> bool:
        """💾 Save chart with zero white space optimization."""
        try:
            config = self.zero_whitespace_config

            # Final optimization before saving
            plt.tight_layout(pad=config['tight_layout_pad'])

            # Save with minimal bbox padding
            plt.savefig(
                filepath,
                dpi=dpi,
                bbox_inches='tight',
                pad_inches=config['bbox_inches_pad'],
                facecolor='white',
                edgecolor='none',
                format='png'
                # Note: optimize=True not supported in all matplotlib versions
            )

            plt.close(fig)
            print(f"      💾 Optimized chart saved: {os.path.basename(filepath)}")
            return True

        except Exception as e:
            print(f"      ❌ Error saving optimized chart: {e}")
            plt.close(fig)
            return False

    # ============================================================================
    # 🗑️ AUTO-DELETE SYSTEM - HỆ THỐNG XÓA TỰ ĐỘNG
    # ============================================================================
    
    def _auto_delete_chart_after_send(self, chart_path: str, send_success: bool) -> bool:
        """🗑️ ENHANCED: Tự động xóa chart ngay sau khi gửi thành công."""
        try:
            if not os.path.exists(chart_path):
                print(f"      ℹ️ Chart already deleted: {os.path.basename(chart_path)}")
                return True  # File already deleted

            should_delete = False
            delete_reason = ""

            # ✅ ENHANCED: Xóa NGAY LẬP TỨC nếu gửi thành công
            if send_success and self.auto_delete_after_send:
                should_delete = True
                delete_reason = "sent_successfully_immediate"
                self.chart_stats['auto_delete']['deleted_after_send'] += 1
                print(f"      🚀 IMMEDIATE DELETE: Chart sent successfully, deleting now...")

            # Xóa nếu gửi thất bại và auto-delete failed charts enabled
            elif not send_success and self.auto_delete_failed_charts:
                should_delete = True
                delete_reason = "send_failed"
                self.chart_stats['auto_delete']['deleted_failed_charts'] += 1
                print(f"      🗑️ CLEANUP: Chart send failed, deleting...")

            if should_delete:
                # Get file info before deletion
                file_size_mb = os.path.getsize(chart_path) / (1024 * 1024)
                filename = os.path.basename(chart_path)

                # ✅ ENHANCED: Immediate deletion with verification
                try:
                    os.remove(chart_path)

                    # Verify deletion
                    if not os.path.exists(chart_path):
                        print(f"      ✅ DELETED: {filename} ({file_size_mb:.2f}MB) - {delete_reason}")

                        # Update statistics
                        self.chart_stats['total_deleted'] += 1
                        self.chart_stats['auto_delete']['space_saved_mb'] += file_size_mb
                        self.chart_stats['auto_delete']['last_delete_time'] = time.time()

                        # Remove from tracking sets
                        if chart_path in self.chart_creation_times:
                            del self.chart_creation_times[chart_path]

                        # Remove from sent/failed tracking
                        self.sent_charts.discard(chart_path)
                        self.failed_charts.discard(chart_path)

                        return True
                    else:
                        print(f"      ⚠️ DELETION FAILED: File still exists after delete attempt")
                        return False

                except PermissionError:
                    print(f"      ❌ PERMISSION ERROR: Cannot delete {filename} (file in use)")
                    return False
                except Exception as delete_error:
                    print(f"      ❌ DELETE ERROR: {delete_error}")
                    return False
            else:
                print(f"      ℹ️ KEEPING: {os.path.basename(chart_path)} (auto-delete disabled or conditions not met)")

            return False

        except Exception as e:
            print(f"      ❌ Error in auto-delete system: {e}")
            return False
    
    def _cleanup_expired_charts(self) -> int:
        """🧹 Cleanup charts cũ quá thời gian giữ."""
        try:
            if not os.path.exists(self.output_dir):
                return 0
            
            current_time = time.time()
            expiry_seconds = self.keep_charts_minutes * 60
            deleted_count = 0
            space_saved = 0
            
            for filename in os.listdir(self.output_dir):
                if not filename.endswith('.png'):
                    continue
                
                file_path = os.path.join(self.output_dir, filename)
                
                # Check if file exists in creation times tracking
                creation_time = self.chart_creation_times.get(file_path)
                
                if creation_time is None:
                    # Use file modification time as fallback
                    creation_time = os.path.getmtime(file_path)
                
                # Check if expired
                if current_time - creation_time > expiry_seconds:
                    try:
                        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        os.remove(file_path)
                        
                        deleted_count += 1
                        space_saved += file_size_mb
                        
                        # Remove from tracking
                        if file_path in self.chart_creation_times:
                            del self.chart_creation_times[file_path]
                        
                        print(f"      🧹 Expired chart deleted: {filename} - {file_size_mb:.2f}MB")
                        
                    except Exception as delete_error:
                        print(f"      ❌ Error deleting expired chart {filename}: {delete_error}")
            
            if deleted_count > 0:
                self.chart_stats['auto_delete']['deleted_expired_charts'] += deleted_count
                self.chart_stats['auto_delete']['space_saved_mb'] += space_saved
                self.chart_stats['auto_delete']['last_cleanup'] = datetime.now().isoformat()
                
                print(f"      ✅ Expired charts cleanup: {deleted_count} files, {space_saved:.2f}MB saved")
            
            return deleted_count
            
        except Exception as e:
            print(f"      ❌ Error cleaning up expired charts: {e}")
            return 0
    
    def _check_storage_limit(self) -> bool:
        """💾 Kiểm tra và xử lý giới hạn dung lượng."""
        try:
            if not os.path.exists(self.output_dir):
                return True
            
            # Calculate total size
            total_size_mb = 0
            file_count = 0
            
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.png'):
                    file_path = os.path.join(self.output_dir, filename)
                    if os.path.exists(file_path):
                        total_size_mb += os.path.getsize(file_path) / (1024 * 1024)
                        file_count += 1
            
            print(f"      💾 Chart storage: {total_size_mb:.2f}MB ({file_count} files) / {self.max_chart_storage_mb}MB limit")
            
            # If over limit, cleanup old files
            if total_size_mb > self.max_chart_storage_mb:
                print(f"      ⚠️ Storage limit exceeded, forcing cleanup...")
                
                # Get all chart files with creation times
                chart_files = []
                for filename in os.listdir(self.output_dir):
                    if filename.endswith('.png'):
                        file_path = os.path.join(self.output_dir, filename)
                        creation_time = self.chart_creation_times.get(file_path, os.path.getmtime(file_path))
                        file_size = os.path.getsize(file_path) / (1024 * 1024)
                        chart_files.append((file_path, creation_time, file_size))
                
                # Sort by creation time (oldest first)
                chart_files.sort(key=lambda x: x[1])
                
                # Delete oldest files until under limit
                deleted_count = 0
                space_freed = 0
                
                for file_path, creation_time, file_size in chart_files:
                    if total_size_mb - space_freed <= self.max_chart_storage_mb * 0.8:  # Keep 20% buffer
                        break
                    
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                        space_freed += file_size
                        
                        # Remove from tracking
                        if file_path in self.chart_creation_times:
                            del self.chart_creation_times[file_path]
                        
                        print(f"        🗑️ Deleted for storage: {os.path.basename(file_path)} - {file_size:.2f}MB")
                        
                    except Exception as delete_error:
                        print(f"        ❌ Error deleting for storage: {delete_error}")
                
                self.chart_stats['auto_delete']['space_saved_mb'] += space_freed
                print(f"      ✅ Storage cleanup: {deleted_count} files, {space_freed:.2f}MB freed")
            
            return total_size_mb <= self.max_chart_storage_mb
            
        except Exception as e:
            print(f"      ❌ Error checking storage limit: {e}")
            return True
    
    def _track_chart_creation(self, chart_path: str):
        """📊 Track chart creation for auto-delete system."""
        try:
            self.chart_creation_times[chart_path] = time.time()
            self.chart_stats['total_generated'] += 1
        except Exception as e:
            print(f"      ⚠️ Error tracking chart creation: {e}")
    
    def _get_storage_stats(self) -> Dict[str, Any]:
        """📊 Get storage statistics."""
        try:
            stats = {
                "total_files": 0,
                "total_size_mb": 0,
                "storage_usage_percent": 0,
                "files_in_tracking": len(self.chart_creation_times),
                "auto_delete_stats": self.chart_stats['auto_delete'].copy()
            }
            
            if os.path.exists(self.output_dir):
                for filename in os.listdir(self.output_dir):
                    if filename.endswith('.png'):
                        file_path = os.path.join(self.output_dir, filename)
                        if os.path.exists(file_path):
                            stats["total_files"] += 1
                            stats["total_size_mb"] += os.path.getsize(file_path) / (1024 * 1024)
                
                stats["storage_usage_percent"] = (stats["total_size_mb"] / self.max_chart_storage_mb) * 100
            
            return stats
            
        except Exception as e:
            return {"error": str(e)}

    # ============================================================================
    # 🎨 BEAUTIFUL MINIMAL CHARTS - V7.0
    # ============================================================================

    def generate_beautiful_minimal_chart(self, coin: str, chart_type: str,
                                       ohlcv_data: pd.DataFrame, current_price: float,
                                       data: Dict[str, Any] = None) -> Optional[str]:
        """🎨 Generate beautiful minimal chart for Telegram."""
        try:
            print(f"🎨 Generating beautiful minimal {chart_type} chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"BEAUTIFUL_{chart_type.upper()}_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # Choose color scheme based on chart type
            color_scheme_map = {
                'fibonacci': 'fibonacci',
                'volume_profile': 'volume_profile',
                'ai_analysis': 'ai_analysis',
                'pump_alert': 'pump_dump',
                'dump_alert': 'pump_dump',
                'consensus': 'consensus'
            }

            color_scheme = color_scheme_map.get(chart_type, 'minimal')

            # Create beautiful chart base
            fig, colors = self._create_beautiful_chart_base(
                figsize=(12, 8),
                color_scheme=color_scheme,
                title=f"{chart_type.replace('_', ' ').title()} - {coin}"
            )

            # Create main price chart
            ax_main = fig.add_subplot(111)
            self._style_beautiful_axis(ax_main, colors, minimal_info=True)

            # Draw beautiful candlesticks
            self._draw_beautiful_candlesticks(ax_main, ohlcv_data, colors, minimal=True)

            # Add minimal indicators based on chart type
            if chart_type == 'fibonacci' and data:
                self._add_fibonacci_levels_minimal(ax_main, data, colors, current_price)
            elif chart_type == 'volume_profile' and data:
                self._add_volume_profile_minimal(ax_main, data, colors, current_price)
            elif chart_type in ['pump_alert', 'dump_alert'] and data:
                self._add_pump_dump_indicators_minimal(ax_main, data, colors, current_price, chart_type)
            else:
                # Default minimal indicators
                self._add_beautiful_indicators(ax_main, ohlcv_data, colors, 'minimal')

            # Add minimal price info
            self._add_minimal_price_info(ax_main, coin, current_price, colors, 'top-right')

            # Add subtle timestamp
            timestamp_text = datetime.now().strftime('%H:%M')
            ax_main.text(0.02, 0.02, timestamp_text, transform=ax_main.transAxes,
                        fontsize=8, color=colors['text'], alpha=0.6)

            # Save beautiful chart
            success = self._save_beautiful_chart(fig, filepath, colors)

            if success:
                self._track_chart_creation(filepath)
                self._log_chart_generation(f"beautiful_{chart_type}", coin, True)
                return filepath
            else:
                self._log_chart_generation(f"beautiful_{chart_type}", coin, False)
                # ✅ FIX: Try to create fallback chart instead of returning None
                return self._create_fallback_chart(coin, chart_type, colors)

        except Exception as e:
            print(f"❌ Error generating beautiful minimal chart: {e}")
            traceback.print_exc()
            # ✅ FIX: Try to create fallback chart instead of returning None
            return self._create_fallback_chart(coin, chart_type, self.color_schemes['minimal'])

    def _add_fibonacci_levels_minimal(self, ax, fibonacci_data: Dict[str, Any],
                                    colors: Dict[str, str], current_price: float) -> None:
        """🌀 Add minimal Fibonacci levels."""
        try:
            retracement_levels = fibonacci_data.get('retracement_levels', [])

            # Show only key levels
            key_levels = [0.236, 0.382, 0.618, 0.786]

            for level in retracement_levels[:4]:  # Top 4 levels only
                price = level.get('price', 0)
                ratio = level.get('ratio', 0)

                if ratio in key_levels:
                    ax.axhline(y=price, color=colors['primary'],
                              linewidth=1.5, alpha=0.7, linestyle='--')

                    # Minimal label
                    ax.text(0.98, price, f'{ratio:.1%}',
                           transform=ax.get_yaxis_transform(),
                           ha='right', va='center', fontsize=8,
                           color=colors['text'], alpha=0.8)

        except Exception as e:
            print(f"❌ Error adding minimal Fibonacci levels: {e}")

    def _add_volume_profile_minimal(self, ax, volume_data: Dict[str, Any],
                                  colors: Dict[str, str], current_price: float) -> None:
        """📊 Add minimal volume profile."""
        try:
            vpoc = volume_data.get('vpoc', {})
            vpoc_price = vpoc.get('price', current_price)

            # VPOC line
            ax.axhline(y=vpoc_price, color=colors['primary'],
                      linewidth=2, alpha=0.8, label='VPOC')

            # Value area (simplified)
            value_area = volume_data.get('value_area', {})
            if value_area:
                va_high = value_area.get('high', current_price * 1.02)
                va_low = value_area.get('low', current_price * 0.98)

                ax.axhspan(va_low, va_high, alpha=0.1, color=colors['secondary'])

        except Exception as e:
            print(f"❌ Error adding minimal volume profile: {e}")

    def _add_pump_dump_indicators_minimal(self, ax, data: Dict[str, Any],
                                        colors: Dict[str, str], current_price: float,
                                        chart_type: str) -> None:
        """🚀📉 Add minimal pump/dump indicators."""
        try:
            # Add current price line with alert color
            alert_color = colors['success'] if chart_type == 'pump_alert' else colors['danger']

            ax.axhline(y=current_price, color=alert_color,
                      linewidth=3, alpha=0.8, label=f'{chart_type.replace("_", " ").title()}')

            # Add probability indicator
            probability = data.get('pump_probability' if chart_type == 'pump_alert' else 'dump_probability', 0)

            ax.text(0.02, 0.98, f'{probability:.0%}', transform=ax.transAxes,
                   fontsize=20, fontweight='bold', color=alert_color,
                   ha='left', va='top')

        except Exception as e:
            print(f"❌ Error adding minimal pump/dump indicators: {e}")

    # ============================================================================
    # 🌀 FIBONACCI ANALYSIS CHARTS với AUTO-SEND
    # ============================================================================
    
    def generate_and_send_fibonacci_chart(self, coin: str, fibonacci_data: Dict[str, Any],
                                        ohlcv_data: pd.DataFrame, current_price: float,
                                        target_chat: str = None) -> Optional[str]:
        """🌀 Generate AND send BEAUTIFUL Fibonacci chart + Auto-Delete."""
        try:
            print(f"🎨 Generating and sending BEAUTIFUL Fibonacci chart for {coin}...")

            # Check storage limit before generating
            self._check_storage_limit()

            # 🎨 Generate BEAUTIFUL minimal chart instead of old chart
            chart_path = self.generate_beautiful_minimal_chart(
                coin=coin,
                chart_type='fibonacci',
                ohlcv_data=ohlcv_data,
                current_price=current_price,
                data=fibonacci_data
            )

            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate beautiful Fibonacci chart for {coin}")
                return None

            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)
            print(f"✅ Beautiful Fibonacci chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            # Track chart creation for cleanup
            self._track_chart_creation(chart_path)

            return chart_path

        except Exception as e:
            print(f"❌ Error generating and sending beautiful Fibonacci chart: {e}")
            traceback.print_exc()
            return None

    def generate_fibonacci_chart(self, coin: str, fibonacci_data: Dict[str, Any],
                               ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🌀 Generate CLEAN Fibonacci chart - Only candlesticks + Fibonacci lines."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        return self.generate_clean_fibonacci_chart(coin, fibonacci_data, ohlcv_data, current_price)

    # ============================================================================
    # 📊 VOLUME PROFILE CHARTS với AUTO-SEND
    # ============================================================================
    
    def generate_and_send_volume_profile_chart(self, coin: str, volume_data: Dict[str, Any], 
                                             ohlcv_data: pd.DataFrame, current_price: float,
                                             target_chat: str = None) -> Optional[str]:
        """📊 Generate AND send Volume Profile chart + Auto-Delete."""
        try:
            print(f"📊 Generating and sending Volume Profile chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # 🎨 Generate BEAUTIFUL minimal chart
            chart_path = self.generate_beautiful_minimal_chart(
                coin=coin,
                chart_type='volume_profile',
                ohlcv_data=ohlcv_data,
                current_price=current_price,
                data=volume_data
            )

            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate beautiful Volume Profile chart for {coin}")
                return None

            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)
            print(f"✅ Volume Profile chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            # Track chart creation for cleanup
            self._track_chart_creation(chart_path)

            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending Volume Profile chart: {e}")
            traceback.print_exc()
            return None

    def generate_volume_profile_chart(self, coin: str, volume_data: Dict[str, Any],
                                    ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📊 Generate CLEAN Volume Profile chart - Only candlesticks + volume levels."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        return self.generate_clean_volume_profile_chart(coin, volume_data, ohlcv_data, current_price)
    
    def _add_volume_profile_levels(self, ax, volume_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📊 Add Volume Profile levels to chart."""
        try:
            # ✅ NULL CHECK: Handle None volume_data
            if volume_data is None:
                print("⚠️ Volume data is None, using fallback")
                volume_data = {}

            # Get VPOC data
            vpoc_data = volume_data.get('vpoc', {})
            vpoc_price = vpoc_data.get('price', current_price)
            
            # Get volume profile data
            volume_profile = volume_data.get('volume_profile', {})
            volume_distribution = volume_profile.get('volume_distribution', {})
            
            # Draw VPOC line
            ax.axhline(y=vpoc_price, color=colors['primary'], linewidth=3, alpha=0.8, label=f'VPOC: {vpoc_price:.6f}')
            ax.text(0.02, vpoc_price, f'VPOC: {vpoc_price:.6f}', 
                transform=ax.get_yaxis_transform(), fontsize=10, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['primary'], alpha=0.8))
            
            # Draw Value Area if available
            value_area = volume_data.get('value_area', {})
            if value_area:
                value_area_high = value_area.get('high', 0)
                value_area_low = value_area.get('low', 0)
                
                if value_area_high > 0 and value_area_low > 0:
                    ax.axhspan(value_area_low, value_area_high, alpha=0.2, color=colors['secondary'], label='Value Area')
            
            # Draw support/resistance levels if available
            sr_levels = volume_data.get('support_resistance_levels', [])
            for i, level in enumerate(sr_levels[:5]):  # Top 5 levels
                price = level.get('price', 0)
                level_type = level.get('type', 'unknown')
                strength = level.get('strength', 0)
                
                if level_type == 'support':
                    color = colors['secondary']
                    style = '-'
                else:
                    color = colors['accent']
                    style = '--'
                
                ax.axhline(y=price, color=color, linestyle=style, alpha=0.6, linewidth=1)
            
            # Highlight current price
            ax.axhline(y=current_price, color='red', linewidth=2, alpha=0.9)
            ax.text(0.5, current_price, f'Current: {current_price:.6f}', 
                transform=ax.get_yaxis_transform(), fontsize=10, ha='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.8, color='white'))
            
        except Exception as e:
            print(f"❌ Error adding Volume Profile levels: {e}")

    def _create_volume_profile_histogram(self, ax, volume_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📊 Create Volume Profile histogram."""
        try:
            # ✅ NULL CHECK: Handle None volume_data
            if volume_data is None:
                print("⚠️ Volume data is None, using fallback")
                volume_data = {}

            ax.clear()
            ax.set_facecolor(colors['background'])
            
            # Get volume distribution
            volume_profile = volume_data.get('volume_profile', {})
            volume_distribution = volume_profile.get('volume_distribution', {})
            
            if volume_distribution:
                prices = []
                volumes = []
                
                for price_str, volume in volume_distribution.items():
                    try:
                        price = float(price_str)
                        prices.append(price)
                        volumes.append(volume)
                    except ValueError:
                        continue
                
                if prices and volumes:
                    # Normalize volumes for display
                    max_volume = max(volumes)
                    normalized_volumes = [v / max_volume for v in volumes]
                    
                    # Create horizontal bar chart
                    ax.barh(prices, normalized_volumes, height=(max(prices) - min(prices)) / len(prices) * 0.8,
                        color=colors['primary'], alpha=0.7)
                    
                    # Mark VPOC
                    vpoc_data = volume_data.get('vpoc', {})
                    vpoc_price = vpoc_data.get('price', current_price)
                    ax.axhline(y=vpoc_price, color=colors['accent'], linewidth=2, alpha=0.9)
                    
                    ax.set_xlabel('Volume (Normalized)', fontsize=10)
                    ax.set_ylabel('Price', fontsize=10)
                    ax.set_title('Volume Profile', fontsize=12, fontweight='bold')
            else:
                # Fallback: Simple volume indicator
                ax.text(0.5, 0.5, 'Volume Profile\nData Processing...', 
                    transform=ax.transAxes, ha='center', va='center',
                    fontsize=12, bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.3))
            
        except Exception as e:
            print(f"❌ Error creating Volume Profile histogram: {e}")

    def _create_vpoc_analysis_panel(self, ax, volume_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """🎯 Create VPOC analysis panel."""
        try:
            # ✅ NULL CHECK: Handle None volume_data
            if volume_data is None:
                print("⚠️ Volume data is None, using fallback")
                volume_data = {}

            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get VPOC data
            vpoc_data = volume_data.get('vpoc', {})
            vpoc_price = vpoc_data.get('price', 0)
            vpoc_volume = vpoc_data.get('volume', 0)
            vpoc_percentage = vpoc_data.get('percentage_of_total', 0)
            
            # Get volume profile data
            volume_profile = volume_data.get('volume_profile', {})
            total_volume = volume_profile.get('total_volume', 0)
            
            # Create VPOC analysis text
            vpoc_text = f"""
    VPOC ANALYSIS
    ═════════════
    VPOC Price: {vpoc_price:.8f}
    VPOC Volume: {vpoc_volume:,.0f}
    VPOC Percentage: {vpoc_percentage:.1f}%
    Total Volume: {total_volume:,.0f}
    Distance from Current: {abs(vpoc_price - current_price):.8f}
            """
            
            ax.text(0.05, 0.95, vpoc_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating VPOC analysis panel: {e}")

    def _create_volume_insights_panel(self, ax, volume_data: Dict[str, Any], colors: Dict[str, str]):
        """💡 Create Volume insights panel."""
        try:
            # ✅ NULL CHECK: Handle None volume_data
            if volume_data is None:
                print("⚠️ Volume data is None, using fallback")
                volume_data = {}

            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get signals and insights
            signals = volume_data.get('signals', {})
            primary_signal = signals.get('primary_signal', 'NONE')
            confidence = signals.get('confidence', 0)
            recommendation = volume_data.get('recommendation', 'HOLD')
            
            # Get volume flow data
            volume_profile = volume_data.get('volume_profile', {})
            volume_flow = volume_profile.get('volume_flow', {})
            flow_direction = volume_flow.get('flow_direction', 'neutral')
            flow_strength = volume_flow.get('flow_strength', 0)
            
            # Create insights text
            insights_text = f"""
    VOLUME INSIGHTS
    ══════════════
    Primary Signal: {primary_signal}
    Confidence: {confidence:.1%}
    Recommendation: {recommendation}
    Flow Direction: {flow_direction.upper()}
    Flow Strength: {flow_strength:.2f}
            """
            
            ax.text(0.05, 0.95, insights_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['secondary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating Volume insights panel: {e}")

    def _add_volume_profile_title_and_annotations(self, fig, coin: str, volume_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📝 Add Volume Profile title and annotations."""
        try:
            # ✅ NULL CHECK: Handle None volume_data
            if volume_data is None:
                print("⚠️ Volume data is None, using fallback")
                volume_data = {}

            # Get signal data
            signals = volume_data.get('signals', {})
            primary_signal = signals.get('primary_signal', 'NONE')
            
            # Main title
            title = f"📊 VOLUME PROFILE ANALYSIS - {coin}"
            subtitle = f"Price: {current_price:.8f} | Signal: {primary_signal}"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding Volume Profile title: {e}")

    # ============================================================================
    # 🤖 AI ANALYSIS CHARTS với AUTO-SEND
    # ============================================================================
    
    def generate_and_send_ai_analysis_chart(self, coin: str, ai_data: Dict[str, Any], 
                                          ohlcv_data: pd.DataFrame, current_price: float,
                                          target_chat: str = None) -> Optional[str]:
        """🤖 Generate AND send AI Analysis chart + Auto-Delete."""
        try:
            print(f"🤖 Generating and sending AI Analysis chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart
            chart_path = self.generate_ai_analysis_chart(coin, ai_data, ohlcv_data, current_price)
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate AI Analysis chart for {coin}")
                return None
            
            # Track creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)

            
            print(f"✅ Chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            
            

            
            # Track chart creation for cleanup

            
            self._track_chart_creation(chart_path)

            
            

            
            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending AI Analysis chart: {e}")
            traceback.print_exc()
            return None

    def generate_ai_analysis_chart(self, coin: str, ai_data: Dict[str, Any],
                                 ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🤖 Generate ENHANCED AI Analysis chart with same format as consensus signal."""
        try:
            print(f"🤖 Generating ENHANCED AI Analysis chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"AI_ANALYSIS_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark (same as consensus)
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # Get AI signal type for title color (same logic as consensus)
            ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
            title_color = '#28A745' if ensemble_signal == 'BUY' else '#DC3545' if ensemble_signal == 'SELL' else '#333333'

            # ✅ ADD TITLE with signal type (same format as consensus)
            fig.suptitle(f'🤖 AI ANALYSIS - {coin} ({ensemble_signal})', fontsize=16, fontweight='bold',
                        color=title_color, y=0.95)

            # Draw enhanced candlesticks with price axis (same as consensus)
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add AI prediction lines (using existing method)
            self._add_clean_ai_prediction_lines(ax, ai_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible (same as consensus)
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis (same as consensus)
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines (same as consensus)
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK (same as consensus)
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark (same as consensus)
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ ENHANCED AI Analysis chart saved: {filepath}")
            self._log_chart_generation("ai_analysis", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating ENHANCED AI Analysis chart: {e}")
            traceback.print_exc()
            self._log_chart_generation("ai_analysis", coin, False)
            return None

    def _add_ai_prediction_levels(self, ax, ai_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """🤖 Add AI prediction levels to chart."""
        try:
            # Get ensemble signal
            ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
            ensemble_confidence = ai_data.get('ensemble_confidence', 0)
            
            # Get trading levels if available
            trading_levels = ai_data.get('trading_levels')
            if not trading_levels:
                ai_prediction = ai_data.get('ai_prediction', {})
                trading_levels = ai_prediction.get('trading_levels')
            
            if trading_levels:
                entry_price = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)
                
                # Draw entry level
                ax.axhline(y=entry_price, color=colors['primary'], linewidth=2, alpha=0.8, label=f'AI Entry: {entry_price:.6f}')
                
                # Draw TP/SL levels
                if take_profit > 0:
                    ax.axhline(y=take_profit, color=colors['secondary'], linewidth=2, alpha=0.8, label=f'AI TP: {take_profit:.6f}')
                
                if stop_loss > 0:
                    ax.axhline(y=stop_loss, color=colors['accent'], linewidth=2, alpha=0.8, label=f'AI SL: {stop_loss:.6f}')
            
            # Highlight current price
            ax.axhline(y=current_price, color='red', linewidth=2, alpha=0.9)
            ax.text(0.5, current_price, f'Current: {current_price:.6f}', 
                transform=ax.get_yaxis_transform(), fontsize=10, ha='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.8, color='white'))
            
            # Add signal annotation
            signal_color = colors['secondary'] if ensemble_signal == 'BUY' else colors['accent'] if ensemble_signal == 'SELL' else colors['primary']
            ax.text(0.02, 0.98, f'AI Signal: {ensemble_signal}\nConfidence: {ensemble_confidence:.1%}', 
                transform=ax.transAxes, fontsize=10, va='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=signal_color, alpha=0.7))
            
        except Exception as e:
            print(f"❌ Error adding AI prediction levels: {e}")

    def _create_ai_consensus_panel(self, ax, ai_data: Dict[str, Any], colors: Dict[str, str]):
        """🤖 Create AI consensus panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get ensemble data
            ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
            ensemble_confidence = ai_data.get('ensemble_confidence', 0)
            model_results = ai_data.get('model_results', {})
            
            # Count predictions
            buy_count = sum(1 for result in model_results.values() if result.get("prediction") == "BUY")
            sell_count = sum(1 for result in model_results.values() if result.get("prediction") == "SELL")
            none_count = len(model_results) - buy_count - sell_count
            
            # Create consensus text
            consensus_text = f"""
    AI ENSEMBLE CONSENSUS
    ═══════════════════
    Ensemble Signal: {ensemble_signal}
    Ensemble Confidence: {ensemble_confidence:.1%}
    Total Models: {len(model_results)}
    BUY Models: {buy_count}
    SELL Models: {sell_count}
    NONE Models: {none_count}
            """
            
            ax.text(0.05, 0.95, consensus_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating AI consensus panel: {e}")

    def _create_model_breakdown_panel(self, ax, ai_data: Dict[str, Any], colors: Dict[str, str]):
        """🔬 Create model breakdown panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get model results
            model_results = ai_data.get('model_results', {})
            
            # Create breakdown text
            breakdown_text = "MODEL BREAKDOWN\n" + "═" * 15 + "\n"
            
            for model_name, result in list(model_results.items())[:8]:  # Top 8 models
                prediction = result.get('prediction', 'NONE')
                confidence = result.get('confidence', 0)
                breakdown_text += f"{model_name}: {prediction} ({confidence:.1%})\n"
            
            ax.text(0.05, 0.95, breakdown_text.strip(), transform=ax.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['secondary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating model breakdown panel: {e}")

    def _create_confidence_metrics_panel(self, ax, ai_data: Dict[str, Any], colors: Dict[str, str]):
        """📊 Create confidence metrics panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get technical analysis data
            technical_analysis = ai_data.get('technical_analysis', {})
            market_sentiment = ai_data.get('market_sentiment', 'NEUTRAL')
            recommendation = ai_data.get('recommendation', 'HOLD')
            
            # Create confidence text
            confidence_text = f"""
    CONFIDENCE METRICS
    ═════════════════
    Market Sentiment: {market_sentiment}
    Recommendation: {recommendation}
    Momentum: {technical_analysis.get('momentum', 0):.2f}
    Volatility: {technical_analysis.get('volatility', 0):.2f}
    Trend Strength: {technical_analysis.get('trend_strength', 0):.2f}
            """
            
            ax.text(0.05, 0.95, confidence_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['tertiary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating confidence metrics panel: {e}")

    def _add_ai_title_and_annotations(self, fig, coin: str, ai_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📝 Add AI analysis title and annotations."""
        try:
            # Get signal data
            ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
            ensemble_confidence = ai_data.get('ensemble_confidence', 0)
            
            # Main title
            title = f"🤖 AI ENSEMBLE ANALYSIS - {coin}"
            subtitle = f"Price: {current_price:.8f} | Signal: {ensemble_signal} ({ensemble_confidence:.1%})"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding AI title: {e}")

    # ============================================================================
    # 🚀 PUMP/DUMP ALERT CHARTS với AUTO-SEND
    # ============================================================================
    
    def generate_and_send_pump_alert_chart(self, coin: str, pump_data: Dict[str, Any], 
                                         ohlcv_data: pd.DataFrame, current_price: float,
                                         target_chat: str = None) -> Optional[str]:
        """🚀 Generate AND send Pump Alert chart + Auto-Delete."""
        try:
            print(f"🚀 Generating and sending Pump Alert chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart
            chart_path = self.generate_pump_alert_chart(coin, pump_data, ohlcv_data, current_price)
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate Pump Alert chart for {coin}")
                return None
            
            # Track creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)

            
            print(f"✅ Chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            
            

            
            # Track chart creation for cleanup

            
            self._track_chart_creation(chart_path)

            
            

            
            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending Pump Alert chart: {e}")
            traceback.print_exc()
            return None

    def generate_pump_alert_chart(self, coin: str, pump_data: Dict[str, Any],
                                ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🚀 Generate CLEAN Pump Alert chart - Only candlesticks + pump indicators."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        return self.generate_clean_pump_alert_chart(coin, pump_data, ohlcv_data, current_price)

    def generate_and_send_dump_alert_chart(self, coin: str, dump_data: Dict[str, Any], 
                                         ohlcv_data: pd.DataFrame, current_price: float,
                                         target_chat: str = None) -> Optional[str]:
        """📉 Generate AND send Dump Alert chart + Auto-Delete."""
        try:
            print(f"📉 Generating and sending Dump Alert chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart
            chart_path = self.generate_dump_alert_chart(coin, dump_data, ohlcv_data, current_price)
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate Dump Alert chart for {coin}")
                return None
            
            # Track creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)

            
            print(f"✅ Chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            
            

            
            # Track chart creation for cleanup

            
            self._track_chart_creation(chart_path)

            
            

            
            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending Dump Alert chart: {e}")
            traceback.print_exc()
            return None
    
    def _draw_pump_highlighted_candlesticks(self, ax, ohlcv_data: pd.DataFrame, pump_data: Dict[str, Any], colors: Dict[str, str]):
        """🚀 Draw candlesticks with pump highlighting."""
        try:
            # Draw basic candlesticks first
            self._draw_professional_candlesticks(ax, ohlcv_data, colors)
            
            # Highlight pump areas
            pump_probability = pump_data.get('pump_probability', 0)
            if pump_probability > 0.5:
                # Highlight the last few candles with pump color
                highlight_count = min(10, len(ohlcv_data))
                ax.axvspan(len(ohlcv_data) - highlight_count, len(ohlcv_data), 
                        alpha=0.2, color='orange', label=f'Pump Zone ({pump_probability:.1%})')
            
        except Exception as e:
            print(f"❌ Error drawing pump highlighted candlesticks: {e}")

    def _create_volume_spike_panel(self, ax, ohlcv_data: pd.DataFrame, pump_data: Dict[str, Any], colors: Dict[str, str]):
        """📊 Create volume spike analysis panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            
            # Plot volume bars
            volumes = ohlcv_data['volume'].values
            ax.bar(range(len(volumes)), volumes, color=colors['primary'], alpha=0.7)
            
            # Highlight volume spikes
            volume_spike_factor = pump_data.get('volume_spike_factor', 1)
            if volume_spike_factor > 2:
                avg_volume = volumes.mean()
                spike_threshold = avg_volume * 2
                
                for i, vol in enumerate(volumes):
                    if vol > spike_threshold:
                        ax.bar(i, vol, color='red', alpha=0.8)
            
            ax.set_title(f'Volume Analysis (Spike: {volume_spike_factor:.1f}x)', fontsize=12)
            ax.set_ylabel('Volume')
            
        except Exception as e:
            print(f"❌ Error creating volume spike panel: {e}")

    def _create_pump_probability_panel(self, ax, pump_data: Dict[str, Any], colors: Dict[str, str]):
        """🔥 Create pump probability panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get pump metrics
            pump_probability = pump_data.get('pump_probability', 0)
            intensity = pump_data.get('intensity', 0)
            volume_spike_factor = pump_data.get('volume_spike_factor', 1)
            price_momentum = pump_data.get('price_momentum', 0)
            
            # Create probability text
            probability_text = f"""
    PUMP PROBABILITY METRICS
    ══════════════════════
    Pump Probability: {pump_probability:.1%}
    Intensity: {intensity:.2f}/10
    Volume Spike: {volume_spike_factor:.1f}x
    Price Momentum: {price_momentum:+.2%}
            """
            
            ax.text(0.05, 0.95, probability_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating pump probability panel: {e}")

    def _create_pump_risk_panel(self, ax, pump_data: Dict[str, Any], colors: Dict[str, str]):
        """⚠️ Create pump risk assessment panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get risk data
            risk_level = pump_data.get('risk_level', 'MEDIUM')
            fomo_risk = pump_data.get('fomo_risk', 'MEDIUM')
            dump_risk = pump_data.get('dump_risk', 'MEDIUM')
            risk_recommendation = pump_data.get('risk_recommendation', 'CAUTION')
            
            # Create risk text
            risk_text = f"""
    PUMP RISK ASSESSMENT
    ══════════════════
    Risk Level: {risk_level}
    FOMO Risk: {fomo_risk}
    Dump Risk: {dump_risk}
    Recommendation: {risk_recommendation}
            """
            
            ax.text(0.05, 0.95, risk_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['accent'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating pump risk panel: {e}")

    def _add_pump_title_and_annotations(self, fig, coin: str, pump_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📝 Add pump alert title and annotations."""
        try:
            # Get pump data
            pump_probability = pump_data.get('pump_probability', 0)
            intensity = pump_data.get('intensity', 0)
            
            # Main title
            title = f"🚀 PUMP ALERT - {coin}"
            subtitle = f"Price: {current_price:.8f} | Probability: {pump_probability:.1%} | Intensity: {intensity:.1f}/10"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Warning
            fig.text(0.5, 0.03, "⚠️ HIGH RISK TRADING - EXERCISE EXTREME CAUTION ⚠️", 
                    ha='center', fontsize=12, color='red', fontweight='bold')
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding pump title: {e}")

    def generate_dump_alert_chart(self, coin: str, dump_data: Dict[str, Any],
                                ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📉 Generate CLEAN Dump Alert chart - Only candlesticks + dump indicators."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        return self.generate_clean_dump_alert_chart(coin, dump_data, ohlcv_data, current_price)

    def _draw_dump_highlighted_candlesticks(self, ax, ohlcv_data: pd.DataFrame, dump_data: Dict[str, Any], colors: Dict[str, str]):
        """📉 Draw candlesticks with dump highlighting."""
        try:
            # Draw basic candlesticks first
            self._draw_professional_candlesticks(ax, ohlcv_data, colors)
            
            # Highlight dump areas
            dump_probability = dump_data.get('dump_probability', 0)
            if dump_probability > 0.5:
                # Highlight the last few candles with dump color
                highlight_count = min(10, len(ohlcv_data))
                ax.axvspan(len(ohlcv_data) - highlight_count, len(ohlcv_data), 
                        alpha=0.2, color='red', label=f'Dump Zone ({dump_probability:.1%})')
            
        except Exception as e:
            print(f"❌ Error drawing dump highlighted candlesticks: {e}")

    def _create_dump_volume_panel(self, ax, ohlcv_data: pd.DataFrame, dump_data: Dict[str, Any], colors: Dict[str, str]):
        """📊 Create dump volume analysis panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            
            # Plot volume bars
            volumes = ohlcv_data['volume'].values
            ax.bar(range(len(volumes)), volumes, color=colors['primary'], alpha=0.7)
            
            # Highlight selling pressure
            selling_pressure = dump_data.get('selling_pressure', 0)
            if selling_pressure > 50:
                # Highlight recent high volume bars as selling
                recent_volumes = volumes[-10:]
                avg_recent = recent_volumes.mean()
                
                for i, vol in enumerate(volumes[-10:], len(volumes)-10):
                    if vol > avg_recent * 1.5:
                        ax.bar(i, vol, color='red', alpha=0.8)
            
            ax.set_title(f'Volume Analysis (Selling Pressure: {selling_pressure:.1f}%)', fontsize=12)
            ax.set_ylabel('Volume')
            
        except Exception as e:
            print(f"❌ Error creating dump volume panel: {e}")

    def _create_dump_probability_panel(self, ax, dump_data: Dict[str, Any], colors: Dict[str, str]):
        """📉 Create dump probability panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get dump metrics
            dump_probability = dump_data.get('dump_probability', 0)
            severity_level = dump_data.get('severity_level', 'MEDIUM')
            selling_pressure = dump_data.get('selling_pressure', 0)
            dump_velocity = dump_data.get('dump_velocity', 0)
            
            # Create probability text
            probability_text = f"""
    DUMP PROBABILITY METRICS
    ══════════════════════
    Dump Probability: {dump_probability:.1%}
    Severity Level: {severity_level}
    Selling Pressure: {selling_pressure:.1f}%
    Dump Velocity: {dump_velocity:.2f}
            """
            
            ax.text(0.05, 0.95, probability_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating dump probability panel: {e}")

    def _create_dump_support_panel(self, ax, dump_data: Dict[str, Any], colors: Dict[str, str]):
        """🛡️ Create dump support analysis panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get support data
            next_support = dump_data.get('next_support', 0)
            resistance_level = dump_data.get('resistance_level', 0)
            rsi_value = dump_data.get('rsi_value', 50)
            macd_signal = dump_data.get('macd_signal', 'NEUTRAL')
            
            # Create support text
            support_text = f"""
    SUPPORT ANALYSIS
    ══════════════
    Next Support: {next_support:.8f}
    Resistance: {resistance_level:.8f}
    RSI: {rsi_value:.1f}
    MACD: {macd_signal}
            """
            
            ax.text(0.05, 0.95, support_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['secondary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating dump support panel: {e}")

    def _add_dump_title_and_annotations(self, fig, coin: str, dump_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📝 Add dump alert title and annotations."""
        try:
            # Get dump data
            dump_probability = dump_data.get('dump_probability', 0)
            severity_level = dump_data.get('severity_level', 'MEDIUM')
            
            # Main title
            title = f"📉 DUMP ALERT - {coin}"
            subtitle = f"Price: {current_price:.8f} | Probability: {dump_probability:.1%} | Severity: {severity_level}"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Warning
            fig.text(0.5, 0.03, "🚨 DUMP RISK - MANAGE POSITIONS CAREFULLY 🚨", 
                    ha='center', fontsize=12, color='red', fontweight='bold')
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding dump title: {e}")

    # ============================================================================
    # 🎯 CONSENSUS SIGNAL CHARTS với AUTO-SEND
    # ============================================================================
    
    def generate_and_send_consensus_chart(self, coin: str, consensus_data: Dict[str, Any], 
                                        signal_data: Dict[str, Any], ohlcv_data: pd.DataFrame,
                                        target_chat: str = None) -> Optional[str]:
        """🎯 Generate AND send Consensus Signal chart + Auto-Delete."""
        try:
            print(f"🎯 Generating and sending Consensus Signal chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart
            chart_path = self.generate_consensus_chart(coin, consensus_data, signal_data, ohlcv_data)
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate Consensus Signal chart for {coin}")
                return None
            
            # Track creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)

            
            print(f"✅ Chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            
            

            
            # Track chart creation for cleanup

            
            self._track_chart_creation(chart_path)

            
            

            
            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending Consensus Signal chart: {e}")
            traceback.print_exc()
            return None

    def generate_consensus_chart(self, coin: str, consensus_data: Dict[str, Any],
                           signal_data: Dict[str, Any], ohlcv_data: pd.DataFrame) -> Optional[str]:
        """🎯 Generate ENHANCED Consensus Signal chart with title, price axis and watermark."""
        try:
            print(f"🎯 Generating ENHANCED Consensus Signal chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"CONSENSUS_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # Get signal type for title color
            signal_type = signal_data.get('signal_type', 'NONE')
            title_color = '#28A745' if signal_type == 'BUY' else '#DC3545' if signal_type == 'SELL' else '#333333'

            # ✅ ADD TITLE with signal type
            fig.suptitle(f'🎯 CONSENSUS SIGNAL - {coin} ({signal_type})', fontsize=16, fontweight='bold',
                        color=title_color, y=0.95)

            # Draw enhanced candlesticks with price axis
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add enhanced signal levels with labels
            self._add_clean_signal_levels(ax, signal_data)

            # ✅ ENHANCED styling - Keep price axis visible
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ ENHANCED Consensus Signal chart saved: {filepath}")
            self._log_chart_generation("consensus_signal", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating ENHANCED Consensus Signal chart: {e}")
            traceback.print_exc()
            self._log_chart_generation("consensus_signal", coin, False)
            return None

    def _add_consensus_signal_levels(self, ax, signal_data: Dict[str, Any], colors: Dict[str, str]):
        """🎯 Add consensus signal levels."""
        try:
            # Get signal levels
            entry = signal_data.get('entry', 0)
            take_profit = signal_data.get('take_profit', 0)
            stop_loss = signal_data.get('stop_loss', 0)
            signal_type = signal_data.get('signal_type', 'NONE')
            
            # Draw levels
            if entry > 0:
                ax.axhline(y=entry, color=colors['primary'], linewidth=2, alpha=0.8, label=f'Entry: {entry:.6f}')
            
            if take_profit > 0:
                ax.axhline(y=take_profit, color=colors['secondary'], linewidth=2, alpha=0.8, label=f'TP: {take_profit:.6f}')
            
            if stop_loss > 0:
                ax.axhline(y=stop_loss, color=colors['accent'], linewidth=2, alpha=0.8, label=f'SL: {stop_loss:.6f}')
            
            # Add signal annotation
            signal_color = colors['secondary'] if signal_type == 'BUY' else colors['accent'] if signal_type == 'SELL' else colors['primary']
            ax.text(0.02, 0.98, f'Consensus: {signal_type}', 
                transform=ax.transAxes, fontsize=12, va='top', fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=signal_color, alpha=0.7))
            
        except Exception as e:
            print(f"❌ Error adding consensus signal levels: {e}")

    def _create_consensus_breakdown_panel(self, ax, consensus_data: Dict[str, Any], colors: Dict[str, str]):
        """🎯 Create consensus breakdown panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get consensus data
            consensus_score = consensus_data.get('consensus_score', 0)
            total_algorithms = consensus_data.get('total_algorithms', 0)
            agreeing_algorithms = consensus_data.get('agreeing_algorithms', 0)
            consensus_signal = consensus_data.get('consensus_signal', 'NONE')
            consensus_confidence = consensus_data.get('consensus_confidence', 0)
            
            # Get algorithm breakdown
            algorithm_votes = consensus_data.get('algorithm_votes', {})
            buy_votes = algorithm_votes.get('BUY', 0)
            sell_votes = algorithm_votes.get('SELL', 0)
            none_votes = algorithm_votes.get('NONE', 0)
            
            # Create consensus breakdown text
            breakdown_text = f"""
    CONSENSUS BREAKDOWN
    ═════════════════
    Consensus Signal: {consensus_signal}
    Consensus Score: {consensus_score:.3f}
    Consensus Confidence: {consensus_confidence:.1%}
    Total Algorithms: {total_algorithms}
    Agreeing Algorithms: {agreeing_algorithms}

    VOTE DISTRIBUTION
    ─────────────────
    BUY Votes: {buy_votes}
    SELL Votes: {sell_votes}
    NONE Votes: {none_votes}
            """
            
            ax.text(0.05, 0.95, breakdown_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating consensus breakdown panel: {e}")

    def _create_algorithm_contributions_panel(self, ax, consensus_data: Dict[str, Any], colors: Dict[str, str]):
        """🔬 Create algorithm contributions panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get algorithm contributions
            algorithm_results = consensus_data.get('algorithm_results', {})
            algorithm_weights = consensus_data.get('algorithm_weights', {})
            
            # Create contributions text
            contributions_text = "ALGORITHM CONTRIBUTIONS\n" + "═" * 23 + "\n"
            
            # Show top contributing algorithms
            for i, (algo_name, result) in enumerate(list(algorithm_results.items())[:8], 1):
                signal = result.get('signal', 'NONE')
                confidence = result.get('confidence', 0)
                weight = algorithm_weights.get(algo_name, 1.0)
                
                # Truncate algorithm name if too long
                display_name = algo_name[:15] + "..." if len(algo_name) > 15 else algo_name
                
                contributions_text += f"{i:2d}. {display_name}\n"
                contributions_text += f"    Signal: {signal} ({confidence:.1%})\n"
                contributions_text += f"    Weight: {weight:.2f}\n"
            
            ax.text(0.05, 0.95, contributions_text.strip(), transform=ax.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['secondary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating algorithm contributions panel: {e}")

    def _create_signal_quality_panel(self, ax, consensus_data: Dict[str, Any], signal_data: Dict[str, Any], colors: Dict[str, str]):
        """📊 Create signal quality metrics panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get quality metrics
            signal_quality = consensus_data.get('signal_quality', {})
            strength = signal_quality.get('strength', 0)
            reliability = signal_quality.get('reliability', 0)
            consistency = signal_quality.get('consistency', 0)
            
            # Get trading metrics
            risk_reward_ratio = signal_data.get('risk_reward_ratio', 0)
            win_probability = signal_data.get('win_probability', 0)
            recommended_position_size = signal_data.get('recommended_position_size', 0)
            
            # Get market conditions
            market_conditions = consensus_data.get('market_conditions', {})
            volatility = market_conditions.get('volatility', 'MEDIUM')
            trend_strength = market_conditions.get('trend_strength', 0)
            
            # Create quality metrics text
            quality_text = f"""
    SIGNAL QUALITY METRICS
    ════════════════════
    Strength: {strength:.2f}/10
    Reliability: {reliability:.1%}
    Consistency: {consistency:.1%}
    Risk/Reward: {risk_reward_ratio:.2f}
    Win Probability: {win_probability:.1%}

    MARKET CONDITIONS
    ───────────────
    Volatility: {volatility}
    Trend Strength: {trend_strength:.2f}
    Position Size: {recommended_position_size:.1%}
            """
            
            ax.text(0.05, 0.95, quality_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['tertiary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating signal quality panel: {e}")

    def _add_consensus_title_and_annotations(self, fig, coin: str, consensus_data: Dict[str, Any], signal_data: Dict[str, Any], colors: Dict[str, str]):
        """📝 Add consensus signal title and annotations."""
        try:
            # Get signal data
            consensus_signal = consensus_data.get('consensus_signal', 'NONE')
            consensus_score = consensus_data.get('consensus_score', 0)
            signal_type = signal_data.get('signal_type', 'NONE')
            entry_price = signal_data.get('entry', 0)
            
            # Main title
            title = f"🎯 CONSENSUS SIGNAL ANALYSIS - {coin}"
            subtitle = f"Signal: {consensus_signal} | Score: {consensus_score:.3f} | Entry: {entry_price:.8f}"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Algorithm count
            total_algorithms = consensus_data.get('total_algorithms', 0)
            agreeing_algorithms = consensus_data.get('agreeing_algorithms', 0)
            fig.text(0.5, 0.89, f"Algorithms: {agreeing_algorithms}/{total_algorithms} agreeing", 
                    ha='center', fontsize=10, color=colors['text'], alpha=0.8)
            
            # Warning if low consensus
            if consensus_score < 0.6:
                fig.text(0.5, 0.05, "⚠️ LOW CONSENSUS - TRADE WITH CAUTION ⚠️", 
                        ha='center', fontsize=12, color='orange', fontweight='bold')
            elif consensus_score > 0.8:
                fig.text(0.5, 0.05, "✅ HIGH CONSENSUS - STRONG SIGNAL ✅", 
                        ha='center', fontsize=12, color='green', fontweight='bold')
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding consensus title: {e}")

    # ============================================================================
    # 🎨 CHART DRAWING HELPER METHODS - Đã fix indentation error
    # ============================================================================
    
    def _draw_professional_candlesticks(self, ax, ohlcv_data: pd.DataFrame, colors: Dict[str, str]):
        """🕯️ Draw professional candlestick chart."""
        try:
            # Ensure datetime index
            if not isinstance(ohlcv_data.index, pd.DatetimeIndex):
                ohlcv_data.index = pd.to_datetime(ohlcv_data.index)

            # Draw candlesticks
            for i, (timestamp, row) in enumerate(ohlcv_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # Color based on bullish/bearish
                color = colors['secondary'] if close_price >= open_price else colors['primary']

                # Draw high-low line
                ax.plot([i, i], [low_price, high_price], color='black', linewidth=0.8, alpha=0.7)

                # Draw body rectangle
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                rect = patches.Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                       facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
                ax.add_patch(rect)

            # Format x-axis
            ax.set_xlim(-0.5, len(ohlcv_data) - 0.5)

            # Add grid
            ax.grid(True, alpha=0.3, color=colors['grid'])
            ax.set_facecolor(colors['background'])

        except Exception as e:
            print(f"❌ Error drawing candlesticks: {e}")

    def _draw_clean_candlesticks(self, ax, ohlcv_data: pd.DataFrame):
        """🕯️ Draw ENHANCED candlestick chart with price axis and professional styling."""
        try:
            # Draw candlesticks with beautiful colors
            for i, (timestamp, row) in enumerate(ohlcv_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # Beautiful color scheme
                if close_price >= open_price:
                    # Bullish candle - Green
                    body_color = '#00C851'  # Beautiful green
                    wick_color = '#00C851'
                else:
                    # Bearish candle - Red
                    body_color = '#FF4444'  # Beautiful red
                    wick_color = '#FF4444'

                # Draw high-low wick
                ax.plot([i, i], [low_price, high_price], color=wick_color, linewidth=1.2, alpha=0.8)

                # Draw body rectangle
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                if body_height > 0:  # Avoid zero-height rectangles
                    rect = patches.Rectangle((i-0.4, body_bottom), 0.8, body_height,
                                           facecolor=body_color, edgecolor=body_color,
                                           linewidth=0.8, alpha=0.9)
                    ax.add_patch(rect)
                else:
                    # Doji candle - draw thin line
                    ax.plot([i-0.4, i+0.4], [open_price, open_price], color=wick_color, linewidth=1.5)

            # ✅ ADD PRICE AXIS - Show Y-axis with price values
            ax.tick_params(axis='y', labelsize=10, labelcolor='#333333', which='major')
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.6f}'))

            # ✅ ADD TIME AXIS - Show X-axis with simplified time labels
            if len(ohlcv_data) > 10:
                step = max(1, len(ohlcv_data) // 5)  # Show ~5 time labels
                x_ticks = list(range(0, len(ohlcv_data), step))
                x_labels = [f'{i//24}d' if i >= 24 else f'{i}h' for i in x_ticks]
                ax.set_xticks(x_ticks)
                ax.set_xticklabels(x_labels, fontsize=9, color='#666666')

            # ✅ PROFESSIONAL GRID for better readability
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5, color='#CCCCCC')
            ax.set_facecolor('#FAFAFA')  # Very light gray background

            # Set clean limits with padding - will be adjusted later for signal levels
            ax.set_xlim(-0.5, len(ohlcv_data) - 0.5)

            # Store original price range for later adjustment
            self._original_price_min = ohlcv_data['low'].min()
            self._original_price_max = ohlcv_data['high'].max()
            price_range = self._original_price_max - self._original_price_min

            # Set initial Y-limits (will be expanded if needed for signal levels)
            ax.set_ylim(self._original_price_min - price_range * 0.02,
                       self._original_price_max + price_range * 0.02)

        except Exception as e:
            print(f"❌ Error drawing enhanced candlesticks: {e}")

    def _add_clean_signal_levels(self, ax, signal_data: Dict[str, Any]):
        """🎯 Add ENHANCED signal levels with clear labels."""
        try:
            # Get signal levels
            entry = signal_data.get('entry', 0)
            take_profit = signal_data.get('take_profit', 0)
            stop_loss = signal_data.get('stop_loss', 0)
            signal_type = signal_data.get('signal_type', 'NONE')

            # ✅ Draw ENHANCED horizontal lines with labels
            signal_levels = []

            if entry > 0:
                # Entry line - Blue with thicker line
                ax.axhline(y=entry, color='#007BFF', linewidth=3.5, alpha=0.9, linestyle='-',
                          label=f'ENTRY: {entry:.6f}')
                # Add small text label on the right
                ax.text(ax.get_xlim()[1] * 0.98, entry, 'ENTRY',
                       fontsize=10, color='#007BFF', fontweight='bold',
                       ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                       facecolor='white', edgecolor='#007BFF', alpha=0.8))
                signal_levels.append(entry)

            if take_profit > 0:
                # Take Profit line - Green with thicker line
                ax.axhline(y=take_profit, color='#28A745', linewidth=3.5, alpha=0.9, linestyle='--',
                          label=f'TP: {take_profit:.6f}')
                # Add small text label on the right
                ax.text(ax.get_xlim()[1] * 0.98, take_profit, 'TP',
                       fontsize=10, color='#28A745', fontweight='bold',
                       ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                       facecolor='white', edgecolor='#28A745', alpha=0.8))
                signal_levels.append(take_profit)

            if stop_loss > 0:
                # Stop Loss line - Red with thicker line
                ax.axhline(y=stop_loss, color='#DC3545', linewidth=3.5, alpha=0.9, linestyle='--',
                          label=f'SL: {stop_loss:.6f}')
                # Add small text label on the right
                ax.text(ax.get_xlim()[1] * 0.98, stop_loss, 'SL',
                       fontsize=10, color='#DC3545', fontweight='bold',
                       ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                       facecolor='white', edgecolor='#DC3545', alpha=0.8))
                signal_levels.append(stop_loss)

            # ✅ Adjust Y-axis to include all signal levels
            if signal_levels:
                self._adjust_ylim_for_signals(ax, signal_levels)

        except Exception as e:
            print(f"❌ Error adding enhanced signal levels: {e}")

    def _adjust_ylim_for_signals(self, ax, signal_levels: list):
        """🎯 Adjust Y-axis limits to include all signal levels."""
        try:
            # Get current Y-limits
            current_ymin, current_ymax = ax.get_ylim()

            # Find min/max of signal levels
            signal_min = min(signal_levels)
            signal_max = max(signal_levels)

            # Expand Y-limits if needed
            new_ymin = min(current_ymin, signal_min)
            new_ymax = max(current_ymax, signal_max)

            # Add padding (5% of the range)
            y_range = new_ymax - new_ymin
            padding = y_range * 0.05

            # Set new Y-limits with padding
            ax.set_ylim(new_ymin - padding, new_ymax + padding)

            print(f"✅ Y-axis adjusted: {new_ymin - padding:.8f} to {new_ymax + padding:.8f}")

        except Exception as e:
            print(f"❌ Error adjusting Y-axis for signals: {e}")

    # ============================================================================
    # 🎨 CLEAN CHART GENERATORS - Beautiful charts with minimal text
    # ============================================================================

    def generate_clean_fibonacci_chart(self, coin: str, fibonacci_data: Dict[str, Any],
                                     ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🌀 Generate ENHANCED Fibonacci chart with same format as consensus signal."""
        try:
            print(f"🌀 Generating ENHANCED Fibonacci chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"FIBONACCI_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark (same as consensus)
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # Get Fibonacci signal type for title color (same logic as consensus)
            fibonacci_signal = fibonacci_data.get('signal', 'NONE')
            title_color = '#28A745' if fibonacci_signal == 'BUY' else '#DC3545' if fibonacci_signal == 'SELL' else '#333333'

            # ✅ ADD TITLE with signal type (same format as consensus)
            fig.suptitle(f'🌀 FIBONACCI ANALYSIS - {coin} ({fibonacci_signal})', fontsize=16, fontweight='bold',
                        color=title_color, y=0.95)

            # Draw enhanced candlesticks with price axis (same as consensus)
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add Fibonacci levels with Entry/TP/SL format (same as consensus)
            self._add_enhanced_fibonacci_levels(ax, fibonacci_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible (same as consensus)
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis (same as consensus)
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines (same as consensus)
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK (same as consensus)
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark (same as consensus)
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ ENHANCED Fibonacci chart saved: {filepath}")
            self._log_chart_generation("fibonacci", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating ENHANCED Fibonacci chart: {e}")
            traceback.print_exc()
            self._log_chart_generation("fibonacci", coin, False)
            return None

    def generate_clean_volume_profile_chart(self, coin: str, volume_data: Dict[str, Any],
                                          ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📊 Generate ENHANCED Volume Profile chart with same format as consensus signal."""
        try:
            print(f"📊 Generating ENHANCED Volume Profile chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"VOLUME_PROFILE_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark (same as consensus)
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # Get Volume Profile signal type for title color (same logic as consensus)
            volume_signal = volume_data.get('signal', 'NONE')
            title_color = '#28A745' if volume_signal == 'BUY' else '#DC3545' if volume_signal == 'SELL' else '#333333'

            # ✅ ADD TITLE with signal type (same format as consensus)
            fig.suptitle(f'📊 VOLUME PROFILE ANALYSIS - {coin} ({volume_signal})', fontsize=16, fontweight='bold',
                        color=title_color, y=0.95)

            # Draw enhanced candlesticks with price axis (same as consensus)
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add volume profile levels with Entry/TP/SL format (same as consensus)
            self._add_enhanced_volume_profile_levels(ax, volume_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible (same as consensus)
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis (same as consensus)
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines (same as consensus)
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK (same as consensus)
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark (same as consensus)
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ ENHANCED Volume Profile chart saved: {filepath}")
            self._log_chart_generation("volume_profile", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating ENHANCED Volume Profile chart: {e}")
            traceback.print_exc()
            self._log_chart_generation("volume_profile", coin, False)
            return None

    def _add_enhanced_fibonacci_levels(self, ax, fibonacci_data: Dict[str, Any], current_price: float):
        """🌀 Add ENHANCED Fibonacci levels with same format as consensus signal."""
        try:
            # Get trading levels if available (same format as consensus signal)
            trading_levels = fibonacci_data.get('trading_levels', {})

            if trading_levels:
                # Use trading levels format (same as consensus)
                entry = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)

                # ✅ Draw ENHANCED horizontal lines with labels (same as consensus)
                signal_levels = []

                if entry > 0:
                    # Entry line - Blue with thicker line (same as consensus)
                    ax.axhline(y=entry, color='#007BFF', linewidth=3.5, alpha=0.9, linestyle='-',
                              label=f'ENTRY: {entry:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, entry, 'ENTRY',
                           fontsize=10, color='#007BFF', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#007BFF', alpha=0.8))
                    signal_levels.append(entry)

                if take_profit > 0:
                    # Take Profit line - Green with thicker line (same as consensus)
                    ax.axhline(y=take_profit, color='#28A745', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'TP: {take_profit:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, take_profit, 'TP',
                           fontsize=10, color='#28A745', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#28A745', alpha=0.8))
                    signal_levels.append(take_profit)

                if stop_loss > 0:
                    # Stop Loss line - Red with thicker line (same as consensus)
                    ax.axhline(y=stop_loss, color='#DC3545', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'SL: {stop_loss:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, stop_loss, 'SL',
                           fontsize=10, color='#DC3545', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#DC3545', alpha=0.8))
                    signal_levels.append(stop_loss)

                # ✅ Adjust Y-axis to include all signal levels (same as consensus)
                if signal_levels:
                    self._adjust_ylim_for_signals(ax, signal_levels)
            else:
                # Fallback to Fibonacci levels if no trading levels
                retracement_levels = fibonacci_data.get('retracement_levels', [])
                extension_levels = fibonacci_data.get('extension_levels', [])

                # Collect all price levels for Y-axis adjustment
                all_levels = [current_price]

                # Draw retracement levels with beautiful colors
                for level in retracement_levels:
                    price = level.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#FFD700', linestyle='-', alpha=0.7, linewidth=1.8)  # Gold
                        all_levels.append(price)

                # Draw extension levels
                for level in extension_levels:
                    price = level.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#FF6B35', linestyle='--', alpha=0.7, linewidth=1.8)  # Orange
                        all_levels.append(price)

                # Highlight current price
                ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

                # ✅ Adjust Y-axis to include all Fibonacci levels
                if len(all_levels) > 1:
                    self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding enhanced Fibonacci levels: {e}")

    def _add_enhanced_volume_profile_levels(self, ax, volume_data: Dict[str, Any], current_price: float):
        """📊 Add ENHANCED Volume Profile levels with same format as consensus signal."""
        try:
            # Get trading levels if available (same format as consensus signal)
            trading_levels = volume_data.get('trading_levels', {})

            if trading_levels:
                # Use trading levels format (same as consensus)
                entry = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)

                # ✅ Draw ENHANCED horizontal lines with labels (same as consensus)
                signal_levels = []

                if entry > 0:
                    # Entry line - Blue with thicker line (same as consensus)
                    ax.axhline(y=entry, color='#007BFF', linewidth=3.5, alpha=0.9, linestyle='-',
                              label=f'ENTRY: {entry:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, entry, 'ENTRY',
                           fontsize=10, color='#007BFF', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#007BFF', alpha=0.8))
                    signal_levels.append(entry)

                if take_profit > 0:
                    # Take Profit line - Green with thicker line (same as consensus)
                    ax.axhline(y=take_profit, color='#28A745', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'TP: {take_profit:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, take_profit, 'TP',
                           fontsize=10, color='#28A745', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#28A745', alpha=0.8))
                    signal_levels.append(take_profit)

                if stop_loss > 0:
                    # Stop Loss line - Red with thicker line (same as consensus)
                    ax.axhline(y=stop_loss, color='#DC3545', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'SL: {stop_loss:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, stop_loss, 'SL',
                           fontsize=10, color='#DC3545', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#DC3545', alpha=0.8))
                    signal_levels.append(stop_loss)

                # ✅ Adjust Y-axis to include all signal levels (same as consensus)
                if signal_levels:
                    self._adjust_ylim_for_signals(ax, signal_levels)
            else:
                # Fallback to volume profile levels if no trading levels
                # Collect all price levels for Y-axis adjustment
                all_levels = [current_price]

                # VPOC (Volume Point of Control)
                vpoc = volume_data.get('vpoc', {})
                vpoc_price = vpoc.get('price', 0)

                if vpoc_price > 0:
                    ax.axhline(y=vpoc_price, color='#9C27B0', linewidth=3, alpha=0.8)  # Purple
                    all_levels.append(vpoc_price)

                # High Volume Nodes
                high_volume_nodes = volume_data.get('high_volume_nodes', [])
                for node in high_volume_nodes[:5]:  # Top 5 nodes
                    price = node.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#4CAF50', linestyle='-', alpha=0.6, linewidth=2)  # Green
                        all_levels.append(price)

                # Low Volume Nodes (gaps)
                low_volume_nodes = volume_data.get('low_volume_nodes', [])
                for node in low_volume_nodes[:3]:  # Top 3 gaps
                    price = node.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#FF9800', linestyle=':', alpha=0.6, linewidth=1.5)  # Orange
                        all_levels.append(price)

                # Current price
                ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

                # ✅ Adjust Y-axis to include all volume profile levels
                if len(all_levels) > 1:
                    self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding enhanced volume profile levels: {e}")

    def generate_clean_ai_analysis_chart(self, coin: str, ai_data: Dict[str, Any],
                                       ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🤖 Generate CLEAN AI Analysis chart - Only candlesticks + AI prediction lines."""
        try:
            print(f"🤖 Generating CLEAN AI Analysis chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"AI_ANALYSIS_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # ✅ ADD TITLE
            fig.suptitle(f'🤖 AI Analysis - {coin}', fontsize=16, fontweight='bold',
                        color='#333333', y=0.95)

            # Draw enhanced candlesticks with price axis
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add AI prediction lines
            self._add_clean_ai_prediction_lines(ax, ai_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ CLEAN AI Analysis chart saved: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error generating CLEAN AI Analysis chart: {e}")
            return None

    def generate_clean_pump_alert_chart(self, coin: str, pump_data: Dict[str, Any],
                                      ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🚀 Generate CLEAN Pump Alert chart - Only candlesticks + pump indicators."""
        try:
            print(f"🚀 Generating CLEAN Pump Alert chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"PUMP_ALERT_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # ✅ ADD TITLE
            fig.suptitle(f'🚀 PUMP ALERT - {coin}', fontsize=16, fontweight='bold',
                        color='#FF5722', y=0.95)

            # Draw enhanced candlesticks with price axis
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add pump indicators
            self._add_clean_pump_indicators(ax, pump_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ CLEAN Pump Alert chart saved: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error generating CLEAN Pump Alert chart: {e}")
            return None

    def generate_pump_alert_early_chart(self, coin: str, pump_data: Dict[str, Any],
                                      ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🚀⚡ Generate EARLY Pump Alert chart with enhanced early warning indicators."""
        try:
            print(f"🚀⚡ Generating EARLY Pump Alert chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"PUMP_ALERT_EARLY_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with early warning theme
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='#1A1A2E')
            ax.set_facecolor('#16213E')

            # ✅ ADD EARLY WARNING TITLE
            fig.suptitle(f'🚀⚡ EARLY PUMP ALERT - {coin}', fontsize=18, fontweight='bold',
                        color='#FFD93D', y=0.95)

            # ✅ Draw candlesticks with early warning colors
            self._draw_enhanced_candlesticks_early_pump(ax, ohlcv_data, current_price)

            # ✅ Add early pump indicators
            self._add_early_pump_indicators(ax, pump_data, current_price)

            # ✅ Add early warning annotations
            self._add_early_warning_annotations_pump(ax, pump_data, current_price)

            # ✅ Style axis for early warning theme
            self._style_early_warning_axis(ax, 'pump')

            # ✅ Add early warning watermark
            self._add_early_warning_watermark(fig, 'PUMP')

            # ✅ Save with enhanced settings
            plt.tight_layout()
            plt.savefig(filepath, dpi=150, bbox_inches='tight', facecolor='#1A1A2E')
            plt.close()

            print(f"✅ EARLY Pump Alert chart saved: {filepath}")
            self._log_chart_generation("pump_alert_early", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating EARLY Pump Alert chart: {e}")
            return None

    def generate_clean_dump_alert_chart(self, coin: str, dump_data: Dict[str, Any],
                                      ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📉 Generate CLEAN Dump Alert chart - Only candlesticks + dump indicators."""
        try:
            print(f"📉 Generating CLEAN Dump Alert chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"DUMP_ALERT_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # ✅ ADD TITLE
            fig.suptitle(f'📉 DUMP ALERT - {coin}', fontsize=16, fontweight='bold',
                        color='#F44336', y=0.95)

            # Draw enhanced candlesticks with price axis
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add dump indicators
            self._add_clean_dump_indicators(ax, dump_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ CLEAN Dump Alert chart saved: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error generating CLEAN Dump Alert chart: {e}")
            return None

    def generate_dump_alert_early_chart(self, coin: str, dump_data: Dict[str, Any],
                                      ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📉⚡ Generate EARLY Dump Alert chart with enhanced early warning indicators."""
        try:
            print(f"📉⚡ Generating EARLY Dump Alert chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"DUMP_ALERT_EARLY_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with early warning theme
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='#2C2C54')
            ax.set_facecolor('#40407A')

            # ✅ ADD EARLY WARNING TITLE
            fig.suptitle(f'📉⚡ EARLY DUMP ALERT - {coin}', fontsize=18, fontweight='bold',
                        color='#FF3838', y=0.95)

            # ✅ Draw candlesticks with early warning colors
            self._draw_enhanced_candlesticks_early_dump(ax, ohlcv_data, current_price)

            # ✅ Add early dump indicators
            self._add_early_dump_indicators(ax, dump_data, current_price)

            # ✅ Add early warning annotations
            self._add_early_warning_annotations_dump(ax, dump_data, current_price)

            # ✅ Style axis for early warning theme
            self._style_early_warning_axis(ax, 'dump')

            # ✅ Add early warning watermark
            self._add_early_warning_watermark(fig, 'DUMP')

            # ✅ Save with enhanced settings
            plt.tight_layout()
            plt.savefig(filepath, dpi=150, bbox_inches='tight', facecolor='#2C2C54')
            plt.close()

            print(f"✅ EARLY Dump Alert chart saved: {filepath}")
            self._log_chart_generation("dump_alert_early", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating EARLY Dump Alert chart: {e}")
            return None

    # ============================================================================
    # 🚀⚡ EARLY WARNING CHART HELPERS
    # ============================================================================

    def _draw_enhanced_candlesticks_early_pump(self, ax, ohlcv_data: pd.DataFrame, current_price: float) -> None:
        """🚀⚡ Draw enhanced candlesticks for early pump warning."""
        try:
            # Limit to recent data for clarity
            if len(ohlcv_data) > 100:
                ohlcv_data = ohlcv_data.tail(100)

            # Early pump colors - more vibrant
            up_color = '#26DE81'    # Bright green
            down_color = '#FC5C65'  # Bright red
            current_color = '#FFD93D'  # Golden yellow for current price

            # Draw candlesticks
            for i, (idx, row) in enumerate(ohlcv_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # Determine color with enhanced brightness for early warning
                color = up_color if close_price >= open_price else down_color
                alpha = 0.9  # Higher alpha for early warning visibility

                # Draw high-low line (wick)
                ax.plot([i, i], [low_price, high_price], color=color, linewidth=1.5, alpha=alpha)

                # Draw open-close rectangle (body)
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                rect = plt.Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                   facecolor=color, alpha=alpha, edgecolor=color, linewidth=1)
                ax.add_patch(rect)

            # Highlight current price with special line
            ax.axhline(y=current_price, color=current_color, linewidth=3, alpha=0.8,
                      linestyle='--', label=f'Current: {current_price:.8f}')

            # Set limits
            ax.set_xlim(-1, len(ohlcv_data))

        except Exception as e:
            print(f"❌ Error drawing early pump candlesticks: {e}")

    def _draw_enhanced_candlesticks_early_dump(self, ax, ohlcv_data: pd.DataFrame, current_price: float) -> None:
        """📉⚡ Draw enhanced candlesticks for early dump warning."""
        try:
            # Limit to recent data for clarity
            if len(ohlcv_data) > 100:
                ohlcv_data = ohlcv_data.tail(100)

            # Early dump colors - more dramatic
            up_color = '#48BB78'    # Muted green
            down_color = '#FF3838'  # Bright red
            current_color = '#FF6B9D'  # Hot pink for current price

            # Draw candlesticks
            for i, (idx, row) in enumerate(ohlcv_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # Determine color with enhanced contrast for early warning
                color = up_color if close_price >= open_price else down_color
                alpha = 0.9  # Higher alpha for early warning visibility

                # Draw high-low line (wick)
                ax.plot([i, i], [low_price, high_price], color=color, linewidth=1.5, alpha=alpha)

                # Draw open-close rectangle (body)
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                rect = plt.Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                   facecolor=color, alpha=alpha, edgecolor=color, linewidth=1)
                ax.add_patch(rect)

            # Highlight current price with warning line
            ax.axhline(y=current_price, color=current_color, linewidth=3, alpha=0.8,
                      linestyle='--', label=f'Current: {current_price:.8f}')

            # Set limits
            ax.set_xlim(-1, len(ohlcv_data))

        except Exception as e:
            print(f"❌ Error drawing early dump candlesticks: {e}")

    def _add_early_pump_indicators(self, ax, pump_data: Dict[str, Any], current_price: float) -> None:
        """🚀⚡ Add early pump warning indicators."""
        try:
            # Early pump probability
            probability = pump_data.get('pump_probability', 0)
            intensity = pump_data.get('intensity', 0)
            volume_spike = pump_data.get('volume_spike_factor', 1)

            # Add probability indicator with enhanced visibility
            ax.text(0.02, 0.98, f'🚀 EARLY PUMP\n{probability:.0%}',
                   transform=ax.transAxes, fontsize=16, fontweight='bold',
                   color='#FFD93D', ha='left', va='top',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='#FF6B6B', alpha=0.8))

            # Add intensity meter
            ax.text(0.02, 0.85, f'⚡ Intensity: {intensity:.2f}',
                   transform=ax.transAxes, fontsize=12, fontweight='bold',
                   color='#FFFFFF', ha='left', va='top')

            # Add volume spike indicator
            if volume_spike > 2:
                ax.text(0.02, 0.78, f'📊 Volume: {volume_spike:.1f}x',
                       transform=ax.transAxes, fontsize=12, fontweight='bold',
                       color='#4ECDC4', ha='left', va='top')

            # Add warning levels
            warning_level = pump_data.get('warning_stage', 'PRE_PUMP')
            colors = {'PRE_PUMP': '#FFD93D', 'EARLY_PUMP': '#FF6B6B', 'ACTIVE_PUMP': '#FF3838'}
            ax.text(0.98, 0.98, f'⚠️ {warning_level}',
                   transform=ax.transAxes, fontsize=14, fontweight='bold',
                   color=colors.get(warning_level, '#FFD93D'), ha='right', va='top')

        except Exception as e:
            print(f"❌ Error adding early pump indicators: {e}")

    def _add_early_dump_indicators(self, ax, dump_data: Dict[str, Any], current_price: float) -> None:
        """📉⚡ Add early dump warning indicators."""
        try:
            # Early dump probability
            probability = dump_data.get('dump_probability', 0)
            severity = dump_data.get('severity_level', 'MEDIUM')
            confidence = dump_data.get('confidence_score', 0)

            # Add probability indicator with enhanced visibility
            ax.text(0.02, 0.98, f'📉 EARLY DUMP\n{probability:.0%}',
                   transform=ax.transAxes, fontsize=16, fontweight='bold',
                   color='#FF3838', ha='left', va='top',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='#C44569', alpha=0.8))

            # Add severity indicator
            severity_colors = {'LOW': '#FFD93D', 'MEDIUM': '#FF6B6B', 'HIGH': '#FF3838', 'CRITICAL': '#8B0000'}
            ax.text(0.02, 0.85, f'⚠️ Severity: {severity}',
                   transform=ax.transAxes, fontsize=12, fontweight='bold',
                   color=severity_colors.get(severity, '#FF6B6B'), ha='left', va='top')

            # Add confidence score
            ax.text(0.02, 0.78, f'🎯 Confidence: {confidence:.1%}',
                   transform=ax.transAxes, fontsize=12, fontweight='bold',
                   color='#FFFFFF', ha='left', va='top')

            # Add warning levels
            warning_level = dump_data.get('warning_stage', 'PRE_DUMP')
            colors = {'PRE_DUMP': '#FFD93D', 'EARLY_DUMP': '#FF6B6B', 'ACTIVE_DUMP': '#FF3838'}
            ax.text(0.98, 0.98, f'🚨 {warning_level}',
                   transform=ax.transAxes, fontsize=14, fontweight='bold',
                   color=colors.get(warning_level, '#FF3838'), ha='right', va='top')

        except Exception as e:
            print(f"❌ Error adding early dump indicators: {e}")

    def _add_early_warning_annotations_pump(self, ax, pump_data: Dict[str, Any], current_price: float) -> None:
        """🚀⚡ Add early warning annotations for pump."""
        try:
            # Add entry point suggestion
            entry_price = pump_data.get('suggested_entry', current_price * 1.02)
            ax.axhline(y=entry_price, color='#26DE81', linewidth=2, alpha=0.7,
                      linestyle=':', label=f'Entry: {entry_price:.8f}')

            # Add target levels
            targets = pump_data.get('targets', [current_price * 1.05, current_price * 1.10])
            for i, target in enumerate(targets[:2]):
                ax.axhline(y=target, color='#FFD93D', linewidth=1.5, alpha=0.6,
                          linestyle='--', label=f'TP{i+1}: {target:.8f}')

            # Add stop loss
            stop_loss = pump_data.get('stop_loss', current_price * 0.98)
            ax.axhline(y=stop_loss, color='#FF6B6B', linewidth=2, alpha=0.7,
                      linestyle='-.', label=f'SL: {stop_loss:.8f}')

            # Add time estimate
            time_estimate = pump_data.get('estimated_time', '5-15 min')
            ax.text(0.98, 0.85, f'⏰ Est. Time: {time_estimate}',
                   transform=ax.transAxes, fontsize=10,
                   color='#FFFFFF', ha='right', va='top')

        except Exception as e:
            print(f"❌ Error adding early pump annotations: {e}")

    def _add_early_warning_annotations_dump(self, ax, dump_data: Dict[str, Any], current_price: float) -> None:
        """📉⚡ Add early warning annotations for dump."""
        try:
            # Add exit point suggestion
            exit_price = dump_data.get('suggested_exit', current_price * 0.98)
            ax.axhline(y=exit_price, color='#FF3838', linewidth=2, alpha=0.7,
                      linestyle=':', label=f'Exit: {exit_price:.8f}')

            # Add support levels
            supports = dump_data.get('support_levels', [current_price * 0.95, current_price * 0.90])
            for i, support in enumerate(supports[:2]):
                ax.axhline(y=support, color='#4ECDC4', linewidth=1.5, alpha=0.6,
                          linestyle='--', label=f'Support{i+1}: {support:.8f}')

            # Add estimated dump magnitude
            dump_magnitude = dump_data.get('estimated_dump_magnitude', 0.05)
            ax.text(0.98, 0.85, f'📉 Est. Drop: {dump_magnitude:.1%}',
                   transform=ax.transAxes, fontsize=10,
                   color='#FF3838', ha='right', va='top')

            # Add time estimate
            time_estimate = dump_data.get('estimated_time', '5-15 min')
            ax.text(0.98, 0.78, f'⏰ Est. Time: {time_estimate}',
                   transform=ax.transAxes, fontsize=10,
                   color='#FFFFFF', ha='right', va='top')

        except Exception as e:
            print(f"❌ Error adding early dump annotations: {e}")

    def _style_early_warning_axis(self, ax, alert_type: str) -> None:
        """⚡ Style axis for early warning theme - REMOVED COLORED BORDERS."""
        try:
            # Color scheme based on alert type
            if alert_type == 'pump':
                grid_color = '#FFD93D'
                text_color = '#FFFFFF'
                # spine_color = '#26DE81'  # ✅ REMOVED: No more green border
            else:  # dump
                grid_color = '#FF6B9D'
                text_color = '#FFFFFF'
                # spine_color = '#FF3838'  # ✅ REMOVED: No more red border

            # Style grid
            ax.grid(True, alpha=0.3, color=grid_color, linestyle='-', linewidth=0.5)

            # ✅ REMOVED COLORED SPINES - Use default clean styling instead
            # Style spines with clean neutral colors (no more colored borders)
            for spine in ax.spines.values():
                spine.set_color('#CCCCCC')  # ✅ NEUTRAL GRAY instead of colored borders
                spine.set_linewidth(1)      # ✅ THINNER lines (was 2)

            # Hide top and right spines for cleaner look
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)

            # Style ticks and labels
            ax.tick_params(colors=text_color, labelsize=10)
            ax.set_xlabel('Time', color=text_color, fontsize=12, fontweight='bold')
            ax.set_ylabel('Price', color=text_color, fontsize=12, fontweight='bold')

            # Add legend with early warning style
            legend = ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
            legend.get_frame().set_facecolor('#000000')
            legend.get_frame().set_alpha(0.8)
            for text in legend.get_texts():
                text.set_color(text_color)

        except Exception as e:
            print(f"❌ Error styling early warning axis: {e}")

    def _add_early_warning_watermark(self, fig, alert_type: str) -> None:
        """⚡ Add early warning watermark."""
        try:
            if alert_type == 'PUMP':
                watermark_text = '🚀⚡ EARLY PUMP WARNING'
                color = '#FFD93D'
            else:  # DUMP
                watermark_text = '📉⚡ EARLY DUMP WARNING'
                color = '#FF3838'

            fig.text(0.5, 0.02, watermark_text, ha='center', va='bottom',
                    fontsize=14, fontweight='bold', color=color, alpha=0.7)

            # Add timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.98, 0.02, f'Generated: {timestamp}', ha='right', va='bottom',
                    fontsize=8, color='#CCCCCC', alpha=0.8)

        except Exception as e:
            print(f"❌ Error adding early warning watermark: {e}")

    def _add_clean_ai_prediction_lines(self, ax, ai_data: Dict[str, Any], current_price: float):
        """🤖 Add ENHANCED AI prediction lines with same format as consensus signal."""
        try:
            # Get AI trading levels if available (same format as consensus signal)
            trading_levels = ai_data.get('trading_levels', {})

            if trading_levels:
                # Use trading levels format (same as consensus)
                entry = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)

                # ✅ Draw ENHANCED horizontal lines with labels (same as consensus)
                signal_levels = []

                if entry > 0:
                    # Entry line - Blue with thicker line (same as consensus)
                    ax.axhline(y=entry, color='#007BFF', linewidth=3.5, alpha=0.9, linestyle='-',
                              label=f'ENTRY: {entry:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, entry, 'ENTRY',
                           fontsize=10, color='#007BFF', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#007BFF', alpha=0.8))
                    signal_levels.append(entry)

                if take_profit > 0:
                    # Take Profit line - Green with thicker line (same as consensus)
                    ax.axhline(y=take_profit, color='#28A745', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'TP: {take_profit:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, take_profit, 'TP',
                           fontsize=10, color='#28A745', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#28A745', alpha=0.8))
                    signal_levels.append(take_profit)

                if stop_loss > 0:
                    # Stop Loss line - Red with thicker line (same as consensus)
                    ax.axhline(y=stop_loss, color='#DC3545', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'SL: {stop_loss:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, stop_loss, 'SL',
                           fontsize=10, color='#DC3545', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#DC3545', alpha=0.8))
                    signal_levels.append(stop_loss)

                # ✅ Adjust Y-axis to include all signal levels (same as consensus)
                if signal_levels:
                    self._adjust_ylim_for_signals(ax, signal_levels)
            else:
                # Fallback to old prediction lines format if no trading levels
                ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
                ensemble_confidence = ai_data.get('ensemble_confidence', 0)

                # Get prediction levels if available
                prediction_levels = ai_data.get('prediction_levels', {})
                support_level = prediction_levels.get('support', 0)
                resistance_level = prediction_levels.get('resistance', 0)

                # Collect all price levels for Y-axis adjustment
                all_levels = [current_price]

                # Current price line
                ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

                # Support/Resistance levels
                if support_level > 0:
                    ax.axhline(y=support_level, color='#28A745', linewidth=2.5, alpha=0.8, linestyle='--')  # Green
                    all_levels.append(support_level)

                if resistance_level > 0:
                    ax.axhline(y=resistance_level, color='#DC3545', linewidth=2.5, alpha=0.8, linestyle='--')  # Red
                    all_levels.append(resistance_level)

                # Signal-based trend line
                if ensemble_signal == 'BUY':
                    # Bullish trend line
                    trend_target = current_price * (1 + ensemble_confidence * 0.05)  # Up to 5% based on confidence
                    ax.axhline(y=trend_target, color='#00C851', linewidth=2, alpha=0.7, linestyle=':')  # Green dotted
                    all_levels.append(trend_target)
                elif ensemble_signal == 'SELL':
                    # Bearish trend line
                    trend_target = current_price * (1 - ensemble_confidence * 0.05)  # Down to 5% based on confidence
                    ax.axhline(y=trend_target, color='#FF4444', linewidth=2, alpha=0.7, linestyle=':')  # Red dotted
                    all_levels.append(trend_target)

                # ✅ Adjust Y-axis to include all AI prediction levels
                if len(all_levels) > 1:
                    self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding enhanced AI prediction lines: {e}")

    def _add_clean_pump_indicators(self, ax, pump_data: Dict[str, Any], current_price: float):
        """🚀 Add CLEAN pump indicators - Only lines, no text."""
        try:
            # Get pump data
            intensity = pump_data.get('intensity', 0)

            # Collect all price levels for Y-axis adjustment
            all_levels = [current_price]

            # Current price line
            ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

            # Pump target levels based on intensity
            if intensity > 0:
                # Multiple pump targets
                target_1 = current_price * (1 + intensity * 0.02)  # 2% per intensity unit
                target_2 = current_price * (1 + intensity * 0.04)  # 4% per intensity unit
                target_3 = current_price * (1 + intensity * 0.06)  # 6% per intensity unit

                ax.axhline(y=target_1, color='#FFC107', linewidth=2.5, alpha=0.8, linestyle='--')  # Yellow
                ax.axhline(y=target_2, color='#FF9800', linewidth=2.5, alpha=0.8, linestyle='--')  # Orange
                ax.axhline(y=target_3, color='#FF5722', linewidth=2.5, alpha=0.8, linestyle='--')  # Red-Orange

                all_levels.extend([target_1, target_2, target_3])

            # Volume spike indicator (if available)
            volume_spike_level = pump_data.get('volume_spike_level', 0)
            if volume_spike_level > 0:
                spike_price = current_price * (1 + volume_spike_level * 0.01)
                ax.axhline(y=spike_price, color='#9C27B0', linewidth=2, alpha=0.7, linestyle=':')  # Purple
                all_levels.append(spike_price)

            # ✅ Adjust Y-axis to include all pump indicator levels
            if len(all_levels) > 1:
                self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding clean pump indicators: {e}")

    def _add_clean_dump_indicators(self, ax, dump_data: Dict[str, Any], current_price: float):
        """📉 Add CLEAN dump indicators - Only lines, no text."""
        try:
            # Get dump data
            dump_probability = dump_data.get('dump_probability', 0)
            severity_level = dump_data.get('severity_level', 'MEDIUM')

            # Collect all price levels for Y-axis adjustment
            all_levels = [current_price]

            # Current price line
            ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

            # Dump target levels based on severity
            severity_multiplier = {'LOW': 0.02, 'MEDIUM': 0.05, 'HIGH': 0.08, 'CRITICAL': 0.12}.get(severity_level, 0.05)

            if dump_probability > 0:
                # Multiple dump targets
                target_1 = current_price * (1 - severity_multiplier * 0.5)
                target_2 = current_price * (1 - severity_multiplier * 1.0)
                target_3 = current_price * (1 - severity_multiplier * 1.5)

                ax.axhline(y=target_1, color='#FF9800', linewidth=2.5, alpha=0.8, linestyle='--')  # Orange
                ax.axhline(y=target_2, color='#FF5722', linewidth=2.5, alpha=0.8, linestyle='--')  # Red-Orange
                ax.axhline(y=target_3, color='#F44336', linewidth=2.5, alpha=0.8, linestyle='--')  # Red

                all_levels.extend([target_1, target_2, target_3])

            # Support levels (if available)
            support_levels = dump_data.get('support_levels', [])
            for i, support in enumerate(support_levels[:3]):  # Max 3 support levels
                support_price = support.get('price', 0)
                if support_price > 0:
                    alpha = 0.8 - (i * 0.2)  # Decreasing alpha for lower supports
                    ax.axhline(y=support_price, color='#4CAF50', linewidth=2, alpha=alpha, linestyle=':')  # Green
                    all_levels.append(support_price)

            # ✅ Adjust Y-axis to include all dump indicator levels
            if len(all_levels) > 1:
                self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding clean dump indicators: {e}")

    def generate_clean_point_figure_chart(self, coin: str, point_figure_data: Dict[str, Any],
                                        ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📈 Generate ENHANCED Point & Figure chart with same format as consensus signal."""
        try:
            print(f"📈 Generating ENHANCED Point & Figure chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"POINT_FIGURE_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark (same as consensus)
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # Get Point Figure signal type for title color (same logic as consensus)
            pf_signal = point_figure_data.get('signal', 'NONE')
            title_color = '#28A745' if pf_signal == 'BUY' else '#DC3545' if pf_signal == 'SELL' else '#333333'

            # ✅ ADD TITLE with signal type (same format as consensus)
            fig.suptitle(f'📈 POINT & FIGURE ANALYSIS - {coin} ({pf_signal})', fontsize=16, fontweight='bold',
                        color=title_color, y=0.95)

            # Draw enhanced candlesticks with price axis (same as consensus)
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add Point & Figure levels with Entry/TP/SL format (same as consensus)
            self._add_enhanced_point_figure_levels(ax, point_figure_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible (same as consensus)
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis (same as consensus)
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines (same as consensus)
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK (same as consensus)
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark (same as consensus)
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ ENHANCED Point & Figure chart saved: {filepath}")
            self._log_chart_generation("point_figure", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating ENHANCED Point & Figure chart: {e}")
            traceback.print_exc()
            self._log_chart_generation("point_figure", coin, False)
            return None

    def generate_clean_orderbook_chart(self, coin: str, orderbook_data: Dict[str, Any],
                                     ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📋 Generate ENHANCED Orderbook chart with same format as consensus signal."""
        try:
            print(f"📋 Generating ENHANCED Orderbook chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"ORDERBOOK_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark (same as consensus)
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # Get Orderbook signal type for title color (same logic as consensus)
            orderbook_signal = orderbook_data.get('signal', 'NONE')
            title_color = '#28A745' if orderbook_signal == 'BUY' else '#DC3545' if orderbook_signal == 'SELL' else '#333333'

            # ✅ ADD TITLE with signal type (same format as consensus)
            fig.suptitle(f'📋 ORDERBOOK ANALYSIS - {coin} ({orderbook_signal})', fontsize=16, fontweight='bold',
                        color=title_color, y=0.95)

            # Draw enhanced candlesticks with price axis (same as consensus)
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add orderbook levels with Entry/TP/SL format (same as consensus)
            self._add_enhanced_orderbook_levels(ax, orderbook_data, current_price)

            # ✅ ENHANCED styling - Keep price axis visible (same as consensus)
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis (same as consensus)
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines (same as consensus)
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK (same as consensus)
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark (same as consensus)
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ ENHANCED Orderbook chart saved: {filepath}")
            self._log_chart_generation("orderbook", coin, True)
            return filepath

        except Exception as e:
            print(f"❌ Error generating ENHANCED Orderbook chart: {e}")
            traceback.print_exc()
            self._log_chart_generation("orderbook", coin, False)
            return None

    def generate_clean_fourier_chart(self, coin: str, fourier_data: Dict[str, Any],
                                   ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🌊 Generate CLEAN Fourier chart - Only candlesticks + Fourier analysis lines."""
        try:
            print(f"🌊 Generating CLEAN Fourier chart for {coin}...")

            # Create filename
            timestamp = int(time.time())
            filename = f"FOURIER_{coin.replace('/', '_')}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)

            # ✅ Setup ENHANCED figure with title and watermark
            fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
            ax.set_facecolor('#FAFAFA')

            # ✅ ADD TITLE
            fig.suptitle(f'🌊 Fourier Analysis - {coin}', fontsize=16, fontweight='bold',
                        color='#333333', y=0.95)

            # Draw enhanced candlesticks with price axis
            self._draw_clean_candlesticks(ax, ohlcv_data)

            # Add Fourier analysis lines
            self._add_clean_fourier_lines(ax, fourier_data, current_price, ohlcv_data)

            # ✅ ENHANCED styling - Keep price axis visible
            ax.set_xlabel('Time', fontsize=10, color='#666666')
            ax.set_ylabel('Price', fontsize=10, color='#666666')

            # Keep Y-axis visible but clean X-axis
            ax.tick_params(axis='x', which='both', bottom=False, top=False, labelbottom=True)
            ax.tick_params(axis='y', which='both', left=True, right=False, labelleft=True)

            # Clean spines
            for spine_name in ['top', 'right']:
                ax.spines[spine_name].set_visible(False)
            for spine_name in ['left', 'bottom']:
                ax.spines[spine_name].set_color('#CCCCCC')

            # ✅ ADD WATERMARK
            fig.text(0.99, 0.01, 'AI TRADING', fontsize=12, color='#CCCCCC',
                    ha='right', va='bottom', alpha=0.7, fontweight='bold')

            # Save with proper padding for title and watermark
            plt.subplots_adjust(left=0.08, right=0.95, top=0.90, bottom=0.08)
            plt.savefig(filepath, dpi=200, bbox_inches='tight', pad_inches=0.1,
                       facecolor='white', edgecolor='none', format='png')
            plt.close(fig)

            print(f"✅ CLEAN Fourier chart saved: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Error generating CLEAN Fourier chart: {e}")
            return None

    def _add_enhanced_point_figure_levels(self, ax, point_figure_data: Dict[str, Any], current_price: float):
        """📈 Add ENHANCED Point & Figure levels with same format as consensus signal."""
        try:
            # Get trading levels if available (same format as consensus signal)
            trading_levels = point_figure_data.get('trading_levels', {})

            if trading_levels:
                # Use trading levels format (same as consensus)
                entry = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)

                # ✅ Draw ENHANCED horizontal lines with labels (same as consensus)
                signal_levels = []

                if entry > 0:
                    # Entry line - Blue with thicker line (same as consensus)
                    ax.axhline(y=entry, color='#007BFF', linewidth=3.5, alpha=0.9, linestyle='-',
                              label=f'ENTRY: {entry:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, entry, 'ENTRY',
                           fontsize=10, color='#007BFF', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#007BFF', alpha=0.8))
                    signal_levels.append(entry)

                if take_profit > 0:
                    # Take Profit line - Green with thicker line (same as consensus)
                    ax.axhline(y=take_profit, color='#28A745', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'TP: {take_profit:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, take_profit, 'TP',
                           fontsize=10, color='#28A745', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#28A745', alpha=0.8))
                    signal_levels.append(take_profit)

                if stop_loss > 0:
                    # Stop Loss line - Red with thicker line (same as consensus)
                    ax.axhline(y=stop_loss, color='#DC3545', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'SL: {stop_loss:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, stop_loss, 'SL',
                           fontsize=10, color='#DC3545', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#DC3545', alpha=0.8))
                    signal_levels.append(stop_loss)

                # ✅ Adjust Y-axis to include all signal levels (same as consensus)
                if signal_levels:
                    self._adjust_ylim_for_signals(ax, signal_levels)
            else:
                # Fallback to Point & Figure levels if no trading levels
                # Get Point & Figure data
                trend_lines = point_figure_data.get('trend_lines', [])
                support_levels = point_figure_data.get('support_levels', [])
                resistance_levels = point_figure_data.get('resistance_levels', [])
                price_targets = point_figure_data.get('price_targets', [])

                # Collect all price levels for Y-axis adjustment
                all_levels = [current_price]

                # Current price line
                ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

                # Trend lines
                for trend in trend_lines[:3]:  # Max 3 trend lines
                    price = trend.get('price', 0)
                    trend_type = trend.get('type', 'unknown')
                    if price > 0:
                        if trend_type == 'bullish':
                            ax.axhline(y=price, color='#00C851', linewidth=2.5, alpha=0.8, linestyle='-')  # Green
                        else:
                            ax.axhline(y=price, color='#FF4444', linewidth=2.5, alpha=0.8, linestyle='-')  # Red
                        all_levels.append(price)

                # Support levels
                for support in support_levels[:3]:  # Max 3 support levels
                    price = support.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#28A745', linewidth=2, alpha=0.7, linestyle='--')  # Green dashed
                        all_levels.append(price)

                # Resistance levels
                for resistance in resistance_levels[:3]:  # Max 3 resistance levels
                    price = resistance.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#DC3545', linewidth=2, alpha=0.7, linestyle='--')  # Red dashed
                        all_levels.append(price)

                # Price targets
                for target in price_targets[:2]:  # Max 2 price targets
                    price = target.get('price', 0)
                    if price > 0:
                        ax.axhline(y=price, color='#FFC107', linewidth=2, alpha=0.8, linestyle=':')  # Yellow dotted
                        all_levels.append(price)

                # ✅ Adjust Y-axis to include all Point & Figure levels
                if len(all_levels) > 1:
                    self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding enhanced Point & Figure levels: {e}")

    def _add_enhanced_orderbook_levels(self, ax, orderbook_data: Dict[str, Any], current_price: float):
        """📋 Add ENHANCED orderbook levels with same format as consensus signal."""
        try:
            # Get trading levels if available (same format as consensus signal)
            trading_levels = orderbook_data.get('trading_levels', {})

            if trading_levels:
                # Use trading levels format (same as consensus)
                entry = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)

                # ✅ Draw ENHANCED horizontal lines with labels (same as consensus)
                signal_levels = []

                if entry > 0:
                    # Entry line - Blue with thicker line (same as consensus)
                    ax.axhline(y=entry, color='#007BFF', linewidth=3.5, alpha=0.9, linestyle='-',
                              label=f'ENTRY: {entry:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, entry, 'ENTRY',
                           fontsize=10, color='#007BFF', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#007BFF', alpha=0.8))
                    signal_levels.append(entry)

                if take_profit > 0:
                    # Take Profit line - Green with thicker line (same as consensus)
                    ax.axhline(y=take_profit, color='#28A745', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'TP: {take_profit:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, take_profit, 'TP',
                           fontsize=10, color='#28A745', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#28A745', alpha=0.8))
                    signal_levels.append(take_profit)

                if stop_loss > 0:
                    # Stop Loss line - Red with thicker line (same as consensus)
                    ax.axhline(y=stop_loss, color='#DC3545', linewidth=3.5, alpha=0.9, linestyle='--',
                              label=f'SL: {stop_loss:.6f}')
                    # Add small text label on the right (same as consensus)
                    ax.text(ax.get_xlim()[1] * 0.98, stop_loss, 'SL',
                           fontsize=10, color='#DC3545', fontweight='bold',
                           ha='right', va='bottom', bbox=dict(boxstyle='round,pad=0.3',
                           facecolor='white', edgecolor='#DC3545', alpha=0.8))
                    signal_levels.append(stop_loss)

                # ✅ Adjust Y-axis to include all signal levels (same as consensus)
                if signal_levels:
                    self._adjust_ylim_for_signals(ax, signal_levels)
            else:
                # Fallback to orderbook levels if no trading levels
                self._add_clean_orderbook_levels(ax, orderbook_data, current_price)

        except Exception as e:
            print(f"❌ Error adding enhanced orderbook levels: {e}")

    def _add_clean_orderbook_levels(self, ax, orderbook_data: Dict[str, Any], current_price: float):
        """📋 Add CLEAN orderbook levels - Only lines, no text."""
        try:
            # Get orderbook data
            bid_levels = orderbook_data.get('significant_bid_levels', [])
            ask_levels = orderbook_data.get('significant_ask_levels', [])
            support_resistance = orderbook_data.get('support_resistance_levels', [])

            # Collect all price levels for Y-axis adjustment
            all_levels = [current_price]

            # Current price line
            ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

            # Significant bid levels (support)
            for bid in bid_levels[:5]:  # Max 5 bid levels
                price = bid.get('price', 0)
                if price > 0:
                    ax.axhline(y=price, color='#28A745', linewidth=2, alpha=0.7, linestyle='-')  # Green
                    all_levels.append(price)

            # Significant ask levels (resistance)
            for ask in ask_levels[:5]:  # Max 5 ask levels
                price = ask.get('price', 0)
                if price > 0:
                    ax.axhline(y=price, color='#DC3545', linewidth=2, alpha=0.7, linestyle='-')  # Red
                    all_levels.append(price)

            # Support/Resistance levels
            for sr in support_resistance[:3]:  # Max 3 S/R levels
                price = sr.get('price', 0)
                level_type = sr.get('type', 'unknown')
                if price > 0:
                    if level_type == 'support':
                        ax.axhline(y=price, color='#4CAF50', linewidth=2.5, alpha=0.8, linestyle='--')  # Green dashed
                    elif level_type == 'resistance':
                        ax.axhline(y=price, color='#F44336', linewidth=2.5, alpha=0.8, linestyle='--')  # Red dashed
                    all_levels.append(price)

            # ✅ Adjust Y-axis to include all orderbook levels
            if len(all_levels) > 1:
                self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding clean orderbook levels: {e}")

    def _add_clean_fourier_lines(self, ax, fourier_data: Dict[str, Any], current_price: float, ohlcv_data: pd.DataFrame):
        """🌊 Add CLEAN Fourier analysis lines - Only lines, no text."""
        try:
            # Get Fourier data
            fourier_prediction = fourier_data.get('fourier_prediction', [])
            dominant_cycle = fourier_data.get('dominant_cycle', 0)
            trend_component = fourier_data.get('trend_component', [])

            # Collect all price levels for Y-axis adjustment
            all_levels = [current_price]

            # Current price line
            ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Blue

            # Fourier prediction line (if available)
            if fourier_prediction and len(fourier_prediction) > 0:
                # Create x-axis for prediction
                x_pred = list(range(len(ohlcv_data), len(ohlcv_data) + len(fourier_prediction)))
                ax.plot(x_pred, fourier_prediction, color='#9C27B0', linewidth=2.5, alpha=0.8, linestyle='-')  # Purple
                # Add prediction levels to Y-axis adjustment
                all_levels.extend(fourier_prediction)

            # Trend component overlay (if available)
            if trend_component and len(trend_component) == len(ohlcv_data):
                x_trend = list(range(len(ohlcv_data)))
                ax.plot(x_trend, trend_component, color='#FF9800', linewidth=2, alpha=0.7, linestyle='--')  # Orange dashed
                # Add trend component levels to Y-axis adjustment
                all_levels.extend(trend_component)

            # Cycle-based support/resistance levels
            if dominant_cycle > 0:
                # Calculate cycle-based levels
                cycle_high = current_price * (1 + dominant_cycle * 0.01)  # 1% per cycle unit
                cycle_low = current_price * (1 - dominant_cycle * 0.01)

                ax.axhline(y=cycle_high, color='#E91E63', linewidth=2, alpha=0.7, linestyle=':')  # Pink dotted
                ax.axhline(y=cycle_low, color='#2196F3', linewidth=2, alpha=0.7, linestyle=':')  # Blue dotted

                all_levels.extend([cycle_high, cycle_low])

            # ✅ Adjust Y-axis to include all Fourier levels
            if len(all_levels) > 1:
                self._adjust_ylim_for_signals(ax, all_levels)

        except Exception as e:
            print(f"❌ Error adding clean Fourier lines: {e}")

    def _add_fibonacci_levels(self, ax, fibonacci_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """🌀 Add Fibonacci retracement levels."""
        try:
            retracement_levels = fibonacci_data.get('retracement_levels', [])
            extension_levels = fibonacci_data.get('extension_levels', [])
            
            # Draw retracement levels
            for level in retracement_levels:
                price = level.get('price', 0)
                ratio = level.get('ratio', 0)
                
                ax.axhline(y=price, color=colors['primary'], linestyle='--', alpha=0.7, linewidth=1.5)
                ax.text(0.02, price, f'{ratio:.1%}: {price:.6f}', 
                       transform=ax.get_yaxis_transform(), fontsize=9, 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['primary'], alpha=0.7))
            
            # Draw extension levels
            for level in extension_levels:
                price = level.get('price', 0)
                ratio = level.get('ratio', 0)
                
                ax.axhline(y=price, color=colors['accent'], linestyle=':', alpha=0.7, linewidth=1.5)
                ax.text(0.98, price, f'{ratio:.1%}: {price:.6f}', 
                       transform=ax.get_yaxis_transform(), fontsize=9, ha='right',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['accent'], alpha=0.7))
            
            # Highlight current price
            ax.axhline(y=current_price, color='red', linewidth=2, alpha=0.9)
            ax.text(0.5, current_price, f'Current: {current_price:.6f}', 
                   transform=ax.get_yaxis_transform(), fontsize=10, ha='center',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.8, color='white'))
            
        except Exception as e:
            print(f"❌ Error adding Fibonacci levels: {e}")

    def _create_fibonacci_statistics_panel(self, ax, fibonacci_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📊 Create Fibonacci statistics panel - FIXED INDENTATION."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Statistics data
            retracement_count = len(fibonacci_data.get('retracement_levels', []))
            extension_count = len(fibonacci_data.get('extension_levels', []))
            trend_direction = fibonacci_data.get('trend_direction', 'UNKNOWN')
            
            # Create statistics text
            stats_text = f"""
FIBONACCI STATISTICS
═══════════════════
Retracement Levels: {retracement_count}
Extension Levels: {extension_count}
Trend Direction: {trend_direction}
Current Price: {current_price:.8f}
            """
            
            ax.text(0.05, 0.95, stats_text.strip(), transform=ax.transAxes, fontsize=12,
                   verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating Fibonacci statistics panel: {e}")

    def _create_fibonacci_insights_panel(self, ax, fibonacci_data: Dict[str, Any], colors: Dict[str, str]):
        """💡 Create Fibonacci insights panel - FIXED INDENTATION."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Insights data
            insights = fibonacci_data.get('insights', [])
            if not insights:
                insights = ["No specific insights available"]
            
            # Create insights text
            insights_text = "FIBONACCI INSIGHTS\n" + "═" * 20 + "\n"
            for i, insight in enumerate(insights[:3], 1):
                insights_text += f"{i}. {insight}\n"
            
            ax.text(0.05, 0.95, insights_text.strip(), transform=ax.transAxes, fontsize=11,
                   verticalalignment='top', fontfamily='sans-serif',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['tertiary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating Fibonacci insights panel: {e}")

    def _add_fibonacci_title_and_annotations(self, fig, coin: str, fibonacci_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📝 Add professional title and annotations - FIXED INDENTATION."""
        try:
            # Main title
            title = f"🌀 FIBONACCI ANALYSIS - {coin}"
            subtitle = f"Price: {current_price:.8f} | Trend: {fibonacci_data.get('trend_direction', 'UNKNOWN')}"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding Fibonacci title: {e}")

    # ============================================================================
    # 📝 MINIMAL CAPTIONS FOR BEAUTIFUL CHARTS
    # ============================================================================

    def _create_minimal_fibonacci_caption(self, coin: str, fibonacci_data: Dict[str, Any], current_price: float) -> str:
        """🌀 Create minimal Fibonacci caption for beautiful chart."""
        try:
            trend = fibonacci_data.get('trend_direction', 'UNKNOWN')
            retracement_levels = fibonacci_data.get('retracement_levels', [])

            # Minimal caption since chart is self-explanatory
            caption = f"""🌀 <b>Fibonacci Analysis - {coin}</b>

💰 <b>Price:</b> <code>{current_price:.8f}</code>
📈 <b>Trend:</b> {trend}
🎯 <b>Key Levels:</b> {len(retracement_levels)} identified

⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>"""

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating minimal Fibonacci caption: {e}")
            return f"🌀 Fibonacci Analysis - {coin}"

    def _create_minimal_volume_profile_caption(self, coin: str, volume_data: Dict[str, Any], current_price: float) -> str:
        """📊 Create minimal Volume Profile caption for beautiful chart."""
        try:
            vpoc = volume_data.get('vpoc', {})
            vpoc_price = vpoc.get('price', 0)

            caption = f"""📊 <b>Volume Profile - {coin}</b>

💰 <b>Price:</b> <code>{current_price:.8f}</code>
🎯 <b>VPOC:</b> <code>{vpoc_price:.8f}</code>
📊 <b>Volume Analysis:</b> Complete

⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>"""

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating minimal Volume Profile caption: {e}")
            return f"📊 Volume Profile - {coin}"

    def _create_minimal_ai_analysis_caption(self, coin: str, ai_data: Dict[str, Any], current_price: float) -> str:
        """🤖 Create minimal AI Analysis caption for beautiful chart."""
        try:
            ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
            ensemble_confidence = ai_data.get('ensemble_confidence', 0)

            caption = f"""🤖 <b>AI Analysis - {coin}</b>

💰 <b>Price:</b> <code>{current_price:.8f}</code>
🎯 <b>Signal:</b> <b>{ensemble_signal}</b>
💪 <b>Confidence:</b> <code>{ensemble_confidence:.1%}</code>

⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>"""

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating minimal AI Analysis caption: {e}")
            return f"🤖 AI Analysis - {coin}"

    def _create_minimal_pump_alert_caption(self, coin: str, pump_data: Dict[str, Any], current_price: float) -> str:
        """🚀 Create minimal Pump Alert caption for beautiful chart."""
        try:
            probability = pump_data.get('pump_probability', 0)
            intensity = pump_data.get('intensity', 0)

            caption = f"""🚀 <b>PUMP ALERT - {coin}</b>

💰 <b>Price:</b> <code>{current_price:.8f}</code>
🚀 <b>Probability:</b> <code>{probability:.1%}</code>
⚡ <b>Intensity:</b> <code>{intensity:.2f}</code>

⚠️ <b>High volatility detected!</b>
⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>"""

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating minimal Pump Alert caption: {e}")
            return f"🚀 Pump Alert - {coin}"

    def _create_minimal_dump_alert_caption(self, coin: str, dump_data: Dict[str, Any], current_price: float) -> str:
        """📉 Create minimal Dump Alert caption for beautiful chart."""
        try:
            probability = dump_data.get('dump_probability', 0)
            severity = dump_data.get('severity_level', 'MEDIUM')

            caption = f"""📉 <b>DUMP ALERT - {coin}</b>

💰 <b>Price:</b> <code>{current_price:.8f}</code>
📉 <b>Probability:</b> <code>{probability:.1%}</code>
⚠️ <b>Severity:</b> <b>{severity}</b>

🛡️ <b>Risk management advised!</b>
⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>"""

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating minimal Dump Alert caption: {e}")
            return f"📉 Dump Alert - {coin}"

    def _create_minimal_consensus_caption(self, coin: str, consensus_data: Dict[str, Any], signal_data: Dict[str, Any]) -> str:
        """🎯 Create minimal Consensus Signal caption for beautiful chart."""
        try:
            signal_type = signal_data.get('signal_type', 'NONE')
            entry = signal_data.get('entry', 0)
            take_profit = signal_data.get('take_profit', 0)
            stop_loss = signal_data.get('stop_loss', 0)

            caption = f"""🎯 <b>CONSENSUS SIGNAL - {coin}</b>

📊 <b>Signal:</b> <b>{signal_type}</b>
💰 <b>Entry:</b> <code>{entry:.8f}</code>
🎯 <b>TP:</b> <code>{take_profit:.8f}</code>
🛡️ <b>SL:</b> <code>{stop_loss:.8f}</code>

⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>"""

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating minimal Consensus Signal caption: {e}")
            return f"🎯 Consensus Signal - {coin}"

    # ============================================================================
    # 📱 TELEGRAM INTEGRATION HELPERS - Đã fix
    # ============================================================================
    def send_chart_photo(self, chart_path: str, caption: str, target_chat: str) -> bool:
        """📊 Send chart photo - compatibility method for chart_generator."""
        return self.send_photo(
            photo_path=chart_path,
            caption=caption,
            chat_id=target_chat,
            parse_mode="HTML"
        )
    
    def _send_chart_to_telegram(self, chart_path: str, caption: str, target_chat: str) -> bool:
        """📱 ENHANCED: Send chart to Telegram với immediate auto-delete feedback."""
        try:
            if not self.telegram_notifier:
                print("❌ No Telegram notifier available")
                return False

            # ✅ FIX: Ensure absolute path
            abs_chart_path = os.path.abspath(chart_path)

            # Check if chart file exists
            if not os.path.exists(abs_chart_path):
                print(f"❌ Chart file not found: {abs_chart_path}")
                # Try original path as fallback
                if os.path.exists(chart_path):
                    abs_chart_path = chart_path
                    print(f"  ✅ Found chart at original path: {chart_path}")
                else:
                    print(f"❌ Chart file not found at either path")
                    return False

            # Get file info before sending
            file_size_mb = os.path.getsize(abs_chart_path) / (1024 * 1024)
            filename = os.path.basename(abs_chart_path)
            print(f"      📤 SENDING: {filename} ({file_size_mb:.2f}MB) → {target_chat}")

            # ✅ ENHANCED: Send with immediate feedback
            success = False
            send_start_time = time.time()

            try:
                if hasattr(self.telegram_notifier, 'send_photo'):
                    success = self.telegram_notifier.send_photo(
                        photo_path=abs_chart_path,
                        caption=caption,
                        chat_id=target_chat,
                        parse_mode="HTML"
                    )
                elif hasattr(self.telegram_notifier, 'send_chart_photo'):
                    success = self.telegram_notifier.send_chart_photo(abs_chart_path, caption, target_chat)
                else:
                    print("      ❌ Telegram notifier doesn't have photo sending capability")
                    return False

                send_duration = time.time() - send_start_time

                # ✅ ENHANCED: Immediate feedback for auto-delete
                if success:
                    self.chart_stats['telegram_sends']['success'] += 1
                    self.chart_stats['telegram_sends']['last_send'] = datetime.now().isoformat()
                    print(f"      ✅ SENT SUCCESSFULLY: {filename} ({file_size_mb:.2f}MB) in {send_duration:.1f}s")
                    print(f"      🗑️ READY FOR AUTO-DELETE: Chart sent successfully")
                else:
                    self.chart_stats['telegram_sends']['failed'] += 1
                    self.chart_stats['total_failed_sends'] += 1
                    print(f"      ❌ SEND FAILED: {filename} - will be handled by cleanup system")

                return success

            except Exception as send_error:
                print(f"      ❌ SEND ERROR: {send_error}")
                self.chart_stats['telegram_sends']['failed'] += 1
                self.chart_stats['total_failed_sends'] += 1
                return False

        except Exception as e:
            print(f"❌ Error in enhanced chart sending: {e}")
            self.chart_stats['telegram_sends']['failed'] += 1
            self.chart_stats['total_failed_sends'] += 1
            return False

    def _create_fibonacci_caption(self, coin: str, fibonacci_data: Dict[str, Any], current_price: float) -> str:
        """🌀 Create detailed Fibonacci chart caption with full analysis report."""
        try:
            # Basic info
            retracement_levels = fibonacci_data.get('retracement_levels', [])
            extension_levels = fibonacci_data.get('extension_levels', [])
            trend = fibonacci_data.get('trend_direction', 'UNKNOWN')
            confluence_zones = fibonacci_data.get('confluence_zones', [])

            # Build detailed report
            caption = f"""🌀 <b>FIBONACCI ANALYSIS REPORT - {coin}</b>

💰 <b>CURRENT MARKET STATUS</b>
├ 💵 Price: <code>{current_price:.8f}</code>
├ 📈 Trend: <b>{trend}</b>
├ 🎯 Retracement Levels: <code>{len(retracement_levels)}</code>
└ 📊 Extension Levels: <code>{len(extension_levels)}</code>

🎯 <b>KEY FIBONACCI LEVELS</b>"""

            # Add retracement levels
            if retracement_levels:
                caption += "\n📉 <b>Retracement Levels:</b>"
                for level in retracement_levels[:5]:  # Top 5 levels
                    ratio = level.get('ratio', 0)
                    price = level.get('price', 0)
                    strength = level.get('strength', 0)
                    distance = abs(current_price - price) / current_price * 100
                    caption += f"\n├ {ratio:.1%}: <code>{price:.8f}</code> (💪{strength:.1f}) [{distance:.1f}%]"

            # Add extension levels
            if extension_levels:
                caption += "\n\n📈 <b>Extension Levels:</b>"
                for level in extension_levels[:3]:  # Top 3 levels
                    ratio = level.get('ratio', 0)
                    price = level.get('price', 0)
                    strength = level.get('strength', 0)
                    distance = abs(current_price - price) / current_price * 100
                    caption += f"\n├ {ratio:.1%}: <code>{price:.8f}</code> (💪{strength:.1f}) [{distance:.1f}%]"

            # Add confluence zones
            if confluence_zones:
                caption += "\n\n🎯 <b>CONFLUENCE ZONES:</b>"
                for zone in confluence_zones[:3]:  # Top 3 zones
                    price = zone.get('price', 0)
                    strength = zone.get('strength', 0)
                    methods = zone.get('methods', [])
                    caption += f"\n├ <code>{price:.8f}</code> (💪{strength}) - {', '.join(methods[:3])}"

            # Add trading signals
            signals = fibonacci_data.get('signals', {})
            if signals:
                primary_signal = signals.get('primary_signal', 'NONE')
                confidence = signals.get('confidence', 0)
                caption += f"\n\n📊 <b>TRADING SIGNALS</b>"
                caption += f"\n├ 🎯 Signal: <b>{primary_signal}</b>"
                caption += f"\n└ 💪 Confidence: <code>{confidence:.1%}</code>"

            caption += f"\n\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"
            caption += f"\n🎨 <i>Enhanced Fibonacci Analysis with Auto-Chart Generation</i>"

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating Fibonacci caption: {e}")
            return f"🌀 Fibonacci Analysis - {coin}"

    def _create_volume_profile_caption(self, coin: str, volume_data: Dict[str, Any], current_price: float) -> str:
        """📊 Create detailed Volume Profile chart caption with full analysis report."""
        try:
            # ✅ NULL CHECK: Handle None volume_data
            if volume_data is None:
                print("⚠️ Volume data is None in caption creation, using fallback")
                volume_data = {}

            # Basic data
            vpoc = volume_data.get('vpoc', {})
            signals = volume_data.get('signals', {})
            volume_profile = volume_data.get('volume_profile', {})
            value_area = volume_data.get('value_area', {})

            # Build detailed report
            caption = f"""📊 <b>VOLUME PROFILE ANALYSIS REPORT - {coin}</b>

💰 <b>CURRENT MARKET STATUS</b>
├ 💵 Price: <code>{current_price:.8f}</code>
├ 🎯 VPOC: <code>{vpoc.get('price', 0):.8f}</code>
├ 📊 VPOC Volume: <code>{vpoc.get('volume', 0):,.0f}</code>
└ 📈 VPOC %: <code>{vpoc.get('percentage_of_total', 0):.1f}%</code>

📊 <b>VOLUME PROFILE METRICS</b>
├ 📈 Total Volume: <code>{volume_profile.get('total_volume', 0):,.0f}</code>
├ 🎯 Value Area High: <code>{value_area.get('high', 0):.8f}</code>
├ 🎯 Value Area Low: <code>{value_area.get('low', 0):.8f}</code>
└ 📊 Value Area %: <code>{value_area.get('percentage', 70):.1f}%</code>"""

            # Add support/resistance levels
            sr_levels = volume_data.get('support_resistance_levels', [])
            if sr_levels:
                caption += "\n\n🛡️ <b>SUPPORT & RESISTANCE LEVELS</b>"
                for i, level in enumerate(sr_levels[:4]):  # Top 4 levels
                    price = level.get('price', 0)
                    level_type = level.get('type', 'unknown')
                    strength = level.get('strength', 0)
                    distance = abs(current_price - price) / current_price * 100
                    icon = "🛡️" if level_type == 'support' else "⚡"
                    caption += f"\n├ {icon} <code>{price:.8f}</code> ({level_type.upper()}) 💪{strength:.1f} [{distance:.1f}%]"

            # Add volume flow analysis
            volume_flow = volume_profile.get('volume_flow', {})
            if volume_flow:
                flow_direction = volume_flow.get('flow_direction', 'neutral')
                flow_strength = volume_flow.get('flow_strength', 0)
                caption += f"\n\n💨 <b>VOLUME FLOW ANALYSIS</b>"
                caption += f"\n├ 🌊 Direction: <b>{flow_direction.upper()}</b>"
                caption += f"\n└ 💪 Strength: <code>{flow_strength:.2f}</code>"

            # Add trading signals
            if signals:
                primary_signal = signals.get('primary_signal', 'NONE')
                confidence = signals.get('confidence', 0)
                recommendation = volume_data.get('recommendation', 'HOLD')
                caption += f"\n\n📊 <b>TRADING SIGNALS</b>"
                caption += f"\n├ 🎯 Signal: <b>{primary_signal}</b>"
                caption += f"\n├ 💪 Confidence: <code>{confidence:.1%}</code>"
                caption += f"\n└ 💡 Recommendation: <b>{recommendation}</b>"

            # Add distance analysis
            vpoc_price = vpoc.get('price', current_price)
            distance_to_vpoc = abs(current_price - vpoc_price) / current_price * 100
            caption += f"\n\n📏 <b>DISTANCE ANALYSIS</b>"
            caption += f"\n└ 🎯 Distance to VPOC: <code>{distance_to_vpoc:.2f}%</code>"

            caption += f"\n\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"
            caption += f"\n🎨 <i>Enhanced Volume Profile Analysis with Auto-Chart Generation</i>"

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating Volume Profile caption: {e}")
            return f"📊 Volume Profile Analysis - {coin}"

    def _create_ai_analysis_caption(self, coin: str, ai_data: Dict[str, Any], current_price: float) -> str:
        """🤖 Create detailed AI Analysis chart caption with full ensemble report."""
        try:
            # Basic AI data
            ensemble_signal = ai_data.get('ensemble_signal', 'NONE')
            ensemble_confidence = ai_data.get('ensemble_confidence', 0)
            model_results = ai_data.get('model_results', {})
            technical_analysis = ai_data.get('technical_analysis', {})

            # Build detailed report
            caption = f"""🤖 <b>AI ENSEMBLE ANALYSIS REPORT - {coin}</b>

💰 <b>CURRENT MARKET STATUS</b>
├ 💵 Price: <code>{current_price:.8f}</code>
├ 🎯 Ensemble Signal: <b>{ensemble_signal}</b>
├ 💪 Ensemble Confidence: <code>{ensemble_confidence:.1%}</code>
└ 🧠 Active Models: <code>{len(model_results)}</code>

🤖 <b>MODEL CONSENSUS BREAKDOWN</b>"""

            # Count predictions
            buy_count = sum(1 for result in model_results.values() if result.get("prediction") == "BUY")
            sell_count = sum(1 for result in model_results.values() if result.get("prediction") == "SELL")
            hold_count = len(model_results) - buy_count - sell_count

            caption += f"\n├ 🟢 BUY Models: <code>{buy_count}</code>"
            caption += f"\n├ 🔴 SELL Models: <code>{sell_count}</code>"
            caption += f"\n└ 🟡 HOLD/NONE Models: <code>{hold_count}</code>"

            # Add top performing models
            if model_results:
                caption += "\n\n🏆 <b>TOP MODEL PREDICTIONS</b>"
                sorted_models = sorted(model_results.items(),
                                     key=lambda x: x[1].get('confidence', 0), reverse=True)
                for i, (model_name, result) in enumerate(sorted_models[:5]):  # Top 5 models
                    prediction = result.get('prediction', 'NONE')
                    confidence = result.get('confidence', 0)
                    icon = "🟢" if prediction == "BUY" else "🔴" if prediction == "SELL" else "🟡"
                    caption += f"\n├ {icon} {model_name}: <b>{prediction}</b> ({confidence:.1%})"

            # Add technical analysis
            if technical_analysis:
                momentum = technical_analysis.get('momentum', 0)
                volatility = technical_analysis.get('volatility', 0)
                trend_strength = technical_analysis.get('trend_strength', 0)
                caption += f"\n\n📊 <b>TECHNICAL METRICS</b>"
                caption += f"\n├ 🚀 Momentum: <code>{momentum:.2f}</code>"
                caption += f"\n├ 📈 Volatility: <code>{volatility:.2f}</code>"
                caption += f"\n└ 💪 Trend Strength: <code>{trend_strength:.2f}</code>"

            # Add trading levels if available
            trading_levels = ai_data.get('trading_levels')
            if not trading_levels:
                ai_prediction = ai_data.get('ai_prediction', {})
                trading_levels = ai_prediction.get('trading_levels')

            if trading_levels:
                entry_price = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)
                caption += f"\n\n💹 <b>AI TRADING LEVELS</b>"
                caption += f"\n├ 🎯 Entry: <code>{entry_price:.8f}</code>"
                if take_profit > 0:
                    caption += f"\n├ 🟢 Take Profit: <code>{take_profit:.8f}</code>"
                if stop_loss > 0:
                    caption += f"\n└ 🔴 Stop Loss: <code>{stop_loss:.8f}</code>"

            # Add market sentiment
            market_sentiment = ai_data.get('market_sentiment', 'NEUTRAL')
            recommendation = ai_data.get('recommendation', 'HOLD')
            caption += f"\n\n🎭 <b>MARKET SENTIMENT</b>"
            caption += f"\n├ 🎭 Sentiment: <b>{market_sentiment}</b>"
            caption += f"\n└ 💡 Recommendation: <b>{recommendation}</b>"

            caption += f"\n\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"
            caption += f"\n🎨 <i>Enhanced AI Ensemble Analysis with Auto-Chart Generation</i>"

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating AI Analysis caption: {e}")
            return f"🤖 AI Analysis - {coin}"

    def _create_pump_alert_caption(self, coin: str, pump_data: Dict[str, Any], current_price: float) -> str:
        """🚀 Create detailed Pump Alert chart caption with full detection report."""
        try:
            # Basic pump data
            probability = pump_data.get('pump_probability', 0)
            intensity = pump_data.get('intensity', 0)
            volume_spike = pump_data.get('volume_spike_factor', 1)
            severity_level = pump_data.get('severity_level', 'MEDIUM')

            # Build detailed report
            caption = f"""🚀 <b>PUMP DETECTION ALERT - {coin}</b>

💰 <b>CURRENT MARKET STATUS</b>
├ 💵 Price: <code>{current_price:.8f}</code>
├ 🚀 Pump Probability: <code>{probability:.1%}</code>
├ ⚡ Intensity Level: <code>{intensity:.2f}</code>
├ 📈 Volume Spike: <code>{volume_spike:.1f}x</code>
└ ⚠️ Severity: <b>{severity_level}</b>"""

            # Add volume analysis
            volume_analysis = pump_data.get('volume_analysis', {})
            if volume_analysis:
                avg_volume = volume_analysis.get('average_volume', 0)
                current_volume = volume_analysis.get('current_volume', 0)
                volume_percentile = volume_analysis.get('volume_percentile', 0)
                caption += f"\n\n📊 <b>VOLUME ANALYSIS</b>"
                caption += f"\n├ 📊 Current Volume: <code>{current_volume:,.0f}</code>"
                caption += f"\n├ 📈 Average Volume: <code>{avg_volume:,.0f}</code>"
                caption += f"\n└ 📊 Volume Percentile: <code>{volume_percentile:.1f}%</code>"

            # Add price movement analysis
            price_movement = pump_data.get('price_movement', {})
            if price_movement:
                price_change_1m = price_movement.get('change_1m', 0)
                price_change_5m = price_movement.get('change_5m', 0)
                price_change_15m = price_movement.get('change_15m', 0)
                caption += f"\n\n📈 <b>PRICE MOVEMENT ANALYSIS</b>"
                caption += f"\n├ 1m: <code>{price_change_1m:+.2f}%</code>"
                caption += f"\n├ 5m: <code>{price_change_5m:+.2f}%</code>"
                caption += f"\n└ 15m: <code>{price_change_15m:+.2f}%</code>"

            # Add pump indicators
            pump_indicators = pump_data.get('pump_indicators', {})
            if pump_indicators:
                rsi_spike = pump_indicators.get('rsi_spike', False)
                macd_bullish = pump_indicators.get('macd_bullish', False)
                breakout_detected = pump_indicators.get('breakout_detected', False)
                caption += f"\n\n🎯 <b>PUMP INDICATORS</b>"
                caption += f"\n├ 📊 RSI Spike: {'✅' if rsi_spike else '❌'}"
                caption += f"\n├ 📈 MACD Bullish: {'✅' if macd_bullish else '❌'}"
                caption += f"\n└ 🚀 Breakout: {'✅' if breakout_detected else '❌'}"

            # Add risk assessment
            risk_level = pump_data.get('risk_level', 'MEDIUM')
            confidence_score = pump_data.get('confidence_score', 0)
            caption += f"\n\n⚠️ <b>RISK ASSESSMENT</b>"
            caption += f"\n├ ⚠️ Risk Level: <b>{risk_level}</b>"
            caption += f"\n└ 💪 Confidence: <code>{confidence_score:.1%}</code>"

            # Add potential targets
            potential_targets = pump_data.get('potential_targets', {})
            if potential_targets:
                target_1 = potential_targets.get('target_1', 0)
                target_2 = potential_targets.get('target_2', 0)
                if target_1 > 0 or target_2 > 0:
                    caption += f"\n\n🎯 <b>POTENTIAL TARGETS</b>"
                    if target_1 > 0:
                        gain_1 = (target_1 - current_price) / current_price * 100
                        caption += f"\n├ 🎯 Target 1: <code>{target_1:.8f}</code> (+{gain_1:.1f}%)"
                    if target_2 > 0:
                        gain_2 = (target_2 - current_price) / current_price * 100
                        caption += f"\n└ 🎯 Target 2: <code>{target_2:.8f}</code> (+{gain_2:.1f}%)"

            caption += f"\n\n⚠️ <b>ENHANCED PUMP DETECTION ALGORITHM</b>"
            caption += f"\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"
            caption += f"\n🎨 <i>Real-time Pump Detection with Auto-Chart Generation</i>"

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating Pump Alert caption: {e}")
            return f"🚀 Pump Alert - {coin}"

    def _create_dump_alert_caption(self, coin: str, dump_data: Dict[str, Any], current_price: float) -> str:
        """📉 Create detailed Dump Alert chart caption with full detection report."""
        try:
            # Basic dump data
            probability = dump_data.get('dump_probability', 0)
            severity = dump_data.get('severity_level', 'MEDIUM')
            support = dump_data.get('next_support', 0)
            intensity = dump_data.get('intensity', 0)

            # Build detailed report
            caption = f"""📉 <b>DUMP DETECTION ALERT - {coin}</b>

💰 <b>CURRENT MARKET STATUS</b>
├ 💵 Price: <code>{current_price:.8f}</code>
├ 📉 Dump Probability: <code>{probability:.1%}</code>
├ ⚠️ Severity Level: <b>{severity}</b>
├ ⚡ Intensity: <code>{intensity:.2f}</code>
└ 🛡️ Next Support: <code>{support:.8f}</code>"""

            # Add volume analysis
            volume_analysis = dump_data.get('volume_analysis', {})
            if volume_analysis:
                sell_volume = volume_analysis.get('sell_volume', 0)
                buy_volume = volume_analysis.get('buy_volume', 0)
                volume_ratio = volume_analysis.get('sell_buy_ratio', 0)
                caption += f"\n\n📊 <b>VOLUME ANALYSIS</b>"
                caption += f"\n├ 🔴 Sell Volume: <code>{sell_volume:,.0f}</code>"
                caption += f"\n├ 🟢 Buy Volume: <code>{buy_volume:,.0f}</code>"
                caption += f"\n└ ⚖️ Sell/Buy Ratio: <code>{volume_ratio:.2f}</code>"

            # Add price decline analysis
            price_decline = dump_data.get('price_decline', {})
            if price_decline:
                decline_1m = price_decline.get('decline_1m', 0)
                decline_5m = price_decline.get('decline_5m', 0)
                decline_15m = price_decline.get('decline_15m', 0)
                caption += f"\n\n📉 <b>PRICE DECLINE ANALYSIS</b>"
                caption += f"\n├ 1m: <code>{decline_1m:.2f}%</code>"
                caption += f"\n├ 5m: <code>{decline_5m:.2f}%</code>"
                caption += f"\n└ 15m: <code>{decline_15m:.2f}%</code>"

            # Add dump indicators
            dump_indicators = dump_data.get('dump_indicators', {})
            if dump_indicators:
                rsi_oversold = dump_indicators.get('rsi_oversold', False)
                macd_bearish = dump_indicators.get('macd_bearish', False)
                breakdown_detected = dump_indicators.get('breakdown_detected', False)
                whale_selling = dump_indicators.get('whale_selling', False)
                caption += f"\n\n🎯 <b>DUMP INDICATORS</b>"
                caption += f"\n├ 📊 RSI Oversold: {'✅' if rsi_oversold else '❌'}"
                caption += f"\n├ 📉 MACD Bearish: {'✅' if macd_bearish else '❌'}"
                caption += f"\n├ 💥 Breakdown: {'✅' if breakdown_detected else '❌'}"
                caption += f"\n└ 🐋 Whale Selling: {'✅' if whale_selling else '❌'}"

            # Add support levels
            support_levels = dump_data.get('support_levels', [])
            if support_levels:
                caption += f"\n\n🛡️ <b>SUPPORT LEVELS</b>"
                for i, level in enumerate(support_levels[:3]):  # Top 3 support levels
                    if isinstance(level, dict):
                        price = level.get('price', 0)
                        strength = level.get('strength', 0)
                    else:
                        price = level
                        strength = 0

                    distance = abs(current_price - price) / current_price * 100
                    caption += f"\n├ 🛡️ S{i+1}: <code>{price:.8f}</code> (💪{strength:.1f}) [-{distance:.1f}%]"

            # Add risk assessment
            risk_level = dump_data.get('risk_level', 'HIGH')
            confidence_score = dump_data.get('confidence_score', 0)
            caption += f"\n\n⚠️ <b>RISK ASSESSMENT</b>"
            caption += f"\n├ ⚠️ Risk Level: <b>{risk_level}</b>"
            caption += f"\n└ 💪 Confidence: <code>{confidence_score:.1%}</code>"

            # Add potential bounce targets
            bounce_targets = dump_data.get('bounce_targets', {})
            if bounce_targets:
                target_1 = bounce_targets.get('target_1', 0)
                target_2 = bounce_targets.get('target_2', 0)
                if target_1 > 0 or target_2 > 0:
                    caption += f"\n\n🎯 <b>POTENTIAL BOUNCE TARGETS</b>"
                    if target_1 > 0:
                        bounce_1 = (target_1 - current_price) / current_price * 100
                        caption += f"\n├ 🎯 Bounce 1: <code>{target_1:.8f}</code> (+{bounce_1:.1f}%)"
                    if target_2 > 0:
                        bounce_2 = (target_2 - current_price) / current_price * 100
                        caption += f"\n└ 🎯 Bounce 2: <code>{target_2:.8f}</code> (+{bounce_2:.1f}%)"

            caption += f"\n\n⚠️ <b>ENHANCED DUMP DETECTION ALGORITHM</b>"
            caption += f"\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"
            caption += f"\n🎨 <i>Real-time Dump Detection with Auto-Chart Generation</i>"

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating Dump Alert caption: {e}")
            return f"📉 Dump Alert - {coin}"

    def _create_consensus_signal_caption(self, coin: str, consensus_data: Dict[str, Any], signal_data: Dict[str, Any]) -> str:
        """🎯 Create detailed Consensus Signal chart caption with full analysis report."""
        try:
            # Basic signal data
            signal_type = signal_data.get('signal_type', 'NONE')
            entry = signal_data.get('entry', 0)
            take_profit = signal_data.get('take_profit', 0)
            stop_loss = signal_data.get('stop_loss', 0)
            risk_reward = signal_data.get('risk_reward_ratio', 0)
            consensus_score = consensus_data.get('consensus_score', 0)

            # Build detailed report
            caption = f"""🎯 <b>CONSENSUS SIGNAL REPORT - {coin}</b>

📊 <b>TRADING SIGNAL</b>
├ 🎯 Signal Type: <b>{signal_type}</b>
├ 💰 Entry Price: <code>{entry:.8f}</code>
├ 🟢 Take Profit: <code>{take_profit:.8f}</code>
├ 🔴 Stop Loss: <code>{stop_loss:.8f}</code>
├ 📈 Risk/Reward: <code>{risk_reward:.2f}</code>
└ 💪 Consensus Score: <code>{consensus_score:.3f}</code>"""

            # Add algorithm contributions
            algorithm_scores = consensus_data.get('algorithm_scores', {})
            if algorithm_scores:
                caption += f"\n\n🧠 <b>ALGORITHM CONTRIBUTIONS</b>"
                sorted_algos = sorted(algorithm_scores.items(), key=lambda x: x[1], reverse=True)
                for algo, score in sorted_algos[:6]:  # Top 6 algorithms
                    algo_name = algo.replace('_', ' ').title()
                    caption += f"\n├ {algo_name}: <code>{score:.3f}</code>"

            # Add confidence breakdown
            confidence_breakdown = consensus_data.get('confidence_breakdown', {})
            if confidence_breakdown:
                ai_confidence = confidence_breakdown.get('ai_models', 0)
                technical_confidence = confidence_breakdown.get('technical_analysis', 0)
                volume_confidence = confidence_breakdown.get('volume_analysis', 0)
                caption += f"\n\n💪 <b>CONFIDENCE BREAKDOWN</b>"
                caption += f"\n├ 🤖 AI Models: <code>{ai_confidence:.1%}</code>"
                caption += f"\n├ 📊 Technical Analysis: <code>{technical_confidence:.1%}</code>"
                caption += f"\n└ 📈 Volume Analysis: <code>{volume_confidence:.1%}</code>"

            # Add supporting indicators
            supporting_indicators = consensus_data.get('supporting_indicators', [])
            if supporting_indicators:
                caption += f"\n\n🎯 <b>SUPPORTING INDICATORS</b>"
                for indicator in supporting_indicators[:5]:  # Top 5 indicators
                    name = indicator.get('name', 'Unknown')
                    signal = indicator.get('signal', 'NONE')
                    strength = indicator.get('strength', 0)
                    icon = "🟢" if signal == "BUY" else "🔴" if signal == "SELL" else "🟡"
                    caption += f"\n├ {icon} {name}: <b>{signal}</b> (💪{strength:.1f})"

            # Add market conditions
            market_conditions = consensus_data.get('market_conditions', {})
            if market_conditions:
                trend = market_conditions.get('trend', 'UNKNOWN')
                volatility = market_conditions.get('volatility', 'MEDIUM')
                volume_profile = market_conditions.get('volume_profile', 'NORMAL')
                caption += f"\n\n🌊 <b>MARKET CONDITIONS</b>"
                caption += f"\n├ 📈 Trend: <b>{trend}</b>"
                caption += f"\n├ 📊 Volatility: <b>{volatility}</b>"
                caption += f"\n└ 📈 Volume Profile: <b>{volume_profile}</b>"

            # Add profit/loss projections
            projections = signal_data.get('projections', {})
            if projections:
                win_probability = projections.get('win_probability', 0)
                expected_return = projections.get('expected_return', 0)
                max_drawdown = projections.get('max_drawdown', 0)
                caption += f"\n\n📊 <b>PROJECTIONS</b>"
                caption += f"\n├ 🎯 Win Probability: <code>{win_probability:.1%}</code>"
                caption += f"\n├ 💰 Expected Return: <code>{expected_return:.1%}</code>"
                caption += f"\n└ 📉 Max Drawdown: <code>{max_drawdown:.1%}</code>"

            # Add time frame analysis
            timeframe_analysis = consensus_data.get('timeframe_analysis', {})
            if timeframe_analysis:
                short_term = timeframe_analysis.get('short_term', 'NEUTRAL')
                medium_term = timeframe_analysis.get('medium_term', 'NEUTRAL')
                long_term = timeframe_analysis.get('long_term', 'NEUTRAL')
                caption += f"\n\n⏰ <b>TIMEFRAME ANALYSIS</b>"
                caption += f"\n├ 🕐 Short Term: <b>{short_term}</b>"
                caption += f"\n├ 🕕 Medium Term: <b>{medium_term}</b>"
                caption += f"\n└ 🕘 Long Term: <b>{long_term}</b>"

            caption += f"\n\n🎯 <b>ENHANCED CONSENSUS ANALYSIS</b>"
            caption += f"\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"
            caption += f"\n🎨 <i>Multi-Algorithm Consensus with Auto-Chart Generation</i>"

            return caption.strip()

        except Exception as e:
            print(f"❌ Error creating Consensus Signal caption: {e}")
            return f"🎯 Consensus Signal - {coin}"
        
    def generate_point_figure_chart(self, coin: str, point_figure_data: Dict[str, Any],
                               ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """📈 Generate CLEAN Point & Figure chart - Only candlesticks + P&F levels."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        return self.generate_clean_point_figure_chart(coin, point_figure_data, ohlcv_data, current_price)

    def _add_point_figure_levels(self, ax, point_figure_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """📈 Add Point & Figure levels to chart."""
        try:
            # Get P&F data
            trend_analysis = point_figure_data.get('trend_analysis', {})
            current_trend = trend_analysis.get('trend', 'NEUTRAL')
            
            # Get support/resistance levels
            support_resistance = point_figure_data.get('support_resistance', {})
            support_levels = support_resistance.get('support_levels', [])
            resistance_levels = support_resistance.get('resistance_levels', [])
            
            # Draw support levels
            for i, level in enumerate(support_levels[:5]):
                if isinstance(level, dict):
                    price = level.get('price', 0)
                else:
                    price = level
                
                if price > 0:
                    ax.axhline(y=price, color=colors['secondary'], linestyle='-', alpha=0.7, linewidth=1.5)
                    ax.text(0.02, price, f'S{i+1}: {price:.6f}', 
                        transform=ax.get_yaxis_transform(), fontsize=9,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['secondary'], alpha=0.7))
            
            # Draw resistance levels
            for i, level in enumerate(resistance_levels[:5]):
                if isinstance(level, dict):
                    price = level.get('price', 0)
                else:
                    price = level
                
                if price > 0:
                    ax.axhline(y=price, color=colors['accent'], linestyle='--', alpha=0.7, linewidth=1.5)
                    ax.text(0.98, price, f'R{i+1}: {price:.6f}', 
                        transform=ax.get_yaxis_transform(), fontsize=9, ha='right',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['accent'], alpha=0.7))
            
            # Get price targets
            price_targets = point_figure_data.get('price_targets', {})
            bullish_target = price_targets.get('bullish_target', 0)
            bearish_target = price_targets.get('bearish_target', 0)
            
            # Draw price targets
            if bullish_target > 0:
                ax.axhline(y=bullish_target, color='green', linestyle=':', alpha=0.8, linewidth=2)
                ax.text(0.5, bullish_target, f'Bull Target: {bullish_target:.6f}', 
                    transform=ax.get_yaxis_transform(), fontsize=10, ha='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='green', alpha=0.8, color='white'))
            
            if bearish_target > 0:
                ax.axhline(y=bearish_target, color='red', linestyle=':', alpha=0.8, linewidth=2)
                ax.text(0.5, bearish_target, f'Bear Target: {bearish_target:.6f}', 
                    transform=ax.get_yaxis_transform(), fontsize=10, ha='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.8, color='white'))
            
            # Highlight current price
            ax.axhline(y=current_price, color='blue', linewidth=2, alpha=0.9)
            ax.text(0.5, current_price, f'Current: {current_price:.6f}', 
                transform=ax.get_yaxis_transform(), fontsize=10, ha='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='blue', alpha=0.8, color='white'))
            
            # Add trend annotation
            trend_color = colors['secondary'] if current_trend == 'UPTREND' else colors['accent'] if current_trend == 'DOWNTREND' else colors['primary']
            ax.text(0.02, 0.98, f'Trend: {current_trend}', 
                transform=ax.transAxes, fontsize=12, va='top', fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=trend_color, alpha=0.7))
            
        except Exception as e:
            print(f"❌ Error adding Point & Figure levels: {e}")

    def _create_point_figure_patterns_panel(self, ax, point_figure_data: Dict[str, Any], colors: Dict[str, str]):
        """🎭 Create Point & Figure patterns panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get pattern analysis
            pattern_analysis = point_figure_data.get('pattern_analysis', {})
            patterns = pattern_analysis.get('patterns', [])
            
            # Create patterns text
            patterns_text = f"POINT & FIGURE PATTERNS ({len(patterns)})\n" + "═" * 35 + "\n"
            
            if patterns:
                for i, pattern in enumerate(patterns[:4]):
                    pattern_name = pattern.get('name', 'Unknown Pattern')
                    pattern_strength = pattern.get('strength', 0)
                    patterns_text += f"{i+1}. {pattern_name}\n"
                    patterns_text += f"   Strength: {pattern_strength:.2f}\n"
            else:
                patterns_text += "No significant patterns detected\n"
                patterns_text += "Chart formation in progress..."
            
            ax.text(0.05, 0.95, patterns_text.strip(), transform=ax.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['primary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating P&F patterns panel: {e}")

    def _create_point_figure_trend_panel(self, ax, point_figure_data: Dict[str, Any], colors: Dict[str, str]):
        """📊 Create Point & Figure trend panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get trend analysis
            trend_analysis = point_figure_data.get('trend_analysis', {})
            current_trend = trend_analysis.get('trend', 'UNKNOWN')
            trend_strength = trend_analysis.get('strength', 0)
            trend_duration = trend_analysis.get('duration', 0)
            trend_reliability = trend_analysis.get('reliability', 'UNKNOWN')
            
            # Create trend text
            trend_text = f"""
    TREND ANALYSIS
    ═════════════
    Current Trend: {current_trend}
    Trend Strength: {trend_strength:.2f}/10
    Duration: {trend_duration} periods
    Reliability: {trend_reliability}
            """
            
            ax.text(0.05, 0.95, trend_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['secondary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating P&F trend panel: {e}")

    def _create_point_figure_signals_panel(self, ax, point_figure_data: Dict[str, Any], current_price: float, colors: Dict[str, str]):
        """🎯 Create Point & Figure signals panel."""
        try:
            ax.clear()
            ax.set_facecolor(colors['background'])
            ax.axis('off')
            
            # Get signals data
            signals = point_figure_data.get('signals', {})
            primary_signal = signals.get('primary_signal', 'NONE')
            signal_confidence = signals.get('confidence', 0)
            
            # Get breakout and reversal signals
            breakout_signal = point_figure_data.get('breakout_analysis', {}).get('signal', 'NONE')
            reversal_signal = point_figure_data.get('reversal_analysis', {}).get('signal', 'NONE')
            
            # Create signals text
            signals_text = f"""
    POINT & FIGURE SIGNALS
    ════════════════════
    Primary Signal: {primary_signal}
    Confidence: {signal_confidence:.1%}
    Breakout Signal: {breakout_signal}
    Reversal Signal: {reversal_signal}

    P&F Configuration:
    Box Size: {point_figure_data.get('box_size', 0):.6f}
    Reversal Amount: {point_figure_data.get('reversal_amount', 3)} boxes
            """
            
            ax.text(0.05, 0.95, signals_text.strip(), transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor=colors['tertiary'], alpha=0.1))
            
        except Exception as e:
            print(f"❌ Error creating P&F signals panel: {e}")

    def _add_point_figure_title_and_annotations(self, fig, coin: str, point_figure_data: Dict[str, Any], 
                                            current_price: float, colors: Dict[str, str]):
        """📝 Add Point & Figure title and annotations."""
        try:
            # Get signal data
            signals = point_figure_data.get('signals', {})
            primary_signal = signals.get('primary_signal', 'NONE')
            signal_confidence = signals.get('confidence', 0)
            
            # Get trend data
            trend_analysis = point_figure_data.get('trend_analysis', {})
            current_trend = trend_analysis.get('trend', 'UNKNOWN')
            
            # Main title
            title = f"📈 POINT & FIGURE ANALYSIS - {coin}"
            subtitle = f"Price: {current_price:.8f} | Signal: {primary_signal} | Trend: {current_trend}"
            
            fig.suptitle(title, fontsize=18, fontweight='bold', color=colors['text'])
            fig.text(0.5, 0.92, subtitle, ha='center', fontsize=12, color=colors['text'])
            
            # Configuration info
            box_size = point_figure_data.get('box_size', 0)
            reversal_amount = point_figure_data.get('reversal_amount', 3)
            fig.text(0.5, 0.89, f"Box Size: {box_size:.6f} | Reversal: {reversal_amount} boxes | Confidence: {signal_confidence:.1%}", 
                    ha='center', fontsize=10, color=colors['text'], alpha=0.8)
            
            # Timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
            fig.text(0.99, 0.01, f"Generated: {timestamp}", ha='right', fontsize=8, 
                    color=colors['text'], alpha=0.7)
            
        except Exception as e:
            print(f"❌ Error adding P&F title: {e}")

    def generate_and_send_point_figure_chart(self, coin: str, point_figure_data: Dict[str, Any], 
                                            ohlcv_data: pd.DataFrame, current_price: float,
                                            target_chat: str = None) -> Optional[str]:
        """📈 Generate AND send Point & Figure chart + Auto-Delete."""
        try:
            print(f"📈 Generating and sending Point & Figure chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart
            chart_path = self.generate_point_figure_chart(coin, point_figure_data, ohlcv_data, current_price)
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate Point & Figure chart for {coin}")
                return None
            
            # Track creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)

            
            print(f"✅ Chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            
            

            
            # Track chart creation for cleanup

            
            self._track_chart_creation(chart_path)

            
            

            
            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending Point & Figure chart: {e}")
            traceback.print_exc()
            return None

    def _create_point_figure_caption(self, coin: str, point_figure_data: Dict[str, Any], current_price: float) -> str:
        """📈 Create Point & Figure chart caption."""
        try:
            signals = point_figure_data.get('signals', {})
            primary_signal = signals.get('primary_signal', 'NONE')
            signal_confidence = signals.get('confidence', 0)
            
            trend_analysis = point_figure_data.get('trend_analysis', {})
            current_trend = trend_analysis.get('trend', 'UNKNOWN')
            
            caption = f"""
    📈 <b>POINT & FIGURE ANALYSIS - {coin}</b>

    💰 <b>Current Price:</b> <code>{current_price:.8f}</code>
    🎯 <b>Signal:</b> <b>{primary_signal}</b>
    💪 <b>Confidence:</b> <code>{signal_confidence:.1%}</code>
    📊 <b>Trend:</b> <b>{current_trend}</b>

    📊 <b>P&F Configuration:</b>
    ├ 📦 Box Size: <code>{point_figure_data.get('box_size', 0):.6f}</code>
    └ 🔄 Reversal: <code>{point_figure_data.get('reversal_amount', 3)} boxes</code>

    ⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
            """
            return caption.strip()
            
        except Exception as e:
            print(f"❌ Error creating Point & Figure caption: {e}")
            return f"📈 Point & Figure Analysis - {coin}"

    def generate_fourier_chart(self, coin: str, fourier_data: Dict[str, Any],
                          ohlcv_data: pd.DataFrame, current_price: float) -> Optional[str]:
        """🌊 Generate CLEAN Fourier chart - Only candlesticks + Fourier lines."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        return self.generate_clean_fourier_chart(coin, fourier_data, ohlcv_data, current_price)

    def generate_and_send_fourier_chart(self, coin: str, fourier_data: Dict[str, Any], 
                                    ohlcv_data: pd.DataFrame, current_price: float,
                                    target_chat: str = None) -> Optional[str]:
        """🌊 Generate AND send Fourier chart + Auto-Delete."""
        try:
            print(f"🌊 Generating and sending Fourier chart for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart
            chart_path = self.generate_fourier_chart(coin, fourier_data, ohlcv_data, current_price)
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate Fourier chart for {coin}")
                return None
            
            # Track creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)

            
            print(f"✅ Chart generated successfully (manual send only): {os.path.basename(chart_path)}")

            
            

            
            # Track chart creation for cleanup

            
            self._track_chart_creation(chart_path)

            
            

            
            return chart_path
            
        except Exception as e:
            print(f"❌ Error generating and sending Fourier chart: {e}")
            return None

    def _create_fourier_caption(self, coin: str, fourier_data: Dict[str, Any], current_price: float) -> str:
        """🌊 Create Fourier chart caption."""
        try:
            signals = fourier_data.get('signals', {})
            overall_signal = signals.get('overall_signal', 'NEUTRAL')
            confidence = signals.get('confidence', 0)
            dominant_cycle = fourier_data.get('dominant_cycle', 0)
            
            caption = f"""
    🌊 <b>FOURIER ANALYSIS - {coin}</b>

    💰 <b>Current Price:</b> <code>{current_price:.8f}</code>
    🎯 <b>Signal:</b> <b>{overall_signal}</b>
    💪 <b>Confidence:</b> <code>{confidence:.1%}</code>
    🔄 <b>Dominant Cycle:</b> <code>{dominant_cycle:.1f} periods</code>

    📊 <b>Frequency Analysis:</b>
    ├ 🌊 Price Cycles: <code>{len(fourier_data.get('price_cycles', []))}</code>
    └ 📊 Volume Cycles: <code>{len(fourier_data.get('volume_cycles', []))}</code>

    ⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
            """
            return caption.strip()
            
        except Exception as e:
            print(f"❌ Error creating Fourier caption: {e}")
            return f"🌊 Fourier Analysis - {coin}"

    # ============================================================================
    # 📊 LOGGING AND STATISTICS - Đã fix
    # ============================================================================
    def generate_and_send_with_detailed_report(self, chart_type: str, coin: str, data: Dict[str, Any], 
                                         ohlcv_data: pd.DataFrame, current_price: float,
                                         target_chat: str = None) -> bool:
        """📊 Generate chart và send KÈM báo cáo chi tiết trong caption."""
        try:
            print(f"📊 Generating {chart_type} chart with embedded report for {coin}...")
            
            # Check storage limit
            self._check_storage_limit()
            
            # Generate chart based on type
            chart_path = None
            detailed_caption = ""
            
            if chart_type == "fibonacci":
                chart_path = self.generate_fibonacci_chart(coin, data, ohlcv_data, current_price)
                detailed_caption = self._create_comprehensive_fibonacci_caption(coin, data, current_price)
                # ✅ FIX: Use .env value instead of hardcode
                target = target_chat or os.getenv("TELEGRAM_FIBONACCI_ZIGZAG_FOURIER", "-1002395637657")

            elif chart_type == "volume_profile":
                chart_path = self.generate_volume_profile_chart(coin, data, ohlcv_data, current_price)
                detailed_caption = self._create_comprehensive_volume_profile_caption(coin, data, current_price)
                # ✅ FIX: Use .env value instead of hardcode
                target = target_chat or os.getenv("TELEGRAM_VOLUME_PROFILE_POINT_FIGURE", "-1002395637657")

            elif chart_type == "ai_analysis":
                chart_path = self.generate_ai_analysis_chart(coin, data, ohlcv_data, current_price)
                detailed_caption = self._create_comprehensive_ai_analysis_caption(coin, data, current_price)
                # ✅ FIX: Use .env value instead of hardcode
                target = target_chat or os.getenv("TELEGRAM_AI_ANALYSIS", "-1002395637657")

            elif chart_type == "pump_alert":
                chart_path = self.generate_pump_alert_chart(coin, data, ohlcv_data, current_price)
                detailed_caption = self._create_comprehensive_pump_caption(coin, data, current_price)
                # ✅ FIX: Use .env value instead of hardcode
                target = target_chat or os.getenv("TELEGRAM_PUMP_DETECTION", "-1002301937119")

            elif chart_type == "dump_alert":
                chart_path = self.generate_dump_alert_chart(coin, data, ohlcv_data, current_price)
                detailed_caption = self._create_comprehensive_dump_caption(coin, data, current_price)
                # ✅ FIX: Use .env value instead of hardcode
                target = target_chat or os.getenv("TELEGRAM_DUMP_DETECTION", "-1002301937119")

            elif chart_type == "consensus":
                chart_path = self.generate_consensus_chart(coin, data.get("consensus_data", {}),
                                                        data.get("signal_data", {}), ohlcv_data)
                detailed_caption = self._create_comprehensive_consensus_caption(coin,
                                                                            data.get("consensus_data", {}),
                                                                            data.get("signal_data", {}))
                # ✅ FIX: Use .env value instead of hardcode
                target = target_chat or os.getenv("TELEGRAM_CONSENSUS_SIGNALS", "-1002301937119")
                
            else:
                print(f"❌ Unknown chart type: {chart_type}")
                return False
            
            if not chart_path or not os.path.exists(chart_path):
                print(f"❌ Failed to generate {chart_type} chart for {coin}")
                return False
            
            # Track chart creation
            self._track_chart_creation(chart_path)
            
            # ✅ FIX: No auto-send - return chart path only (prevents duplicates)
            print(f"✅ {chart_type} chart with comprehensive report generated successfully (manual send only): {os.path.basename(chart_path)}")

            # Track chart creation for cleanup
            self._track_chart_creation(chart_path)

            # Return chart path instead of send_success
            return chart_path
            
        except Exception as e:
            print(f"❌ Error in generate_and_send_with_detailed_report: {e}")
            traceback.print_exc()
            return False
    
    def _send_chart_with_detailed_caption(self, chart_path: str, caption: str, target_chat: str) -> bool:
        """📸 Send chart với detailed caption."""
        try:
            if not self.telegram_notifier:
                print("❌ No Telegram notifier available")
                return False
            
            # Check file exists
            if not os.path.exists(chart_path):
                print(f"❌ Chart file not found: {chart_path}")
                return False
            
            file_size_mb = os.path.getsize(chart_path) / (1024 * 1024)
            print(f"      📤 Sending chart with embedded report: {os.path.basename(chart_path)} ({file_size_mb:.2f}MB)")
            
            # Send photo with detailed caption
            success = self.telegram_notifier.send_photo(
                photo_path=chart_path,
                caption=caption,
                chat_id=target_chat,
                parse_mode="HTML"
            )
            
            if success:
                self.chart_stats['telegram_sends']['success'] += 1
                self.chart_stats['telegram_sends']['last_send'] = datetime.now().isoformat()
                print(f"      ✅ Chart with embedded report sent successfully ({file_size_mb:.2f}MB)")
            else:
                self.chart_stats['telegram_sends']['failed'] += 1
                self.chart_stats['total_failed_sends'] += 1
                print(f"      ❌ Chart with embedded report send failed")
            
            return success
            
        except Exception as e:
            print(f"❌ Error sending chart with detailed caption: {e}")
            return False

    def _log_chart_generation(self, chart_type: str, coin: str, success: bool):
        """📊 Log chart generation statistics."""
        try:
            self.chart_stats['total_generated'] += 1
            
            if chart_type not in self.chart_stats['by_type']:
                self.chart_stats['by_type'][chart_type] = {'success': 0, 'failed': 0}
            
            if coin not in self.chart_stats['by_coin']:
                self.chart_stats['by_coin'][coin] = {'success': 0, 'failed': 0}
            
            if success:
                self.chart_stats['by_type'][chart_type]['success'] += 1
                self.chart_stats['by_coin'][coin]['success'] += 1
            else:
                self.chart_stats['by_type'][chart_type]['failed'] += 1
                self.chart_stats['by_coin'][coin]['failed'] += 1
                self.chart_stats['failures'].append({
                    'timestamp': datetime.now().isoformat(),
                    'chart_type': chart_type,
                    'coin': coin
                })
        except Exception as e:
            print(f"❌ Error logging chart generation: {e}")

    def _log_telegram_send_success(self, chart_type: str, coin: str):
        """✅ Log successful Telegram send."""
        try:
            self.chart_stats['total_sent'] += 1
            print(f"✅ Chart sent successfully: {chart_type} for {coin}")
        except Exception as e:
            print(f"❌ Error logging Telegram success: {e}")

    def _log_telegram_send_failure(self, chart_type: str, coin: str):
        """❌ Log failed Telegram send."""
        try:
            print(f"❌ Chart send failed: {chart_type} for {coin}")
        except Exception as e:
            print(f"❌ Error logging Telegram failure: {e}")

    def get_chart_statistics(self) -> Dict[str, Any]:
        """📊 Get comprehensive chart generation statistics."""
        try:
            return {
                "summary": {
                    "total_generated": self.chart_stats['total_generated'],
                    "total_sent": self.chart_stats['total_sent'],
                    "success_rate": (self.chart_stats['total_sent'] / max(self.chart_stats['total_generated'], 1)) * 100
                },
                "by_type": self.chart_stats['by_type'],
                "by_coin": self.chart_stats['by_coin'],
                "telegram_stats": self.chart_stats['telegram_sends'],
                "recent_failures": self.chart_stats['failures'][-10:],  # Last 10 failures
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ Error getting chart statistics: {e}")
            return {"error": str(e)}

    def cleanup_old_charts(self, hours: int = 24) -> int:
        """🧹 Enhanced cleanup of old chart files + Auto-Delete system."""
        try:
            print(f"🧹 Starting enhanced cleanup (older than {hours} hours) + Auto-Delete maintenance...")
            
            cleanup_count = 0
            space_saved = 0
            
            # 1. Cleanup expired charts
            expired_count = self._cleanup_expired_charts()
            cleanup_count += expired_count
            
            # 2. Traditional time-based cleanup
            cleanup_threshold = time.time() - (hours * 3600)
            
            if os.path.exists(self.output_dir):
                for filename in os.listdir(self.output_dir):
                    file_path = os.path.join(self.output_dir, filename)
                    
                    if os.path.isfile(file_path) and filename.endswith('.png'):
                        try:
                            if os.path.getmtime(file_path) < cleanup_threshold:
                                file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                                os.remove(file_path)
                                cleanup_count += 1
                                space_saved += file_size_mb
                                
                                # Remove from tracking
                                if file_path in self.chart_creation_times:
                                    del self.chart_creation_times[file_path]
                                
                        except Exception as cleanup_error:
                            print(f"❌ Error cleaning {file_path}: {cleanup_error}")
            
            # 3. Check storage limit and cleanup if needed
            self._check_storage_limit()
            
            # Update statistics
            if cleanup_count > 0:
                self.chart_stats['auto_delete']['space_saved_mb'] += space_saved
                self.chart_stats['auto_delete']['last_cleanup'] = datetime.now().isoformat()
                
                print(f"✅ Enhanced cleanup completed: {cleanup_count} files, {space_saved:.2f}MB saved")
            else:
                print(f"✅ No charts to cleanup")
            
            return cleanup_count
            
        except Exception as e:
            print(f"❌ Error during enhanced cleanup: {e}")
            return 0
        
    def get_enhanced_chart_statistics(self) -> Dict[str, Any]:
        """📊 Get comprehensive chart generation statistics với Auto-Delete metrics."""
        try:
            storage_stats = self._get_storage_stats()
            
            enhanced_stats = {
                "summary": {
                    "total_generated": self.chart_stats['total_generated'],
                    "total_sent": self.chart_stats['total_sent'],
                    "total_deleted": self.chart_stats['total_deleted'],  # ✅ NEW
                    "total_failed_sends": self.chart_stats['total_failed_sends'],  # ✅ NEW
                    "send_success_rate": (self.chart_stats['total_sent'] / max(self.chart_stats['total_generated'], 1)) * 100,
                    "auto_delete_rate": (self.chart_stats['total_deleted'] / max(self.chart_stats['total_generated'], 1)) * 100  # ✅ NEW
                },
                "auto_delete_metrics": self.chart_stats['auto_delete'].copy(),  # ✅ NEW
                "storage_info": storage_stats,  # ✅ NEW
                "by_type": self.chart_stats['by_type'],
                "by_coin": self.chart_stats['by_coin'],
                "telegram_stats": self.chart_stats['telegram_sends'],
                "recent_failures": self.chart_stats['failures'][-10:],
                "configuration": {  # ✅ NEW
                    "auto_delete_after_send": self.auto_delete_after_send,
                    "auto_delete_failed_charts": self.auto_delete_failed_charts,
                    "keep_charts_minutes": self.keep_charts_minutes,
                    "max_chart_storage_mb": self.max_chart_storage_mb
                },
                "timestamp": datetime.now().isoformat()
            }
            
            return enhanced_stats
            
        except Exception as e:
            print(f"❌ Error getting enhanced chart statistics: {e}")
            return {"error": str(e)}
    
    def force_cleanup_all_charts(self) -> Dict[str, Any]:
        """🗑️ Force cleanup all charts (emergency cleanup)."""
        try:
            print("🚨 FORCE CLEANUP: Deleting all charts...")
            
            deleted_count = 0
            space_freed = 0
            errors = []
            
            if os.path.exists(self.output_dir):
                for filename in os.listdir(self.output_dir):
                    if filename.endswith('.png'):
                        file_path = os.path.join(self.output_dir, filename)
                        try:
                            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                            os.remove(file_path)
                            deleted_count += 1
                            space_freed += file_size_mb
                        except Exception as e:
                            errors.append(f"{filename}: {str(e)}")
            
            # Clear all tracking
            self.chart_creation_times.clear()
            self.sent_charts.clear()
            self.failed_charts.clear()
            
            # Update statistics
            self.chart_stats['auto_delete']['space_saved_mb'] += space_freed
            
            cleanup_result = {
                "deleted_count": deleted_count,
                "space_freed_mb": space_freed,
                "errors": errors,
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"✅ Force cleanup completed: {deleted_count} files, {space_freed:.2f}MB freed")
            
            return cleanup_result
            
        except Exception as e:
            print(f"❌ Error in force cleanup: {e}")
            return {"error": str(e)}

    # ============================================================================
    # 🚀📉 PUMP/DUMP DETECTION CHARTS - NEW METHODS
    # ============================================================================

    def generate_pump_dump_chart(self, coin: str, pump_dump_data: Dict[str, Any],
                                ohlcv_data, current_price: float, chart_type: str = "pump") -> str:
        """🚀📉 Generate CLEAN pump/dump detection chart - Redirect to clean generators."""
        try:
            print(f"🚀📉 Redirecting {chart_type} detection chart to clean generator for {coin}...")

            # ✅ Redirect to appropriate clean chart generator
            if chart_type == "pump":
                result = self.generate_clean_pump_alert_chart(coin, pump_dump_data, ohlcv_data, current_price)
            else:  # dump
                result = self.generate_clean_dump_alert_chart(coin, pump_dump_data, ohlcv_data, current_price)

            return result if result else ""

        except Exception as e:
            print(f"❌ Error redirecting {chart_type} detection chart: {e}")
            import traceback
            traceback.print_exc()
            return ""

    # ============================================================================
    # 🎯 CONSENSUS SIGNAL CHARTS - REMOVED DUPLICATE METHOD
    # ============================================================================
    # Note: The main generate_consensus_chart method is at line 2124 with correct parameter order

    # ============================================================================
    # 📋 ORDERBOOK ANALYSIS CHARTS - NEW METHODS
    # ============================================================================

    def generate_orderbook_chart(self, coin: str, orderbook_data: Dict[str, Any],
                               ohlcv_data, current_price: float) -> str:
        """📋 Generate CLEAN Orderbook chart - Only candlesticks + orderbook levels."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        result = self.generate_clean_orderbook_chart(coin, orderbook_data, ohlcv_data, current_price)
        return result if result else ""

    # ============================================================================
    # 🛠️ HELPER METHODS FOR NEW CHARTS
    # ============================================================================

    def _plot_candlestick_chart(self, ax, ohlcv_data, coin: str):
        """📊 Plot basic candlestick chart."""
        try:
            # Simple candlestick plotting
            for i, (idx, row) in enumerate(ohlcv_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # Color based on open/close
                color = '#4CAF50' if close_price >= open_price else '#F44336'

                # Draw high-low line
                ax.plot([i, i], [low_price, high_price], color='black', linewidth=1, alpha=0.7)

                # Draw open-close rectangle
                height = abs(close_price - open_price)
                bottom = min(open_price, close_price)
                ax.bar(i, height, bottom=bottom, color=color, alpha=0.8, width=0.8)

            ax.set_xlabel('Time')
            ax.set_ylabel('Price')
            ax.grid(True, alpha=0.3)

        except Exception as e:
            print(f"❌ Error plotting candlestick chart: {e}")
            # Fallback to simple line chart
            ax.plot(ohlcv_data['close'], color='#2196F3', linewidth=2)
            ax.set_title(f"Price Chart - {coin}")
            ax.grid(True, alpha=0.3)

    # ============================================================================
    # 📈 POINT & FIGURE CHARTS - NEW METHODS
    # ============================================================================

    def generate_point_figure_chart(self, coin: str, pf_data: Dict[str, Any],
                                   ohlcv_data, current_price: float) -> str:
        """📈 Generate CLEAN Point & Figure chart - Only candlesticks + P&F levels."""
        # ✅ NEW: Use clean chart generation instead of complex chart
        result = self.generate_clean_point_figure_chart(coin, pf_data, ohlcv_data, current_price)
        return result if result else ""

    def _create_point_figure_caption(self, coin: str, pf_data: Dict[str, Any], current_price: float) -> str:
        """📈 Create detailed Point & Figure caption with full analysis report like AI Analysis."""
        try:
            # ✅ Extract comprehensive P&F data
            trend_analysis = pf_data.get('trend_analysis', {})
            chart_state = pf_data.get('chart_state', {})
            signals = pf_data.get('signals', {})
            trading_levels = pf_data.get('trading_levels', {})
            price_targets = pf_data.get('price_targets', {})
            support_resistance = pf_data.get('support_resistance', {})
            breakout_analysis = pf_data.get('breakout_analysis', {})
            reversal_analysis = pf_data.get('reversal_analysis', {})

            # Basic P&F info
            trend = trend_analysis.get('trend', 'UNKNOWN')
            trend_strength = trend_analysis.get('strength', 0)
            latest_column = chart_state.get('latest_column', 'X')
            consecutive_columns = chart_state.get('consecutive_columns', 0)
            primary_signal = signals.get('primary_signal', 'NONE')
            signal_confidence = signals.get('confidence', 0)

            # Signal emoji
            signal_emoji = "🟢" if primary_signal == "BUY" else "🔴" if primary_signal == "SELL" else "🟡"

            caption = f"""📈 <b>POINT & FIGURE ANALYSIS - {coin}</b> 📈

💰 <b>Giá hiện tại:</b> <code>{current_price:.8f}</code>

🎯 <b>P&F Signal:</b> {signal_emoji} <b>{primary_signal}</b>
├ 💪 <b>Confidence:</b> <code>{signal_confidence:.1%}</code> ({'CAO' if signal_confidence > 0.7 else 'TRUNG BÌNH' if signal_confidence > 0.4 else 'THẤP'})
├ 📈 <b>Trend:</b> <b>{trend}</b> (Strength: <code>{trend_strength:.1%}</code>)
├ 📊 <b>Latest Column:</b> <b>{latest_column}</b>
└ 🔄 <b>Consecutive:</b> <code>{consecutive_columns}</code> columns"""

            # ✅ ADD TRADING SETUP if available
            has_trading_levels = trading_levels.get('has_trading_levels', False)
            if has_trading_levels:
                signal_type = trading_levels.get('signal_type', primary_signal)
                entry_price = trading_levels.get('entry_price', current_price)
                take_profit = trading_levels.get('take_profit', 0)
                stop_loss = trading_levels.get('stop_loss', 0)
                risk_reward = trading_levels.get('risk_reward_ratio', 0)

                # Calculate percentages
                if signal_type == "BUY":
                    tp_pct = ((take_profit / entry_price) - 1) * 100 if take_profit > 0 else 0
                    sl_pct = ((entry_price / stop_loss) - 1) * 100 if stop_loss > 0 else 0
                else:
                    tp_pct = ((entry_price / take_profit) - 1) * 100 if take_profit > 0 else 0
                    sl_pct = ((stop_loss / entry_price) - 1) * 100 if stop_loss > 0 else 0

                caption += f"""

🎯 <b>P&F TRADING SETUP:</b>
├ 📈 <b>Entry:</b> <code>{entry_price:.8f}</code>
├ 🎯 <b>Take Profit:</b> <code>{take_profit:.8f}</code> (<b>+{tp_pct:.1f}%</b>)
├ 🛡️ <b>Stop Loss:</b> <code>{stop_loss:.8f}</code> (<b>-{sl_pct:.1f}%</b>)
├ ⚖️ <b>Risk/Reward:</b> <code>{risk_reward:.2f}</code>
└ 💡 <b>P&F Quality:</b> <b>{trading_levels.get('pf_analysis', {}).get('signal_confidence', signal_confidence):.1%}</b>"""

                # Extended TP levels
                tp_levels = trading_levels.get('tp_levels', {})
                if tp_levels:
                    tp1 = tp_levels.get('tp1', 0)
                    tp2 = tp_levels.get('tp2', 0)
                    tp3 = tp_levels.get('tp3', 0)

                    if tp1 > 0 and tp2 > 0 and tp3 > 0:
                        if signal_type == "BUY":
                            tp1_pct = ((tp1 / entry_price) - 1) * 100
                            tp2_pct = ((tp2 / entry_price) - 1) * 100
                            tp3_pct = ((tp3 / entry_price) - 1) * 100
                        else:
                            tp1_pct = ((entry_price / tp1) - 1) * 100
                            tp2_pct = ((entry_price / tp2) - 1) * 100
                            tp3_pct = ((entry_price / tp3) - 1) * 100

                        caption += f"""

🎯 <b>Extended P&F Targets:</b>
├ 🥇 <b>TP1:</b> <code>{tp1:.8f}</code> (<b>+{tp1_pct:.1f}%</b>)
├ 🥈 <b>TP2:</b> <code>{tp2:.8f}</code> (<b>+{tp2_pct:.1f}%</b>)
└ 🥉 <b>TP3:</b> <code>{tp3:.8f}</code> (<b>+{tp3_pct:.1f}%</b>)"""

            # ✅ ADD P&F ANALYSIS DETAILS
            caption += f"""

🎯 <b>Price Targets:</b>
├ 🟢 <b>Bullish Target:</b> <code>{price_targets.get('bullish_target', 0):.8f}</code>
├ 🔴 <b>Bearish Target:</b> <code>{price_targets.get('bearish_target', 0):.8f}</code>

🛡️ <b>Support & Resistance:</b>
├ 🛡️ <b>Nearest Support:</b> <code>{support_resistance.get('nearest_support', 0):.8f}</code>
├ ⚡ <b>Nearest Resistance:</b> <code>{support_resistance.get('nearest_resistance', 0):.8f}</code>

📊 <b>Pattern Analysis:</b>
├ 💥 <b>Breakout Signal:</b> <b>{breakout_analysis.get('signal', 'NONE')}</b>
├ 🔄 <b>Reversal Signal:</b> <b>{reversal_analysis.get('signal', 'NONE')}</b>
├ 📦 <b>Box Size:</b> <code>{pf_data.get('box_size', 0):.6f}</code>
└ 🔄 <b>Reversal Amount:</b> <code>{pf_data.get('reversal_amount', 3)} boxes</code>

📊 <b>Analysis Summary:</b>
├ 📈 <b>Chart Columns:</b> <code>{trading_levels.get('pf_analysis', {}).get('chart_columns', 0)}</code>
├ 🎯 <b>Method:</b> <b>{trading_levels.get('calculation_methods', {}).get('target_method', 'Standard P&F')}</b>
├ 💪 <b>Trend Strength:</b> <b>{'MẠNH' if trend_strength > 0.7 else 'TRUNG BÌNH' if trend_strength > 0.4 else 'YẾU'}</b>
└ ⚡ <b>Signal Quality:</b> <b>{'CAO' if signal_confidence > 0.7 else 'TRUNG BÌNH' if signal_confidence > 0.4 else 'THẤP'}</b>

⏰ <b>Thời gian:</b> <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
🆔 <b>Analysis ID:</b> <code>PF_{coin}_{int(time.time())}</code>"""

            return caption

        except Exception as e:
            print(f"❌ Error creating detailed Point & Figure caption: {e}")
            import traceback
            traceback.print_exc()
            return f"📈 Point & Figure Analysis - {coin}\n💰 Price: {current_price:.8f}"