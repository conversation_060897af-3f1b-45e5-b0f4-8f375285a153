#!/usr/bin/env python3
"""
🔍 Debug script để tìm chính xác nơi consensus threshold 85% được sử dụng
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def debug_threshold_sources():
    """Debug tất cả nguồn threshold có thể"""
    print("🔍 DEBUGGING CONSENSUS THRESHOLD SOURCES")
    print("=" * 70)
    
    # 1. Check environment variables
    print("📊 Environment Variables:")
    env_threshold = os.getenv("MIN_CONFIDENCE_THRESHOLD", "NOT_SET")
    print(f"  MIN_CONFIDENCE_THRESHOLD = {env_threshold}")
    
    # 2. Check main_bot.py constants
    try:
        import main_bot
        print(f"\n📊 main_bot.py Constants:")
        print(f"  MIN_CONFIDENCE_THRESHOLD = {main_bot.MIN_CONFIDENCE_THRESHOLD}")
        print(f"  SIGNAL_QUALITY_FILTER_ENABLED = {main_bot.SIGNAL_QUALITY_FILTER_ENABLED}")
        
        # Check if consensus analyzer is initialized with correct threshold
        if hasattr(main_bot, 'TradingBot'):
            print(f"\n🤖 TradingBot Class Available")
            # We can't instantiate it here, but we can check the initialization code
        
    except ImportError as e:
        print(f"❌ Cannot import main_bot: {e}")
    
    # 3. Check consensus_analyzer.py
    try:
        import consensus_analyzer
        print(f"\n📊 consensus_analyzer.py:")
        
        # Create a test instance to see what threshold it uses
        analyzer = consensus_analyzer.ConsensusAnalyzer(
            min_consensus_score=0.6,
            confidence_threshold=0.8  # Explicitly set to 80%
        )
        print(f"  Test analyzer confidence_threshold = {analyzer.confidence_threshold}")
        
    except ImportError as e:
        print(f"❌ Cannot import consensus_analyzer: {e}")
    except Exception as e:
        print(f"❌ Error creating consensus analyzer: {e}")
    
    # 4. Search for hardcoded 0.85 values
    print(f"\n🔍 Searching for hardcoded 0.85 values...")
    
    files_to_check = [
        "main_bot.py",
        "consensus_analyzer.py", 
        "trigger_consensus_analysis.py"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"\n📄 Checking {filename}:")
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                found_085 = []
                for i, line in enumerate(lines, 1):
                    if '0.85' in line or '85%' in line or '85.0' in line:
                        found_085.append((i, line.strip()))
                
                if found_085:
                    print(f"  Found {len(found_085)} occurrences of 0.85/85%:")
                    for line_num, line_content in found_085:
                        print(f"    Line {line_num}: {line_content}")
                else:
                    print(f"  ✅ No hardcoded 0.85/85% found")
                    
            except Exception as e:
                print(f"  ❌ Error reading {filename}: {e}")
        else:
            print(f"  ⚠️ {filename} not found")

def test_actual_threshold_usage():
    """Test thực tế threshold được sử dụng như thế nào"""
    print(f"\n🧪 TESTING ACTUAL THRESHOLD USAGE")
    print("=" * 70)
    
    try:
        # Import and check actual values
        import main_bot
        
        threshold = main_bot.MIN_CONFIDENCE_THRESHOLD
        print(f"📊 Current MIN_CONFIDENCE_THRESHOLD: {threshold}")
        print(f"📊 Type: {type(threshold)}")
        print(f"📊 As percentage: {threshold:.1%}")
        
        # Test the comparison logic
        test_confidence = 0.807  # From user's log
        
        print(f"\n🔍 Testing comparison logic:")
        print(f"  Test confidence: {test_confidence}")
        print(f"  Threshold: {threshold}")
        print(f"  test_confidence >= threshold: {test_confidence >= threshold}")
        print(f"  test_confidence < threshold: {test_confidence < threshold}")
        
        # Test display formatting
        print(f"\n📊 Display formatting:")
        print(f"  Confidence: {test_confidence:.1%}")
        print(f"  Threshold: {threshold:.1%}")
        print(f"  Comparison: {test_confidence:.1%} >= {threshold:.1%}")
        
        # Simulate the exact log message
        if test_confidence < threshold:
            print(f"\n❌ Simulated log message:")
            print(f"  ❌ Consensus signal below quality threshold ({test_confidence:.1%} < {threshold:.1%}) - SKIPPING")
        else:
            print(f"\n✅ Simulated log message:")
            print(f"  ✅ Consensus signal meets quality threshold ({test_confidence:.1%} >= {threshold:.1%})")
            
    except Exception as e:
        print(f"❌ Error testing threshold usage: {e}")
        import traceback
        traceback.print_exc()

def check_consensus_analyzer_initialization():
    """Check how consensus analyzer is initialized in main_bot"""
    print(f"\n🔧 CHECKING CONSENSUS ANALYZER INITIALIZATION")
    print("=" * 70)
    
    try:
        # Read main_bot.py to find consensus analyzer initialization
        if os.path.exists("main_bot.py"):
            with open("main_bot.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find consensus analyzer initialization
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'ConsensusAnalyzer(' in line:
                    print(f"📊 Found ConsensusAnalyzer initialization at line {i+1}:")
                    # Print surrounding lines for context
                    start = max(0, i-3)
                    end = min(len(lines), i+10)
                    for j in range(start, end):
                        marker = ">>> " if j == i else "    "
                        print(f"{marker}Line {j+1}: {lines[j]}")
                    break
            else:
                print("❌ ConsensusAnalyzer initialization not found")
                
    except Exception as e:
        print(f"❌ Error checking initialization: {e}")

def create_minimal_test():
    """Create a minimal test to reproduce the issue"""
    print(f"\n🧪 MINIMAL REPRODUCTION TEST")
    print("=" * 70)
    
    try:
        # Simulate the exact scenario from user's log
        consensus_confidence = 0.807
        
        # Test with different threshold sources
        thresholds_to_test = [
            ("Hardcoded 0.80", 0.80),
            ("Hardcoded 0.85", 0.85),
            ("Environment variable", float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.80"))),
        ]
        
        print(f"Testing consensus confidence: {consensus_confidence:.3f} ({consensus_confidence:.1%})")
        print()
        
        for source, threshold in thresholds_to_test:
            meets_threshold = consensus_confidence >= threshold
            status = "✅ PASS" if meets_threshold else "❌ FAIL"
            
            print(f"📊 {source}:")
            print(f"  Threshold: {threshold:.3f} ({threshold:.1%})")
            print(f"  Result: {status}")
            print(f"  Message: {consensus_confidence:.1%} {'≥' if meets_threshold else '<'} {threshold:.1%}")
            print()
            
    except Exception as e:
        print(f"❌ Error in minimal test: {e}")

def main():
    """Main debug function"""
    print("🔍 CONSENSUS THRESHOLD DEBUG TOOL")
    print("=" * 80)
    print("This tool will help identify why threshold shows 85% instead of 80%")
    print()
    
    # Run all debug functions
    debug_threshold_sources()
    test_actual_threshold_usage()
    check_consensus_analyzer_initialization()
    create_minimal_test()
    
    print("\n" + "=" * 80)
    print("🎯 DEBUG COMPLETE")
    print("=" * 80)
    print("If threshold still shows 85%, check:")
    print("1. Bot restart required after code changes")
    print("2. Environment variable override")
    print("3. Cached imports or modules")
    print("4. Different consensus analyzer instance")

if __name__ == "__main__":
    main()
