#!/usr/bin/env python3
"""
🧪 Test Chart System - Debug script for chart generation and Telegram sending
"""

import os
import sys
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_system():
    """🧪 Test the chart system."""
    try:
        print(f"🧪 CHART SYSTEM TEST STARTED")
        print(f"⏰ Time: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
        print(f"{'='*60}")
        
        # Import main bot
        from main_bot import TradingBot
        
        print(f"📦 Importing TradingBot...")
        
        # Initialize bot
        print(f"🚀 Initializing TradingBot...")
        bot = TradingBot()

        print(f"✅ Bot initialized successfully")

        # Force enable chart system
        print(f"🔧 Force enabling chart system...")
        bot.force_enable_chart_system()

        # Debug chart system status
        print(f"🔍 Debugging chart system status...")
        bot.debug_chart_system_status()
        
        # Run comprehensive test
        print(f"\n🧪 Running comprehensive chart test...")
        success = bot.run_comprehensive_chart_test()
        
        if success:
            print(f"\n🎉 CHART SYSTEM TEST COMPLETED SUCCESSFULLY!")
            print(f"   📱 Check your Telegram for images with detailed reports")
        else:
            print(f"\n❌ CHART SYSTEM TEST FAILED!")
            print(f"   🔍 Check the logs above for error details")
        
        return success
        
    except Exception as e:
        print(f"❌ Test script error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """🔧 Test individual components."""
    try:
        print(f"\n🔧 TESTING INDIVIDUAL COMPONENTS...")
        
        from main_bot import TradingBot
        bot = TradingBot()

        # Force enable chart system
        bot.force_enable_chart_system()
        
        # Test 1: Basic photo send
        print(f"\n1️⃣ Testing basic photo send...")
        basic_result = bot.notifier.test_basic_photo_send()
        print(f"   Result: {'✅ PASS' if basic_result else '❌ FAIL'}")
        
        # Test 2: Force chart send
        print(f"\n2️⃣ Testing force chart send...")
        chart_result = bot.force_send_test_chart_with_report("BTC/USDT")
        print(f"   Result: {'✅ PASS' if chart_result else '❌ FAIL'}")
        
        # Test 3: Detailed analysis system
        print(f"\n3️⃣ Testing detailed analysis system...")
        analysis_result = bot.test_detailed_chart_system("BTC/USDT", "fibonacci")
        print(f"   Result: {'✅ PASS' if analysis_result else '❌ FAIL'}")
        
        return basic_result and chart_result and analysis_result
        
    except Exception as e:
        print(f"❌ Individual component test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"🚀 CHART SYSTEM TESTING UTILITY")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "individual":
            print(f"🔧 Running individual component tests...")
            success = test_individual_components()
        elif sys.argv[1] == "comprehensive":
            print(f"🧪 Running comprehensive test...")
            success = test_chart_system()
        else:
            print(f"❌ Unknown test type: {sys.argv[1]}")
            print(f"Usage: python test_chart_system.py [individual|comprehensive]")
            sys.exit(1)
    else:
        # Default: run comprehensive test
        print(f"🧪 Running default comprehensive test...")
        success = test_chart_system()
    
    # Exit with appropriate code
    if success:
        print(f"\n✅ ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        sys.exit(1)
