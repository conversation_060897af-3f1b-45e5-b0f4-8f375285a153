#!/usr/bin/env python3
"""
🧪 TEST FIXES
============

Test để kiểm tra các lỗi đã được sửa.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixes():
    """Test các fixes đã áp dụng."""
    print("🧪 TESTING FIXES")
    print("=" * 50)
    
    try:
        # Test 1: Chart generator initialization
        print("📊 Testing chart generator initialization...")
        from main_bot import TradingBot
        
        # Initialize bot (this should not hang anymore)
        print("🚀 Initializing trading bot...")
        bot = TradingBot()
        
        # Check chart generator
        print(f"📊 Chart generator exists: {hasattr(bot, 'chart_generator')}")
        if hasattr(bot, 'chart_generator'):
            chart_gen = getattr(bot, 'chart_generator')
            print(f"📊 Chart generator type: {type(chart_gen)}")
            print(f"📊 Chart generator is None: {chart_gen is None}")
            
            if chart_gen is not None:
                print("✅ Chart generator initialized successfully!")
                
                # Test methods
                methods = [
                    'generate_fibonacci_chart',
                    'generate_volume_profile_chart', 
                    'generate_point_figure_chart',
                    'generate_fourier_chart',
                    'generate_ai_analysis_chart'
                ]
                
                print("🔧 Testing chart generator methods:")
                for method in methods:
                    has_method = hasattr(chart_gen, method)
                    print(f"  📊 {method}: {'✅' if has_method else '❌'}")
            else:
                print("❌ Chart generator is None")
        
        # Test 2: Signal manager integration
        print("\n📡 Testing signal manager integration...")
        if hasattr(bot, 'signal_integration') and bot.signal_integration:
            signal_integration = bot.signal_integration
            print(f"📡 Signal integration type: {type(signal_integration)}")
            
            # Check signal_manager attribute
            has_signal_manager = hasattr(signal_integration, 'signal_manager')
            print(f"📡 Has signal_manager attribute: {has_signal_manager}")
            
            if has_signal_manager:
                signal_manager = getattr(signal_integration, 'signal_manager')
                print(f"📡 Signal manager value: {signal_manager}")
                print(f"📡 Signal manager is None: {signal_manager is None}")
        
        # Test 3: Consensus analyzer weight threshold
        print("\n⚖️ Testing consensus analyzer weight threshold...")
        try:
            import consensus_analyzer
            analyzer = consensus_analyzer.ConsensusAnalyzer()
            
            weight_threshold = analyzer.quality_control.get('min_weight_threshold', 0.6)
            print(f"⚖️ Weight threshold: {weight_threshold}")
            print(f"⚖️ Weight threshold <= 0.5: {weight_threshold <= 0.5}")
            
            if weight_threshold <= 0.5:
                print("✅ Weight threshold fixed!")
            else:
                print("❌ Weight threshold still too high")
                
        except Exception as e:
            print(f"❌ Error testing consensus analyzer: {e}")
        
        # Test 4: Type checking for TP/SL
        print("\n🎯 Testing TP/SL type checking...")
        
        # Test _calculate_percentage_change method
        if hasattr(bot, '_calculate_percentage_change'):
            calc_method = getattr(bot, '_calculate_percentage_change')
            
            # Test with valid inputs
            result1 = calc_method(100.0, 105.0)
            print(f"🎯 Valid input test: {result1}")
            
            # Test with invalid inputs (should not crash)
            try:
                result2 = calc_method(100.0, {"invalid": "dict"})
                print(f"🎯 Invalid input test: {result2}")
            except Exception as e:
                print(f"🎯 Invalid input handled: {e}")
            
            print("✅ TP/SL type checking works!")
        
        print("\n🎯 ALL FIXES TESTED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Fix testing failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixes()
