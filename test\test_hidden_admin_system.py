#!/usr/bin/env python3
"""
🔒 TEST HIDDEN ADMIN SYSTEM
============================

Test script để kiểm tra hidden admin CSV system
"""

import os
from datetime import datetime

def test_admin_config():
    """Test admin configuration"""
    print("👑 === TESTING ADMIN CONFIGURATION ===")
    print("=" * 50)
    
    try:
        from admin_config import (
            ADMIN_USERS, SUPER_ADMIN_USERS, CSV_EXPORT_ADMIN_USERS,
            is_admin, is_super_admin, is_csv_export_admin,
            get_admin_info, validate_admin_config
        )
        
        # Validate config
        is_valid = validate_admin_config()
        print(f"✅ Config validation: {'PASSED' if is_valid else 'FAILED'}")
        
        # Get admin info
        info = get_admin_info()
        print(f"📊 Admin info: {info}")
        
        # Test permissions
        test_user_id = 123456789
        print(f"\n🧪 Testing user {test_user_id}:")
        print(f"  - Is admin: {is_admin(test_user_id)}")
        print(f"  - Is super admin: {is_super_admin(test_user_id)}")
        print(f"  - Has CSV export: {is_csv_export_admin(test_user_id)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin config test failed: {e}")
        return False

def test_hidden_csv_system():
    """Test hidden CSV system"""
    print("\n🔒 === TESTING HIDDEN CSV SYSTEM ===")
    print("=" * 50)
    
    try:
        from hidden_admin_csv_system import HiddenAdminCSVSystem
        
        # Initialize system
        admin_csv = HiddenAdminCSVSystem()
        print(f"✅ Hidden CSV system initialized")
        print(f"👑 Admin users configured: {len(admin_csv.admin_users)}")
        
        # Test admin check
        test_user_id = 123456789
        is_admin = admin_csv.is_admin(test_user_id)
        print(f"🧪 User {test_user_id} is admin: {is_admin}")
        
        # Test command processing (should fail for non-admin)
        test_command = "/admin_export_all"
        result = admin_csv.process_hidden_command(test_command, test_user_id, "-1002301937119")
        print(f"🧪 Command processing result: {result}")
        
        # Test stats
        stats = admin_csv._get_admin_export_stats()
        print(f"📊 Admin stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Hidden CSV system test failed: {e}")
        return False

def test_member_manager_integration():
    """Test member manager integration"""
    print("\n🤖 === TESTING MEMBER MANAGER INTEGRATION ===")
    print("=" * 50)
    
    try:
        from telegram_member_manager import TelegramMemberManager
        
        # Initialize member manager
        manager = TelegramMemberManager()
        print("✅ Member manager initialized")
        
        # Check hidden admin CSV integration
        if hasattr(manager, 'hidden_admin_csv') and manager.hidden_admin_csv:
            print("✅ Hidden admin CSV system integrated")
            
            # Test hidden command processing
            test_user_id = 123456789
            test_command = "/admin_export_stats"
            result = manager.process_hidden_admin_command(test_command, test_user_id, "-1002301937119")
            print(f"🧪 Hidden command processing: {result}")
            
        else:
            print("❌ Hidden admin CSV system not integrated")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Member manager integration test failed: {e}")
        return False

def test_admin_commands_integration():
    """Test admin commands integration"""
    print("\n👑 === TESTING ADMIN COMMANDS INTEGRATION ===")
    print("=" * 50)
    
    try:
        # Mock bot instance
        class MockBot:
            def __init__(self):
                self.notifier = None
        
        mock_bot = MockBot()
        
        from member_admin_commands import MemberAdminCommands
        
        # Initialize admin commands
        admin_commands = MemberAdminCommands(mock_bot)
        print("✅ Admin commands initialized")
        
        # Check admin lists
        print(f"👑 Basic admins: {len(admin_commands.admin_users)}")
        print(f"🔒 Super admins: {len(admin_commands.super_admin_users)}")
        print(f"📊 CSV export admins: {len(admin_commands.csv_export_admin_users)}")
        
        # Test admin checks
        test_user_id = 123456789
        print(f"\n🧪 Testing user {test_user_id}:")
        print(f"  - Is admin: {admin_commands.is_admin(test_user_id)}")
        print(f"  - Is super admin: {admin_commands.is_super_admin(test_user_id)}")
        
        # Test hidden export command
        test_command = "/export all"
        result = admin_commands.handle_hidden_export_command(test_command, test_user_id, "-1002301937119")
        print(f"🧪 Hidden export command result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin commands integration test failed: {e}")
        return False

def test_security_features():
    """Test security features"""
    print("\n🔒 === TESTING SECURITY FEATURES ===")
    print("=" * 50)
    
    try:
        from admin_config import SECURITY_CONFIG
        
        print("🔒 Security Configuration:")
        for key, value in SECURITY_CONFIG.items():
            print(f"  - {key}: {value}")
        
        # Test admin exports directory
        admin_export_dir = SECURITY_CONFIG.get("admin_export_directory", "admin_exports")
        if os.path.exists(admin_export_dir):
            files = [f for f in os.listdir(admin_export_dir) if f.endswith('.csv')]
            print(f"\n📁 Admin export directory: {admin_export_dir}")
            print(f"📊 CSV files found: {len(files)}")
            
            if files:
                print("📄 Recent files:")
                for file in files[-3:]:  # Show last 3 files
                    filepath = os.path.join(admin_export_dir, file)
                    size = os.path.getsize(filepath)
                    mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                    print(f"  - {file} ({size} bytes, {mtime.strftime('%Y-%m-%d %H:%M')})")
        else:
            print(f"📁 Admin export directory not found: {admin_export_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Security features test failed: {e}")
        return False

def show_hidden_commands_info():
    """Hiển thị thông tin hidden commands"""
    print("\n🔒 === HIDDEN COMMANDS INFORMATION ===")
    print("=" * 50)
    
    print("🔒 HIDDEN EXPORT COMMANDS:")
    print("  /export all - Export tất cả thành viên")
    print("  /export group <chat_id> - Export theo nhóm")
    print("  /export new - Export thành viên mới hôm nay")
    print("  /export expiring [days] - Export sắp hết hạn")
    print("  /export status <status> - Export theo trạng thái")
    print("  /export summary - Tổng kết export")
    
    print("\n🔒 DIRECT ADMIN COMMANDS:")
    print("  /admin_export_all - Export tất cả (direct)")
    print("  /admin_export_group <chat_id> - Export nhóm (direct)")
    print("  /admin_export_new - Export mới (direct)")
    print("  /admin_export_expiring [days] - Export sắp hết hạn (direct)")
    print("  /admin_export_stats - Thống kê admin")
    print("  /admin_export_help - Trợ giúp admin export")
    
    print("\n🔒 SECURITY FEATURES:")
    print("  ✅ Commands hoàn toàn ẩn khỏi users thường")
    print("  ✅ Không hiển thị trong /help_admin")
    print("  ✅ Files lưu trong admin_exports/ riêng biệt")
    print("  ✅ Chỉ admin được cấu hình mới có quyền")
    print("  ✅ Không có phản hồi nào cho non-admin")
    print("  ✅ Silent rejection để giữ bí mật")

def main():
    """Main test function"""
    print("🔒 === HIDDEN ADMIN SYSTEM TEST ===")
    print("🎯 Testing hidden CSV export system for admins only")
    print()
    
    # Run all tests
    test1 = test_admin_config()
    test2 = test_hidden_csv_system()
    test3 = test_member_manager_integration()
    test4 = test_admin_commands_integration()
    test5 = test_security_features()
    
    # Show hidden commands info
    show_hidden_commands_info()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Admin Config", test1),
        ("Hidden CSV System", test2),
        ("Member Manager Integration", test3),
        ("Admin Commands Integration", test4),
        ("Security Features", test5)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ HIDDEN ADMIN SYSTEM STATUS:")
        print("  • Hidden CSV export system: OPERATIONAL")
        print("  • Admin-only access: SECURED")
        print("  • Commands hidden from help: CONFIRMED")
        print("  • Silent rejection for non-admins: ACTIVE")
        print("  • Admin exports directory: SEPARATED")
        print("\n💡 TO ENABLE ADMIN ACCESS:")
        print("  1. Edit admin_config.py")
        print("  2. Add admin Telegram User IDs to appropriate lists")
        print("  3. Configure ADMIN_USERS for basic commands")
        print("  4. Configure SUPER_ADMIN_USERS for hidden export")
        print("  5. Configure CSV_EXPORT_ADMIN_USERS for CSV access")
        print("\n🔒 SYSTEM READY FOR DEPLOYMENT!")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the configuration and try again.")
        print("  Make sure all required files are present:")
        print("  - admin_config.py")
        print("  - hidden_admin_csv_system.py")
        print("  - member_admin_commands.py")
        print("  - telegram_member_manager.py")

if __name__ == "__main__":
    main()
