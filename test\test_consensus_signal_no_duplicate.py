#!/usr/bin/env python3
"""
🧪 TEST: Consensus Signal No Duplicate Format
Test để xác nhận consensus signal không có format trùng lặp
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from telegram_notifier import EnhancedTelegramNotifier

def create_test_consensus_data():
    """Create test consensus data"""
    return {
        'consensus_score': 0.639,
        'confidence': 0.75,
        'signal_quality': {
            'strength': 0.82,
            'overall_quality': 0.78
        },
        'contributing_algorithms': [
            {
                'algorithm': 'AI Analysis',
                'signal': 'SELL',
                'confidence': 0.85,
                'details': 'Strong bearish pattern detected'
            },
            {
                'algorithm': '<PERSON><PERSON><PERSON><PERSON>',
                'signal': 'SELL',
                'confidence': 0.72,
                'details': 'Price at 61.8% retracement resistance'
            }
        ]
    }

def create_test_signal_data():
    """Create test signal data"""
    return {
        'signal_id': 'SIG_BTC/USDT_1749972075',
        'coin': 'BTC/USDT',
        'coin_category': 'MAJOR',
        'signal_type': 'SELL',
        'entry': 105367.43000000,
        'take_profit': 98749.20776578,
        'stop_loss': 110989.82100000,
        'risk_reward_ratio': 1.18,
        'primary_tf': '4h',
        'tp_sl_methods': ['ATR', 'Fibonacci', 'Volume Profile', 'Support/Resistance'],
        'tp_sl_confidence': 0.78
    }

def test_no_duplicate_format():
    """Test that consensus signal has no duplicate format"""
    print("\n🧪 === TESTING NO DUPLICATE FORMAT ===")
    
    try:
        # Initialize notifier
        notifier = EnhancedTelegramNotifier(
            bot_token="test_token",
            chat_id="-1002301937119"
        )
        
        # Check if the duplicate method still exists
        has_duplicate_method = hasattr(notifier, '_create_detailed_consensus_signal_caption')
        
        if has_duplicate_method:
            print("❌ Duplicate method '_create_detailed_consensus_signal_caption' still exists!")
            return False
        else:
            print("✅ Duplicate method '_create_detailed_consensus_signal_caption' has been removed!")
        
        # Override send_message and send_photo to capture messages
        sent_messages = []
        sent_photos = []
        
        def mock_send_message(message, chat_id=None, parse_mode="HTML"):
            sent_messages.append({
                'message': message,
                'chat_id': chat_id,
                'parse_mode': parse_mode
            })
            print(f"📤 MOCK: Would send message to {chat_id}")
            return True
        
        def mock_send_photo(photo_path, caption="", chat_id=None, parse_mode="HTML"):
            sent_photos.append({
                'photo_path': photo_path,
                'caption': caption,
                'chat_id': chat_id,
                'parse_mode': parse_mode
            })
            print(f"📸 MOCK: Would send photo to {chat_id}")
            return True
        
        # Store original methods
        original_send_message = notifier.send_message
        original_send_photo = notifier.send_photo
        
        # Apply mocks
        notifier.send_message = mock_send_message
        notifier.send_photo = mock_send_photo
        
        # Create test data
        consensus_data = create_test_consensus_data()
        signal_data = create_test_signal_data()
        
        # Test consensus signal method
        result = notifier.send_consensus_signal(
            coin="BTC/USDT",
            consensus_data=consensus_data,
            signal_data=signal_data,
            use_html=True,
            ohlcv_data=None,  # No chart generation
            chart_generator=None
        )
        
        # Restore original methods
        notifier.send_message = original_send_message
        notifier.send_photo = original_send_photo
        
        print(f"✅ Consensus signal method completed: {result}")
        print(f"📤 Messages sent: {len(sent_messages)}")
        print(f"📸 Photos sent: {len(sent_photos)}")
        
        if sent_messages:
            message_content = sent_messages[0]['message']
            
            print("\n📝 SENT MESSAGE CONTENT:")
            print("=" * 80)
            print(message_content)
            print("=" * 80)
            
            # Check for duplicate format elements (should NOT be present)
            duplicate_elements = [
                "CONSENSUS SIGNAL REPORT",
                "TRADING SIGNAL",
                "├ 🎯 Signal Type:",
                "├ 💰 Entry Price:",
                "├ 🟢 Take Profit:",
                "├ 🔴 Stop Loss:",
                "├ 📈 Risk/Reward:",
                "└ 💪 Consensus Score:",
                "ENHANCED CONSENSUS ANALYSIS",
                "Multi-Algorithm Consensus with Auto-Chart Generation"
            ]
            
            # Check for correct main format elements (should be present)
            main_format_elements = [
                "🎯 CONSENSUS SIGNAL - BTC/USDT 🎯",
                "🔴 SIGNAL TYPE: SELL 🔴",
                "🪙 BTC/USDT (MAJOR) | 📈 4h",
                "💰 Entry:",
                "🎯 Take Profit:",
                "🛡️ Stop Loss:",
                "⚖️ Risk/Reward:",
                "🎯 PHÂN TÍCH ĐỒNG THUẬN:",
                "📊 PHÂN TÍCH CHI TIẾT:",
                "🎯 PHÂN TÍCH TP/SL:",
                "💡 NÂNG CAO:",
                "🆔 Signal ID:",
                "⏰ Thời gian:",
                "⚡ Tín hiệu này đạt tiêu chuẩn nghiêm ngặt"
            ]
            
            # Check for duplicate elements
            found_duplicates = []
            for element in duplicate_elements:
                if element in message_content:
                    found_duplicates.append(element)
            
            # Check for main format elements
            missing_main_elements = []
            for element in main_format_elements:
                if element not in message_content:
                    missing_main_elements.append(element)
            
            if found_duplicates:
                print(f"\n❌ Found duplicate format elements (should not be present):")
                for element in found_duplicates:
                    print(f"  - {element}")
                return False
            
            if missing_main_elements:
                print(f"\n❌ Missing main format elements:")
                for element in missing_main_elements:
                    print(f"  - {element}")
                return False
            
            print(f"\n✅ No duplicate format elements found!")
            print(f"✅ All main format elements present!")
            print(f"✅ Using single, comprehensive format only!")
            
            return True
        else:
            print("❌ No messages were sent")
            return False
        
    except Exception as e:
        print(f"❌ Error testing no duplicate format: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run test"""
    print("🧪 === CONSENSUS SIGNAL NO DUPLICATE FORMAT TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    success = test_no_duplicate_format()
    end_time = time.time()
    
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nNo Duplicate Format Test: {status} ({end_time - start_time:.2f}s)")
    
    if success:
        print("\n🎉 SUCCESS! Consensus signals use single format without duplication!")
        print("\n📋 CONFIRMED:")
        print("✅ Duplicate method '_create_detailed_consensus_signal_caption' removed")
        print("✅ No duplicate format elements found")
        print("✅ Using single comprehensive format from lines 2763-2796")
        print("✅ Clean, non-redundant message structure")
        print("✅ All essential information included in one format")
    else:
        print("\n⚠️ Test failed. Duplicate format may still exist.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
