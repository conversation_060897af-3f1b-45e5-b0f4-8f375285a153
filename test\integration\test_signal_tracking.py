#!/usr/bin/env python3
"""
🧪 SIGNAL TRACKING SYSTEM TEST
==============================

Test script to verify Multi-Analyzer Signal Manager functionality.
Tests signal limits, tracking, TP/SL detection, and queue management.

Author: AI Trading Bot
Version: 1.0
"""

import time
import random
from multi_analyzer_signal_manager import MultiAnalyzerSignalManager, AnalyzerType

class MockDataFetcher:
    """Mock data fetcher for testing."""
    
    def __init__(self):
        self.prices = {
            "BTC/USDT": 45000.0,
            "ETH/USDT": 3000.0,
            "SHIB/USDT": 0.00002500
        }
    
    def get_current_price(self, coin: str) -> float:
        """Get mock current price with random fluctuation."""
        base_price = self.prices.get(coin, 1.0)
        # Add random fluctuation ±2%
        fluctuation = random.uniform(-0.02, 0.02)
        return base_price * (1 + fluctuation)

class MockTelegramNotifier:
    """Mock telegram notifier for testing."""
    
    def __init__(self):
        self.sent_messages = []
    
    def send_message(self, message: str, chat_id: str, parse_mode: str = "HTML") -> bool:
        """Mock send message."""
        self.sent_messages.append({
            "message": message,
            "chat_id": chat_id,
            "timestamp": time.time()
        })
        print(f"📱 Mock message sent to {chat_id}: {message[:100]}...")
        return True

def create_test_signal_data(coin: str, signal_type: str, current_price: float) -> dict:
    """Create test signal data."""
    if signal_type == "BUY":
        entry = current_price
        take_profit = current_price * 1.02  # 2% profit
        stop_loss = current_price * 0.99    # 1% loss
    else:  # SELL
        entry = current_price
        take_profit = current_price * 0.98  # 2% profit
        stop_loss = current_price * 1.01    # 1% loss
    
    return {
        "coin": coin,
        "signal_type": signal_type,
        "entry": entry,
        "take_profit": take_profit,
        "stop_loss": stop_loss,
        "confidence": random.uniform(0.6, 0.9),
        "current_price": current_price
    }

def test_signal_limits():
    """Test shared pool signal limit functionality."""
    print("\n🧪 Testing Shared Pool Signal Limits...")

    # Initialize manager
    data_fetcher = MockDataFetcher()
    notifier = MockTelegramNotifier()
    manager = MultiAnalyzerSignalManager(data_fetcher, notifier)

    # Test adding signals up to shared pool limit
    coin = "BTC/USDT"
    current_price = data_fetcher.get_current_price(coin)

    print(f"📊 Adding signals to shared pool for {coin} at price {current_price:.2f}")
    print(f"    🎯 Shared pool limit: 20 signals total across ALL analyzers")

    # Add 15 AI signals + 5 Fibonacci signals = 20 total
    print(f"\n🤖 Adding 15 AI Analysis signals...")
    for i in range(15):
        signal_data = create_test_signal_data(coin, "BUY", current_price)
        success = manager.add_signal(AnalyzerType.AI_ANALYSIS, signal_data)
        print(f"  AI Signal {i+1}: {'✅' if success else '❌'}")

    print(f"\n🌀 Adding 5 Fibonacci signals...")
    for i in range(5):
        signal_data = create_test_signal_data(coin, "BUY", current_price)
        success = manager.add_signal(AnalyzerType.FIBONACCI, signal_data)
        print(f"  Fibonacci Signal {i+1}: {'✅' if success else '❌'}")

    # Try to add 21st signal (should be queued)
    print(f"\n📋 Trying to add 21st signal (should be queued)...")
    signal_data = create_test_signal_data(coin, "BUY", current_price)
    success = manager.add_signal(AnalyzerType.VOLUME_PROFILE, signal_data)
    print(f"  21st Signal: {'✅ Added' if success else '📋 Queued (Expected)'}")

    # Check shared pool status
    status = manager.get_all_analyzers_status()
    pool_status = status['shared_pool_status']
    print(f"\n📊 Shared Pool Status:")
    print(f"  Total signals: {pool_status['total_signals']}/20")
    print(f"  Active signals: {pool_status['active_signals']}")
    print(f"  Can send new: {pool_status['can_send_new']}")
    print(f"  Queue count: {pool_status['queue_count']}")
    print(f"  Utilization: {pool_status['utilization_percentage']:.1f}%")

    return manager

def test_tp_sl_detection(manager: MultiAnalyzerSignalManager):
    """Test TP/SL hit detection."""
    print("\n🧪 Testing TP/SL Detection...")
    
    coin = "BTC/USDT"
    
    # Get current signals
    signals = manager.analyzer_signals[AnalyzerType.AI_ANALYSIS]
    if not signals:
        print("❌ No signals to test")
        return
    
    # Get first signal
    signal_id, signal = next(iter(signals.items()))
    print(f"📊 Testing signal: {signal.coin} {signal.signal_type}")
    print(f"  Entry: {signal.entry_price:.2f}")
    print(f"  TP: {signal.take_profit:.2f}")
    print(f"  SL: {signal.stop_loss:.2f}")
    
    # Simulate price hitting TP
    tp_price = signal.take_profit + 0.01  # Slightly above TP
    print(f"\n🎯 Simulating price hitting TP: {tp_price:.2f}")
    updates = manager.update_signal_prices(coin, tp_price)
    
    if updates:
        for update in updates:
            if update["action"] == "completed":
                print(f"  ✅ Signal completed: {update['close_reason']}")
                print(f"  💰 PnL: {update['pnl_percentage']:.2f}%")
    
    # Check if signal was marked as completed
    updated_signal = manager.analyzer_signals[AnalyzerType.AI_ANALYSIS].get(signal_id)
    if updated_signal:
        print(f"  📊 Signal status: {updated_signal.status.value}")

def test_completion_threshold(manager: MultiAnalyzerSignalManager):
    """Test completion threshold and queue processing (shared pool)."""
    print("\n🧪 Testing Shared Pool Completion Threshold...")

    # Get active signals from shared pool
    active_signals = [s for s in manager.all_signals.values() if s.status.value == "active"]

    print(f"📊 Active signals in shared pool: {len(active_signals)}")

    # Complete enough signals to reach 18/20 threshold
    completed_count = 0
    signals_to_complete = min(18, len(active_signals))

    print(f"🎯 Completing {signals_to_complete} signals to reach threshold...")

    for signal in active_signals[:signals_to_complete]:  # Complete first 18
        # Simulate TP hit
        tp_price = signal.take_profit + 0.01
        updates = manager.update_signal_prices(signal.coin, tp_price)
        if updates and any(u["action"] == "completed" for u in updates):
            completed_count += 1
            print(f"  ✅ Completed signal {completed_count}: {signal.analyzer_type.value} {signal.coin}")

    print(f"✅ Completed {completed_count} signals")

    # Check if can send new signals now
    can_send = manager.can_send_new_signal(AnalyzerType.AI_ANALYSIS)
    print(f"📤 Can send new signals: {can_send}")

    # Check shared pool status
    status = manager.get_all_analyzers_status()
    pool_status = status['shared_pool_status']
    print(f"📊 Shared Pool After Completion:")
    print(f"  Total signals: {pool_status['total_signals']}/20")
    print(f"  Active signals: {pool_status['active_signals']}")
    print(f"  Completed signals: {pool_status['completed_signals']}")
    print(f"  Queue count: {pool_status['queue_count']}")

    # Try to add a new signal (should work now)
    print(f"\n🆕 Testing new signal after threshold...")
    signal_data = create_test_signal_data("ETH/USDT", "BUY", 3000.0)
    success = manager.add_signal(AnalyzerType.VOLUME_PROFILE, signal_data)
    print(f"  New signal added: {'✅' if success else '❌'}")

def test_multiple_analyzers():
    """Test multiple analyzer types."""
    print("\n🧪 Testing Multiple Analyzers...")
    
    data_fetcher = MockDataFetcher()
    notifier = MockTelegramNotifier()
    manager = MultiAnalyzerSignalManager(data_fetcher, notifier)
    
    analyzers = [
        AnalyzerType.AI_ANALYSIS,
        AnalyzerType.FIBONACCI,
        AnalyzerType.VOLUME_PROFILE,
        AnalyzerType.CONSENSUS
    ]
    
    coin = "ETH/USDT"
    current_price = data_fetcher.get_current_price(coin)
    
    # Add 5 signals to each analyzer
    for analyzer in analyzers:
        print(f"📊 Adding signals to {analyzer.value}...")
        for i in range(5):
            signal_data = create_test_signal_data(coin, "BUY", current_price)
            success = manager.add_signal(analyzer, signal_data)
            if not success:
                print(f"  ❌ Failed to add signal {i+1}")
    
    # Check status for all analyzers
    print(f"\n📊 Multi-Analyzer Status:")
    for analyzer in analyzers:
        status = manager.get_analyzer_status(analyzer)
        active_count = status['signal_limits']['active_count']
        can_send = status['signal_limits']['can_send_new']
        print(f"  {analyzer.value}: {active_count} active, can_send: {can_send}")

def test_price_monitoring():
    """Test continuous price monitoring."""
    print("\n🧪 Testing Price Monitoring...")
    
    data_fetcher = MockDataFetcher()
    notifier = MockTelegramNotifier()
    manager = MultiAnalyzerSignalManager(data_fetcher, notifier)
    
    # Add a few signals
    coin = "SHIB/USDT"
    current_price = data_fetcher.get_current_price(coin)
    
    for i in range(3):
        signal_data = create_test_signal_data(coin, "BUY", current_price)
        manager.add_signal(AnalyzerType.AI_ANALYSIS, signal_data)
    
    print(f"📊 Added 3 signals for {coin}")
    
    # Simulate price updates
    for i in range(5):
        new_price = data_fetcher.get_current_price(coin)
        print(f"  Price update {i+1}: {new_price:.8f}")
        updates = manager.update_signal_prices(coin, new_price)
        
        if updates:
            for update in updates:
                action = update["action"]
                if action == "completed":
                    print(f"    ✅ Signal completed: {update['close_reason']}")
                elif action == "price_update":
                    print(f"    📊 Price change: {update['price_change_pct']:.2f}%")
        
        time.sleep(1)  # Small delay

def main():
    """Run all tests."""
    print("🚀 MULTI-ANALYZER SIGNAL TRACKING TESTS")
    print("=" * 50)
    
    try:
        # Test 1: Signal limits
        manager = test_signal_limits()
        
        # Test 2: TP/SL detection
        test_tp_sl_detection(manager)
        
        # Test 3: Completion threshold
        test_completion_threshold(manager)
        
        # Test 4: Multiple analyzers
        test_multiple_analyzers()
        
        # Test 5: Price monitoring
        test_price_monitoring()
        
        print("\n✅ All tests completed!")
        
        # Final status report
        print("\n📊 FINAL STATUS REPORT:")
        status = manager.get_all_analyzers_status()
        for analyzer_name, analyzer_data in status["analyzers"].items():
            if "error" not in analyzer_data:
                limits = analyzer_data["signal_limits"]
                performance = analyzer_data["performance"]
                print(f"  {analyzer_name}: {limits['active_count']} active, "
                      f"{limits['completed_count']} completed, "
                      f"win_rate: {performance['win_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Stop monitoring
        if 'manager' in locals():
            manager.stop_monitoring()

if __name__ == "__main__":
    main()
