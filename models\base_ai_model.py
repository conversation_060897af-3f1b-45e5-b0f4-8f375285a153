from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging
import os
import pickle
import json

class BaseAIModel(ABC):
    def __init__(self, model_name: str, model_path: Optional[str] = None):
        self.model_name = model_name
        self.model_path = model_path
        self.model = None
        self.is_trained = False
        self.is_mock = False
        
        # Initialize logger
        self.logger = logging.getLogger(f"{__name__}.{model_name}")
        
        # Load or create model
        self._load_model()
        if self.model is None:
            self._create_new_model()

    @abstractmethod
    def _load_model(self):
        """Load the model from file."""
        pass

    @abstractmethod
    def _create_new_model(self):
        """Create a new model."""
        pass

    @abstractmethod
    def preprocess_features(self, features: Dict[str, Any]) -> Optional[Any]:
        """Preprocess features for model input."""
        pass

    @abstractmethod
    def predict_signals(self, processed_features: Any) -> Dict[str, Any]:
        """Generate trading signals from processed features."""
        pass

    def get_name(self) -> str:
        """Get the model name."""
        return self.model_name

    def save_model(self, path: Optional[str] = None):
        """Save the model to file."""
        save_path = path or self.model_path
        if save_path and self.model is not None:
            try:
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                if save_path.endswith('.json'):
                    with open(save_path, 'w') as f:
                        json.dump({"model_type": self.model_name, "is_trained": self.is_trained}, f)
                else:
                    with open(save_path, 'wb') as f:
                        pickle.dump(self.model, f)
                self.logger.info(f"Model saved to {save_path}")
            except Exception as e:
                self.logger.error(f"Error saving model: {e}")

    def predict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Main prediction method."""
        try:
            processed_features = self.preprocess_features(data)
            if processed_features is not None:
                return self.predict_signals(processed_features)
            else:
                return self._get_fallback_prediction()
        except Exception as e:
            self.logger.error(f"Error in prediction: {e}")
            return self._get_fallback_prediction()
    
    def _get_fallback_prediction(self) -> Dict[str, Any]:
        """Get fallback prediction when processing fails."""
        import random
        return {
            "signal_type": random.choice(["BUY", "SELL", "NONE"]),
            "confidence": random.uniform(0.5, 0.7),
            "model_type": f"{self.model_name} (Fallback)"
        }
    
    def is_ready(self) -> bool:
        """Check if model is ready for predictions."""
        return self.is_trained
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        return {
            "name": self.model_name,
            "is_trained": self.is_trained,
            "is_mock": self.is_mock,
            "model_path": self.model_path
        }

