# 📊 TRADING P&L TRACKING SYSTEM

Comprehensive Profit & Loss tracking system for trading bot with detailed analytics, performance monitoring, and web dashboard.

## 🎯 FEATURES

### ✅ Core Functionality
- **Real-time P&L tracking** for all signal types
- **Win/Loss ratio analysis** with detailed breakdowns
- **Risk-reward ratio calculations** for each trade
- **Performance metrics** by algorithm and signal source
- **Historical performance analysis** with trend tracking
- **Automated reporting** with customizable intervals
- **Export capabilities** for detailed analysis (CSV, JSON)

### 📊 Analytics & Metrics
- **Overall Performance**: Total P&L, win rate, average returns
- **Signal Breakdown**: Performance by signal type and source
- **Risk Metrics**: Max profit/loss, drawdown analysis, Sharpe ratio
- **Trade Analysis**: Entry/exit tracking, duration analysis
- **Market Conditions**: Performance correlation with market regimes

### 🌐 Web Dashboard
- **Real-time visualization** of trading performance
- **Interactive charts** and performance graphs
- **Signal performance breakdown** with detailed metrics
- **Active trades monitoring** with real-time updates
- **Export functionality** directly from web interface

## 📁 FILE STRUCTURE

```
📊 P&L Tracking System/
├── profit_loss_tracker.py           # Core P&L tracking engine
├── trading_performance_integration.py # Integration layer for main bot
├── main_bot_pnl_integration.py      # Drop-in integration for main_bot.py
├── pnl_web_dashboard.py             # Web dashboard for visualization
└── PNL_TRACKING_README.md           # This documentation file
```

## 🚀 QUICK START

### 1. Basic Usage (Standalone)

```python
from profit_loss_tracker import ProfitLossTracker

# Initialize tracker
tracker = ProfitLossTracker("my_trading_performance.db")

# Record a signal entry
trade_id = tracker.record_signal_entry(
    coin="BTC/USDT",
    signal_type="consensus",
    signal_source="enhanced_consensus_v4",
    entry_signal="BUY",
    entry_price=45000.0,
    entry_confidence=0.85,
    stop_loss_price=43000.0,
    take_profit_price=48000.0
)

# Record signal exit
tracker.record_signal_exit(
    trade_id=trade_id,
    exit_price=47500.0,
    exit_reason="TP"  # Take Profit
)

# Get performance stats
stats = tracker.get_performance_stats(days=30)
print(f"Win Rate: {stats['win_rate']}%")
print(f"Total P&L: {stats['total_profit_loss']}%")

# Generate report
report = tracker.generate_performance_report(days=30)
print(report)
```

### 2. Integration with Main Bot

```python
from main_bot_pnl_integration import MainBotPnLIntegration

class TradingBot(MainBotPnLIntegration):
    def __init__(self, config_file="config.json"):
        # Your existing initialization
        self.load_config(config_file)
        # ... other setup ...
        
        # Initialize P&L tracking
        super().__init__(enable_pnl_tracking=True)
    
    def analyze_consensus(self, coin, ohlcv_data, current_price):
        # Your existing consensus analysis
        consensus_result = self.consensus_analyzer.analyze(...)
        
        # Add P&L tracking
        if hasattr(self, 'track_consensus_signal'):
            trade_id = self.track_consensus_signal(
                coin=coin,
                consensus_result=consensus_result,
                current_price=current_price
            )
            if trade_id:
                print(f"📊 Consensus signal tracked: {trade_id}")
        
        return consensus_result
```

### 3. Web Dashboard

```python
from pnl_web_dashboard import run_dashboard

# Run web dashboard
run_dashboard(
    host='127.0.0.1',
    port=5000,
    db_path="trading_performance.db"
)

# Access at: http://127.0.0.1:5000
```

## 🔧 INTEGRATION GUIDE

### Step 1: Install Dependencies

```bash
pip install pandas sqlite3 flask
```

### Step 2: Add to Main Bot

1. **Import the integration module:**
```python
from main_bot_pnl_integration import MainBotPnLIntegration
```

2. **Modify your bot class:**
```python
class TradingBot(MainBotPnLIntegration):
    def __init__(self, ...):
        # Your existing code
        super().__init__(enable_pnl_tracking=True)
```

3. **Add tracking calls:**
```python
# For consensus signals
trade_id = self.track_consensus_signal(coin, consensus_result, current_price)

# For AI predictions
trade_id = self.track_ai_prediction_signal(coin, ai_result, current_price)

# For individual signals
trade_id = self.track_individual_signal(
    coin, "fibonacci", "fibonacci_analyzer", 
    "BUY", current_price, 0.75
)
```

### Step 3: Enable Auto-Reporting

The system automatically generates performance reports every 12 hours by default. You can customize this:

```python
# Custom reporting interval (6 hours)
super().__init__(enable_pnl_tracking=True)
self.pnl_integration.auto_report_interval = 6
```

## 📊 PERFORMANCE METRICS

### Core Metrics
- **Total Trades**: Number of completed trades
- **Win Rate**: Percentage of profitable trades
- **Total P&L**: Cumulative profit/loss percentage
- **Average P&L**: Average return per trade
- **Max Profit/Loss**: Best and worst single trades
- **Profit Factor**: Ratio of total profits to total losses
- **Sharpe Ratio**: Risk-adjusted return metric

### Signal-Specific Metrics
- **Performance by Signal Type**: Consensus, AI, Fibonacci, etc.
- **Performance by Signal Source**: Individual algorithm performance
- **Confidence Correlation**: How confidence relates to success
- **Risk-Reward Analysis**: Actual vs. planned risk/reward ratios

### Risk Metrics
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Average Risk per Trade**: Typical risk exposure
- **Risk-Adjusted Returns**: Performance relative to risk taken
- **Win/Loss Streaks**: Longest winning and losing streaks

## 🌐 WEB DASHBOARD FEATURES

### Real-Time Monitoring
- **Live Performance Stats**: Updated every 5 minutes
- **Active Trades Tracking**: Monitor open positions
- **Signal Performance**: Real-time algorithm comparison

### Interactive Features
- **Refresh Data**: Manual data refresh button
- **Export CSV**: Download performance data
- **Generate Reports**: Create detailed text reports
- **Responsive Design**: Works on desktop and mobile

### Visual Analytics
- **Performance Cards**: Key metrics at a glance
- **Signal Breakdown**: Algorithm performance comparison
- **Recent Trades Table**: Latest trading activity
- **Color-Coded Results**: Green for profits, red for losses

## 📁 DATA EXPORT & REPORTING

### CSV Export
```python
# Export last 30 days to CSV
filename = tracker.export_to_csv("my_performance.csv", days=30)
```

### Performance Reports
```python
# Generate comprehensive report
report = tracker.generate_performance_report(days=30)
print(report)

# Daily summary
summary = integration.generate_daily_summary()
```

### Database Access
The system uses SQLite database with two main tables:
- **trades**: Individual trade records
- **performance_summary**: Daily aggregated statistics

## 🔧 CONFIGURATION OPTIONS

### Database Configuration
```python
tracker = ProfitLossTracker(
    db_path="custom_performance.db"  # Custom database location
)
```

### Integration Configuration
```python
config = {
    'db_path': 'trading_performance.db',
    'auto_report_interval': 12,  # Hours between reports
    'position_size': 1000.0,     # Default position size in USD
    'enable_auto_reporting': True
}

integration = TradingPerformanceIntegration(**config)
```

### Web Dashboard Configuration
```python
run_dashboard(
    host='0.0.0.0',      # Listen on all interfaces
    port=8080,           # Custom port
    debug=False,         # Production mode
    db_path="performance.db"
)
```

## 🧹 MAINTENANCE

### Automatic Cleanup
The system automatically cleans up old data to prevent database bloat:

```python
# Clean up trades older than 90 days
tracker.cleanup_old_data(days_to_keep=90)
```

### Manual Maintenance
```python
# Perform maintenance tasks
integration.cleanup_and_maintain()
```

## 🧪 TESTING & DEMO

### Run Demo
```python
# Test the system with demo data
python profit_loss_tracker.py

# Test integration
python trading_performance_integration.py

# Test web dashboard
python pnl_web_dashboard.py
```

### Demo Data
The system includes demo functions that create sample trades for testing:
- BTC/USDT consensus trade (profitable)
- ETH/USDT AI prediction trade (profitable)
- ADA/USDT Fibonacci trade (loss)

## 🎯 BENEFITS

### For Traders
- **Performance Transparency**: Clear view of what's working
- **Risk Management**: Better understanding of risk exposure
- **Strategy Optimization**: Data-driven strategy improvements
- **Accountability**: Objective performance measurement

### For Developers
- **Easy Integration**: Drop-in compatibility with existing bots
- **Comprehensive Tracking**: All signal types supported
- **Flexible Configuration**: Customizable to specific needs
- **Scalable Architecture**: Handles high-frequency trading

### For Analysis
- **Historical Data**: Complete trade history preservation
- **Export Capabilities**: Data available for external analysis
- **Real-Time Monitoring**: Live performance tracking
- **Automated Reporting**: Regular performance summaries

## 🚀 GETTING STARTED CHECKLIST

- [ ] Install required dependencies
- [ ] Copy P&L tracking files to your bot directory
- [ ] Integrate with your main bot class
- [ ] Add tracking calls to signal processing methods
- [ ] Test with demo data
- [ ] Configure auto-reporting
- [ ] Set up web dashboard (optional)
- [ ] Monitor performance and optimize strategies

## 📞 SUPPORT

For questions or issues with the P&L tracking system:
1. Check the demo functions for usage examples
2. Review the integration instructions
3. Test with demo data first
4. Ensure all dependencies are installed

The system is designed to be robust and handle errors gracefully, but proper integration is key to getting accurate performance data.

---

**🎉 Happy Trading and May Your P&L Always Be Green! 📈**
