#!/usr/bin/env python3
"""
🤖 MAIN BOT WITH ADMIN COMMANDS TEST
====================================

Quick test để kiểm tra main bot với admin commands
"""

import os
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_main_bot_imports():
    """Test main bot imports"""
    print("🤖 === TESTING MAIN BOT IMPORTS ===")
    print("=" * 50)
    
    try:
        print("🔧 Testing main bot imports...")
        
        # Test basic imports
        import main_bot
        print("  ✅ main_bot")
        
        # Test message handler import
        from telegram_message_handler import TelegramMessageHandler
        print("  ✅ telegram_message_handler")
        
        # Test admin systems
        from telegram_member_manager import TelegramMemberManager
        print("  ✅ telegram_member_manager")
        
        from member_admin_commands import MemberAdminCommands
        print("  ✅ member_admin_commands")
        
        from hidden_admin_csv_system import HiddenAdminCSVSystem
        print("  ✅ hidden_admin_csv_system")
        
        import admin_config
        print("  ✅ admin_config")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_admin_config():
    """Test admin configuration"""
    print("\n👑 === TESTING ADMIN CONFIGURATION ===")
    print("=" * 50)
    
    try:
        import admin_config
        
        # Check admin users
        admin_count = len(admin_config.ADMIN_USERS)
        super_admin_count = len(admin_config.SUPER_ADMIN_USERS)
        csv_admin_count = len(admin_config.CSV_EXPORT_ADMIN_USERS)
        
        print(f"👑 Basic Admins: {admin_count}")
        print(f"🔒 Super Admins: {super_admin_count}")
        print(f"📊 CSV Export Admins: {csv_admin_count}")
        
        if admin_count > 0:
            print("✅ Admin users configured")
            
            # Test permissions for first admin
            test_user_id = admin_config.ADMIN_USERS[0]
            permissions = admin_config.get_user_permissions(test_user_id)
            print(f"🧪 User {test_user_id} permissions: {permissions}")
            
            return True
        else:
            print("⚠️ No admin users configured")
            return False
            
    except Exception as e:
        print(f"❌ Admin config test failed: {e}")
        return False

def test_telegram_connection():
    """Test Telegram connection"""
    print("\n📱 === TESTING TELEGRAM CONNECTION ===")
    print("=" * 50)
    
    try:
        from telegram_message_handler import TelegramMessageHandler
        
        # Create mock bot
        class MockBot:
            def __init__(self):
                self.notifier = None
        
        mock_bot = MockBot()
        handler = TelegramMessageHandler(mock_bot)
        
        # Test connection
        print("🔧 Testing Telegram API connection...")
        success = handler.test_connection()
        
        if success:
            print("✅ Telegram API connection successful!")
            return True
        else:
            print("❌ Telegram API connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Telegram connection test failed: {e}")
        return False

def test_bot_initialization():
    """Test bot initialization (without running)"""
    print("\n🤖 === TESTING BOT INITIALIZATION ===")
    print("=" * 50)
    
    try:
        print("🔧 Testing TradingBot class...")
        
        # Import main bot
        import main_bot
        
        # Check if TradingBot class exists
        bot_class = main_bot.TradingBot
        print("  ✅ TradingBot class accessible")
        
        # Check if required methods exist
        required_methods = [
            'process_telegram_message',
            'handle_new_member_join',
            'handle_member_leave',
            'check_member_management_tasks'
        ]
        
        print("🔧 Checking required methods...")
        for method_name in required_methods:
            if hasattr(bot_class, method_name):
                print(f"  ✅ {method_name}")
            else:
                print(f"  ❌ {method_name} - Missing")
        
        print("✅ Bot initialization test completed")
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization test failed: {e}")
        return False

def show_usage_guide():
    """Show usage guide"""
    print("\n📋 === USAGE GUIDE ===")
    print("=" * 50)
    
    print("🚀 TO START THE BOT WITH ADMIN COMMANDS:")
    print()
    print("1️⃣ CONFIGURE ADMIN USERS:")
    print("   • Get your Telegram User ID from @userinfobot")
    print("   • Edit admin_config.py:")
    print("     ADMIN_USERS = [YOUR_USER_ID_HERE]")
    print()
    print("2️⃣ START THE BOT:")
    print("   • Run: python main_bot.py")
    print("   • Bot will start with message polling")
    print("   • Admin commands will be active")
    print()
    print("3️⃣ TEST ADMIN COMMANDS:")
    print("   • Send /help_admin in Telegram")
    print("   • Try /stats for member statistics")
    print("   • Use /export all for CSV export")
    print()
    print("🔒 ADMIN COMMANDS:")
    print("   👑 Basic: /help_admin, /stats, /members, /donation, /extend")
    print("   🔒 Hidden: /export all, /export group, /export new")
    print("   📊 Direct: /admin_export_all, /admin_export_group")
    print()
    print("🤖 USER COMMANDS:")
    print("   📱 Basic: /start, /help, /status, /donate")
    print()
    print("👥 MEMBER MANAGEMENT:")
    print("   • Auto welcome new members")
    print("   • 60-day trial system")
    print("   • QR code donation")
    print("   • Expiration warnings")

def main():
    """Main test function"""
    print("🤖 === MAIN BOT WITH ADMIN COMMANDS TEST ===")
    print("🎯 Testing main bot integration with admin commands")
    print()
    
    # Run tests
    test1 = test_main_bot_imports()
    test2 = test_admin_config()
    test3 = test_telegram_connection()
    test4 = test_bot_initialization()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Main Bot Imports", test1),
        ("Admin Configuration", test2),
        ("Telegram Connection", test3),
        ("Bot Initialization", test4)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL RESULT: {passed}/{total} tests passed")
    
    # Show usage guide
    show_usage_guide()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ MAIN BOT WITH ADMIN COMMANDS STATUS:")
        print("  • Main bot imports: SUCCESSFUL")
        print("  • Admin configuration: READY")
        print("  • Telegram connection: ACTIVE")
        print("  • Bot initialization: FUNCTIONAL")
        print("  • Message handler: INTEGRATED")
        print("  • Admin commands: ENABLED")
        print("  • Hidden commands: SECURED")
        print("  • Member management: AUTOMATED")
        print("\n🚀 READY TO START BOT:")
        print("  python main_bot.py")
        print("\n💡 ADMIN COMMANDS WILL BE ACTIVE!")
        print("  • Basic admin commands: /help_admin, /stats")
        print("  • Hidden export commands: /export all")
        print("  • Member management: Automatic")
        print("  • QR code system: Auto-send")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the configuration and try again.")
        
        if not test2:  # Admin config failed
            print("\n🔧 QUICK FIX:")
            print("  1. Get your Telegram User ID from @userinfobot")
            print("  2. Add it to admin_config.py:")
            print("     ADMIN_USERS = [YOUR_USER_ID_HERE]")
            print("  3. Run this test again")
        
        if not test3:  # Telegram connection failed
            print("\n🔧 TELEGRAM FIX:")
            print("  1. Check TELEGRAM_BOT_TOKEN in .env")
            print("  2. Verify bot token is correct")
            print("  3. Check internet connection")

if __name__ == "__main__":
    main()
