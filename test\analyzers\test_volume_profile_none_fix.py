#!/usr/bin/env python3
"""
🧪 Test script để kiểm tra Volume Profile Analyzer không trả về NONE
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def create_test_data():
    """Tạo dữ liệu test realistic"""
    try:
        # Create 200 candles of realistic data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=200), 
                             end=datetime.now(), freq='1H')
        
        # Generate realistic ZK/USDT-like data
        base_price = 0.15  # ZK price around $0.15
        prices = []
        volumes = []
        
        for i in range(len(dates)):
            # Add some trend and volatility
            trend_factor = 1 + (i * 0.0001)  # Slight uptrend
            volatility = np.random.normal(0, 0.05)  # 5% volatility
            
            if i == 0:
                price = base_price
            else:
                price = prices[-1] * trend_factor * (1 + volatility)
            
            prices.append(max(0.01, price))  # Prevent negative prices
            
            # Volume with some spikes
            base_volume = np.random.uniform(100000, 500000)
            if i % 10 == 0:  # Volume spike every 10 candles
                base_volume *= 2
            volumes.append(base_volume)
        
        # Create OHLCV data
        data = []
        for i, (date, price, volume) in enumerate(zip(dates, prices, volumes)):
            volatility = abs(np.random.normal(0, 0.03))
            
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            open_price = price * (1 + np.random.normal(0, 0.01))
            close_price = price * (1 + np.random.normal(0, 0.01))
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ Created test data: {len(df)} candles")
        print(f"  📊 Price range: ${df['close'].min():.4f} - ${df['close'].max():.4f}")
        print(f"  📊 Volume range: {df['volume'].min():,.0f} - {df['volume'].max():,.0f}")
        print(f"  📊 Total volume: {df['volume'].sum():,.0f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        return None

def test_volume_profile_analyzer():
    """Test Volume Profile Analyzer để đảm bảo không trả về NONE"""
    print("📊 TESTING VOLUME PROFILE ANALYZER - NONE SIGNAL FIX")
    print("=" * 70)
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        # Create analyzer
        analyzer = VolumeProfileAnalyzer(
            price_bins=50,
            value_area_percentage=70.0,
            min_data_points=100
        )
        
        # Create test data
        df = create_test_data()
        if df is None:
            return False
        
        print(f"\n🧪 Testing Volume Profile analysis...")
        print(f"  📊 Data points: {len(df)}")
        print(f"  💰 Current price: ${df['close'].iloc[-1]:.6f}")
        
        # Test multiple scenarios
        test_scenarios = [
            ("Full data (200 candles)", df),
            ("Reduced data (150 candles)", df.tail(150)),
            ("Minimum data (100 candles)", df.tail(100)),
            ("Small data (50 candles)", df.tail(50))
        ]
        
        all_passed = True
        
        for scenario_name, test_df in test_scenarios:
            print(f"\n🔍 Testing scenario: {scenario_name}")
            print(f"  📊 Candles: {len(test_df)}")
            
            try:
                # Run analysis
                result = analyzer.analyze_volume_profile(test_df, lookback_periods=min(200, len(test_df)))
                
                # Check result
                if result and result.get("status") == "success":
                    signals = result.get("signals", {})
                    primary_signal = signals.get("primary_signal", "NONE")
                    confidence = signals.get("confidence", 0)
                    
                    print(f"  📊 Analysis status: {result.get('status')}")
                    print(f"  🎯 Primary signal: {primary_signal}")
                    print(f"  💪 Confidence: {confidence:.3f}")
                    
                    # Check if signal is valid
                    if primary_signal in ["BUY", "SELL"]:
                        print(f"  ✅ PASSED: Valid signal generated")
                    else:
                        print(f"  ❌ FAILED: Invalid signal '{primary_signal}'")
                        all_passed = False
                        
                        # Debug information
                        print(f"    🔍 Debug info:")
                        print(f"      - Volume profile: {result.get('volume_profile', {}).get('total_volume', 0):,.0f}")
                        print(f"      - VPOC: {result.get('vpoc', {}).get('price', 0):.6f}")
                        print(f"      - Value area: {result.get('value_area', {})}")
                        
                elif result and result.get("status") == "error":
                    print(f"  ❌ FAILED: Analysis error - {result.get('message', 'Unknown error')}")
                    all_passed = False
                else:
                    print(f"  ❌ FAILED: No result returned")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ FAILED: Exception - {e}")
                all_passed = False
                import traceback
                traceback.print_exc()
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Cannot import VolumeProfileAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Volume Profile analyzer: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases để đảm bảo fallbacks hoạt động"""
    print("\n🔄 TESTING EDGE CASES")
    print("=" * 70)
    
    try:
        from volume_profile_analyzer import VolumeProfileAnalyzer
        
        analyzer = VolumeProfileAnalyzer()
        
        # Test case 1: Very small data
        print(f"\n🧪 Test case 1: Very small data (10 candles)")
        small_df = create_test_data().tail(10)
        
        try:
            result = analyzer.analyze_volume_profile(small_df, lookback_periods=10)
            if result and result.get("status") == "error":
                print(f"  ✅ PASSED: Correctly rejected small data")
            else:
                print(f"  ❌ FAILED: Should reject small data")
                return False
        except Exception as e:
            print(f"  ✅ PASSED: Exception handling for small data - {e}")
        
        # Test case 2: Zero volume data
        print(f"\n🧪 Test case 2: Zero volume data")
        zero_vol_df = create_test_data().copy()
        zero_vol_df['volume'] = 0
        
        try:
            result = analyzer.analyze_volume_profile(zero_vol_df, lookback_periods=200)
            if result and result.get("status") == "success":
                signals = result.get("signals", {})
                primary_signal = signals.get("primary_signal", "NONE")
                
                if primary_signal in ["BUY", "SELL"]:
                    print(f"  ✅ PASSED: Generated signal despite zero volume - {primary_signal}")
                else:
                    print(f"  ❌ FAILED: No signal generated for zero volume")
                    return False
            else:
                print(f"  ❌ FAILED: Analysis failed for zero volume")
                return False
        except Exception as e:
            print(f"  ❌ FAILED: Exception with zero volume - {e}")
            return False
        
        # Test case 3: Extreme price volatility
        print(f"\n🧪 Test case 3: Extreme price volatility")
        volatile_df = create_test_data().copy()
        # Add extreme price swings
        for i in range(len(volatile_df)):
            if i % 5 == 0:  # Every 5th candle
                volatile_df.iloc[i, volatile_df.columns.get_loc('high')] *= 1.5
                volatile_df.iloc[i, volatile_df.columns.get_loc('low')] *= 0.5
        
        try:
            result = analyzer.analyze_volume_profile(volatile_df, lookback_periods=200)
            if result and result.get("status") == "success":
                signals = result.get("signals", {})
                primary_signal = signals.get("primary_signal", "NONE")
                
                if primary_signal in ["BUY", "SELL"]:
                    print(f"  ✅ PASSED: Handled extreme volatility - {primary_signal}")
                else:
                    print(f"  ❌ FAILED: No signal for extreme volatility")
                    return False
            else:
                print(f"  ❌ FAILED: Analysis failed for extreme volatility")
                return False
        except Exception as e:
            print(f"  ❌ FAILED: Exception with extreme volatility - {e}")
            return False
        
        print(f"\n✅ All edge cases passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in edge case testing: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 VOLUME PROFILE NONE SIGNAL FIX TEST")
    print("=" * 80)
    print(f"⏰ Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Basic functionality
    basic_result = test_volume_profile_analyzer()
    
    # Test 2: Edge cases
    edge_result = test_edge_cases()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 VOLUME PROFILE NONE SIGNAL FIX TEST RESULTS")
    print("=" * 80)
    
    tests = [
        ("Basic Functionality", basic_result),
        ("Edge Cases", edge_result)
    ]
    
    all_passed = True
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Volume Profile analyzer never returns NONE signals")
        print("✅ Emergency fallbacks working correctly")
        print("✅ Edge cases handled properly")
        print("✅ Ready for consensus analysis integration")
        
        print(f"\n🔧 Expected behavior:")
        print(f"  • Volume Profile: Always returns BUY or SELL")
        print(f"  • Consensus analysis will receive valid signals")
        print(f"  • No more 'signal=NONE' debug messages")
        print(f"  • Higher consensus quality and confidence")
    else:
        print("❌ SOME TESTS FAILED")
        print("Volume Profile analyzer may still return NONE signals")
        print("Check the fallback mechanisms")
    
    print(f"\n⏰ Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
