#!/usr/bin/env python3
"""
🔧 SIGNAL STRENGTH 65% VERIFICATION TEST
Test that consensus signals are accepted when signal strength >= 65%
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_strength_configuration():
    """Test that signal strength threshold is properly configured to 65%."""
    print("🔧 TESTING SIGNAL STRENGTH CONFIGURATION")
    print("=" * 50)
    
    try:
        import main_bot
        
        print(f"📊 Current Signal Strength Configuration:")
        print(f"  🎯 MIN_SIGNAL_STRENGTH: {main_bot.MIN_SIGNAL_STRENGTH*100:.1f}%")
        
        # Check if threshold is at expected level
        expected_threshold = 0.65  # 65%
        
        if main_bot.MIN_SIGNAL_STRENGTH == expected_threshold:
            print(f"✅ Signal strength threshold properly set: {main_bot.MIN_SIGNAL_STRENGTH*100:.1f}% = {expected_threshold*100:.1f}%")
            return True
        else:
            print(f"❌ Signal strength threshold incorrect: {main_bot.MIN_SIGNAL_STRENGTH*100:.1f}% ≠ {expected_threshold*100:.1f}%")
            return False
        
    except Exception as e:
        print(f"❌ Signal strength configuration test failed: {e}")
        return False

def test_signal_strength_logic():
    """Test 65% signal strength logic for consensus signals."""
    print("\n🔍 TESTING SIGNAL STRENGTH LOGIC")
    print("=" * 50)
    
    try:
        import main_bot
        
        # Test scenarios with 65% threshold
        test_cases = [
            {"strength": 0.417, "should_pass": False, "description": "41.7% - Below 65% threshold (current ROSE case)"},
            {"strength": 0.50, "should_pass": False, "description": "50% - Below 65% threshold"},
            {"strength": 0.60, "should_pass": False, "description": "60% - Below 65% threshold"},
            {"strength": 0.64, "should_pass": False, "description": "64% - Just below 65% threshold"},
            {"strength": 0.65, "should_pass": True, "description": "65% - At threshold"},
            {"strength": 0.70, "should_pass": True, "description": "70% - Above threshold"},
            {"strength": 0.75, "should_pass": True, "description": "75% - Well above threshold"},
            {"strength": 0.80, "should_pass": True, "description": "80% - Well above threshold"}
        ]
        
        all_passed = True
        
        print("🧪 Signal Strength Logic Tests:")
        for i, case in enumerate(test_cases, 1):
            strength = case["strength"]
            should_pass = case["should_pass"]
            description = case["description"]
            
            # Test signal strength logic
            would_pass = strength >= main_bot.MIN_SIGNAL_STRENGTH
            
            print(f"  Test {i}: {description}")
            print(f"    📊 Signal Strength: {strength:.1%}")
            print(f"    🎯 Threshold: {main_bot.MIN_SIGNAL_STRENGTH:.1%}")
            print(f"    🤔 Expected: {'PASS' if should_pass else 'FAIL'}")
            print(f"    🔍 Actual: {'PASS' if would_pass else 'FAIL'}")
            
            if would_pass == should_pass:
                print(f"    ✅ CORRECT")
            else:
                print(f"    ❌ INCORRECT")
                all_passed = False
        
        if all_passed:
            print("\n✅ ALL SIGNAL STRENGTH LOGIC TESTS PASSED")
        else:
            print("\n❌ SOME SIGNAL STRENGTH LOGIC TESTS FAILED")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Signal strength logic test failed: {e}")
        return False

def test_consensus_impact():
    """Test impact on consensus signal acceptance."""
    print("\n📊 TESTING CONSENSUS IMPACT")
    print("=" * 50)
    
    try:
        import main_bot
        
        # Simulate the ROSE/USDT case from the log
        rose_case = {
            "signal": "BUY",
            "confidence": 0.25,  # 25%
            "consensus_score": 0.25,  # 25%
            "ai_confidence": 0.585,  # 58.5%
            "algorithm_count": 4,
            "signal_strength": 0.417  # 41.7%
        }
        
        print("🌹 ROSE/USDT Case Analysis:")
        print(f"  📊 Signal: {rose_case['signal']}")
        print(f"  📊 Confidence: {rose_case['confidence']:.1%}")
        print(f"  📊 Consensus Score: {rose_case['consensus_score']:.1%}")
        print(f"  📊 AI Confidence: {rose_case['ai_confidence']:.1%}")
        print(f"  📊 Algorithm Count: {rose_case['algorithm_count']}")
        print(f"  📊 Signal Strength: {rose_case['signal_strength']:.1%}")
        
        # Check each requirement
        print(f"\n🎯 Quality Requirements Check:")
        
        # 1. Confidence (80% required)
        conf_pass = rose_case['confidence'] >= main_bot.MIN_CONFIDENCE_THRESHOLD
        print(f"  📊 Confidence: {rose_case['confidence']:.1%} vs {main_bot.MIN_CONFIDENCE_THRESHOLD:.1%} → {'✅ PASS' if conf_pass else '❌ FAIL'}")
        
        # 2. Consensus Score (40% required)
        score_pass = rose_case['consensus_score'] >= main_bot.MIN_CONSENSUS_SCORE
        print(f"  📊 Consensus Score: {rose_case['consensus_score']:.1%} vs {main_bot.MIN_CONSENSUS_SCORE:.1%} → {'✅ PASS' if score_pass else '❌ FAIL'}")
        
        # 3. AI Confidence (40% required)
        ai_pass = rose_case['ai_confidence'] >= main_bot.MIN_AI_CONFIDENCE
        print(f"  📊 AI Confidence: {rose_case['ai_confidence']:.1%} vs {main_bot.MIN_AI_CONFIDENCE:.1%} → {'✅ PASS' if ai_pass else '❌ FAIL'}")
        
        # 4. Algorithm Count (3 required)
        algo_pass = rose_case['algorithm_count'] >= main_bot.MIN_ALGORITHMS_REQUIRED
        print(f"  📊 Algorithm Count: {rose_case['algorithm_count']} vs {main_bot.MIN_ALGORITHMS_REQUIRED} → {'✅ PASS' if algo_pass else '❌ FAIL'}")
        
        # 5. Signal Strength (65% required with new threshold)
        strength_pass = rose_case['signal_strength'] >= main_bot.MIN_SIGNAL_STRENGTH
        print(f"  📊 Signal Strength: {rose_case['signal_strength']:.1%} vs {main_bot.MIN_SIGNAL_STRENGTH:.1%} → {'✅ PASS' if strength_pass else '❌ FAIL'}")
        
        # Overall result
        all_pass = conf_pass and score_pass and ai_pass and algo_pass and strength_pass
        print(f"\n🎯 Overall Result: {'✅ SIGNAL ACCEPTED' if all_pass else '❌ SIGNAL REJECTED'}")
        
        # Show what changed
        print(f"\n📈 Impact of 65% Signal Strength Threshold:")
        old_strength_pass = rose_case['signal_strength'] >= 0.75  # Old 75% threshold
        new_strength_pass = rose_case['signal_strength'] >= 0.65  # New 65% threshold
        
        print(f"  📊 With 75% threshold: {rose_case['signal_strength']:.1%} → {'✅ PASS' if old_strength_pass else '❌ FAIL'}")
        print(f"  📊 With 65% threshold: {rose_case['signal_strength']:.1%} → {'✅ PASS' if new_strength_pass else '❌ FAIL'}")
        
        if not old_strength_pass and not new_strength_pass:
            print(f"  📊 Result: Still rejected (signal strength too low)")
        elif not old_strength_pass and new_strength_pass:
            print(f"  📊 Result: ✅ Now passes signal strength check!")
        else:
            print(f"  📊 Result: No change (was already passing)")
        
        return True
        
    except Exception as e:
        print(f"❌ Consensus impact test failed: {e}")
        return False

def test_threshold_comparison():
    """Compare old vs new signal strength threshold behavior."""
    print("\n📊 TESTING THRESHOLD COMPARISON")
    print("=" * 50)
    
    try:
        import main_bot
        
        # Test cases that show the difference between 75% and 65%
        comparison_cases = [
            {"strength": 0.417, "old_75": False, "new_65": False, "description": "41.7% - Still rejected"},
            {"strength": 0.60, "old_75": False, "new_65": False, "description": "60% - Still rejected"},
            {"strength": 0.65, "old_75": False, "new_65": True, "description": "65% - Now accepted (was rejected)"},
            {"strength": 0.70, "old_75": False, "new_65": True, "description": "70% - Now accepted (was rejected)"},
            {"strength": 0.75, "old_75": True, "new_65": True, "description": "75% - Still accepted"},
            {"strength": 0.80, "old_75": True, "new_65": True, "description": "80% - Still accepted"}
        ]
        
        print("📈 Signal Strength Threshold Impact Analysis:")
        print("  Strength | Old (75%) | New (65%) | Change")
        print("  ---------|-----------|-----------|--------")
        
        newly_allowed_count = 0
        
        for case in comparison_cases:
            strength = case["strength"]
            old_result = case["old_75"]
            new_result = case["new_65"]
            description = case["description"]
            
            # Check actual new threshold
            actual_new = strength >= main_bot.MIN_SIGNAL_STRENGTH
            
            old_status = "PASS" if old_result else "FAIL"
            new_status = "PASS" if actual_new else "FAIL"
            
            if not old_result and actual_new:
                change = "✅ NEW"
                newly_allowed_count += 1
            elif old_result and actual_new:
                change = "✅ SAME"
            else:
                change = "🚫 SAME"
            
            print(f"  {strength:>8.1%} | {old_status:>9} | {new_status:>9} | {change}")
        
        print(f"\n📊 Summary:")
        print(f"  🎯 New threshold: {main_bot.MIN_SIGNAL_STRENGTH:.1%}")
        print(f"  📈 Newly allowed signals: {newly_allowed_count}")
        print(f"  📊 Quality range: 65% - 100%")
        print(f"  ⚖️ Balance: More consensus signals while maintaining quality")
        
        return True
        
    except Exception as e:
        print(f"❌ Threshold comparison test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 SIGNAL STRENGTH 65% VERIFICATION TEST")
    print("=" * 60)
    
    # Test signal strength configuration
    config_ok = test_signal_strength_configuration()
    
    # Test signal strength logic
    logic_ok = test_signal_strength_logic()
    
    # Test consensus impact
    impact_ok = test_consensus_impact()
    
    # Test threshold comparison
    comparison_ok = test_threshold_comparison()
    
    # Overall results
    print("\n" + "=" * 60)
    print("🎯 SIGNAL STRENGTH 65% VERIFICATION RESULTS")
    print("=" * 60)
    
    print(f"⚙️ Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"🔍 Logic Tests: {'✅ PASS' if logic_ok else '❌ FAIL'}")
    print(f"📊 Impact Analysis: {'✅ PASS' if impact_ok else '❌ FAIL'}")
    print(f"📈 Comparison: {'✅ PASS' if comparison_ok else '❌ FAIL'}")
    
    overall_success = config_ok and logic_ok and impact_ok and comparison_ok
    
    if overall_success:
        print("\n🎉 SIGNAL STRENGTH 65% SUCCESSFULLY CONFIGURED!")
        print("✅ Consensus signals will be accepted when signal strength >= 65%")
        print("✅ More consensus signals while maintaining quality")
        print("✅ Better balance for consensus analysis")
        
        print("\n📊 Expected behavior with 65% signal strength threshold:")
        print("  🚫 41.7% signal strength → REJECTED (ROSE case still rejected)")
        print("  🚫 60% signal strength → REJECTED")
        print("  ✅ 65% signal strength → ACCEPTED")
        print("  ✅ 70% signal strength → ACCEPTED")
        print("  ✅ 75% signal strength → ACCEPTED")
        
        print("\n🎯 Benefits of 65% signal strength threshold:")
        print("  📈 More consensus signals accepted")
        print("  ⚖️ Balanced quality vs quantity")
        print("  🎯 Still filters out weak signals")
        print("  📊 Better consensus signal flow")
        
    else:
        print("\n⚠️ SIGNAL STRENGTH 65% CONFIGURATION FAILED")
        if not config_ok:
            print("🔧 Fix signal strength configuration")
        if not logic_ok:
            print("🔧 Fix signal strength logic")
        if not impact_ok:
            print("🔧 Fix impact analysis")
        if not comparison_ok:
            print("🔧 Fix threshold comparison")
    
    print(f"\n🎯 Final Result: {'SUCCESS' if overall_success else 'NEEDS_ATTENTION'}")
    sys.exit(0 if overall_success else 1)
