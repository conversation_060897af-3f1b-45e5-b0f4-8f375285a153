#!/usr/bin/env python3
"""
🔧 SIMPLE ADMIN TEST
====================

Simple test để kiểm tra admin commands hoạt động
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_admin_config():
    """Test admin configuration"""
    print("👑 === TESTING ADMIN CONFIG ===")
    
    try:
        import admin_config
        
        admin_users = admin_config.ADMIN_USERS
        super_admin_users = admin_config.SUPER_ADMIN_USERS
        csv_export_users = admin_config.CSV_EXPORT_ADMIN_USERS
        
        print(f"👑 Admin users: {admin_users}")
        print(f"🔒 Super admin users: {super_admin_users}")
        print(f"📊 CSV export users: {csv_export_users}")
        
        if len(admin_users) > 0:
            test_user_id = admin_users[0]
            print(f"\n🧪 Testing permissions for user {test_user_id}:")
            print(f"  - Is admin: {admin_config.is_admin(test_user_id)}")
            print(f"  - Is super admin: {admin_config.is_super_admin(test_user_id)}")
            print(f"  - Has CSV export: {admin_config.is_csv_export_admin(test_user_id)}")
            return True
        else:
            print("❌ No admin users configured")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_telegram_bot():
    """Test Telegram bot connection"""
    print("\n📱 === TESTING TELEGRAM BOT ===")
    
    try:
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        if not bot_token:
            print("❌ TELEGRAM_BOT_TOKEN missing from .env")
            return False
        
        print(f"🤖 Bot token: {bot_token[:10]}...")
        
        # Test API call
        import requests
        api_url = f"https://api.telegram.org/bot{bot_token}/getMe"
        
        response = requests.get(api_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                bot_info = data.get('result', {})
                print(f"✅ Bot connected: {bot_info.get('first_name')} (@{bot_info.get('username')})")
                return True
        
        print("❌ Bot connection failed")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_admin_commands():
    """Test admin commands functionality"""
    print("\n🔧 === TESTING ADMIN COMMANDS ===")
    
    try:
        from member_admin_commands import MemberAdminCommands
        
        # Create mock bot
        class MockBot:
            def __init__(self):
                self.notifier = None
                self.member_manager = None
        
        mock_bot = MockBot()
        admin_commands = MemberAdminCommands(mock_bot)
        
        print("✅ MemberAdminCommands initialized")
        
        # Test command processing
        test_user_id = 6228875204  # Known admin
        test_command = "/help_admin"
        test_chat_id = "-1002301937119"
        
        print(f"🧪 Testing command processing:")
        print(f"   User: {test_user_id}")
        print(f"   Command: {test_command}")
        
        # This should work for admin
        result = admin_commands.process_admin_command(test_command, test_user_id, test_chat_id)
        print(f"   Result: {'✅ PROCESSED' if result else '❌ NOT PROCESSED'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_solution():
    """Show solution for admin commands"""
    print("\n💡 === SOLUTION ===")
    print("=" * 50)
    
    print("🔧 TO MAKE ADMIN COMMANDS WORK:")
    print()
    print("1️⃣ GET YOUR TELEGRAM USER ID:")
    print("   • Message @userinfobot on Telegram")
    print("   • Send any message")
    print("   • Copy your numeric User ID")
    print()
    print("2️⃣ ADD YOUR USER ID TO ADMIN_CONFIG.PY:")
    print("   • Open admin_config.py")
    print("   • Add your User ID to ADMIN_USERS:")
    print("     ADMIN_USERS = [6228875204, YOUR_USER_ID_HERE]")
    print()
    print("3️⃣ START THE BOT WITH MESSAGE POLLING:")
    print("   • Run: python main_bot.py")
    print("   • Look for: 'Telegram message polling started'")
    print("   • Bot must be running to receive commands")
    print()
    print("4️⃣ TEST ADMIN COMMANDS IN TELEGRAM:")
    print("   • Send /help_admin to the bot")
    print("   • Try /stats for member statistics")
    print("   • Use /donation for QR code")
    print()
    print("🔒 ADMIN COMMANDS AVAILABLE:")
    print("   /help_admin     - Admin help menu")
    print("   /stats          - Member statistics")
    print("   /members        - Recent members")
    print("   /donation       - Send QR code")
    print("   /extend USER_ID CHAT_ID DAYS - Extend trial")
    print()
    print("🔒 HIDDEN EXPORT COMMANDS (Super Admin):")
    print("   /export all     - Export all members")
    print("   /export group CHAT_ID - Export group")
    print("   /export new     - Export new members")
    print()
    print("⚠️ IMPORTANT:")
    print("   • Bot MUST be running (python main_bot.py)")
    print("   • Message polling MUST be active")
    print("   • Your User ID MUST be in admin_config.py")
    print("   • Bot MUST have admin rights in groups")

def main():
    """Main test function"""
    print("🔧 === SIMPLE ADMIN TEST ===")
    print("🎯 Testing admin commands setup")
    print()
    
    # Run tests
    test1 = test_admin_config()
    test2 = test_telegram_bot()
    test3 = test_admin_commands()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Admin Configuration", test1),
        ("Telegram Bot", test2),
        ("Admin Commands", test3)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed")
    
    # Show solution
    show_solution()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ ADMIN SYSTEM IS READY!")
        print("  • Admin config: VALID")
        print("  • Telegram bot: CONNECTED")
        print("  • Admin commands: FUNCTIONAL")
        print("\n🚀 NEXT STEP:")
        print("  Start the bot: python main_bot.py")
        print("  Then test commands in Telegram!")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please fix the issues and try again.")
        
        if not test1:
            print("\n🔧 FIX ADMIN CONFIG:")
            print("  Add your User ID to admin_config.py")
        
        if not test2:
            print("\n🔧 FIX TELEGRAM BOT:")
            print("  Check TELEGRAM_BOT_TOKEN in .env")

if __name__ == "__main__":
    main()
