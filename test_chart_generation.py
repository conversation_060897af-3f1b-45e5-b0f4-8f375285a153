#!/usr/bin/env python3
"""
🧪 TEST CHART GENERATION SYSTEM
===============================

Test script để kiểm tra hệ thống gửi ảnh kèm thông tin tín hiệu chi tiết.
Kiểm tra toàn bộ quy trình từ tạo chart đến gửi qua Telegram.

Features:
- Test chart generator availability
- Test OHLCV data creation
- Test chart generation methods
- Test Telegram photo sending
- Test cleanup and error handling
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime
import traceback

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv("E:/BOT-2/.env")
    print("✅ Environment variables loaded")
except ImportError:
    print("⚠️ python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"⚠️ Error loading environment variables: {e}")

def test_chart_generation_system():
    """🧪 Test comprehensive chart generation system."""
    try:
        print(f"🧪 Testing comprehensive chart generation system...")
        print(f"=" * 60)
        
        # Import main bot
        from main_bot import TradingBot
        
        # Initialize bot for testing
        print(f"  🤖 Initializing TradingBot...")
        bot = TradingBot()
        print(f"  ✅ TradingBot initialized successfully")
        
        # Test 1: Check chart generator availability
        print(f"\n  🔍 Test 1: Chart generator availability")
        print(f"    - hasattr(bot, 'chart_generator'): {hasattr(bot, 'chart_generator')}")
        print(f"    - chart_generator is not None: {getattr(bot, 'chart_generator', None) is not None}")
        print(f"    - chart_generator type: {type(getattr(bot, 'chart_generator', None))}")
        
        if not hasattr(bot, 'chart_generator') or not bot.chart_generator:
            print(f"    ❌ Chart generator not available, creating fallback...")
            bot.chart_generator = bot.create_enhanced_fallback_chart_generator()
            if not bot.chart_generator:
                print(f"    ❌ Failed to create fallback chart generator")
                return False
            print(f"    ✅ Fallback chart generator created")
        else:
            print(f"    ✅ Chart generator is available")
        
        # Test 2: Create test OHLCV data
        print(f"\n  📊 Test 2: Creating test OHLCV data")
        
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
        base_price = 50000.0
        price_changes = np.random.normal(0, 100, 100)
        prices = base_price + np.cumsum(price_changes)
        
        test_ohlcv = pd.DataFrame({
            'timestamp': dates,
            'open': prices + np.random.uniform(-50, 50, 100),
            'high': prices + np.random.uniform(50, 150, 100),
            'low': prices - np.random.uniform(50, 150, 100),
            'close': prices,
            'volume': np.random.uniform(1000, 10000, 100)
        })
        
        # Ensure OHLC consistency
        test_ohlcv['high'] = np.maximum(test_ohlcv['high'], test_ohlcv[['open', 'close']].max(axis=1))
        test_ohlcv['low'] = np.minimum(test_ohlcv['low'], test_ohlcv[['open', 'close']].min(axis=1))
        
        print(f"    ✅ Test OHLCV data created: {len(test_ohlcv)} rows")
        print(f"    📊 Price range: {test_ohlcv['low'].min():.2f} - {test_ohlcv['high'].max():.2f}")
        
        # Test 3: Test chart generation methods
        print(f"\n  🎨 Test 3: Testing chart generation methods")
        test_coin = "BTCUSDT"
        current_price = test_ohlcv['close'].iloc[-1]
        
        print(f"    🪙 Test coin: {test_coin}")
        print(f"    💰 Current price: {current_price:.2f}")
        
        # Test signal data
        test_signal_data = {
            "signal": "BUY",
            "confidence": 0.85,
            "entry": current_price,
            "take_profit": current_price * 1.05,
            "stop_loss": current_price * 0.95
        }
        
        # Test consensus data
        test_consensus_data = {
            "consensus_score": 0.8,
            "agreement_level": "HIGH",
            "participating_algorithms": ["Volume Profile", "Point & Figure", "AI Ensemble"]
        }
        
        print(f"    📊 Signal data: {test_signal_data['signal']} (conf: {test_signal_data['confidence']*100:.1f}%)")
        print(f"    🎯 Consensus score: {test_consensus_data['consensus_score']*100:.1f}%")
        
        # Test enhanced signal chart
        try:
            print(f"\n    🎯 Testing enhanced signal chart generation...")
            chart_path = bot.chart_generator.generate_enhanced_signal_chart(
                test_coin, test_ohlcv, test_signal_data, test_consensus_data, {}
            )
            
            if chart_path:
                print(f"    ✅ Enhanced signal chart generated: {chart_path}")
                
                # Check if file exists and get size
                if os.path.exists(chart_path):
                    file_size = os.path.getsize(chart_path) / 1024  # KB
                    print(f"    📁 Chart file size: {file_size:.1f} KB")
                else:
                    print(f"    ❌ Chart file does not exist: {chart_path}")
                    return False
                
                # Test 4: Test sending the chart
                print(f"\n  📤 Test 4: Testing chart send to Telegram")
                
                if hasattr(bot, 'notifier') and bot.notifier:
                    print(f"    📱 Notifier available, testing send...")
                    
                    caption = f"""🧪 <b>CHART GENERATION TEST - {test_coin}</b>

📊 <b>SIGNAL INFORMATION</b>
├ 🎯 Signal: <b>{test_signal_data['signal']}</b>
├ 💰 Entry Price: <code>{current_price:.2f}</code>
├ 📈 Take Profit: <code>{test_signal_data['take_profit']:.2f}</code>
├ 📉 Stop Loss: <code>{test_signal_data['stop_loss']:.2f}</code>
└ 💪 Confidence: <code>{test_signal_data['confidence']*100:.1f}%</code>

🎯 <b>CONSENSUS DATA</b>
├ 📊 Consensus Score: <code>{test_consensus_data['consensus_score']*100:.1f}%</code>
├ 🤝 Agreement Level: <b>{test_consensus_data['agreement_level']}</b>
└ 🧠 Algorithms: <code>{len(test_consensus_data['participating_algorithms'])}</code>

⏰ <b>Test Time:</b> <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>

✅ <b>If you see this chart with detailed information, the system is working perfectly!</b>"""

                    print(f"    📝 Caption length: {len(caption)} characters")
                    
                    send_success = bot.notifier.send_photo(
                        photo_path=chart_path,
                        caption=caption,
                        parse_mode="HTML"
                    )
                    
                    if send_success:
                        print(f"    ✅ Chart sent successfully to Telegram!")
                        print(f"    🎉 TEST PASSED: Chart generation and sending works!")
                    else:
                        print(f"    ❌ Chart send failed")
                        print(f"    ⚠️ Check Telegram bot token and chat ID")
                        return False
                else:
                    print(f"    ⚠️ Notifier not available, skipping send test")
                    print(f"    📊 Chart generation test: ✅ PASSED")
                    print(f"    📤 Telegram send test: ⚠️ SKIPPED")
                
                # Test 5: Cleanup
                print(f"\n  🧹 Test 5: Testing cleanup")
                try:
                    if os.path.exists(chart_path):
                        os.remove(chart_path)
                        print(f"    ✅ Test chart cleaned up successfully")
                    else:
                        print(f"    ⚠️ Chart file already removed")
                except Exception as cleanup_error:
                    print(f"    ⚠️ Cleanup warning: {cleanup_error}")
                
                print(f"\n🎉 ALL TESTS PASSED!")
                print(f"✅ Chart generation system is working correctly")
                print(f"✅ Telegram photo sending is functional")
                print(f"✅ System ready for production use")
                return True
                
            else:
                print(f"    ❌ Enhanced signal chart generation returned None")
                print(f"    🔍 Check chart generator implementation")
                return False
                
        except Exception as chart_error:
            print(f"    ❌ Enhanced signal chart generation failed: {chart_error}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Chart generation system test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test execution."""
    print("🧪 CHART GENERATION SYSTEM TEST")
    print("=" * 60)
    print("🎯 Purpose: Test chart generation and Telegram photo sending")
    print("📊 Features: Enhanced signal charts with detailed information")
    print("📱 Integration: Full Telegram notification system")
    print("=" * 60)
    
    try:
        success = test_chart_generation_system()
        
        if success:
            print(f"\n🏆 TEST RESULT: SUCCESS")
            print(f"✅ Chart generation system is fully functional")
            print(f"✅ Ready for production use")
            return 0
        else:
            print(f"\n❌ TEST RESULT: FAILED")
            print(f"🔧 Chart generation system needs attention")
            print(f"💡 Check the error messages above for details")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n⏹️ Test interrupted by user")
        return 0
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        traceback.print_exc()
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
