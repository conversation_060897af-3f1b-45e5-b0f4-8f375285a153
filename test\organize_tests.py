#!/usr/bin/env python3
"""
🗂️ ORGANIZE TESTS - Tự động tổ chức các file test v<PERSON>o đúng thư mục
"""

import os
import shutil
from pathlib import Path

def organize_test_files():
    """Tổ chức các file test v<PERSON>o đúng thư mục con"""
    
    # Mapping file patterns to directories
    file_mappings = {
        'analyzers': [
            'test_analyzer_', 'test_ai_', 'test_fibonacci_', 'test_volume_profile_',
            'test_orderbook_', 'test_point_figure_', 'test_fourier_', 'test_dump_detector_',
            'test_volume_pattern_', 'test_early_warning'
        ],
        'charts': [
            'test_chart_', 'test_beautiful_', 'test_clean_chart', 'chart_test',
            'test_direct_chart'
        ],
        'consensus': [
            'test_consensus_', 'simple_consensus_', 'force_consensus_', 'trigger_consensus_',
            'debug_consensus_'
        ],
        'debug': [
            'debug_', 'force_fix_', 'fix_datafetcher_'
        ],
        'fixes': [
            'test_all_fixes', 'test_final_fixes', 'test_quality_', 'test_signal_quality_',
            'test_threshold_', 'test_rate_limit_', 'test_enhanced_', 'verify_'
        ],
        'integration': [
            'test_signal_tracking', 'test_analysis_flow', 'test_all_missing_',
            'test_separate_messages', 'test_full_improvements'
        ],
        'performance': [
            'run_30min_', 'test_long_running_', 'run_stability_'
        ],
        'stability': [
            'test_bot_stability', 'test_stability_', 'stability_test', 'demo_stability_',
            'quick_stability_', 'simple_stability_'
        ],
        'system': [
            'test_main_', 'test_config', 'minimal_test', 'quick_test', 'simple_test',
            'complete_test', 'final_test', 'test_simple_', 'test_coin_categorizer',
            'restart_bot_'
        ]
    }
    
    current_dir = Path(__file__).parent
    moved_files = []
    
    print("🗂️ ORGANIZING TEST FILES")
    print("=" * 50)
    
    # Get all Python files in current directory
    test_files = [f for f in os.listdir(current_dir) if f.endswith('.py') and f != 'organize_tests.py' and f != 'run_tests.py']
    
    for file_name in test_files:
        moved = False
        
        # Check each category
        for category, patterns in file_mappings.items():
            if moved:
                break
                
            for pattern in patterns:
                if pattern in file_name:
                    # Create category directory if it doesn't exist
                    category_dir = current_dir / category
                    category_dir.mkdir(exist_ok=True)
                    
                    # Move file
                    source = current_dir / file_name
                    destination = category_dir / file_name
                    
                    try:
                        shutil.move(str(source), str(destination))
                        moved_files.append((file_name, category))
                        print(f"📁 {file_name} → {category}/")
                        moved = True
                        break
                    except Exception as e:
                        print(f"❌ Error moving {file_name}: {e}")
        
        if not moved:
            print(f"⚠️ {file_name} - No category match")
    
    # Also move JSON and other test data files
    data_files = [f for f in os.listdir(current_dir) if f.startswith('test_') and (f.endswith('.json') or f.endswith('.cache'))]
    
    for data_file in data_files:
        # Try to match with moved Python files
        base_name = data_file.replace('.json', '').replace('.cache', '')
        
        for moved_file, category in moved_files:
            if base_name in moved_file or moved_file.replace('.py', '') in base_name:
                category_dir = current_dir / category
                source = current_dir / data_file
                destination = category_dir / data_file
                
                try:
                    shutil.move(str(source), str(destination))
                    print(f"📄 {data_file} → {category}/")
                    break
                except Exception as e:
                    print(f"❌ Error moving {data_file}: {e}")
    
    print(f"\n✅ ORGANIZATION COMPLETE")
    print(f"📊 Moved {len(moved_files)} Python files")
    print(f"📁 Categories used: {len(set(category for _, category in moved_files))}")
    
    # Show summary by category
    print(f"\n📋 SUMMARY BY CATEGORY:")
    category_counts = {}
    for _, category in moved_files:
        category_counts[category] = category_counts.get(category, 0) + 1
    
    for category, count in sorted(category_counts.items()):
        print(f"  📁 {category}: {count} files")
    
    return moved_files

def create_category_indexes():
    """Tạo index files cho mỗi category"""
    current_dir = Path(__file__).parent
    categories = ['analyzers', 'charts', 'consensus', 'debug', 'fixes', 'integration', 'performance', 'stability', 'system']
    
    print(f"\n📝 CREATING CATEGORY INDEXES")
    print("=" * 50)
    
    for category in categories:
        category_dir = current_dir / category
        if category_dir.exists():
            # Get all Python files in category
            py_files = [f for f in os.listdir(category_dir) if f.endswith('.py')]
            
            if py_files:
                index_content = f"""# 🧪 {category.upper()} TESTS

## 📁 Files in this category:

"""
                for py_file in sorted(py_files):
                    index_content += f"- `{py_file}`\n"
                
                index_content += f"""
## 🚀 Run all tests in this category:

```bash
cd {category}
python ../run_tests.py
```

## 🎯 Run specific test:

```bash
cd {category}
python test_name.py
```
"""
                
                index_file = category_dir / "INDEX.md"
                with open(index_file, 'w', encoding='utf-8') as f:
                    f.write(index_content)
                
                print(f"📝 Created {category}/INDEX.md ({len(py_files)} files)")

if __name__ == "__main__":
    print("🗂️ TEST FILE ORGANIZER")
    print("=" * 50)
    
    # Organize files
    moved_files = organize_test_files()
    
    # Create indexes
    create_category_indexes()
    
    print(f"\n🎉 ORGANIZATION COMPLETED!")
    print(f"✅ All test files have been organized into appropriate categories")
    print(f"📚 Category indexes have been created")
    print(f"🚀 Use 'python run_tests.py' to run all tests")
