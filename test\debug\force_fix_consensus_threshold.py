#!/usr/bin/env python3
"""
🔧 Force fix consensus threshold to 80% - tìm và sửa tất cả hardcoded 85% values
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(filepath):
    """Create backup of file before modification"""
    if os.path.exists(filepath):
        backup_path = f"{filepath}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(filepath, backup_path)
        print(f"  📁 Backup created: {backup_path}")
        return backup_path
    return None

def find_and_replace_threshold_values(filepath, replacements):
    """Find and replace threshold values in a file"""
    if not os.path.exists(filepath):
        print(f"  ⚠️ File not found: {filepath}")
        return False
    
    print(f"\n🔧 Processing {filepath}...")
    
    # Create backup
    backup_path = backup_file(filepath)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Apply replacements
        for pattern, replacement, description in replacements:
            matches = re.findall(pattern, content)
            if matches:
                print(f"  🔍 Found {len(matches)} matches for: {description}")
                for match in matches:
                    print(f"    - {match}")
                
                content = re.sub(pattern, replacement, content)
                changes_made.append(description)
        
        # Write back if changes were made
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ Updated {filepath}")
            print(f"  📝 Changes made: {', '.join(changes_made)}")
            return True
        else:
            print(f"  ℹ️ No changes needed in {filepath}")
            # Remove backup if no changes
            if backup_path and os.path.exists(backup_path):
                os.remove(backup_path)
            return False
            
    except Exception as e:
        print(f"  ❌ Error processing {filepath}: {e}")
        # Restore from backup if error occurred
        if backup_path and os.path.exists(backup_path):
            shutil.copy2(backup_path, filepath)
            print(f"  🔄 Restored from backup")
        return False

def fix_main_bot_py():
    """Fix main_bot.py threshold issues"""
    print("🔧 FIXING main_bot.py")
    print("=" * 50)
    
    replacements = [
        # Fix any remaining 0.85 consensus thresholds
        (r'MIN_CONFIDENCE_THRESHOLD\s*=\s*[^(]*\([^)]*["\']0\.85["\'][^)]*\)', 
         'MIN_CONFIDENCE_THRESHOLD = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.80"))', 
         'MIN_CONFIDENCE_THRESHOLD environment variable'),
        
        # Fix hardcoded 0.85 in consensus quality checks
        (r'consensus_confidence\s*<\s*0\.85', 
         'consensus_confidence < MIN_CONFIDENCE_THRESHOLD', 
         'Consensus quality comparison'),
        
        (r'consensus_confidence\s*>=\s*0\.85', 
         'consensus_confidence >= MIN_CONFIDENCE_THRESHOLD', 
         'Consensus quality comparison'),
        
        # Fix display threshold references
        (r'Required Threshold:\s*85\.0%', 
         'Required Threshold: {MIN_CONFIDENCE_THRESHOLD:.1%}', 
         'Display threshold format'),
        
        # Fix any hardcoded 85% in comments or strings
        (r'85%\s*threshold', 
         '80% threshold', 
         'Comment threshold references'),
        
        (r'threshold.*85%', 
         'threshold 80%', 
         'Threshold description'),
    ]
    
    return find_and_replace_threshold_values("main_bot.py", replacements)

def fix_consensus_analyzer_py():
    """Fix consensus_analyzer.py threshold issues"""
    print("\n🔧 FIXING consensus_analyzer.py")
    print("=" * 50)
    
    replacements = [
        # Fix default confidence_threshold in constructor
        (r'confidence_threshold\s*=\s*0\.85', 
         'confidence_threshold=0.8', 
         'Default confidence threshold'),
        
        # Fix any hardcoded 0.85 quality checks
        (r'confidence\s*>=\s*0\.85', 
         'confidence >= self.confidence_threshold', 
         'Confidence quality check'),
        
        (r'confidence\s*<\s*0\.85', 
         'confidence < self.confidence_threshold', 
         'Confidence quality check'),
    ]
    
    return find_and_replace_threshold_values("consensus_analyzer.py", replacements)

def fix_trigger_consensus_analysis_py():
    """Fix trigger_consensus_analysis.py threshold issues"""
    print("\n🔧 FIXING trigger_consensus_analysis.py")
    print("=" * 50)
    
    replacements = [
        # Fix hardcoded min_confidence
        (r'min_confidence\s*=\s*0\.85', 
         'min_confidence = 0.80', 
         'Hardcoded min_confidence'),
        
        # Fix any 85% references
        (r'85%\s*threshold', 
         '80% threshold', 
         'Threshold references'),
    ]
    
    return find_and_replace_threshold_values("trigger_consensus_analysis.py", replacements)

def create_environment_file():
    """Create or update .env file with correct threshold"""
    print("\n🔧 UPDATING .env FILE")
    print("=" * 50)
    
    env_file = ".env"
    threshold_line = "MIN_CONFIDENCE_THRESHOLD=0.80"
    
    try:
        # Read existing .env file
        env_content = ""
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
        
        # Check if threshold is already set correctly
        if "MIN_CONFIDENCE_THRESHOLD=0.80" in env_content:
            print("  ✅ .env already has correct threshold")
            return True
        
        # Remove any existing MIN_CONFIDENCE_THRESHOLD lines
        lines = env_content.split('\n')
        filtered_lines = [line for line in lines if not line.startswith('MIN_CONFIDENCE_THRESHOLD=')]
        
        # Add correct threshold
        filtered_lines.append(threshold_line)
        
        # Write back
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(filtered_lines))
        
        print(f"  ✅ Updated {env_file} with {threshold_line}")
        return True
        
    except Exception as e:
        print(f"  ❌ Error updating .env file: {e}")
        return False

def verify_fixes():
    """Verify that all fixes were applied correctly"""
    print("\n🔍 VERIFYING FIXES")
    print("=" * 50)
    
    files_to_check = ["main_bot.py", "consensus_analyzer.py", "trigger_consensus_analysis.py"]
    
    all_good = True
    
    for filepath in files_to_check:
        if not os.path.exists(filepath):
            continue
            
        print(f"\n📄 Checking {filepath}:")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for remaining 0.85 values (excluding comments and non-threshold related)
            problematic_patterns = [
                r'min_confidence\s*=\s*0\.85',
                r'confidence_threshold\s*=\s*0\.85',
                r'consensus_confidence\s*[<>=]+\s*0\.85',
                r'Required Threshold:\s*85\.0%',
            ]
            
            issues_found = []
            for pattern in problematic_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    issues_found.extend(matches)
            
            if issues_found:
                print(f"  ❌ Still has issues: {issues_found}")
                all_good = False
            else:
                print(f"  ✅ No threshold issues found")
                
        except Exception as e:
            print(f"  ❌ Error checking {filepath}: {e}")
            all_good = False
    
    return all_good

def main():
    """Main fix function"""
    print("🔧 FORCE FIX CONSENSUS THRESHOLD TO 80%")
    print("=" * 80)
    print("This tool will find and fix ALL hardcoded 85% threshold values")
    print()
    
    # Track changes
    changes_made = []
    
    # Fix each file
    if fix_main_bot_py():
        changes_made.append("main_bot.py")
    
    if fix_consensus_analyzer_py():
        changes_made.append("consensus_analyzer.py")
    
    if fix_trigger_consensus_analysis_py():
        changes_made.append("trigger_consensus_analysis.py")
    
    # Update environment file
    if create_environment_file():
        changes_made.append(".env")
    
    # Verify fixes
    verification_passed = verify_fixes()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 FORCE FIX SUMMARY")
    print("=" * 80)
    
    if changes_made:
        print(f"✅ Files modified: {', '.join(changes_made)}")
    else:
        print("ℹ️ No files needed modification")
    
    if verification_passed:
        print("✅ Verification: All threshold issues resolved")
    else:
        print("❌ Verification: Some issues may remain")
    
    print("\n🚀 Next steps:")
    print("1. Restart the bot to load new configuration")
    print("2. Check logs to confirm threshold shows 80.0%")
    print("3. Test consensus analysis with the new threshold")
    
    print(f"\n⏰ Fix completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
