#!/usr/bin/env python3
"""
🧪 TEST CHART FIX
================

Test để sửa vấn đề chart generation.
"""

import os
import sys
import traceback
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_fix():
    """Test chart fix."""
    print("🧪 TESTING CHART FIX")
    print("=" * 50)
    
    try:
        # Import chart generator directly
        print("📦 Importing chart_generator...")
        import chart_generator
        print("✅ chart_generator imported successfully")
        
        # Import telegram notifier
        print("📱 Importing telegram_notifier...")
        import telegram_notifier
        print("✅ telegram_notifier imported successfully")
        
        # Initialize telegram notifier
        print("🚀 Initializing telegram notifier...")
        notifier = telegram_notifier.EnhancedTelegramNotifier()
        print("✅ Telegram notifier initialized")
        
        # Initialize chart generator
        print("🎨 Initializing chart generator...")
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="charts",
            telegram_notifier=notifier,
            enable_auto_delete=True,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=500
        )
        print("✅ Chart generator initialized successfully!")
        
        # Create sample data
        print("📊 Creating sample data...")
        dates = pd.date_range(start=datetime.now() - timedelta(hours=100), periods=100, freq='1H')
        sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': [50000 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'high': [50100 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'low': [49900 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'close': [50050 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'volume': [1000 + i * 5 + np.random.randn() * 100 for i in range(100)]
        })
        sample_data.set_index('timestamp', inplace=True)
        print("✅ Sample data created")
        
        # Test chart generation
        coin = "BTCUSDT"
        current_price = 51000.0
        
        print("🎨 Testing Fibonacci chart generation...")
        fibonacci_data = {
            "status": "success",
            "retracement_levels": [
                {"ratio": 0.236, "price": current_price * 0.976},
                {"ratio": 0.382, "price": current_price * 0.962},
                {"ratio": 0.618, "price": current_price * 0.938}
            ],
            "extension_levels": [
                {"ratio": 1.618, "price": current_price * 1.062},
                {"ratio": 2.618, "price": current_price * 1.124}
            ],
            "confluence_zones": [],
            "pivot_high": current_price * 1.05,
            "pivot_low": current_price * 0.95,
            "trend_direction": "UPTREND",
            "calculation_method": "enhanced_automatic",
            "confidence": 0.8,
            "signal_strength": "STRONG",
            "signals": {"overall_signal": "BUY", "confidence": 0.8}
        }
        
        chart_path = chart_gen.generate_fibonacci_chart(coin, fibonacci_data, sample_data, current_price)
        print(f"📊 Fibonacci chart result: {'✅ SUCCESS' if chart_path else '❌ FAILED'}")
        if chart_path:
            print(f"    📁 Chart path: {chart_path}")
            print(f"    📁 File exists: {os.path.exists(chart_path)}")
            
            # Test sending chart
            print("📱 Testing chart sending...")
            if notifier and os.path.exists(chart_path):
                # Send to a test chat (you can change this)
                test_message = f"🎨 Test Chart Generation\n\n📊 {coin} Fibonacci Analysis\n💰 Price: ${current_price:,.2f}\n📈 Signal: BUY\n🎯 Confidence: 80%"
                
                # Note: This will actually send to Telegram if configured
                # Uncomment the line below to test actual sending
                # success = notifier.send_photo(chart_path, test_message, chat_id="YOUR_TEST_CHAT_ID")
                # print(f"📱 Chart sending result: {'✅ SUCCESS' if success else '❌ FAILED'}")
                print("📱 Chart sending test skipped (uncomment to test actual sending)")
        
        print("\n🎯 CHART FIX TEST COMPLETED SUCCESSFULLY")
        return True
        
    except Exception as e:
        print(f"❌ Chart fix test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_chart_fix()
