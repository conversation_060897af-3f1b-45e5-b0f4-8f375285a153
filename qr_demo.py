#!/usr/bin/env python3
"""
📱 QR CODE DEMO
===============

Demo script để hiển thị QR codes đã tạo
"""

import os
from qr_code_generator import DonationQRGenerator

def show_qr_info():
    """Hiển thị thông tin QR codes"""
    print("📱 === QR CODE DEMO ===")
    print("=" * 50)
    
    # Khởi tạo QR generator
    qr_gen = DonationQRGenerator()
    
    # Hiển thị thông tin wallet
    print("🏦 DONATION WALLET INFO:")
    print(f"  Address: {qr_gen.wallet_address}")
    print(f"  Network: {qr_gen.network}")
    print(f"  Currency: {qr_gen.currency}")
    print()
    
    # Kiểm tra QR files
    print("📱 QR CODE FILES:")
    qr_files = [
        ("Basic QR", "qr_codes/donation_wallet_basic.png"),
        ("Enhanced QR", "qr_codes/donation_wallet_enhanced.png"),
        ("Telegram QR", "qr_codes/donation_telegram.png"),
        ("SVG QR", "qr_codes/donation_wallet.svg")
    ]
    
    for name, path in qr_files:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"  ✅ {name}: {path} ({size} bytes)")
        else:
            print(f"  ❌ {name}: Not found")
    
    print()
    
    # Hiển thị usage
    print("📋 USAGE INSTRUCTIONS:")
    print("  1. Scan any QR code with crypto wallet")
    print("  2. Wallet will auto-fill the address")
    print("  3. Enter donation amount")
    print("  4. Confirm transaction")
    print("  5. Contact admin with proof")
    print()
    
    # Hiển thị QR info
    qr_info = qr_gen.get_qr_info()
    print("📊 QR SYSTEM INFO:")
    for key, value in qr_info.items():
        print(f"  {key}: {value}")

def generate_qr_if_missing():
    """Tạo QR nếu chưa có"""
    qr_gen = DonationQRGenerator()
    
    # Kiểm tra xem có QR nào chưa
    missing_qr = []
    qr_files = {
        "basic": "qr_codes/donation_wallet_basic.png",
        "enhanced": "qr_codes/donation_wallet_enhanced.png",
        "telegram": "qr_codes/donation_telegram.png",
        "svg": "qr_codes/donation_wallet.svg"
    }
    
    for qr_type, path in qr_files.items():
        if not os.path.exists(path):
            missing_qr.append(qr_type)
    
    if missing_qr:
        print(f"🔧 Generating missing QR codes: {missing_qr}")
        qr_gen.generate_all_formats()
    else:
        print("✅ All QR codes already exist")

def show_qr_content():
    """Hiển thị nội dung QR codes"""
    print("\n📱 QR CODE CONTENT:")
    print("=" * 50)
    
    wallet_address = "******************************************"
    
    print("🔹 BASIC QR CODE:")
    print(f"  Content: {wallet_address}")
    print("  Size: 400x400px")
    print("  Use: Simple wallet scanning")
    print()
    
    print("🔹 ENHANCED QR CODE:")
    print("  Content: Wallet + Network + Currency info")
    print("  Size: 600x700px")
    print("  Use: Detailed information display")
    print()
    
    print("🔹 TELEGRAM QR CODE:")
    print("  Content: Wallet + 'USDT BEP20 Donation'")
    print("  Size: 512x512px (Telegram optimized)")
    print("  Use: Telegram sharing")
    print()
    
    print("🔹 SVG QR CODE:")
    print("  Content: Wallet address")
    print("  Format: Vector SVG")
    print("  Use: Web/print scalable")

def demo_qr_integration():
    """Demo QR integration với member manager"""
    print("\n🤖 QR INTEGRATION DEMO:")
    print("=" * 50)
    
    try:
        from telegram_member_manager import TelegramMemberManager
        
        # Tạo member manager (sẽ auto-generate QR)
        print("🔧 Initializing Member Manager with QR...")
        manager = TelegramMemberManager()
        
        # Kiểm tra QR integration
        qr_telegram = manager.get_qr_code_path("telegram")
        if qr_telegram and os.path.exists(qr_telegram):
            print(f"✅ QR integration successful: {qr_telegram}")
        else:
            print("❌ QR integration failed")
        
        # Test donation message
        donation_msg = manager.get_donation_message()
        if "QR code" in donation_msg:
            print("✅ Donation message includes QR info")
        else:
            print("❌ Donation message missing QR info")
        
        print("✅ QR integration demo completed")
        
    except Exception as e:
        print(f"❌ QR integration demo failed: {e}")

def main():
    """Main demo function"""
    print("🎯 === QR CODE SYSTEM DEMO ===")
    print("🎉 Demonstrating QR code system for donation wallet")
    print()
    
    # Generate QR if missing
    generate_qr_if_missing()
    
    # Show QR info
    show_qr_info()
    
    # Show QR content
    show_qr_content()
    
    # Demo integration
    demo_qr_integration()
    
    print("\n" + "=" * 50)
    print("🎯 DEMO SUMMARY")
    print("=" * 50)
    print("✅ QR Code System: Fully operational")
    print("✅ Multiple Formats: 4 types available")
    print("✅ Auto Generation: Working")
    print("✅ Integration: Member manager connected")
    print("✅ File Management: Organized in qr_codes/")
    print()
    print("💰 DONATION WALLET:")
    print("  Address: ******************************************")
    print("  Network: BNB Smart Chain (BEP20)")
    print("  Currency: USDT")
    print()
    print("📱 QR CODES READY FOR USE!")
    print("🚀 Users can now scan QR to donate easily!")

if __name__ == "__main__":
    main()
