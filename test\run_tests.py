#!/usr/bin/env python3
"""
🧪 TEST RUNNER - <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> cả tests trong thư mục test
"""

import os
import sys
import time
import importlib.util
from datetime import datetime

def run_test_file(test_file_path):
    """Chạy một test file và return kết quả"""
    try:
        print(f"\n{'='*60}")
        print(f"🧪 RUNNING TEST: {os.path.basename(test_file_path)}")
        print(f"{'='*60}")
        
        # Import và chạy test module
        spec = importlib.util.spec_from_file_location("test_module", test_file_path)
        test_module = importlib.util.module_from_spec(spec)
        
        start_time = time.time()
        spec.loader.exec_module(test_module)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"\n✅ TEST COMPLETED in {duration:.2f} seconds")
        
        return {
            "file": os.path.basename(test_file_path),
            "status": "PASSED",
            "duration": duration,
            "error": None
        }
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        return {
            "file": os.path.basename(test_file_path),
            "status": "FAILED", 
            "duration": 0,
            "error": str(e)
        }

def discover_tests():
    """Tìm tất cả test files trong thư mục hiện tại"""
    test_files = []
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    for file in os.listdir(current_dir):
        if file.startswith("test_") and file.endswith(".py"):
            test_files.append(os.path.join(current_dir, file))
    
    return sorted(test_files)

def run_all_tests():
    """Chạy tất cả tests và tạo report"""
    print(f"🚀 TRADING BOT TEST RUNNER")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Discover test files
    test_files = discover_tests()
    
    if not test_files:
        print(f"\n⚠️ No test files found in current directory")
        return
    
    print(f"\n📁 Found {len(test_files)} test files:")
    for test_file in test_files:
        print(f"  - {os.path.basename(test_file)}")
    
    # Run all tests
    results = []
    total_start_time = time.time()
    
    for test_file in test_files:
        result = run_test_file(test_file)
        results.append(result)
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # Generate summary report
    print(f"\n{'='*60}")
    print(f"📊 TEST SUMMARY REPORT")
    print(f"{'='*60}")
    
    passed_tests = [r for r in results if r["status"] == "PASSED"]
    failed_tests = [r for r in results if r["status"] == "FAILED"]
    
    print(f"📈 Total Tests: {len(results)}")
    print(f"✅ Passed: {len(passed_tests)}")
    print(f"❌ Failed: {len(failed_tests)}")
    print(f"⏱️ Total Duration: {total_duration:.2f} seconds")
    print(f"📊 Success Rate: {len(passed_tests)/len(results)*100:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status_emoji = "✅" if result["status"] == "PASSED" else "❌"
        print(f"  {status_emoji} {result['file']} - {result['status']} ({result['duration']:.2f}s)")
        if result["error"]:
            print(f"      Error: {result['error']}")
    
    # Failed tests details
    if failed_tests:
        print(f"\n🚨 FAILED TESTS DETAILS:")
        for failed_test in failed_tests:
            print(f"  ❌ {failed_test['file']}")
            print(f"     Error: {failed_test['error']}")
    
    # Save results to file
    save_test_results(results, total_duration)
    
    print(f"\n🎯 Test run completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results

def save_test_results(results, total_duration):
    """Lưu kết quả test vào file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"test_results_{timestamp}.txt"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            f.write(f"🧪 TRADING BOT TEST RESULTS\n")
            f.write(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*60}\n\n")
            
            passed_tests = [r for r in results if r["status"] == "PASSED"]
            failed_tests = [r for r in results if r["status"] == "FAILED"]
            
            f.write(f"📊 SUMMARY:\n")
            f.write(f"  Total Tests: {len(results)}\n")
            f.write(f"  Passed: {len(passed_tests)}\n")
            f.write(f"  Failed: {len(failed_tests)}\n")
            f.write(f"  Total Duration: {total_duration:.2f} seconds\n")
            f.write(f"  Success Rate: {len(passed_tests)/len(results)*100:.1f}%\n\n")
            
            f.write(f"📋 DETAILED RESULTS:\n")
            for result in results:
                status_emoji = "✅" if result["status"] == "PASSED" else "❌"
                f.write(f"  {status_emoji} {result['file']} - {result['status']} ({result['duration']:.2f}s)\n")
                if result["error"]:
                    f.write(f"      Error: {result['error']}\n")
            
            if failed_tests:
                f.write(f"\n🚨 FAILED TESTS:\n")
                for failed_test in failed_tests:
                    f.write(f"  ❌ {failed_test['file']}: {failed_test['error']}\n")
        
        print(f"💾 Test results saved to: {results_file}")
        
    except Exception as e:
        print(f"⚠️ Failed to save test results: {e}")

def run_specific_test(test_name):
    """Chạy một test cụ thể"""
    test_files = discover_tests()
    
    # Tìm test file matching
    matching_files = [f for f in test_files if test_name in os.path.basename(f)]
    
    if not matching_files:
        print(f"❌ No test file found matching: {test_name}")
        return
    
    if len(matching_files) > 1:
        print(f"⚠️ Multiple test files found matching '{test_name}':")
        for f in matching_files:
            print(f"  - {os.path.basename(f)}")
        return
    
    test_file = matching_files[0]
    print(f"🎯 Running specific test: {os.path.basename(test_file)}")
    
    result = run_test_file(test_file)
    
    print(f"\n📊 RESULT:")
    status_emoji = "✅" if result["status"] == "PASSED" else "❌"
    print(f"  {status_emoji} {result['file']} - {result['status']} ({result['duration']:.2f}s)")
    if result["error"]:
        print(f"  Error: {result['error']}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Chạy test cụ thể
        test_name = sys.argv[1]
        run_specific_test(test_name)
    else:
        # Chạy tất cả tests
        run_all_tests()
