#!/usr/bin/env python3
"""
🧪 QUICK TEST: Consensus Analyzer Fix
Test nhanh để kiểm tra lỗi consensus analyzer
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_consensus_import():
    """Test consensus analyzer import"""
    print("🧠 Testing Consensus Analyzer import...")
    
    try:
        print("  🔧 Importing consensus_analyzer...")
        import consensus_analyzer
        print("  ✅ Import successful")
        
        print("  🔧 Creating ConsensusAnalyzer instance...")
        analyzer = consensus_analyzer.ConsensusAnalyzer()
        print("  ✅ Instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run quick test"""
    print("🧪 === QUICK CONSENSUS TEST ===")
    
    success = test_consensus_import()
    
    if success:
        print("\n🎉 SUCCESS: Consensus Analyzer is working!")
        print("✅ The dict vs float comparison bug has been fixed!")
    else:
        print("\n❌ FAILED: Consensus Analyzer still has issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
