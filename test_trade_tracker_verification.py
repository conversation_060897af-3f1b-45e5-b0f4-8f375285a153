#!/usr/bin/env python3
"""
🔍 TRADE TRACKER VERIFICATION TEST
Test Trade Tracker to ensure Ultra Tracker V3.0 is working correctly
"""

import sys
import os
import time
import pandas as pd
from typing import Dict, Any

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_trade_tracker_initialization():
    """Test Trade Tracker initialization"""
    print("\n🔍 Testing Trade Tracker initialization...")
    
    try:
        from trade_tracker import TradeTracker
        
        tracker = TradeTracker()
        
        # Check if Ultra Tracker V3.0 is initialized
        if hasattr(tracker, 'version'):
            print(f"  ✅ Trade Tracker version: {tracker.version}")
        
        if hasattr(tracker, 'signal_management'):
            print(f"  ✅ Signal management initialized")
            print(f"    Max signals: {tracker.signal_management.get('max_signals', 'Unknown')}")
            print(f"    Completion threshold: {tracker.signal_management.get('completion_threshold', 'Unknown')}")
        
        if hasattr(tracker, 'active_signals'):
            print(f"  ✅ Active signals list initialized: {len(tracker.active_signals)} signals")
        
        if hasattr(tracker, 'completed_signals'):
            print(f"  ✅ Completed signals list initialized: {len(tracker.completed_signals)} signals")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Trade Tracker initialization failed: {e}")
        return False

def test_signal_integration():
    """Test Signal Integration with Trade Tracker"""
    print("\n🔍 Testing Signal Integration...")
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Create mock telegram_notifier
        class MockTelegramNotifier:
            def send_volume_profile_report(self, *args, **kwargs):
                print("    📤 Mock: Volume Profile report sent")
                return True
            
            def send_ai_analysis_report(self, *args, **kwargs):
                print("    📤 Mock: AI analysis report sent")
                return True
        
        # Initialize signal integration
        integration = SignalManagerIntegration(
            telegram_notifier=MockTelegramNotifier(),
            trade_tracker=None  # Will be set if available
        )
        
        # Test can_send_signal method
        can_send = integration.can_send_signal("volume_profile")
        print(f"  ✅ Can send volume_profile signal: {can_send}")
        
        # Test system status
        status = integration.get_system_status()
        if "error" not in status:
            print(f"  ✅ System status retrieved successfully")
            if "system_info" in status:
                system_type = status["system_info"].get("signal_pool_type", "Unknown")
                print(f"    System type: {system_type}")
        else:
            print(f"  ⚠️ System status error: {status.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Signal Integration test failed: {e}")
        return False

def test_main_bot_signal_integration():
    """Test Main Bot Signal Integration"""
    print("\n🔍 Testing Main Bot Signal Integration...")
    
    try:
        from main_bot_signal_integration import MainBotSignalIntegration
        
        # Create mock main_bot
        class MockMainBot:
            def __init__(self):
                self.notifier = None
                self.chart_generator = None
        
        # Initialize main bot signal integration
        main_integration = MainBotSignalIntegration(MockMainBot())
        
        # Test method existence
        methods_to_check = [
            'send_volume_profile_with_tracking',
            'send_point_figure_with_tracking',
            'send_ai_analysis_with_tracking',
            'can_send_signal'
        ]
        
        for method_name in methods_to_check:
            if hasattr(main_integration, method_name):
                print(f"  ✅ Method exists: {method_name}")
            else:
                print(f"  ❌ Method missing: {method_name}")
        
        # Test can_send_signal
        if hasattr(main_integration, 'can_send_signal'):
            can_send = main_integration.can_send_signal("volume_profile")
            print(f"  ✅ Can send signal test: {can_send}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Main Bot Signal Integration test failed: {e}")
        return False

def test_ultra_tracker_status():
    """Test Ultra Tracker status and signal management"""
    print("\n🔍 Testing Ultra Tracker status...")
    
    try:
        from trade_tracker import TradeTracker
        
        tracker = TradeTracker()
        
        # Test Ultra Tracker status method
        if hasattr(tracker, 'get_ultra_tracker_status'):
            status = tracker.get_ultra_tracker_status()
            print(f"  ✅ Ultra Tracker status retrieved")
            
            if 'signal_limits' in status:
                limits = status['signal_limits']
                print(f"    Max signals: {limits.get('max_signals', 'Unknown')}")
                print(f"    Active signals: {limits.get('active_signals', 'Unknown')}")
                print(f"    Can send new: {limits.get('can_send_new', 'Unknown')}")
            
            if 'system_info' in status:
                system_info = status['system_info']
                print(f"    System version: {system_info.get('version', 'Unknown')}")
                print(f"    Monitoring active: {system_info.get('monitoring_active', 'Unknown')}")
        
        # Test can_send_new_signal method
        if hasattr(tracker, 'can_send_new_signal'):
            can_send = tracker.can_send_new_signal()
            print(f"  ✅ Can send new signal: {can_send}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Ultra Tracker status test failed: {e}")
        return False

def test_volume_profile_integration():
    """Test Volume Profile integration with tracking"""
    print("\n🔍 Testing Volume Profile integration...")

    try:
        # Test Volume Profile method names in telegram_notifier
        from telegram_notifier import TelegramNotifier

        # Check if required methods exist - use mock credentials for testing
        notifier = TelegramNotifier(bot_token="test_token", chat_id="test_chat")
        
        required_methods = [
            'send_volume_profile_report',
            'send_ai_analysis_report',
            'send_fibonacci_analysis_report',
            'send_orderbook_analysis_report',
            'send_fourier_analysis_report'
        ]
        
        for method_name in required_methods:
            if hasattr(notifier, method_name):
                print(f"  ✅ TelegramNotifier has method: {method_name}")
            else:
                print(f"  ❌ TelegramNotifier missing method: {method_name}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Volume Profile integration test failed: {e}")
        return False

def run_trade_tracker_verification():
    """Run all Trade Tracker verification tests"""
    print("🚀 STARTING TRADE TRACKER VERIFICATION TEST")
    print("=" * 60)
    
    tests = [
        test_trade_tracker_initialization,
        test_signal_integration,
        test_main_bot_signal_integration,
        test_ultra_tracker_status,
        test_volume_profile_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 TRADE TRACKER VERIFICATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ ALL TESTS PASSED - Trade Tracker is working correctly!")
        print("🚀 Ultra Tracker V3.0 is operational")
        print("📊 Signal management is functional")
        print("🔧 Volume Profile tracking should work properly")
        return True
    else:
        print(f"❌ {total - passed} tests failed - Issues need to be addressed")
        return False

if __name__ == "__main__":
    success = run_trade_tracker_verification()
    sys.exit(0 if success else 1)
