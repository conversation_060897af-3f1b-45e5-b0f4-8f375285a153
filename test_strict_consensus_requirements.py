#!/usr/bin/env python3
"""
🧪 TEST STRICT CONSENSUS REQUIREMENTS
====================================

Test để kiểm tra các yêu cầu nghiêm ngặt cho consensus signals.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_strict_consensus_requirements():
    """Test strict consensus requirements."""
    print("🧪 TESTING STRICT CONSENSUS REQUIREMENTS")
    print("=" * 50)
    
    # Test current SUSHI/USDT signal against strict requirements
    print("🔴 TESTING SUSHI/USDT SIGNAL (Should be REJECTED):")
    
    sushi_signal = {
        "signal_type": "SELL",
        "confidence": 0.80,  # 80% - meets requirement (≥80%)
        "consensus_score": 0.421,  # 42.1% - FAILS requirement (≥60%)
        "ai_confidence": 0.52,  # 52% - FAILS requirement (≥65%)
        "algorithms_agreeing": 2,  # 2/4 - FAILS requirement (≥5/8)
        "signal_strength": 0.50,  # 50% - FAILS requirement (≥60%)
        "overall_quality": "THẤP"  # LOW - FAILS requirement (HIGH)
    }
    
    # Define strict requirements
    requirements = {
        "MIN_CONFIDENCE_THRESHOLD": 0.80,  # ≥80%
        "MIN_CONSENSUS_SCORE": 0.60,       # ≥60%
        "MIN_AI_CONFIDENCE": 0.65,         # ≥65%
        "MIN_ALGORITHMS_REQUIRED": 5,      # ≥5 algorithms
        "MIN_SIGNAL_STRENGTH": 0.60,       # ≥60%
        "REQUIRED_QUALITY": "HIGH"         # HIGH quality
    }
    
    print(f"📊 SUSHI/USDT Signal Analysis:")
    print(f"  🔴 Signal Type: {sushi_signal['signal_type']}")
    print(f"  📈 Confidence: {sushi_signal['confidence']:.1%} (req: ≥{requirements['MIN_CONFIDENCE_THRESHOLD']:.1%}) {'✅' if sushi_signal['confidence'] >= requirements['MIN_CONFIDENCE_THRESHOLD'] else '❌'}")
    print(f"  🎯 Consensus Score: {sushi_signal['consensus_score']:.1%} (req: ≥{requirements['MIN_CONSENSUS_SCORE']:.1%}) {'✅' if sushi_signal['consensus_score'] >= requirements['MIN_CONSENSUS_SCORE'] else '❌'}")
    print(f"  🤖 AI Confidence: {sushi_signal['ai_confidence']:.1%} (req: ≥{requirements['MIN_AI_CONFIDENCE']:.1%}) {'✅' if sushi_signal['ai_confidence'] >= requirements['MIN_AI_CONFIDENCE'] else '❌'}")
    print(f"  🔢 Algorithms: {sushi_signal['algorithms_agreeing']}/8 (req: ≥{requirements['MIN_ALGORITHMS_REQUIRED']}/8) {'✅' if sushi_signal['algorithms_agreeing'] >= requirements['MIN_ALGORITHMS_REQUIRED'] else '❌'}")
    print(f"  💪 Signal Strength: {sushi_signal['signal_strength']:.1%} (req: ≥{requirements['MIN_SIGNAL_STRENGTH']:.1%}) {'✅' if sushi_signal['signal_strength'] >= requirements['MIN_SIGNAL_STRENGTH'] else '❌'}")
    print(f"  🏆 Overall Quality: {sushi_signal['overall_quality']} (req: {requirements['REQUIRED_QUALITY']}) {'✅' if sushi_signal['overall_quality'] == requirements['REQUIRED_QUALITY'] else '❌'}")
    
    # Check if signal meets ALL requirements
    checks = [
        sushi_signal['confidence'] >= requirements['MIN_CONFIDENCE_THRESHOLD'],
        sushi_signal['consensus_score'] >= requirements['MIN_CONSENSUS_SCORE'],
        sushi_signal['ai_confidence'] >= requirements['MIN_AI_CONFIDENCE'],
        sushi_signal['algorithms_agreeing'] >= requirements['MIN_ALGORITHMS_REQUIRED'],
        sushi_signal['signal_strength'] >= requirements['MIN_SIGNAL_STRENGTH'],
        sushi_signal['overall_quality'] == requirements['REQUIRED_QUALITY']
    ]
    
    passed_checks = sum(checks)
    total_checks = len(checks)
    
    print(f"\n📊 QUALITY CHECK SUMMARY:")
    print(f"  ✅ Passed: {passed_checks}/{total_checks} checks")
    print(f"  ❌ Failed: {total_checks - passed_checks}/{total_checks} checks")
    print(f"  🎯 Result: {'✅ SIGNAL APPROVED' if all(checks) else '❌ SIGNAL REJECTED'}")
    
    sushi_should_be_rejected = not all(checks)
    
    # Test a GOOD signal that should pass
    print(f"\n🟢 TESTING GOOD SIGNAL (Should be APPROVED):")
    
    good_signal = {
        "signal_type": "BUY",
        "confidence": 0.85,  # 85% - meets requirement (≥80%)
        "consensus_score": 0.72,  # 72% - meets requirement (≥60%)
        "ai_confidence": 0.78,  # 78% - meets requirement (≥65%)
        "algorithms_agreeing": 6,  # 6/8 - meets requirement (≥5/8)
        "signal_strength": 0.68,  # 68% - meets requirement (≥60%)
        "overall_quality": "HIGH"  # HIGH - meets requirement (HIGH)
    }
    
    print(f"📊 GOOD Signal Analysis:")
    print(f"  🟢 Signal Type: {good_signal['signal_type']}")
    print(f"  📈 Confidence: {good_signal['confidence']:.1%} (req: ≥{requirements['MIN_CONFIDENCE_THRESHOLD']:.1%}) {'✅' if good_signal['confidence'] >= requirements['MIN_CONFIDENCE_THRESHOLD'] else '❌'}")
    print(f"  🎯 Consensus Score: {good_signal['consensus_score']:.1%} (req: ≥{requirements['MIN_CONSENSUS_SCORE']:.1%}) {'✅' if good_signal['consensus_score'] >= requirements['MIN_CONSENSUS_SCORE'] else '❌'}")
    print(f"  🤖 AI Confidence: {good_signal['ai_confidence']:.1%} (req: ≥{requirements['MIN_AI_CONFIDENCE']:.1%}) {'✅' if good_signal['ai_confidence'] >= requirements['MIN_AI_CONFIDENCE'] else '❌'}")
    print(f"  🔢 Algorithms: {good_signal['algorithms_agreeing']}/8 (req: ≥{requirements['MIN_ALGORITHMS_REQUIRED']}/8) {'✅' if good_signal['algorithms_agreeing'] >= requirements['MIN_ALGORITHMS_REQUIRED'] else '❌'}")
    print(f"  💪 Signal Strength: {good_signal['signal_strength']:.1%} (req: ≥{requirements['MIN_SIGNAL_STRENGTH']:.1%}) {'✅' if good_signal['signal_strength'] >= requirements['MIN_SIGNAL_STRENGTH'] else '❌'}")
    print(f"  🏆 Overall Quality: {good_signal['overall_quality']} (req: {requirements['REQUIRED_QUALITY']}) {'✅' if good_signal['overall_quality'] == requirements['REQUIRED_QUALITY'] else '❌'}")
    
    # Check if good signal meets ALL requirements
    good_checks = [
        good_signal['confidence'] >= requirements['MIN_CONFIDENCE_THRESHOLD'],
        good_signal['consensus_score'] >= requirements['MIN_CONSENSUS_SCORE'],
        good_signal['ai_confidence'] >= requirements['MIN_AI_CONFIDENCE'],
        good_signal['algorithms_agreeing'] >= requirements['MIN_ALGORITHMS_REQUIRED'],
        good_signal['signal_strength'] >= requirements['MIN_SIGNAL_STRENGTH'],
        good_signal['overall_quality'] == requirements['REQUIRED_QUALITY']
    ]
    
    good_passed_checks = sum(good_checks)
    
    print(f"\n📊 GOOD SIGNAL QUALITY CHECK:")
    print(f"  ✅ Passed: {good_passed_checks}/{total_checks} checks")
    print(f"  ❌ Failed: {total_checks - good_passed_checks}/{total_checks} checks")
    print(f"  🎯 Result: {'✅ SIGNAL APPROVED' if all(good_checks) else '❌ SIGNAL REJECTED'}")
    
    good_should_be_approved = all(good_checks)
    
    # Test consensus analyzer configuration
    print(f"\n⚖️ TESTING CONSENSUS ANALYZER CONFIGURATION:")
    
    try:
        import consensus_analyzer
        
        analyzer = consensus_analyzer.ConsensusAnalyzer()
        
        # Check weight threshold
        weight_threshold = analyzer.quality_control.get('min_weight_threshold', 0.6)
        ai_threshold = 0.65  # Should be updated to 65%
        
        print(f"📊 Consensus Analyzer Settings:")
        print(f"  ⚖️ Weight Threshold: {weight_threshold:.1%} (should be ≥60%)")
        print(f"  🤖 AI Threshold: {ai_threshold:.1%} (should be ≥65%)")
        print(f"  🔢 Min Algorithms: 5/8 (should be ≥5/8)")
        
        analyzer_config_correct = (
            weight_threshold >= 0.6 and
            ai_threshold >= 0.65
        )
        
        print(f"  🎯 Analyzer Config: {'✅ CORRECT' if analyzer_config_correct else '❌ NEEDS UPDATE'}")
        
    except Exception as e:
        print(f"❌ Error testing consensus analyzer: {e}")
        analyzer_config_correct = False
    
    # Summary
    print(f"\n🎯 STRICT REQUIREMENTS TEST SUMMARY:")
    print(f"=" * 50)
    print(f"🔴 SUSHI/USDT (Bad Signal): {'✅ CORRECTLY REJECTED' if sushi_should_be_rejected else '❌ INCORRECTLY APPROVED'}")
    print(f"🟢 Good Signal: {'✅ CORRECTLY APPROVED' if good_should_be_approved else '❌ INCORRECTLY REJECTED'}")
    print(f"⚖️ Analyzer Config: {'✅ CORRECT' if analyzer_config_correct else '❌ NEEDS UPDATE'}")
    
    all_tests_passed = sushi_should_be_rejected and good_should_be_approved and analyzer_config_correct
    
    if all_tests_passed:
        print(f"\n🎉 ALL STRICT REQUIREMENTS WORKING CORRECTLY!")
        print(f"\n📋 WHAT WILL HAPPEN NOW:")
        print(f"1. ❌ SUSHI/USDT signal will be REJECTED (doesn't meet requirements)")
        print(f"2. ✅ Only HIGH-QUALITY signals will be approved:")
        print(f"   - Confidence ≥80%")
        print(f"   - Consensus Score ≥60%")
        print(f"   - AI Confidence ≥65%")
        print(f"   - ≥5 algorithms agreeing")
        print(f"   - Signal Strength ≥60%")
        print(f"   - Overall Quality = HIGH")
        print(f"3. 📊 All algorithms will be used (8 total)")
        print(f"4. 🎯 Only premium signals will be sent")
    else:
        print(f"\n⚠️ SOME REQUIREMENTS STILL NEED FIXING:")
        if not sushi_should_be_rejected:
            print(f"  - SUSHI signal should be rejected but isn't")
        if not good_should_be_approved:
            print(f"  - Good signal should be approved but isn't")
        if not analyzer_config_correct:
            print(f"  - Consensus analyzer configuration needs update")
    
    return all_tests_passed

if __name__ == "__main__":
    test_strict_consensus_requirements()
