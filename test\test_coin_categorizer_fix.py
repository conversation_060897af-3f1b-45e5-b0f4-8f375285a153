#!/usr/bin/env python3
"""
🧪 TEST: CoinCategorizer Fix
Test để kiểm tra CoinCategorizer với danh sách động
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_coin_categorizer():
    """Test CoinCategorizer initialization"""
    print("🏷️ Testing CoinCategorizer...")
    
    try:
        print("  🔧 Importing coin_categorizer...")
        import coin_categorizer
        print("  ✅ Import successful")
        
        print("  🔧 Creating CoinCategorizer instance...")
        categorizer = coin_categorizer.CoinCategorizer(
            cache_file="coin_categories_cache.json",
            auto_update=True,
            update_interval=3600
        )
        print("  ✅ Instance created successfully")
        
        # Test getting coin count
        print("  🔧 Testing coin count...")
        if hasattr(categorizer, 'use_dynamic_sectors') and categorizer.use_dynamic_sectors:
            total_coins = len(categorizer.dynamic_sectors.get('all_coins', []))
            mode = "Dynamic"
        elif hasattr(categorizer, 'known_coins'):
            total_coins = len(categorizer.known_coins)
            mode = "Static"
        else:
            total_coins = 0
            mode = "Unknown"
        
        print(f"  ✅ Coin count: {total_coins} coins ({mode} mode)")
        
        # Test categorization
        print("  🔧 Testing coin categorization...")
        test_coins = ['BTC', 'ETH', 'DOGE', 'UNI', 'LINK']
        for coin in test_coins:
            category = categorizer.get_coin_category(coin)
            print(f"    {coin}: {category}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_bot_initialization():
    """Test main bot initialization"""
    print("\n🤖 Testing TradingBot initialization...")
    
    try:
        print("  🔧 Importing main_bot...")
        from main_bot import TradingBot
        print("  ✅ Import successful")
        
        print("  🔧 Creating TradingBot instance...")
        # This will test the fixed coin categorizer code
        bot = TradingBot()
        print("  ✅ TradingBot initialized successfully!")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tests"""
    print("🧪 === COIN CATEGORIZER FIX TEST ===")
    
    success1 = test_coin_categorizer()
    success2 = test_main_bot_initialization()
    
    if success1 and success2:
        print("\n🎉 SUCCESS: All tests passed!")
        print("✅ CoinCategorizer dynamic list issue has been fixed!")
    else:
        print("\n❌ FAILED: Some tests failed")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
