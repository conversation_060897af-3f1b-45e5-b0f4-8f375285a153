#!/usr/bin/env python3
"""
🧪 TEST SIGNAL ENTRY/TP/SL FIX
Test để kiểm tra việc sửa lỗi thiếu entry/TP/SL trong signals và duplicate chart sending
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_signal_entry_tp_sl_fix():
    """Test signal entry/TP/SL fix and duplicate chart prevention"""
    print("🧪 === TESTING SIGNAL ENTRY/TP/SL FIX ===")
    print(f"⏰ Test started at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Test 1: Check AI signal method has entry/TP/SL
    print(f"\n🧪 TEST 1: Check AI signal method has entry/TP/SL calculation")
    
    try:
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            main_bot_content = f.read()
        
        # Check for AI signal improvements
        ai_improvements = [
            "Calculate entry/TP/SL for AI signals",
            "entry_price = current_price",
            "take_profit = ",
            "stop_loss = ",
            "risk_reward = ",
            "tp_sl_methods = ",
            "tp_sl_confidence = "
        ]
        
        ai_improvements_found = 0
        for improvement in ai_improvements:
            if improvement in main_bot_content:
                ai_improvements_found += 1
                print(f"  ✅ Found: {improvement}")
            else:
                print(f"  ❌ Missing: {improvement}")
        
        if ai_improvements_found >= 5:
            print(f"  ✅ AI signal entry/TP/SL calculation: IMPLEMENTED ({ai_improvements_found}/{len(ai_improvements)})")
        else:
            print(f"  ❌ AI signal entry/TP/SL calculation: INCOMPLETE ({ai_improvements_found}/{len(ai_improvements)})")
        
    except Exception as e:
        print(f"❌ Error checking AI signal improvements: {e}")
        return False
    
    # Test 2: Check Fibonacci signal method has entry/TP/SL
    print(f"\n🧪 TEST 2: Check Fibonacci signal method has entry/TP/SL calculation")
    
    try:
        fibonacci_improvements = [
            "Calculate entry/TP/SL for Fibonacci signals",
            "Use Fibonacci levels for TP/SL calculation",
            "support_levels = ",
            "resistance_levels = ",
            "Fibonacci-Levels"
        ]
        
        fib_improvements_found = 0
        for improvement in fibonacci_improvements:
            if improvement in main_bot_content:
                fib_improvements_found += 1
                print(f"  ✅ Found: {improvement}")
            else:
                print(f"  ❌ Missing: {improvement}")
        
        if fib_improvements_found >= 3:
            print(f"  ✅ Fibonacci signal entry/TP/SL calculation: IMPLEMENTED ({fib_improvements_found}/{len(fibonacci_improvements)})")
        else:
            print(f"  ❌ Fibonacci signal entry/TP/SL calculation: INCOMPLETE ({fib_improvements_found}/{len(fibonacci_improvements)})")
        
    except Exception as e:
        print(f"❌ Error checking Fibonacci signal improvements: {e}")
        return False
    
    # Test 3: Check Fourier signal method has entry/TP/SL
    print(f"\n🧪 TEST 3: Check Fourier signal method has entry/TP/SL calculation")
    
    try:
        fourier_improvements = [
            "Calculate entry/TP/SL for Fourier signals",
            "Use Fourier cycle analysis for TP/SL calculation",
            "cycle_amplitude = ",
            "cycle_multiplier = ",
            "Fourier-Cycles"
        ]
        
        fourier_improvements_found = 0
        for improvement in fourier_improvements:
            if improvement in main_bot_content:
                fourier_improvements_found += 1
                print(f"  ✅ Found: {improvement}")
            else:
                print(f"  ❌ Missing: {improvement}")
        
        if fourier_improvements_found >= 3:
            print(f"  ✅ Fourier signal entry/TP/SL calculation: IMPLEMENTED ({fourier_improvements_found}/{len(fourier_improvements)})")
        else:
            print(f"  ❌ Fourier signal entry/TP/SL calculation: INCOMPLETE ({fourier_improvements_found}/{len(fourier_improvements)})")
        
    except Exception as e:
        print(f"❌ Error checking Fourier signal improvements: {e}")
        return False
    
    # Test 4: Check Orderbook signal method has entry/TP/SL
    print(f"\n🧪 TEST 4: Check Orderbook signal method has entry/TP/SL calculation")
    
    try:
        orderbook_improvements = [
            "Add entry/TP/SL for orderbook signals",
            "Use spread and liquidity for TP/SL calculation",
            "spread_percentage = ",
            "liquidity_multiplier = ",
            "Orderbook-Spread"
        ]
        
        orderbook_improvements_found = 0
        for improvement in orderbook_improvements:
            if improvement in main_bot_content:
                orderbook_improvements_found += 1
                print(f"  ✅ Found: {improvement}")
            else:
                print(f"  ❌ Missing: {improvement}")
        
        if orderbook_improvements_found >= 3:
            print(f"  ✅ Orderbook signal entry/TP/SL calculation: IMPLEMENTED ({orderbook_improvements_found}/{len(orderbook_improvements)})")
        else:
            print(f"  ❌ Orderbook signal entry/TP/SL calculation: INCOMPLETE ({orderbook_improvements_found}/{len(orderbook_improvements)})")
        
    except Exception as e:
        print(f"❌ Error checking Orderbook signal improvements: {e}")
        return False
    
    # Test 5: Check for duplicate chart sending prevention
    print(f"\n🧪 TEST 5: Check for duplicate chart sending prevention")
    
    try:
        duplicate_prevention = [
            "_send_ai_text_report",
            "Call the enhanced method that includes entry/TP/SL calculation",
            "NO DUPLICATE SENDING",
            "FIXED: Send AI analysis with entry/TP/SL"
        ]
        
        duplicate_prevention_found = 0
        for prevention in duplicate_prevention:
            if prevention in main_bot_content:
                duplicate_prevention_found += 1
                print(f"  ✅ Found: {prevention}")
            else:
                print(f"  ❌ Missing: {prevention}")
        
        if duplicate_prevention_found >= 2:
            print(f"  ✅ Duplicate chart sending prevention: IMPLEMENTED ({duplicate_prevention_found}/{len(duplicate_prevention)})")
        else:
            print(f"  ❌ Duplicate chart sending prevention: INCOMPLETE ({duplicate_prevention_found}/{len(duplicate_prevention)})")
        
    except Exception as e:
        print(f"❌ Error checking duplicate prevention: {e}")
        return False
    
    # Test 6: Check signal message format improvements
    print(f"\n🧪 TEST 6: Check signal message format improvements")
    
    try:
        format_improvements = [
            "Trading Levels:",
            "Entry:",
            "Take Profit:",
            "Stop Loss:",
            "Risk/Reward:",
            "TP/SL Methods:",
            "TP/SL Confidence:"
        ]
        
        format_improvements_found = 0
        for improvement in format_improvements:
            count = main_bot_content.count(improvement)
            if count >= 3:  # Should appear in multiple signal types
                format_improvements_found += 1
                print(f"  ✅ Found {count}x: {improvement}")
            else:
                print(f"  ❌ Found {count}x: {improvement} (expected ≥3)")
        
        if format_improvements_found >= 5:
            print(f"  ✅ Signal message format improvements: IMPLEMENTED ({format_improvements_found}/{len(format_improvements)})")
        else:
            print(f"  ❌ Signal message format improvements: INCOMPLETE ({format_improvements_found}/{len(format_improvements)})")
        
    except Exception as e:
        print(f"❌ Error checking format improvements: {e}")
        return False
    
    # Test 7: Summary and recommendations
    print(f"\n📊 SIGNAL ENTRY/TP/SL FIX SUMMARY:")
    
    total_score = 0
    max_score = 6
    
    if ai_improvements_found >= 5:
        total_score += 1
        print(f"  ✅ AI signals: ENTRY/TP/SL ADDED")
    else:
        print(f"  ❌ AI signals: ENTRY/TP/SL MISSING")
    
    if fib_improvements_found >= 3:
        total_score += 1
        print(f"  ✅ Fibonacci signals: ENTRY/TP/SL ADDED")
    else:
        print(f"  ❌ Fibonacci signals: ENTRY/TP/SL MISSING")
    
    if fourier_improvements_found >= 3:
        total_score += 1
        print(f"  ✅ Fourier signals: ENTRY/TP/SL ADDED")
    else:
        print(f"  ❌ Fourier signals: ENTRY/TP/SL MISSING")
    
    if orderbook_improvements_found >= 3:
        total_score += 1
        print(f"  ✅ Orderbook signals: ENTRY/TP/SL ADDED")
    else:
        print(f"  ❌ Orderbook signals: ENTRY/TP/SL MISSING")
    
    if duplicate_prevention_found >= 2:
        total_score += 1
        print(f"  ✅ Duplicate chart prevention: IMPLEMENTED")
    else:
        print(f"  ❌ Duplicate chart prevention: MISSING")
    
    if format_improvements_found >= 5:
        total_score += 1
        print(f"  ✅ Message format: ENHANCED")
    else:
        print(f"  ❌ Message format: NEEDS IMPROVEMENT")
    
    print(f"\n🎯 OVERALL SCORE: {total_score}/{max_score}")
    
    if total_score == max_score:
        print(f"  🎉 EXCELLENT: All signal types have entry/TP/SL!")
        print(f"  ✅ No more incomplete trading signals")
        print(f"  ✅ Duplicate chart sending prevented")
    elif total_score >= 4:
        print(f"  ✅ GOOD: Most signal types improved")
        print(f"  🔧 Minor improvements may be needed")
    else:
        print(f"  ❌ NEEDS WORK: Significant signal improvements needed")
        print(f"  🚨 Many signals still missing entry/TP/SL")
    
    print(f"\n💡 WHAT WAS FIXED:")
    print(f"  1. ✅ AI signals now include entry/TP/SL calculation")
    print(f"  2. ✅ Fibonacci signals use Fibonacci levels for TP/SL")
    print(f"  3. ✅ Fourier signals use cycle analysis for TP/SL")
    print(f"  4. ✅ Orderbook signals use spread/liquidity for TP/SL")
    print(f"  5. ✅ Duplicate chart sending prevented")
    print(f"  6. ✅ Enhanced message format with trading levels")
    
    print(f"\n✅ Signal entry/TP/SL fix test completed!")
    print(f"⏰ Test finished at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    return total_score >= 4

if __name__ == "__main__":
    success = test_signal_entry_tp_sl_fix()
    if success:
        print(f"\n🎉 SIGNAL ENTRY/TP/SL FIX TEST PASSED!")
    else:
        print(f"\n❌ SIGNAL ENTRY/TP/SL FIX TEST FAILED!")
    
    sys.exit(0 if success else 1)
