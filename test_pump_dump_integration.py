#!/usr/bin/env python3
"""
🚨 PUMP/DUMP CONSENSUS INTEGRATION TEST
Test PUMP/DUMP signals in consensus analysis
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pump_dump_consensus_integration():
    """Test PUMP/DUMP signals integration with consensus analyzer"""
    print("🚨 TESTING PUMP/DUMP CONSENSUS INTEGRATION")
    print("=" * 60)
    
    try:
        # Import consensus analyzer
        from consensus_analyzer import ConsensusAnalyzer
        print("✅ ConsensusAnalyzer imported successfully")
        
        # Create test consensus analyzer
        consensus = ConsensusAnalyzer(
            min_consensus_score=0.4,
            confidence_threshold=0.3
        )
        print("✅ ConsensusAnalyzer initialized")
        
        # Test 1: High DUMP signal
        print("\n🔍 TEST 1: High DUMP Signal (80% probability)")
        dump_test_input = {
            'coin': 'TEST/USDT',
            'dump_analysis': {
                'probability': 0.8,  # 80% dump probability
                'stage': 'ACTIVE_DUMP',
                'confidence': 0.85,
                'severity': 'HIGH'
            },
            'pump_analysis': {
                'probability': 0.0,
                'stage': 'NONE',
                'confidence': 0.0,
                'severity': 'LOW'
            },
            'ai_prediction': {'ensemble_signal': 'BUY', 'ensemble_confidence': 0.3},
            'volume_profile': {'signal': 'BUY', 'confidence': 0.2},
            'point_figure': {'signal': 'BUY', 'confidence': 0.4},
            'fibonacci': {'signal': 'BUY', 'confidence': 0.3},
            'fourier': {'signal': 'BUY', 'confidence': 0.2},
            'orderbook': {'signals': {'primary_signal': 'BUY', 'confidence': 0.3}}
        }
        
        result1 = consensus.analyze_consensus(dump_test_input)
        consensus1 = result1.get('consensus', {})
        signal1 = consensus1.get('signal', 'NONE')
        confidence1 = consensus1.get('confidence', 0)
        print(f"📊 Result: {signal1} (confidence: {confidence1:.1%})")
        
        # Test 2: High PUMP signal
        print("\n🔍 TEST 2: High PUMP Signal (80% probability)")
        pump_test_input = {
            'coin': 'TEST/USDT',
            'dump_analysis': {
                'probability': 0.0,
                'stage': 'NONE',
                'confidence': 0.0,
                'severity': 'LOW'
            },
            'pump_analysis': {
                'probability': 0.8,  # 80% pump probability
                'stage': 'ACTIVE_PUMP',
                'confidence': 0.85,
                'severity': 'HIGH'
            },
            'ai_prediction': {'ensemble_signal': 'SELL', 'ensemble_confidence': 0.3},
            'volume_profile': {'signal': 'SELL', 'confidence': 0.2},
            'point_figure': {'signal': 'SELL', 'confidence': 0.4},
            'fibonacci': {'signal': 'SELL', 'confidence': 0.3},
            'fourier': {'signal': 'SELL', 'confidence': 0.2},
            'orderbook': {'signals': {'primary_signal': 'SELL', 'confidence': 0.3}}
        }
        
        result2 = consensus.analyze_consensus(pump_test_input)
        consensus2 = result2.get('consensus', {})
        signal2 = consensus2.get('signal', 'NONE')
        confidence2 = consensus2.get('confidence', 0)
        print(f"📊 Result: {signal2} (confidence: {confidence2:.1%})")
        
        # Test 3: Medium DUMP signal (15% - should be included)
        print("\n🔍 TEST 3: Medium DUMP Signal (15% probability)")
        medium_test_input = {
            'coin': 'TEST/USDT',
            'dump_analysis': {
                'probability': 0.15,  # 15% dump probability
                'stage': 'EARLY_DUMP',
                'confidence': 0.15,
                'severity': 'MEDIUM'
            },
            'pump_analysis': {
                'probability': 0.0,
                'stage': 'NONE',
                'confidence': 0.0,
                'severity': 'LOW'
            },
            'ai_prediction': {'ensemble_signal': 'BUY', 'ensemble_confidence': 0.7},
            'volume_profile': {'signal': 'BUY', 'confidence': 0.6},
            'point_figure': {'signal': 'BUY', 'confidence': 0.8},
            'fibonacci': {'signal': 'BUY', 'confidence': 0.7},
            'fourier': {'signal': 'BUY', 'confidence': 0.6},
            'orderbook': {'signals': {'primary_signal': 'BUY', 'confidence': 0.7}}
        }
        
        result3 = consensus.analyze_consensus(medium_test_input)
        consensus3 = result3.get('consensus', {})
        signal3 = consensus3.get('signal', 'NONE')
        confidence3 = consensus3.get('confidence', 0)
        print(f"📊 Result: {signal3} (confidence: {confidence3:.1%})")
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 PUMP/DUMP CONSENSUS INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        tests_passed = 0
        total_tests = 3
        
        # Check Test 1: High DUMP should override other signals
        if signal1 == 'SELL':
            print("✅ Test 1 PASSED: High DUMP signal correctly overrides BUY signals")
            tests_passed += 1
        else:
            print(f"❌ Test 1 FAILED: Expected SELL, got {signal1}")

        # Check Test 2: High PUMP should override other signals
        if signal2 == 'BUY':
            print("✅ Test 2 PASSED: High PUMP signal correctly overrides SELL signals")
            tests_passed += 1
        else:
            print(f"❌ Test 2 FAILED: Expected BUY, got {signal2}")

        # Check Test 3: Medium DUMP should be included in consensus
        if signal3 in ['SELL', 'BUY']:  # Should contribute to consensus
            print("✅ Test 3 PASSED: Medium DUMP signal correctly included in consensus")
            tests_passed += 1
        else:
            print(f"❌ Test 3 FAILED: Expected valid consensus signal, got {signal3}")
        
        print(f"\n🎯 FINAL RESULT: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 ALL TESTS PASSED - PUMP/DUMP consensus integration working correctly!")
            return True
        else:
            print(f"⚠️ {total_tests - tests_passed} tests failed - PUMP/DUMP integration needs fixes")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pump_dump_consensus_integration()
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
