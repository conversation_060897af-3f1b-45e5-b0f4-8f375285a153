<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            font-weight: bold;
            border-radius: 10px 10px 0 0 !important;
        }
        .active-signals-card {
            border-left: 5px solid #2ecc71;
        }
        .tp-sl-stats-card {
            border-left: 5px solid #3498db;
        }
        .model-performance-card {
            border-left: 5px solid #9b59b6;
        }
        .signal-history-card {
            border-left: 5px solid #e74c3c;
        }
        .stats-section .card {
            height: 100%;
        }
        .signal-table {
            font-size: 0.9rem;
        }
        .bg-buy {
            background-color: rgba(46, 204, 113, 0.15);
        }
        .bg-sell {
            background-color: rgba(231, 76, 60, 0.15);
        }
        .dashboard-heading {
            background: linear-gradient(45deg, #3498db, #2ecc71);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .chart-container {
            height: 300px;
        }
        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .profit {
            color: #2ecc71;
            font-weight: bold;
        }
        .loss {
            color: #e74c3c;
            font-weight: bold;
        }
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background-color: #f8f9fa;
            border-bottom-color: #f8f9fa;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
        .refresh-btn {
            cursor: pointer;
            transition: transform 0.3s;
        }
        .refresh-btn:hover {
            transform: rotate(180deg);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        .signal-badge {
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 50px;
            font-weight: 500;
        }
        @media (max-width: 768px) {
            .chart-container {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <header class="mb-4 d-flex justify-content-between align-items-center">
            <h1 class="dashboard-heading">Trading Bot Dashboard</h1>
            <div>
                <span class="badge bg-secondary me-2" id="last-updated">Last updated: Never</span>
                <i class="bi bi-arrow-clockwise refresh-btn fs-4" onclick="fetchDashboardData()"></i>
            </div>
        </header>
    
        <!-- Stats Overview Section -->
        <div class="row stats-section mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-value text-primary" id="active-signals-count">0</div>
                        <div class="stat-label">Active Signals</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-value text-success" id="tp-win-rate">0%</div>
                        <div class="stat-label">TP Win Rate</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-value text-warning" id="avg-holding-time">0h</div>
                        <div class="stat-label">Avg Holding Time</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-value text-info" id="avg-profit-pct">0%</div>
                        <div class="stat-label">Avg Profit per Trade</div>
                    </div>
                </div>
            </div>
        </div>
            
        <!-- Main Dashboard Content -->
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Active Signals Card -->
                <div class="card active-signals-card mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Active Signals</h5>
                        <span class="badge bg-primary" id="active-count">0</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover signal-table mb-0">
                                <thead>
                                    <tr>
                                        <th>Coin</th>
                                        <th>Type</th>
                                        <th>Entry</th>
                                        <th>Current</th>
                                        <th>TP</th>
                                        <th>SL</th>
                                        <th>PnL</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody id="active-signals-table">
                                    <!-- Active signals will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Signal History Card -->
                <div class="card signal-history-card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Signal History</h5>
                        <div>
                            <select class="form-select form-select-sm d-inline-block w-auto me-2" id="history-filter">
                                <option value="all">All</option>
                                <option value="tp">TP Hit</option>
                                <option value="sl">SL Hit</option>
                            </select>
                            <span class="badge bg-secondary" id="history-count">0</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover signal-table mb-0">
                                <thead>
                                    <tr>
                                        <th>Coin</th>
                                        <th>Type</th>
                                        <th>Entry</th>
                                        <th>Exit</th>
                                        <th>PnL</th>
                                        <th>Status</th>
                                        <th>Duration</th>
                                        <th>Closed</th>
                                    </tr>
                                </thead>
                                <tbody id="history-table">
                                    <!-- Signal history will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- TP/SL Statistics Card -->
                <div class="card tp-sl-stats-card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">TP/SL Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div id="tp-sl-chart" class="chart-container"></div>
                        <hr>
                        <div class="row text-center mt-3">
                            <div class="col-6">
                                <div class="stat-value text-success" id="tp-hit-count">0</div>
                                <div class="stat-label">TP Hits</div>
                            </div>
                            <div class="col-6">
                                <div class="stat-value text-danger" id="sl-hit-count">0</div>
                                <div class="stat-label">SL Hits</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Model Performance Card -->
                <div class="card model-performance-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Model Performance</h5>
                    </div>
                    <div class="card-body">
                        <div id="model-performance-chart" class="chart-container"></div>
                        <hr>
                        <div class="mt-3">
                            <h6>Top Contributing Models</h6>
                            <ul class="list-group list-group-flush" id="top-models-list">
                                <!-- Top models will be populated here -->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize dashboard data
        document.addEventListener('DOMContentLoaded', fetchDashboardData);
        
        // Set up periodic refresh
        setInterval(fetchDashboardData, 60000); // Refresh every minute
        
        // Function to fetch dashboard data from API
        function fetchDashboardData() {
            fetch('/api/dashboard-data')
                .then(response => response.json())
                .then(data => {
                    updateDashboard(data);
                    updateLastUpdated();
                })
                .catch(error => console.error('Error fetching dashboard data:', error));
        }
        
        // Update last updated timestamp
        function updateLastUpdated() {
            const now = new Date();
            const formattedTime = now.toLocaleTimeString();
            document.getElementById('last-updated').textContent = `Last updated: ${formattedTime}`;
        }
        
        // Update dashboard with new data
        function updateDashboard(data) {
            // Update stats overview
            document.getElementById('active-signals-count').textContent = data.stats.active_count;
            document.getElementById('tp-win-rate').textContent = data.stats.tp_win_rate + '%';
            document.getElementById('avg-holding-time').textContent = data.stats.avg_holding_time;
            document.getElementById('avg-profit-pct').textContent = data.stats.avg_profit_pct + '%';
            
            // Update active signals
            document.getElementById('active-count').textContent = data.active_signals.length;
            updateActiveSignalsTable(data.active_signals);
            
            // Update signal history
            document.getElementById('history-count').textContent = data.signal_history.length;
            updateHistoryTable(data.signal_history);
            
            // Update TP/SL statistics chart
            updateTpSlChart(data.stats.tp_count, data.stats.sl_count);
            document.getElementById('tp-hit-count').textContent = data.stats.tp_count;
            document.getElementById('sl-hit-count').textContent = data.stats.sl_count;
            
            // Update model performance chart
            updateModelPerformanceChart(data.model_stats);
            updateTopModelsList(data.model_stats);
        }
        
        // Update active signals table
        function updateActiveSignalsTable(signals) {
            const tableBody = document.getElementById('active-signals-table');
            tableBody.innerHTML = '';
            
            if (signals.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="text-center">No active signals</td>';
                tableBody.appendChild(row);
                return;
            }
            
            signals.forEach(signal => {
                const pnlClass = parseFloat(signal.pnl) >= 0 ? 'profit' : 'loss';
                const rowClass = signal.signal_type === 'BUY' ? 'bg-buy' : 'bg-sell';
                
                const row = document.createElement('tr');
                row.className = rowClass;
                
                row.innerHTML = `
                    <td>${signal.coin}</td>
                    <td><span class="badge ${signal.signal_type === 'BUY' ? 'bg-success' : 'bg-danger'}">${signal.signal_type}</span></td>
                    <td>${parseFloat(signal.entry).toFixed(8)}</td>
                    <td>${parseFloat(signal.current_price).toFixed(8)}</td>
                    <td>${parseFloat(signal.take_profit).toFixed(8)}</td>
                    <td>${parseFloat(signal.stop_loss).toFixed(8)}</td>
                    <td class="${pnlClass}">${signal.pnl}%</td>
                    <td>${signal.time_active}</td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // Update history table
        function updateHistoryTable(signals) {
            const tableBody = document.getElementById('history-table');
            tableBody.innerHTML = '';
            
            if (signals.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="text-center">No signal history</td>';
                tableBody.appendChild(row);
                return;
            }
            
            signals.forEach(signal => {
                const pnlClass = parseFloat(signal.pnl_percentage) >= 0 ? 'profit' : 'loss';
                const statusClass = signal.status === 'TP_HIT' ? 'bg-success' : 'bg-danger';
                const rowClass = signal.signal_type === 'BUY' ? 'bg-buy' : 'bg-sell';
                
                const row = document.createElement('tr');
                row.className = rowClass;
                
                row.innerHTML = `
                    <td>${signal.coin}</td>
                    <td><span class="badge ${signal.signal_type === 'BUY' ? 'bg-success' : 'bg-danger'}">${signal.signal_type}</span></td>
                    <td>${parseFloat(signal.entry).toFixed(8)}</td>
                    <td>${parseFloat(signal.closed_price).toFixed(8)}</td>
                    <td class="${pnlClass}">${signal.pnl_percentage}%</td>
                    <td><span class="badge ${statusClass}">${signal.status}</span></td>
                    <td>${signal.duration}</td>
                    <td>${signal.closed_time}</td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // Update TP/SL chart
        function updateTpSlChart(tpCount, slCount) {
            const data = [{
                values: [tpCount, slCount],
                labels: ['Take Profit', 'Stop Loss'],
                type: 'pie',
                marker: {
                    colors: ['#2ecc71', '#e74c3c']
                },
                textinfo: 'label+percent',
                textposition: 'inside',
                automargin: true
            }];
            
            const layout = {
                margin: {l: 0, r: 0, b: 30, t: 30, pad: 0},
                height: 250,
                showlegend: false
            };
            
            Plotly.newPlot('tp-sl-chart', data, layout, {responsive: true});
        }
        
        // Update model performance chart
        function updateModelPerformanceChart(modelStats) {
            // Sort models by success rate
            const sortedModels = Object.entries(modelStats)
                .sort((a, b) => b[1].success_rate - a[1].success_rate)
                .slice(0, 5); // Top 5 models
            
            const models = sortedModels.map(m => m[0]);
            const successRates = sortedModels.map(m => m[1].success_rate);
            const signalCounts = sortedModels.map(m => m[1].signal_count);
            
            const data = [{
                x: models,
                y: successRates,
                type: 'bar',
                marker: {
                    color: '#9b59b6'
                },
                text: signalCounts.map(count => `${count} signals`),
                textposition: 'auto',
                hoverinfo: 'x+y+text',
                name: 'Success Rate'
            }];
            
            const layout = {
                margin: {l: 50, r: 20, b: 50, t: 30, pad: 0},
                height: 250,
                yaxis: {
                    title: 'Success Rate (%)',
                    range: [0, 100]
                },
                xaxis: {
                    tickangle: -45
                },
                bargap: 0.3
            };
            
            Plotly.newPlot('model-performance-chart', data, layout, {responsive: true});
        }
        
        // Update top models list
        function updateTopModelsList(modelStats) {
            const topModelsList = document.getElementById('top-models-list');
            topModelsList.innerHTML = '';
            
            // Sort models by success rate
            const sortedModels = Object.entries(modelStats)
                .sort((a, b) => b[1].success_rate - a[1].success_rate)
                .slice(0, 5); // Top 5 models
            
            sortedModels.forEach(([model, stats]) => {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                listItem.innerHTML = `
                    ${model}
                    <div>
                        <span class="badge bg-primary rounded-pill me-2">${stats.signal_count} signals</span>
                        <span class="badge bg-success rounded-pill">${stats.success_rate}% success</span>
                    </div>
                `;
                topModelsList.appendChild(listItem);
            });
        }
        
        // Event listeners for filters
        document.getElementById('history-filter').addEventListener('change', function() {
            const filter = this.value;
            fetch(`/api/dashboard-data?history_filter=${filter}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('history-count').textContent = data.signal_history.length;
                    updateHistoryTable(data.signal_history);
                })
                .catch(error => console.error('Error applying filter:', error));
        });
    </script>
</body>
</html>
