#!/usr/bin/env python3
"""
🔧 MAIN_BOT.PY REFACTOR TOOL
===========================

Tool để phân tích và refactor main_bot.py để tối ưu hóa cấu trúc code.
Loại bỏ code trùng lặp, tối ưu hóa imports, và cải thiện tổ chức code.

Features:
- Phân tích code trùng lặp
- Tối ưu hóa imports
- Tổ chức lại cấu trúc logic
- Loại bỏ code không cần thiết
- Tạo backup trước khi refactor
"""

import os
import re
import time
import shutil
from typing import List, Dict, Set, Tu<PERSON>

def analyze_main_bot_structure():
    """Phân tích cấu trúc hiện tại của main_bot.py"""
    print("🔍 Analyzing main_bot.py structure...")
    
    if not os.path.exists("main_bot.py"):
        print("❌ main_bot.py not found!")
        return None
    
    with open("main_bot.py", "r", encoding="utf-8") as f:
        content = f.read()
        lines = content.split("\n")
    
    analysis = {
        "total_lines": len(lines),
        "import_lines": [],
        "class_definitions": [],
        "function_definitions": [],
        "duplicate_imports": [],
        "config_sections": [],
        "module_loading_sections": [],
        "large_functions": [],
        "comments_and_docstrings": 0,
        "empty_lines": 0
    }
    
    # Analyze line by line
    current_function = None
    function_line_count = 0
    in_multiline_comment = False
    
    for i, line in enumerate(lines, 1):
        stripped = line.strip()
        
        # Count empty lines
        if not stripped:
            analysis["empty_lines"] += 1
            continue
        
        # Count comments and docstrings
        if stripped.startswith("#") or '"""' in stripped or "'''" in stripped:
            analysis["comments_and_docstrings"] += 1
            if '"""' in stripped or "'''" in stripped:
                in_multiline_comment = not in_multiline_comment
            continue
        
        if in_multiline_comment:
            analysis["comments_and_docstrings"] += 1
            continue
        
        # Find imports
        if stripped.startswith("import ") or stripped.startswith("from "):
            analysis["import_lines"].append((i, stripped))
        
        # Find class definitions
        if stripped.startswith("class "):
            class_name = stripped.split("class ")[1].split("(")[0].split(":")[0].strip()
            analysis["class_definitions"].append((i, class_name))
        
        # Find function definitions
        if stripped.startswith("def "):
            if current_function and function_line_count > 100:
                analysis["large_functions"].append((current_function[0], current_function[1], function_line_count))
            
            func_name = stripped.split("def ")[1].split("(")[0].strip()
            current_function = (i, func_name)
            function_line_count = 0
            analysis["function_definitions"].append((i, func_name))
        
        if current_function:
            function_line_count += 1
        
        # Find configuration sections
        if "CONFIGURATION" in stripped.upper() or "CONFIG" in stripped.upper():
            analysis["config_sections"].append((i, stripped))
        
        # Find module loading sections
        if "MODULES" in stripped.upper() and ("LOADING" in stripped.upper() or "IMPORT" in stripped.upper()):
            analysis["module_loading_sections"].append((i, stripped))
    
    # Check for duplicate imports
    import_statements = [imp[1] for imp in analysis["import_lines"]]
    seen_imports = set()
    for imp in import_statements:
        if imp in seen_imports:
            analysis["duplicate_imports"].append(imp)
        seen_imports.add(imp)
    
    return analysis

def find_duplicate_code_blocks():
    """Tìm các khối code trùng lặp"""
    print("🔍 Finding duplicate code blocks...")
    
    with open("main_bot.py", "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    duplicates = []
    
    # Look for duplicate module loading patterns
    module_loading_patterns = []
    for i, line in enumerate(lines):
        if "enhanced_module_loader" in line or "MODULES[" in line:
            # Get context (5 lines before and after)
            start = max(0, i - 5)
            end = min(len(lines), i + 5)
            context = "".join(lines[start:end]).strip()
            module_loading_patterns.append((i, context))
    
    # Find similar patterns
    for i, (line1, pattern1) in enumerate(module_loading_patterns):
        for j, (line2, pattern2) in enumerate(module_loading_patterns[i+1:], i+1):
            similarity = calculate_similarity(pattern1, pattern2)
            if similarity > 0.8:  # 80% similar
                duplicates.append({
                    "type": "module_loading",
                    "line1": line1,
                    "line2": line2,
                    "similarity": similarity,
                    "pattern1": pattern1[:100] + "...",
                    "pattern2": pattern2[:100] + "..."
                })
    
    return duplicates

def calculate_similarity(text1: str, text2: str) -> float:
    """Tính độ tương đồng giữa 2 đoạn text"""
    words1 = set(text1.split())
    words2 = set(text2.split())
    
    if not words1 and not words2:
        return 1.0
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union)

def create_backup():
    """Tạo backup của main_bot.py"""
    timestamp = int(time.time())
    backup_name = f"main_bot_backup_{timestamp}.py"
    
    try:
        shutil.copy2("main_bot.py", backup_name)
        print(f"✅ Backup created: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ Failed to create backup: {e}")
        return None

def optimize_imports():
    """Tối ưu hóa imports - loại bỏ trùng lặp và sắp xếp"""
    print("🔧 Optimizing imports...")
    
    with open("main_bot.py", "r", encoding="utf-8") as f:
        content = f.read()
        lines = content.split("\n")
    
    # Collect all imports
    imports = []
    non_import_lines = []
    
    for line in lines:
        stripped = line.strip()
        if stripped.startswith("import ") or stripped.startswith("from "):
            imports.append(line)
        else:
            non_import_lines.append(line)
    
    # Remove duplicates while preserving order
    seen = set()
    unique_imports = []
    for imp in imports:
        if imp.strip() not in seen:
            unique_imports.append(imp)
            seen.add(imp.strip())
    
    # Sort imports
    standard_imports = []
    third_party_imports = []
    local_imports = []
    
    for imp in unique_imports:
        stripped = imp.strip()
        if stripped.startswith("from .") or stripped.startswith("import ."):
            local_imports.append(imp)
        elif any(lib in stripped for lib in ["os", "sys", "time", "json", "random", "traceback", "warnings", "asyncio", "argparse"]):
            standard_imports.append(imp)
        else:
            third_party_imports.append(imp)
    
    # Combine optimized imports
    optimized_imports = []
    if standard_imports:
        optimized_imports.extend(standard_imports)
        optimized_imports.append("")
    if third_party_imports:
        optimized_imports.extend(third_party_imports)
        optimized_imports.append("")
    if local_imports:
        optimized_imports.extend(local_imports)
        optimized_imports.append("")
    
    return optimized_imports, non_import_lines

def remove_duplicate_module_loading():
    """Loại bỏ code trùng lặp trong module loading"""
    print("🔧 Removing duplicate module loading code...")
    
    with open("main_bot.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Remove duplicate advanced modules loading
    pattern1 = r'try:\s*\n\s*# Advanced analysis imports.*?except ImportError as e:.*?print\("Bot will continue with basic functionality"\)'
    content = re.sub(pattern1, "# ✅ REMOVED DUPLICATE ADVANCED MODULE LOADING", content, flags=re.DOTALL)
    
    # Remove duplicate communication modules loading
    pattern2 = r'try:\s*\n\s*# Communication and notification imports.*?except ImportError as e:.*?print\("Telegram features may be limited"\)'
    content = re.sub(pattern2, "# ✅ REMOVED DUPLICATE COMMUNICATION MODULE LOADING", content, flags=re.DOTALL)
    
    return content

def consolidate_configuration_sections():
    """Hợp nhất các section configuration"""
    print("🔧 Consolidating configuration sections...")
    
    with open("main_bot.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Find all configuration sections
    config_sections = re.findall(r'# ={10,}.*?CONFIGURATION.*?# ={10,}', content, re.DOTALL | re.IGNORECASE)
    
    print(f"   Found {len(config_sections)} configuration sections")
    
    # This is a complex refactor that would need more detailed analysis
    # For now, just mark duplicate sections
    
    return content

def refactor_large_functions():
    """Refactor các function quá lớn"""
    print("🔧 Analyzing large functions for refactoring...")
    
    analysis = analyze_main_bot_structure()
    if not analysis:
        return
    
    large_functions = analysis.get("large_functions", [])
    
    if large_functions:
        print(f"   Found {len(large_functions)} large functions:")
        for line_num, func_name, line_count in large_functions:
            print(f"     - {func_name} (line {line_num}): {line_count} lines")
    else:
        print("   No excessively large functions found")

def main():
    """Main refactor execution"""
    print("🔧 MAIN_BOT.PY REFACTOR TOOL")
    print("=" * 50)
    
    # Step 1: Analyze current structure
    analysis = analyze_main_bot_structure()
    if not analysis:
        return
    
    print(f"\n📊 ANALYSIS RESULTS:")
    print(f"  📄 Total lines: {analysis['total_lines']:,}")
    print(f"  📦 Import statements: {len(analysis['import_lines'])}")
    print(f"  🏗️ Class definitions: {len(analysis['class_definitions'])}")
    print(f"  ⚙️ Function definitions: {len(analysis['function_definitions'])}")
    print(f"  🔄 Duplicate imports: {len(analysis['duplicate_imports'])}")
    print(f"  📝 Comments/docstrings: {analysis['comments_and_docstrings']}")
    print(f"  📄 Empty lines: {analysis['empty_lines']}")
    print(f"  📊 Large functions: {len(analysis['large_functions'])}")
    
    # Step 2: Find duplicates
    duplicates = find_duplicate_code_blocks()
    print(f"\n🔍 DUPLICATE CODE ANALYSIS:")
    print(f"  🔄 Duplicate code blocks found: {len(duplicates)}")
    
    for dup in duplicates[:3]:  # Show first 3
        print(f"    - {dup['type']} (lines {dup['line1']}, {dup['line2']}) - {dup['similarity']*100:.1f}% similar")
    
    # Step 3: Create backup
    print(f"\n💾 CREATING BACKUP...")
    backup_file = create_backup()
    if not backup_file:
        print("❌ Cannot proceed without backup")
        return
    
    # Step 4: Perform refactoring
    print(f"\n🔧 PERFORMING REFACTORING...")
    
    try:
        # Optimize imports
        optimized_imports, non_import_lines = optimize_imports()
        print(f"  ✅ Optimized imports: {len(analysis['import_lines'])} → {len([i for i in optimized_imports if i.strip()])}")
        
        # Remove duplicate module loading
        content = remove_duplicate_module_loading()
        print(f"  ✅ Removed duplicate module loading sections")
        
        # Analyze large functions
        refactor_large_functions()
        
        # Write optimized content
        with open("main_bot_optimized.py", "w", encoding="utf-8") as f:
            # Write optimized imports
            for imp in optimized_imports:
                f.write(imp + "\n")
            
            # Write rest of content (skip original imports)
            in_import_section = True
            for line in non_import_lines:
                stripped = line.strip()
                if in_import_section and stripped and not stripped.startswith("#") and not stripped.startswith("import") and not stripped.startswith("from"):
                    in_import_section = False
                
                if not in_import_section:
                    f.write(line + "\n")
        
        print(f"\n✅ REFACTORING COMPLETED!")
        print(f"  📄 Original file: main_bot.py ({analysis['total_lines']:,} lines)")
        print(f"  💾 Backup file: {backup_file}")
        print(f"  🔧 Optimized file: main_bot_optimized.py")
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"  1. Review main_bot_optimized.py")
        print(f"  2. Test the optimized version")
        print(f"  3. Replace main_bot.py if tests pass")
        print(f"  4. Consider breaking large functions into smaller ones")
        print(f"  5. Move configuration to separate config files")
        
    except Exception as e:
        print(f"❌ Refactoring failed: {e}")
        print(f"💾 Original file preserved, backup available: {backup_file}")

if __name__ == "__main__":
    main()
