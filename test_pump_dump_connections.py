#!/usr/bin/env python3
"""
🧪 TEST PUMP/DUMP DETECTOR CONNECTIONS
=====================================

Test để kiểm tra PUMP/DUMP detectors có được kết nối với consensus analyzer không.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pump_dump_connections():
    """Test PUMP/DUMP detector connections."""
    print("🧪 TESTING PUMP/DUMP DETECTOR CONNECTIONS")
    print("=" * 70)
    
    try:
        # Test main_bot.py external_analyzers configuration
        print("📊 1. CHECKING MAIN_BOT EXTERNAL_ANALYZERS:")
        print("=" * 50)
        
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check if external_analyzers includes pump_detector and dump_detector
        if '"dump_detector": self.dump_detector' in content:
            print("  ✅ dump_detector: Found in external_analyzers")
        else:
            print("  ❌ dump_detector: NOT found in external_analyzers")
            
        if '"pump_detector": self.volume_detector' in content:
            print("  ✅ pump_detector: Found in external_analyzers (using volume_detector)")
        else:
            print("  ❌ pump_detector: NOT found in external_analyzers")
            
        # Check weight configuration
        if '"dump_detector": 0.05' in content or '"dump_detector": 0.08' in content:
            print("  ✅ dump_detector weight: Found in weight_config")
        else:
            print("  ❌ dump_detector weight: NOT found in weight_config")
            
        if '"pump_detector": 0.05' in content or '"pump_detector": 0.08' in content:
            print("  ✅ pump_detector weight: Found in weight_config")
        else:
            print("  ❌ pump_detector weight: NOT found in weight_config")
            
        print("\n📊 2. CHECKING CONSENSUS_ANALYZER CONNECTIONS:")
        print("=" * 50)
        
        # Test consensus_analyzer.py connections
        with open('consensus_analyzer.py', 'r', encoding='utf-8') as f:
            consensus_content = f.read()
            
        # Check analyzer_connections
        if '"dump_detector": None' in consensus_content:
            print("  ✅ dump_detector: Found in analyzer_connections")
        else:
            print("  ❌ dump_detector: NOT found in analyzer_connections")
            
        if '"pump_detector": None' in consensus_content:
            print("  ✅ pump_detector: Found in analyzer_connections")
        else:
            print("  ❌ pump_detector: NOT found in analyzer_connections")
            
        # Check weight configuration
        if '"dump_detector": 0.08' in consensus_content:
            print("  ✅ dump_detector weight: Found in consensus weight_config")
        else:
            print("  ❌ dump_detector weight: NOT found in consensus weight_config")
            
        if '"pump_detector": 0.08' in consensus_content:
            print("  ✅ pump_detector weight: Found in consensus weight_config")
        else:
            print("  ❌ pump_detector weight: NOT found in consensus weight_config")
            
        # Check analysis logic
        if 'Pump Detector:' in consensus_content:
            print("  ✅ pump_detector analysis: Found in consensus logic")
        else:
            print("  ❌ pump_detector analysis: NOT found in consensus logic")
            
        if 'Dump Detector:' in consensus_content:
            print("  ✅ dump_detector analysis: Found in consensus logic")
        else:
            print("  ❌ dump_detector analysis: NOT found in consensus logic")
            
        print("\n📊 3. CHECKING ALGORITHM COUNT:")
        print("=" * 50)
        
        # Check total algorithm count
        if 'total_possible_algorithms = 8' in consensus_content:
            print("  ✅ Total algorithms: Updated to 8 (includes PUMP/DUMP)")
        elif 'total_possible_algorithms = 6' in consensus_content:
            print("  ❌ Total algorithms: Still 6 (missing PUMP/DUMP)")
        else:
            print("  ❓ Total algorithms: Count not found or different")
            
        # Check minimum required signals
        if 'min_required_signals = max(5' in consensus_content:
            print("  ✅ Min required signals: Set to 5+ (62.5% of 8)")
        else:
            print("  ❌ Min required signals: Not properly configured")
            
        print("\n📊 4. EXPECTED BEHAVIOR:")
        print("=" * 50)
        
        print("✅ When PUMP/DUMP detectors are connected:")
        print("   - Consensus will show: '✅ Pump Detector: BUY (XX%) - Weight: 0.08'")
        print("   - Consensus will show: '✅ Dump Detector: SELL (XX%) - Weight: 0.08'")
        print("   - Total possible algorithms: 8")
        print("   - Minimum required signals: 5/8 (62.5%)")
        print("")
        
        print("❌ Current behavior (from your log):")
        print("   - Shows: '❌ Dump Detector: Not connected'")
        print("   - Missing: Pump Detector analysis")
        print("   - Only 3/8 algorithms contributing")
        print("   - Consensus requirements not met")
        print("")
        
        print("🔧 DIAGNOSIS:")
        print("=" * 50)
        
        # Analyze the issue
        main_bot_ok = ('"dump_detector": self.dump_detector' in content and 
                      '"pump_detector": self.volume_detector' in content)
        consensus_ok = ('"dump_detector": None' in consensus_content and 
                       '"pump_detector": None' in consensus_content)
        
        if main_bot_ok and consensus_ok:
            print("✅ Configuration looks correct in both files")
            print("🔍 Issue might be:")
            print("   1. dump_detector object is None in main_bot")
            print("   2. volume_detector doesn't have pump analysis methods")
            print("   3. analyzer_connections not properly passed to consensus")
            print("   4. OHLCV data missing pump/dump analysis results")
        elif not main_bot_ok:
            print("❌ main_bot.py configuration incomplete")
            print("🔧 Need to fix external_analyzers and weight_config")
        elif not consensus_ok:
            print("❌ consensus_analyzer.py configuration incomplete")
            print("🔧 Need to fix analyzer_connections and analysis logic")
        else:
            print("❓ Configuration status unclear")
            
        return main_bot_ok and consensus_ok
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_volume_detector_pump_methods():
    """Test if volume_detector has pump detection methods."""
    print("\n🧪 TESTING VOLUME_DETECTOR PUMP METHODS")
    print("=" * 70)
    
    try:
        # Check if volume_detector has pump-related methods
        with open('volume_detector.py', 'r', encoding='utf-8') as f:
            volume_content = f.read()
            
        pump_methods = [
            'detect_pump',
            'analyze_pump',
            'pump_probability',
            'pump_analysis'
        ]
        
        found_methods = []
        for method in pump_methods:
            if method in volume_content:
                found_methods.append(method)
                print(f"  ✅ {method}: Found in volume_detector.py")
            else:
                print(f"  ❌ {method}: NOT found in volume_detector.py")
                
        print(f"\n📊 PUMP METHODS SUMMARY:")
        print(f"  Found: {len(found_methods)}/{len(pump_methods)} methods")
        
        if len(found_methods) >= 2:
            print(f"  ✅ Volume detector likely supports pump detection")
            return True
        else:
            print(f"  ❌ Volume detector may not support pump detection")
            print(f"  🔧 May need to add pump detection methods to volume_detector")
            return False
            
    except FileNotFoundError:
        print("  ❌ volume_detector.py not found")
        return False
    except Exception as e:
        print(f"  ❌ Error checking volume_detector: {e}")
        return False

def main():
    """Run all connection tests."""
    print("🧪 TESTING PUMP/DUMP DETECTOR CONNECTIONS")
    print("=" * 70)
    
    # Run tests
    test1 = test_pump_dump_connections()
    test2 = test_volume_detector_pump_methods()
    
    # Summary
    print(f"\n🎯 CONNECTION TEST SUMMARY:")
    print("=" * 50)
    print(f"🔧 Configuration Status: {'✅ CORRECT' if test1 else '❌ NEEDS FIXING'}")
    print(f"📊 Volume Detector Pump Support: {'✅ AVAILABLE' if test2 else '❌ MISSING'}")
    
    if test1 and test2:
        print(f"\n🎉 PUMP/DUMP DETECTORS SHOULD BE WORKING!")
        print(f"🔍 If still showing 'Not connected', check:")
        print(f"   1. self.dump_detector is not None in main_bot")
        print(f"   2. analyzer_connections properly passed to consensus")
        print(f"   3. OHLCV data includes pump/dump analysis")
    elif test1 and not test2:
        print(f"\n⚠️ Configuration OK but volume_detector may lack pump methods")
        print(f"🔧 Consider adding pump detection methods to volume_detector.py")
    elif not test1:
        print(f"\n❌ Configuration issues found")
        print(f"🔧 Fix the configuration issues identified above")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"1. Run the bot and check if PUMP/DUMP detectors now show as connected")
    print(f"2. Look for '✅ Pump Detector:' and '✅ Dump Detector:' in logs")
    print(f"3. Verify total algorithms count shows 8 instead of 6")

if __name__ == "__main__":
    main()
