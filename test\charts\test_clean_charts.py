#!/usr/bin/env python3
"""
🧪 Test Clean Chart Generation
Test the new clean chart generation functionality
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_ohlcv_data(periods: int = 100) -> pd.DataFrame:
    """📊 Create sample OHLCV data for testing."""
    
    # Generate realistic price data
    base_price = 0.32
    dates = pd.date_range(start=datetime.now() - timedelta(hours=periods), periods=periods, freq='1H')
    
    # Generate price movements
    np.random.seed(42)  # For reproducible results
    price_changes = np.random.normal(0, 0.002, periods)  # 0.2% volatility
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(0.001, new_price))  # Ensure positive prices
    
    # Create OHLCV data
    ohlcv_data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from close price
        close = price
        open_price = close * (1 + np.random.normal(0, 0.001))
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.002)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.002)))
        volume = np.random.uniform(1000000, 5000000)  # Random volume
        
        ohlcv_data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(ohlcv_data)
    df.set_index('timestamp', inplace=True)
    return df

def create_sample_consensus_data() -> dict:
    """🎯 Create sample consensus data for testing."""
    return {
        'consensus_signal': 'BUY',
        'consensus_score': 0.847,
        'consensus_confidence': 0.905,
        'total_algorithms': 6,
        'agreeing_algorithms': 5,
        'algorithm_votes': {
            'BUY': 5,
            'SELL': 1,
            'NONE': 0
        },
        'signal_quality': {
            'strength': 0.791,
            'overall_quality': 0.674,
            'tp_sl_methods_count': 3,
            'algorithm_diversity': 5,
            'confidence_score': 0.850
        }
    }

def create_sample_signal_data(current_price: float) -> dict:
    """📊 Create sample signal data for testing."""
    return {
        'signal_type': 'BUY',
        'entry': current_price,
        'take_profit': current_price * 1.05,  # 5% profit
        'stop_loss': current_price * 0.98,    # 2% loss
        'risk_reward_ratio': 2.5,
        'tp_sl_methods': ['Intelligent_Fallback', 'Risk_Management', 'Technical_Analysis'],
        'tp_sl_confidence': 0.710
    }

def create_sample_fibonacci_data(current_price: float) -> dict:
    """🌀 Create sample Fibonacci data for testing."""
    return {
        'trend_direction': 'BULLISH',
        'retracement_levels': [
            {'ratio': 0.236, 'price': current_price * 0.98},
            {'ratio': 0.382, 'price': current_price * 0.96},
            {'ratio': 0.500, 'price': current_price * 0.94},
            {'ratio': 0.618, 'price': current_price * 0.92},
        ],
        'extension_levels': [
            {'ratio': 1.272, 'price': current_price * 1.08},
            {'ratio': 1.618, 'price': current_price * 1.12},
        ]
    }

def create_sample_volume_data(current_price: float) -> dict:
    """📊 Create sample volume profile data for testing."""
    return {
        'vpoc': {
            'price': current_price * 0.995,
            'volume': 2500000
        },
        'high_volume_nodes': [
            {'price': current_price * 0.99, 'volume': 2000000},
            {'price': current_price * 1.01, 'volume': 1800000},
            {'price': current_price * 0.97, 'volume': 1500000},
        ],
        'low_volume_nodes': [
            {'price': current_price * 1.03, 'volume': 500000},
            {'price': current_price * 0.95, 'volume': 600000},
        ]
    }

def test_clean_chart_generation():
    """🧪 Test clean chart generation."""
    print("🧪 Testing Clean Chart Generation...")
    print("=" * 50)
    
    try:
        # Import chart generator
        from chart_generator import EnhancedChartGenerator
        
        # Create chart generator
        chart_gen = EnhancedChartGenerator()
        
        # Create sample data
        ohlcv_data = create_sample_ohlcv_data(100)
        current_price = ohlcv_data['close'].iloc[-1]
        
        print(f"📊 Sample data created:")
        print(f"  - OHLCV periods: {len(ohlcv_data)}")
        print(f"  - Current price: {current_price:.8f}")
        print(f"  - Price range: {ohlcv_data['low'].min():.8f} - {ohlcv_data['high'].max():.8f}")
        
        # Test 1: Clean Consensus Chart
        print(f"\n🎯 Test 1: Clean Consensus Chart")
        consensus_data = create_sample_consensus_data()
        signal_data = create_sample_signal_data(current_price)
        
        consensus_chart = chart_gen.generate_consensus_chart("TEST/USDT", consensus_data, signal_data, ohlcv_data)
        
        if consensus_chart and os.path.exists(consensus_chart):
            print(f"  ✅ Clean consensus chart generated: {os.path.basename(consensus_chart)}")
            print(f"  📁 Path: {consensus_chart}")
        else:
            print(f"  ❌ Failed to generate clean consensus chart")
        
        # Test 2: Clean Fibonacci Chart
        print(f"\n🌀 Test 2: Clean Fibonacci Chart")
        fibonacci_data = create_sample_fibonacci_data(current_price)
        
        if hasattr(chart_gen, 'generate_clean_fibonacci_chart'):
            fibonacci_chart = chart_gen.generate_clean_fibonacci_chart("TEST/USDT", fibonacci_data, ohlcv_data, current_price)
            
            if fibonacci_chart and os.path.exists(fibonacci_chart):
                print(f"  ✅ Clean Fibonacci chart generated: {os.path.basename(fibonacci_chart)}")
                print(f"  📁 Path: {fibonacci_chart}")
            else:
                print(f"  ❌ Failed to generate clean Fibonacci chart")
        else:
            print(f"  ⚠️ Clean Fibonacci chart method not found")
        
        # Test 3: Clean Volume Profile Chart
        print(f"\n📊 Test 3: Clean Volume Profile Chart")
        volume_data = create_sample_volume_data(current_price)
        
        if hasattr(chart_gen, 'generate_clean_volume_profile_chart'):
            volume_chart = chart_gen.generate_clean_volume_profile_chart("TEST/USDT", volume_data, ohlcv_data, current_price)
            
            if volume_chart and os.path.exists(volume_chart):
                print(f"  ✅ Clean Volume Profile chart generated: {os.path.basename(volume_chart)}")
                print(f"  📁 Path: {volume_chart}")
            else:
                print(f"  ❌ Failed to generate clean Volume Profile chart")
        else:
            print(f"  ⚠️ Clean Volume Profile chart method not found")
        
        print(f"\n🎯 Test Results Summary:")
        print(f"✅ Clean chart generation system is working!")
        print(f"📊 Charts are saved in: {chart_gen.output_dir}")
        print(f"🎨 Charts should have:")
        print(f"  - Only candlesticks + technical lines")
        print(f"  - No text annotations or legends")
        print(f"  - Clean white background")
        print(f"  - Beautiful colors")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing clean chart generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Clean Chart Generation Test Suite")
    print("=" * 60)
    
    success = test_clean_chart_generation()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 Clean chart generation is ready!")
    else:
        print("\n❌ TESTS FAILED!")
        print("🔧 Check the error messages above.")
