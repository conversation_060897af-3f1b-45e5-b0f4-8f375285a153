#!/usr/bin/env python3
"""
🧪 Test Analysis Flow - Check if all algorithms are running
"""

import os
import sys
import time
from datetime import datetime

def test_analysis_flow():
    """Test if all analysis algorithms are running properly."""
    print("🧪 Testing Analysis Flow...")
    print("=" * 60)
    
    # Check environment variables
    print("📊 Checking Signal Quality Filter Configuration:")
    
    filter_enabled = os.getenv("SIGNAL_QUALITY_FILTER_ENABLED", "1")
    min_threshold = os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.70")
    
    print(f"  - Filter Enabled: {filter_enabled}")
    print(f"  - Min Threshold: {min_threshold}")
    
    if filter_enabled == "0":
        print("  ✅ Signal Quality Filter is DISABLED - All algorithms should run")
    else:
        print(f"  ⚠️ Signal Quality Filter is ENABLED with {float(min_threshold)*100:.0f}% threshold")
    
    print("\n📋 Expected Analysis Flow:")
    expected_analyses = [
        "1. 🚨 ENHANCED dump detection",
        "2. 📋 ENHANCED orderbook analysis", 
        "3. 📊 ENHANCED volume pattern analysis",
        "4. ⚡ ENHANCED volume spike + pump detection",
        "5. 🌀 ENHANCED signal processing (ZigZag+Fib+Fourier)",
        "6. 🌊 ENHANCED Fourier analysis",
        "7. 📊 ENHANCED Volume Profile analysis",
        "8. 📈 ENHANCED Point & Figure analysis",
        "9. 🤖 FORCING AI Analysis",
        "10. 🎯 ENHANCED consensus analysis"
    ]
    
    for analysis in expected_analyses:
        print(f"  {analysis}")
    
    print("\n🔍 Analysis Status Check:")
    print("  - If you see all 10 analyses in the log, the flow is working correctly")
    print("  - If some analyses are missing, there might be errors or early exits")
    print("  - Check for error messages or exceptions in the log")
    
    print("\n💡 Troubleshooting Tips:")
    print("  1. Check if all analyzer modules are properly initialized")
    print("  2. Look for 'Error in enhanced...' messages")
    print("  3. Verify data availability (OHLCV, volume, etc.)")
    print("  4. Check if Signal Quality Filter is blocking analyses")
    print("  5. Look for 'continue' statements that might skip coins")
    
    print("\n🎯 What to Look For in Logs:")
    print("  ✅ '📊 Running ENHANCED Volume Profile analysis...'")
    print("  ✅ '📈 Running ENHANCED Point & Figure analysis...'") 
    print("  ✅ '🤖 FORCING AI Analysis regardless of consensus status...'")
    print("  ✅ '🎯 Running ENHANCED consensus analysis with AI prediction...'")
    
    print("\n" + "=" * 60)
    print("✅ Analysis Flow Test Complete!")
    print("Now run the main bot and check if all analyses appear in the log.")

if __name__ == "__main__":
    test_analysis_flow()
