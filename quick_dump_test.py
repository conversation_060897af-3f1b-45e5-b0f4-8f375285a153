#!/usr/bin/env python3
"""
🚨 QUICK DUMP DETECTOR TEST
Quick test to verify dump detector fixes
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test for dump detector fixes"""
    print("🚨 QUICK DUMP DETECTOR FIXES TEST")
    print("=" * 50)
    
    try:
        print("🔍 Testing UltraEarlyDumpAlert import...")
        from dump_detector import UltraEarlyDumpAlert
        print("✅ UltraEarlyDumpAlert imported")
        
        print("\n🔍 Testing UltraEarlyDumpAlert current_price attribute...")
        from datetime import datetime, timedelta
        
        alert = UltraEarlyDumpAlert(
            coin="TEST/USDT",
            detection_time=datetime.now(),
            dump_probability=0.5,
            risk_level="MEDIUM",
            confidence_score=0.6,
            estimated_dump_time=datetime.now() + timedelta(minutes=30),
            estimated_dump_magnitude=0.1,
            warning_stage="PRE_DUMP",
            current_price=100.0
        )
        
        print(f"✅ UltraEarlyDumpAlert created with current_price: {alert.current_price}")
        
        print("\n🔍 Testing DumpDetector import...")
        from dump_detector import DumpDetector
        print("✅ DumpDetector imported")
        
        print("\n🎉 ALL QUICK TESTS PASSED!")
        print("✅ current_price attribute fix is working")
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    print(f"\n🎯 Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
