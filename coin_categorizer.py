#!/usr/bin/env python3
"""
🏷️ ENHANCED COIN CATEGORIZER V2.0 - PRODUCTION READY
====================================================

Advanced Coin Categorization System with Machine Learning Integration:
- 🏷️ Intelligent coin categorization with dynamic sector detection
- 📊 Advanced market cap analysis and trend recognition
- 🔄 Real-time category updates with API integration
- 🎯 Smart categorization with ML-based pattern recognition
- 📈 Cross-market correlation analysis for better categorization
- 🚀 Performance optimized for large coin databases
- 🛡️ Comprehensive error handling and fallback systems

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import re
import json
import os
import requests
import time
import threading
import warnings
import hashlib
from typing import Dict, List, Set, Optional, Union
from datetime import datetime, timedelta

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    import pandas as pd
    AVAILABLE_MODULES['pandas'] = True
    print("✅ pandas imported successfully - Advanced data analysis available")
except ImportError:
    AVAILABLE_MODULES['pandas'] = False
    print("⚠️ pandas not available - Using basic data processing")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.cluster import KMeans
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - ML categorization available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic categorization")

try:
    import nltk
    AVAILABLE_MODULES['nltk'] = True
    print("✅ NLTK imported successfully - Natural language processing available")
except ImportError:
    AVAILABLE_MODULES['nltk'] = False
    print("⚠️ NLTK not available - Limited text analysis")

print(f"🏷️ Coin Categorizer V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class CoinCategorizer:
    """
    🏷️ ENHANCED COIN CATEGORIZER V2.0 - PRODUCTION READY
    ====================================================

    Advanced Coin Categorization System with comprehensive features:
    - 🏷️ Intelligent coin categorization with dynamic sector detection
    - 📊 Advanced market cap analysis and trend recognition
    - 🔄 Real-time category updates with API integration
    - 🎯 Smart categorization with ML-based pattern recognition
    - 📈 Cross-market correlation analysis for better categorization
    - 🚀 Performance optimized for large coin databases
    """

    def __init__(self, cache_file: str = "coin_categories_cache.json",
                 auto_update: bool = True, update_interval: int = 3600,
                 use_dynamic_sectors: bool = True,
                 enable_smart_categorization: bool = True,
                 enable_market_cap_analysis: bool = True,
                 enable_ml_clustering: bool = False):
        """
        Initialize Enhanced Coin Categorizer V2.0.

        Args:
            cache_file: Cache file for storing categorizations
            auto_update: Enable automatic updates from APIs
            update_interval: Update interval in seconds (3600)
            use_dynamic_sectors: Use dynamic sector detection
            enable_smart_categorization: Enable smart categorization
            enable_market_cap_analysis: Enable market cap analysis
            enable_ml_clustering: Enable ML-based clustering
        """
        print("🏷️ Initializing Enhanced Coin Categorizer V2.0...")

        # Core configuration with validation
        self.cache_file = cache_file
        self.cache_ttl = max(3600, min(604800, 86400))  # 1 hour - 1 week
        self.category_cache = {}
        self.auto_update = auto_update
        self.update_interval = max(300, min(86400, update_interval))  # 5min-24hours
        self.use_dynamic_sectors = use_dynamic_sectors

        # Enhanced features
        self.enable_smart_categorization = enable_smart_categorization
        self.enable_market_cap_analysis = enable_market_cap_analysis and AVAILABLE_MODULES.get('pandas', False)
        self.enable_ml_clustering = enable_ml_clustering and AVAILABLE_MODULES.get('sklearn', False)

        # Performance tracking
        self.categorization_stats = {
            "total_categorizations": 0,
            "successful_categorizations": 0,
            "failed_categorizations": 0,
            "cache_hits": 0,
            "api_calls": 0,
            "average_response_time": 0.0
        }

        # ✅ DYNAMIC: Initialize dynamic sectors integration
        self.dynamic_sectors = {}
        self.money_flow_analyzer = None
        
        # API endpoints for automatic updates
        self.api_endpoints = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'coinmarketcap': 'https://pro-api.coinmarketcap.com/v1',
            'binance': 'https://api.binance.com/api/v3'
        }
        
        # API keys (set these if you have premium access)
        self.api_keys = {
            'coinmarketcap': os.getenv('COINMARKETCAP_API_KEY', None)
        }
        
        # Load cached data
        self._load_cache()

        # ✅ DYNAMIC: Initialize dynamic sectors first if enabled
        if use_dynamic_sectors:
            self._init_dynamic_sectors()
        else:
            # Fallback to static category mappings
            self._init_category_data()

        # Start automatic update thread if enabled
        if auto_update:
            self._start_auto_update_thread()

        total_coins = len(self.dynamic_sectors.get('all_coins', [])) if use_dynamic_sectors else len(getattr(self, 'known_coins', {}))
        total_categories = len(self.dynamic_sectors) if use_dynamic_sectors else len(getattr(self, 'categories', {}))

        print(f"🔄 CoinCategorizer initialized ({'Dynamic' if use_dynamic_sectors else 'Static'} mode)")
        print(f"  📊 {total_coins} coins across {total_categories} categories")
        print(f"  🔄 Auto-update: {'Enabled' if auto_update else 'Disabled'} (interval: {update_interval}s)")

    def _init_dynamic_sectors(self):
        """🔄 Initialize dynamic sectors from MoneyFlowAnalyzer"""
        try:
            print("🔄 Initializing dynamic sectors integration...")

            # Try to import and initialize MoneyFlowAnalyzer
            try:
                from money_flow_analyzer import MoneyFlowAnalyzer
                self.money_flow_analyzer = MoneyFlowAnalyzer()

                # Get dynamic sectors
                sector_info = self.money_flow_analyzer.get_sector_info()
                self.dynamic_sectors = sector_info['sectors']

                # Create reverse mapping for fast lookup
                self.coin_to_sector = {}
                all_coins = []

                for sector, coins in self.dynamic_sectors.items():
                    for coin in coins:
                        # Remove USDT suffix for mapping
                        base_coin = coin.replace('USDT', '').upper()
                        self.coin_to_sector[base_coin] = self._map_sector_to_category(sector)
                        all_coins.append(base_coin)

                # Store all coins for statistics
                self.dynamic_sectors['all_coins'] = all_coins

                print(f"✅ Dynamic sectors loaded: {len(self.dynamic_sectors)} sectors, {len(all_coins)} coins")

                # Create category mappings compatible with existing system
                self.categories = self._create_dynamic_categories()

            except ImportError as e:
                print(f"⚠️ MoneyFlowAnalyzer not available: {e}")
                print("🔄 Falling back to static categories...")
                self.use_dynamic_sectors = False
                self._init_category_data()

        except Exception as e:
            print(f"❌ Error initializing dynamic sectors: {e}")
            print("🔄 Falling back to static categories...")
            self.use_dynamic_sectors = False
            self._init_category_data()

    def _map_sector_to_category(self, sector: str) -> str:
        """🏷️ Map dynamic sector names to categorizer categories"""
        sector_mapping = {
            'Layer1': 'LAYER1',
            'Layer2': 'LAYER2',
            'DeFi': 'DEFI',
            'Exchange': 'EXCHANGE_TOKEN',
            'AI': 'AI',
            'Gaming': 'GAMEFI',
            'Meme': 'MEME',
            'Infrastructure': 'INFRASTRUCTURE',
            'Privacy': 'PRIVACY',
            'Oracle': 'ORACLE',
            'Storage': 'STORAGE',
            'NFT': 'NFT',
            'Web3': 'INFRASTRUCTURE',
            'RWA': 'REAL_WORLD_ASSETS',
            'Metaverse': 'GAMEFI'
        }
        return sector_mapping.get(sector, 'OTHER')

    def _create_dynamic_categories(self) -> Dict[str, str]:
        """📊 Create category descriptions from dynamic sectors"""
        return {
            "LAYER1": "Layer 1 Blockchains",
            "LAYER2": "Layer 2 Scaling Solutions",
            "DEFI": "Decentralized Finance",
            "EXCHANGE_TOKEN": "Exchange Tokens",
            "STABLECOIN": "Stablecoins",
            "MEME": "Meme Coins",
            "AI": "Artificial Intelligence",
            "GAMEFI": "Gaming & Metaverse",
            "NFT": "NFT & Collectibles",
            "PRIVACY": "Privacy Coins",
            "ORACLE": "Oracle Networks",
            "STORAGE": "Decentralized Storage",
            "INFRASTRUCTURE": "Infrastructure & Tools",
            "REAL_WORLD_ASSETS": "Real World Assets",
            "OTHER": "Other/Miscellaneous"
        }

    def update_dynamic_sectors(self):
        """🔄 Update dynamic sectors from MoneyFlowAnalyzer"""
        if not self.use_dynamic_sectors or not self.money_flow_analyzer:
            return

        try:
            print("🔄 Updating dynamic sectors...")

            # Update sectors in MoneyFlowAnalyzer
            self.money_flow_analyzer.update_dynamic_sectors()

            # Reload sector information
            sector_info = self.money_flow_analyzer.get_sector_info()
            self.dynamic_sectors = sector_info['sectors']

            # Update coin mappings
            self.coin_to_sector = {}
            all_coins = []

            for sector, coins in self.dynamic_sectors.items():
                for coin in coins:
                    base_coin = coin.replace('USDT', '').upper()
                    self.coin_to_sector[base_coin] = self._map_sector_to_category(sector)
                    all_coins.append(base_coin)

            self.dynamic_sectors['all_coins'] = all_coins

            print(f"✅ Dynamic sectors updated: {len(self.dynamic_sectors)} sectors, {len(all_coins)} coins")

        except Exception as e:
            print(f"❌ Error updating dynamic sectors: {e}")

    def get_dynamic_sector_info(self) -> Dict:
        """📊 Get current dynamic sector information"""
        if not self.use_dynamic_sectors:
            return {"error": "Dynamic sectors not enabled"}

        return {
            "enabled": self.use_dynamic_sectors,
            "sectors": dict(self.dynamic_sectors),
            "total_sectors": len([k for k in self.dynamic_sectors.keys() if k != 'all_coins']),
            "total_coins": len(self.dynamic_sectors.get('all_coins', [])),
            "coin_mappings": len(self.coin_to_sector)
        }

    def _init_category_data(self):
        """Initialize comprehensive category data with known coins and patterns."""
        
        # Define all categories
        self.categories = {
            "LAYER1": "Layer 1 Blockchains",
            "LAYER2": "Layer 2 Scaling Solutions", 
            "DEFI": "Decentralized Finance",
            "EXCHANGE_TOKEN": "Exchange Tokens",
            "STABLECOIN": "Stablecoins",
            "MEME": "Meme Coins",
            "AI": "Artificial Intelligence",
            "GAMEFI": "Gaming & Metaverse",
            "NFT": "NFT & Collectibles",
            "PRIVACY": "Privacy Coins",
            "ORACLE": "Oracle Networks",
            "STORAGE": "Decentralized Storage",
            "INFRASTRUCTURE": "Infrastructure & Tools",
            "SOCIAL": "Social & Content",
            "CROSS_CHAIN": "Cross-chain & Interoperability",
            "YIELD_FARMING": "Yield Farming & Liquidity Mining",
            "DERIVATIVES": "Derivatives & Synthetic Assets",
            "REAL_WORLD_ASSETS": "Real World Assets",
            "FAN_TOKEN": "Fan & Sports Tokens",
            "OTHER": "Other/Miscellaneous"
        }
        
        # Comprehensive known coin mappings
        self.known_coins = {
            # Layer 1 Blockchains
            "BTC": "LAYER1", "BITCOIN": "LAYER1",
            "ETH": "LAYER1", "ETHEREUM": "LAYER1",
            "BNB": "LAYER1",  # BNB Smart Chain
            "ADA": "LAYER1", "CARDANO": "LAYER1",
            "SOL": "LAYER1", "SOLANA": "LAYER1",
            "DOT": "LAYER1", "POLKADOT": "LAYER1",
            "AVAX": "LAYER1", "AVALANCHE": "LAYER1",
            "ATOM": "LAYER1", "COSMOS": "LAYER1",
            "NEAR": "LAYER1",
            "ALGO": "LAYER1", "ALGORAND": "LAYER1",
            "EGLD": "LAYER1", "ELROND": "LAYER1",
            "FTM": "LAYER1", "FANTOM": "LAYER1",
            "ONE": "LAYER1", "HARMONY": "LAYER1",
            "HBAR": "LAYER1", "HEDERA": "LAYER1",
            "XTZ": "LAYER1", "TEZOS": "LAYER1",
            "EOS": "LAYER1",
            "TRX": "LAYER1", "TRON": "LAYER1",
            "VET": "LAYER1", "VECHAIN": "LAYER1",
            "XLM": "LAYER1", "STELLAR": "LAYER1",
            "ICP": "LAYER1", "INTERNET": "LAYER1",
            "APT": "LAYER1", "APTOS": "LAYER1",
            "SUI": "LAYER1",
            "SEI": "LAYER1",
            "INJ": "LAYER1", "INJECTIVE": "LAYER1",
            "TIA": "LAYER1", "CELESTIA": "LAYER1",
            "KAS": "LAYER1", "KASPA": "LAYER1",
            
            # Layer 2 Solutions
            "MATIC": "LAYER2", "POLYGON": "LAYER2",
            "OP": "LAYER2", "OPTIMISM": "LAYER2",
            "ARB": "LAYER2", "ARBITRUM": "LAYER2",
            "LRC": "LAYER2", "LOOPRING": "LAYER2",
            "IMX": "LAYER2", "IMMUTABLE": "LAYER2",
            "STRK": "LAYER2", "STARKNET": "LAYER2",
            "METIS": "LAYER2",
            "BOBA": "LAYER2",
            
            # DeFi Protocols
            "UNI": "DEFI", "UNISWAP": "DEFI",
            "AAVE": "DEFI",
            "COMP": "DEFI", "COMPOUND": "DEFI",
            "SUSHI": "DEFI", "SUSHISWAP": "DEFI",
            "CRV": "DEFI", "CURVE": "DEFI",
            "1INCH": "DEFI",
            "SNX": "DEFI", "SYNTHETIX": "DEFI",
            "MKR": "DEFI", "MAKER": "DEFI",
            "YFI": "DEFI", "YEARN": "DEFI",
            "BAL": "DEFI", "BALANCER": "DEFI",
            "ALPHA": "DEFI",
            "CAKE": "DEFI", "PANCAKESWAP": "DEFI",
            "JOE": "DEFI", "TRADERJOE": "DEFI",
            "DYDX": "DEFI",
            "GMX": "DEFI",
            "PERP": "DEFI", "PERPETUAL": "DEFI",
            "RUNE": "DEFI", "THORCHAIN": "DEFI",
            "OSMO": "DEFI", "OSMOSIS": "DEFI",
            
            # Exchange Tokens
            "BNB": "EXCHANGE_TOKEN",  # Binance
            "FTT": "EXCHANGE_TOKEN",  # FTX
            "CRO": "EXCHANGE_TOKEN",  # Crypto.com
            "LEO": "EXCHANGE_TOKEN",  # Bitfinex
            "HT": "EXCHANGE_TOKEN",   # Huobi
            "KCS": "EXCHANGE_TOKEN",  # KuCoin
            "OKB": "EXCHANGE_TOKEN",  # OKX
            "GT": "EXCHANGE_TOKEN",   # Gate.io
            "MX": "EXCHANGE_TOKEN",   # MEXC
            
            # Stablecoins
            "USDT": "STABLECOIN", "TETHER": "STABLECOIN",
            "USDC": "STABLECOIN", "USD": "STABLECOIN",
            "BUSD": "STABLECOIN",
            "DAI": "STABLECOIN",
            "FRAX": "STABLECOIN",
            "TUSD": "STABLECOIN", "TRUEUSD": "STABLECOIN",
            "USDP": "STABLECOIN", "PAX": "STABLECOIN",
            "FDUSD": "STABLECOIN",
            "USDD": "STABLECOIN",
            "LUSD": "STABLECOIN",
            "SUSD": "STABLECOIN",
            
            # Meme Coins
            "DOGE": "MEME", "DOGECOIN": "MEME",
            "SHIB": "MEME", "SHIBA": "MEME",
            "PEPE": "MEME",
            "FLOKI": "MEME",
            "BONK": "MEME",
            "WIF": "MEME", "DOGWIFHAT": "MEME",
            "MEME": "MEME",
            "BABYDOGE": "MEME",
            "ELON": "MEME",
            "AKITA": "MEME",
            "KISHU": "MEME",
            "SAFEMOON": "MEME",
            
            # AI Tokens
            "FET": "AI", "FETCH": "AI",
            "AGIX": "AI", "SINGULARITYNET": "AI",
            "OCEAN": "AI",
            "RLC": "AI", "IEXEC": "AI",
            "NMR": "AI", "NUMERAIRE": "AI",
            "AI": "AI",
            "GPT": "AI",
            "RNDR": "AI", "RENDER": "AI",
            "TAO": "AI", "BITTENSOR": "AI",
            "AKT": "AI", "AKASH": "AI",
            
            # Gaming & Metaverse
            "AXS": "GAMEFI", "AXIE": "GAMEFI",
            "SAND": "GAMEFI", "SANDBOX": "GAMEFI",
            "MANA": "GAMEFI", "DECENTRALAND": "GAMEFI",
            "ENJ": "GAMEFI", "ENJIN": "GAMEFI",
            "GALA": "GAMEFI",
            "ILV": "GAMEFI", "ILLUVIUM": "GAMEFI",
            "SLP": "GAMEFI", "SMOOTH": "GAMEFI",
            "ALICE": "GAMEFI", "MYNEIGHBORALICE": "GAMEFI",
            "TLM": "GAMEFI", "ALIEN": "GAMEFI",
            "CHR": "GAMEFI", "CHROMIA": "GAMEFI",
            "GAME": "GAMEFI",
            "PLAY": "GAMEFI",
            "YGG": "GAMEFI", "YIELD": "GAMEFI",
            "MAGIC": "GAMEFI", "TREASURE": "GAMEFI",
            "GMT": "GAMEFI", "STEPN": "GAMEFI",
            "GST": "GAMEFI",
            
            # Privacy Coins
            "XMR": "PRIVACY", "MONERO": "PRIVACY",
            "ZEC": "PRIVACY", "ZCASH": "PRIVACY",
            "DASH": "PRIVACY",
            "FIRO": "PRIVACY",
            "BEAM": "PRIVACY",
            "GRIN": "PRIVACY",
            "ARRR": "PRIVACY", "PIRATE": "PRIVACY",
            "TORN": "PRIVACY", "TORNADO": "PRIVACY",
            
            # Oracles
            "LINK": "ORACLE", "CHAINLINK": "ORACLE",
            "BAND": "ORACLE", "BANDPROTOCOL": "ORACLE",
            "TRB": "ORACLE", "TELLOR": "ORACLE",
            "API3": "ORACLE",
            "DIA": "ORACLE",
            "UMA": "ORACLE",
            
            # Storage
            "FIL": "STORAGE", "FILECOIN": "STORAGE",
            "AR": "STORAGE", "ARWEAVE": "STORAGE",
            "STORJ": "STORAGE",
            "SC": "STORAGE", "SIACOIN": "STORAGE",
            "BTT": "STORAGE", "BITTORRENT": "STORAGE",
            
            # Cross-chain
            "DOT": "CROSS_CHAIN", "POLKADOT": "CROSS_CHAIN",
            "ATOM": "CROSS_CHAIN", "COSMOS": "CROSS_CHAIN",
            "IBC": "CROSS_CHAIN",
            "RUNE": "CROSS_CHAIN", "THORCHAIN": "CROSS_CHAIN",
            "REN": "CROSS_CHAIN",
            "ANYSWAP": "CROSS_CHAIN",
            "POLY": "CROSS_CHAIN", "POLYMATH": "CROSS_CHAIN",
        }
        
        # API category mappings for different sources
        self.api_category_mappings = {
            'coingecko': {
                'smart-contract-platform': 'LAYER1',
                'decentralized-finance-defi': 'DEFI',
                'exchange-based-tokens': 'EXCHANGE_TOKEN',
                'stablecoins': 'STABLECOIN',
                'meme-token': 'MEME',
                'artificial-intelligence': 'AI',
                'gaming': 'GAMEFI',
                'non-fungible-tokens-nft': 'NFT',
                'privacy-coins': 'PRIVACY',
                'oracle': 'ORACLE',
                'storage': 'STORAGE',
                'layer-2': 'LAYER2',
                'fan-token': 'FAN_TOKEN',
                'yield-farming': 'YIELD_FARMING',
                'derivatives': 'DERIVATIVES',
                'real-world-assets': 'REAL_WORLD_ASSETS'
            },
            'coinmarketcap': {
                'blockchain': 'LAYER1',
                'defi': 'DEFI',
                'exchange-token': 'EXCHANGE_TOKEN',
                'stablecoin': 'STABLECOIN',
                'meme': 'MEME',
                'ai-big-data': 'AI',
                'gaming': 'GAMEFI',
                'nft': 'NFT',
                'privacy': 'PRIVACY',
                'oracle': 'ORACLE',
                'storage': 'STORAGE',
                'scaling': 'LAYER2',
                'fan-token': 'FAN_TOKEN'
            }
        }
        
        # Pattern-based detection rules
        self.pattern_rules = [
            # AI patterns
            (r".*AI.*", "AI"),
            (r".*GPT.*", "AI"),
            (r".*NEURAL.*", "AI"),
            (r".*BRAIN.*", "AI"),
            (r".*ROBOT.*", "AI"),
            (r".*CHATGPT.*", "AI"),
            (r".*OPENAI.*", "AI"),
            
            # Meme patterns
            (r".*DOGE.*", "MEME"),
            (r".*SHIB.*", "MEME"),
            (r".*PEPE.*", "MEME"),
            (r".*MEME.*", "MEME"),
            (r".*MOON.*", "MEME"),
            (r".*SAFE.*", "MEME"),
            (r".*BABY.*", "MEME"),
            (r".*ELON.*", "MEME"),
            (r".*INU.*", "MEME"),
            (r".*FLOKI.*", "MEME"),
            
            # Gaming patterns
            (r".*GAME.*", "GAMEFI"),
            (r".*PLAY.*", "GAMEFI"),
            (r".*NFT.*", "NFT"),
            (r".*METAVERSE.*", "GAMEFI"),
            (r".*VIRTUAL.*", "GAMEFI"),
            (r".*LAND.*", "GAMEFI"),
            (r".*AVATAR.*", "GAMEFI"),
            
            # DeFi patterns
            (r".*SWAP.*", "DEFI"),
            (r".*DEX.*", "DEFI"),
            (r".*YIELD.*", "DEFI"),
            (r".*FARM.*", "DEFI"),
            (r".*LEND.*", "DEFI"),
            (r".*BORROW.*", "DEFI"),
            (r".*LIQUIDITY.*", "DEFI"),
            (r".*STAKE.*", "DEFI"),
            (r".*POOL.*", "DEFI"),
            (r".*VAULT.*", "DEFI"),
            (r".*FINANCE.*", "DEFI"),
            (r".*PROTOCOL.*", "DEFI"),
            
            # Stablecoin patterns
            (r".*USD.*", "STABLECOIN"),
            (r".*USDT.*", "STABLECOIN"),
            (r".*USDC.*", "STABLECOIN"),
            (r".*BUSD.*", "STABLECOIN"),
            (r".*TUSD.*", "STABLECOIN"),
            (r".*DAI.*", "STABLECOIN"),
            (r".*STABLE.*", "STABLECOIN"),
            
            # Layer 2 patterns
            (r".*LAYER2.*", "LAYER2"),
            (r".*L2.*", "LAYER2"),
            (r".*POLYGON.*", "LAYER2"),
            (r".*ARBITRUM.*", "LAYER2"),
            (r".*OPTIMISM.*", "LAYER2"),
            (r".*SCALING.*", "LAYER2"),
            
            # Privacy patterns
            (r".*PRIVACY.*", "PRIVACY"),
            (r".*PRIVATE.*", "PRIVACY"),
            (r".*ANON.*", "PRIVACY"),
            (r".*STEALTH.*", "PRIVACY"),
            (r".*ZERO.*KNOWLEDGE.*", "PRIVACY"),
            (r".*ZK.*", "PRIVACY"),
            
            # Oracle patterns
            (r".*ORACLE.*", "ORACLE"),
            (r".*PRICE.*FEED.*", "ORACLE"),
            (r".*DATA.*FEED.*", "ORACLE"),
            
            # Storage patterns
            (r".*STORAGE.*", "STORAGE"),
            (r".*FILE.*", "STORAGE"),
            (r".*CLOUD.*", "STORAGE"),
            (r".*DECENTRALIZED.*STORAGE.*", "STORAGE"),
            
            # Fan token patterns
            (r".*FAN.*", "FAN_TOKEN"),
            (r".*FC.*", "FAN_TOKEN"),
            (r".*CITY.*", "FAN_TOKEN"),
            (r".*UNITED.*", "FAN_TOKEN"),
            (r".*MADRID.*", "FAN_TOKEN"),
            (r".*BARCELONA.*", "FAN_TOKEN"),
            (r".*JUVENTUS.*", "FAN_TOKEN"),
            (r".*PSG.*", "FAN_TOKEN"),
        ]
        
        # Compile regex patterns for better performance
        self.compiled_patterns = [(re.compile(pattern, re.IGNORECASE), category) 
                                  for pattern, category in self.pattern_rules]
    
    def get_coin_category(self, symbol: str) -> str:
        """
        🔄 DYNAMIC: Get coin category with dynamic sectors integration.

        Args:
            symbol: Trading symbol (e.g., 'BTC/USDT', 'ETH/USDC')

        Returns:
            str: Category name
        """
        if not symbol:
            return "OTHER"

        try:
            # Check cache first for speed
            cache_key = symbol.upper()
            if cache_key in self.category_cache:
                cache_entry = self.category_cache[cache_key]
                # Check if cache is still valid
                if datetime.now().timestamp() - cache_entry['timestamp'] < self.cache_ttl:
                    return cache_entry['category']

            # Extract base asset from trading pair
            base_asset = self._extract_base_asset(symbol)

            # ✅ DYNAMIC: Method 1 - Check dynamic sectors first (fastest, most accurate)
            if self.use_dynamic_sectors:
                category = self._check_dynamic_sectors(base_asset)
                if category != "OTHER":
                    self._cache_result(cache_key, category)
                    return category

            # Method 2: Check known coins mapping (fallback for static mode)
            category = self._check_known_coins(base_asset)
            if category != "OTHER":
                self._cache_result(cache_key, category)
                return category

            # Method 3: Pattern-based detection (fast, offline)
            category = self._pattern_based_detection(base_asset)
            if category != "OTHER":
                self._cache_result(cache_key, category)
                return category

            # Method 4: Advanced heuristics (fast, offline)
            category = self._advanced_heuristics(base_asset, symbol)
            if category != "OTHER":
                self._cache_result(cache_key, category)
                return category

            # Method 5: API fetch only if auto_update enabled (slower, online)
            if self.auto_update:
                try:
                    category = self._fetch_category_from_apis(base_asset)
                    if category != "OTHER":
                        # ✅ DYNAMIC: Update dynamic sectors if available
                        if self.use_dynamic_sectors and hasattr(self, 'coin_to_sector'):
                            self.coin_to_sector[base_asset.upper()] = category
                        elif hasattr(self, 'known_coins'):
                            self.known_coins[base_asset.upper()] = category

                        self._cache_result(cache_key, category)
                        print(f"Auto-learned: {symbol} -> {category}")
                        return category
                except Exception as api_error:
                    print(f"API fetch failed for {symbol}: {api_error}")

            # Default fallback
            category = "OTHER"
            self._cache_result(cache_key, category)
            return category

        except Exception as e:
            print(f"Error categorizing {symbol}: {e}")
            return "OTHER"

    def _check_dynamic_sectors(self, base_asset: str) -> str:
        """🔄 Check dynamic sectors for coin category"""
        if not self.use_dynamic_sectors or not hasattr(self, 'coin_to_sector'):
            return "OTHER"

        try:
            base_upper = base_asset.upper()

            # Direct lookup in dynamic sectors
            if base_upper in self.coin_to_sector:
                return self.coin_to_sector[base_upper]

            # Check with USDT suffix
            usdt_pair = f"{base_upper}USDT"
            for sector, coins in self.dynamic_sectors.items():
                if sector == 'all_coins':
                    continue
                if usdt_pair in coins:
                    category = self._map_sector_to_category(sector)
                    # Cache for future lookups
                    self.coin_to_sector[base_upper] = category
                    return category

            return "OTHER"

        except Exception as e:
            print(f"Error checking dynamic sectors for {base_asset}: {e}")
            return "OTHER"
    
    def _fetch_category_from_apis(self, symbol: str) -> str:
        """
        Fetch category information from multiple API sources.
        
        Args:
            symbol: Base asset symbol
            
        Returns:
            str: Category name or "OTHER" if not found
        """
        # Try CoinGecko first (free API)
        category = self._fetch_from_coingecko(symbol)
        if category != "OTHER":
            return category
        
        # Try CoinMarketCap if API key is available
        if self.api_keys.get('coinmarketcap'):
            category = self._fetch_from_coinmarketcap(symbol)
            if category != "OTHER":
                return category
        
        return "OTHER"
    
    def _fetch_from_coingecko(self, symbol: str) -> str:
        """
        Fetch category from CoinGecko API.
        
        Args:
            symbol: Base asset symbol
            
        Returns:
            str: Category name or "OTHER" if not found
        """
        try:
            # First, search for the coin
            search_url = f"{self.api_endpoints['coingecko']}/search"
            search_params = {'query': symbol}
            
            response = requests.get(search_url, params=search_params, timeout=10)
            if response.status_code != 200:
                return "OTHER"
            
            search_data = response.json()
            
            # Find the coin in search results
            coin_id = None
            for coin in search_data.get('coins', []):
                if coin.get('symbol', '').upper() == symbol.upper():
                    coin_id = coin.get('id')
                    break
            
            if not coin_id:
                return "OTHER"
            
            # Get detailed coin information
            coin_url = f"{self.api_endpoints['coingecko']}/coins/{coin_id}"
            response = requests.get(coin_url, timeout=10)
            if response.status_code != 200:
                return "OTHER"
            
            coin_data = response.json()
            
            # Extract categories
            categories = coin_data.get('categories', [])
            
            # Map CoinGecko categories to our categories
            for cg_category in categories:
                if cg_category in self.api_category_mappings['coingecko']:
                    return self.api_category_mappings['coingecko'][cg_category]
            
            # Check description for keywords if no direct category match
            description = coin_data.get('description', {}).get('en', '').lower()
            return self._classify_by_description(description)
            
        except Exception as e:
            print(f"Error fetching from CoinGecko for {symbol}: {e}")
            return "OTHER"
    
    def _fetch_from_coinmarketcap(self, symbol: str) -> str:
        """
        Fetch category from CoinMarketCap API (requires API key).
        
        Args:
            symbol: Base asset symbol
            
        Returns:
            str: Category name or "OTHER" if not found
        """
        try:
            if not self.api_keys.get('coinmarketcap'):
                return "OTHER"
            
            # Get coin metadata
            url = f"{self.api_endpoints['coinmarketcap']}/cryptocurrency/info"
            params = {'symbol': symbol.upper()}
            headers = {
                'X-CMC_PRO_API_KEY': self.api_keys['coinmarketcap'],
                'Accept': 'application/json'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            if response.status_code != 200:
                return "OTHER"
            
            data = response.json()
            
            # Extract coin info
            coin_info = data.get('data', {}).get(symbol.upper())
            if not coin_info:
                return "OTHER"
            
            # Extract tags/categories
            tags = coin_info.get('tags', [])
            
            # Map CoinMarketCap tags to our categories
            for tag in tags:
                if tag in self.api_category_mappings['coinmarketcap']:
                    return self.api_category_mappings['coinmarketcap'][tag]
            
            # Check description for keywords
            description = coin_info.get('description', '').lower()
            return self._classify_by_description(description)
            
        except Exception as e:
            print(f"Error fetching from CoinMarketCap for {symbol}: {e}")
            return "OTHER"
    
    def _classify_by_description(self, description: str) -> str:
        """
        Classify coin based on description text using keyword analysis.
        
        Args:
            description: Coin description text
            
        Returns:
            str: Category name or "OTHER"
        """
        description = description.lower()
        
        # Define keyword mappings
        keyword_mappings = {
            'DEFI': ['defi', 'decentralized finance', 'liquidity', 'yield farming', 'lending', 'borrowing', 'swap', 'dex'],
            'AI': ['artificial intelligence', 'machine learning', 'ai', 'neural', 'automation', 'prediction'],
            'GAMEFI': ['gaming', 'game', 'metaverse', 'nft', 'play to earn', 'virtual world'],
            'LAYER2': ['layer 2', 'layer-2', 'scaling', 'rollup', 'sidechain'],
            'STABLECOIN': ['stablecoin', 'stable coin', 'pegged', 'dollar', 'fiat'],
            'MEME': ['meme', 'community', 'fun', 'joke', 'social'],
            'PRIVACY': ['privacy', 'anonymous', 'confidential', 'zero knowledge'],
            'ORACLE': ['oracle', 'data feed', 'price feed', 'external data'],
            'STORAGE': ['storage', 'file', 'data storage', 'decentralized storage'],
            'CROSS_CHAIN': ['cross chain', 'interoperability', 'bridge', 'multi-chain']
        }
        
        # Score each category
        category_scores = {}
        for category, keywords in keyword_mappings.items():
            score = sum(1 for keyword in keywords if keyword in description)
            if score > 0:
                category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        return "OTHER"
    
    def _start_auto_update_thread(self):
        """🔄 ENHANCED: Start non-blocking background thread for automatic updates."""
        def update_task():
            print("🔄 Auto-update thread started (non-blocking)")
            while True:
                try:
                    time.sleep(self.update_interval)
                    # Only update if there are items to update
                    if len(self.category_cache) > 0:
                        self._perform_bulk_update()
                except Exception as e:
                    print(f"⚠️ Auto-update thread error (non-critical): {e}")
                    # Continue running even if update fails
                    time.sleep(60)  # Wait 1 minute before retrying

        update_thread = threading.Thread(target=update_task, daemon=True)
        update_thread.start()
        print("✅ Auto-update thread started (background, non-blocking)")
    
    def _perform_bulk_update(self):
        """
        Perform bulk update of categories for unknown/old coins.
        """
        print("Performing automatic category update...")
        
        # Get list of coins that need updating
        update_candidates = []
        current_time = datetime.now().timestamp()
        
        # Find cached entries that are older than TTL
        for symbol, cache_entry in self.category_cache.items():
            if (current_time - cache_entry['timestamp'] > self.cache_ttl or 
                cache_entry['category'] == 'OTHER'):
                update_candidates.append(symbol)
        
        # Limit updates to avoid API rate limits
        update_candidates = update_candidates[:50]  # Update max 50 coins per cycle
        
        updated_count = 0
        for symbol in update_candidates:
            base_asset = self._extract_base_asset(symbol)
            new_category = self._fetch_category_from_apis(base_asset)
            
            if new_category != "OTHER":
                # Update known coins and cache
                self.known_coins[base_asset.upper()] = new_category
                self._cache_result(symbol, new_category)
                updated_count += 1
                print(f"Updated: {symbol} -> {new_category}")
                
                # Rate limiting
                time.sleep(0.5)  # 500ms delay between requests
        
        if updated_count > 0:
            print(f"Auto-update completed: {updated_count} coins updated")
            self._save_cache()
            self._save_known_coins()
    
    def _save_known_coins(self):
        """Save known coins database to file."""
        try:
            # ✅ FIX: Skip if using dynamic sectors (no known_coins attribute)
            if self.use_dynamic_sectors and not hasattr(self, 'known_coins'):
                return

            known_coins_file = "known_coins_db.json"
            with open(known_coins_file, 'w') as f:
                json.dump({
                    'known_coins': self.known_coins,
                    'last_updated': datetime.now().timestamp(),
                    'version': '2.0'
                }, f, indent=2)
        except Exception as e:
            print(f"Error saving known coins database: {e}")
    
    def _load_known_coins(self):
        """Load known coins database from file."""
        try:
            # ✅ DYNAMIC: Skip if using dynamic sectors (no known_coins attribute)
            if self.use_dynamic_sectors and not hasattr(self, 'known_coins'):
                return

            known_coins_file = "known_coins_db.json"
            if os.path.exists(known_coins_file):
                with open(known_coins_file, 'r') as f:
                    data = json.load(f)
                    saved_coins = data.get('known_coins', {})

                    # Merge with existing known coins (only in static mode)
                    if hasattr(self, 'known_coins'):
                        self.known_coins.update(saved_coins)
                        print(f"Loaded {len(saved_coins)} saved coin categorizations")
        except Exception as e:
            print(f"Error loading known coins database: {e}")
    
    def force_update_coin(self, symbol: str) -> str:
        """
        Force update a specific coin's category from API sources.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            str: Updated category
        """
        base_asset = self._extract_base_asset(symbol)
        category = self._fetch_category_from_apis(base_asset)
        
        if category != "OTHER":
            # Update known coins and cache
            self.known_coins[base_asset.upper()] = category
            self._cache_result(symbol.upper(), category)
            print(f"Force updated: {symbol} -> {category}")
            self._save_cache()
            self._save_known_coins()
        
        return category
    
    def get_update_stats(self) -> Dict[str, any]:
        """Get statistics about automatic updates."""
        # ✅ DYNAMIC: Handle both dynamic and static modes
        total_coins = 0
        if self.use_dynamic_sectors and hasattr(self, 'coin_to_sector'):
            total_coins = len(self.coin_to_sector)
        elif hasattr(self, 'known_coins'):
            total_coins = len(self.known_coins)

        stats = {
            'mode': 'dynamic' if self.use_dynamic_sectors else 'static',
            'total_known_coins': total_coins,
            'cached_coins': len(self.category_cache),
            'auto_update_enabled': self.auto_update,
            'update_interval': self.update_interval,
            'last_cache_update': None
        }
        
        # Find most recent cache entry
        if self.category_cache:
            latest_timestamp = max(entry['timestamp'] for entry in self.category_cache.values())
            stats['last_cache_update'] = datetime.fromtimestamp(latest_timestamp).isoformat()
        
        return stats
    

    
    def _extract_base_asset(self, symbol: str) -> str:
        """Extract base asset from trading pair."""
        symbol_upper = symbol.upper()
        
        # Remove common quote assets
        quote_assets = ['USDT', 'USDC', 'BUSD', 'BTC', 'ETH', 'BNB', 'FDUSD', 'DAI', 'TUSD']
        
        # Try to split by common separators
        for separator in ['/', '-', '_']:
            if separator in symbol_upper:
                base = symbol_upper.split(separator)[0]
                return base.strip()
        
        # Remove quote assets from the end
        for quote in sorted(quote_assets, key=len, reverse=True):
            if symbol_upper.endswith(quote):
                base = symbol_upper[:-len(quote)]
                if base:
                    return base
        
        # Remove leveraged tokens suffixes
        leveraged_suffixes = ['UP', 'DOWN', 'BULL', 'BEAR', '2L', '3L', '4L', '5L', '2S', '3S', '4S', '5S']
        for suffix in leveraged_suffixes:
            if symbol_upper.endswith(suffix):
                base = symbol_upper[:-len(suffix)]
                if base:
                    return base
        
        return symbol_upper
    
    def _check_known_coins(self, base_asset: str) -> str:
        """Check if coin is in known coins mapping."""
        # ✅ DYNAMIC: Skip if using dynamic sectors
        if self.use_dynamic_sectors or not hasattr(self, 'known_coins'):
            return "OTHER"

        base_upper = base_asset.upper()

        # Direct lookup
        if base_upper in self.known_coins:
            return self.known_coins[base_upper]

        # Check for partial matches (useful for tokens with numbers/versions)
        for known_coin, category in self.known_coins.items():
            if known_coin in base_upper or base_upper.startswith(known_coin):
                return category

        return "OTHER"
    
    def _pattern_based_detection(self, base_asset: str) -> str:
        """Use regex patterns to detect category."""
        base_upper = base_asset.upper()
        
        for pattern, category in self.compiled_patterns:
            if pattern.match(base_upper):
                return category
        
        return "OTHER"
    
    def _advanced_heuristics(self, base_asset: str, full_symbol: str) -> str:
        """Apply advanced heuristics for categorization."""
        base_upper = base_asset.upper()

        # ✅ DYNAMIC: Check version numbers with dynamic sectors
        if re.match(r".*[0-9].*", base_upper):
            # Could be a version of existing blockchain
            base_name = re.sub(r'[0-9]+', '', base_upper)

            # Check in dynamic sectors first
            if self.use_dynamic_sectors and hasattr(self, 'coin_to_sector'):
                if base_name in self.coin_to_sector:
                    return self.coin_to_sector[base_name]
            # Fallback to known coins
            elif hasattr(self, 'known_coins') and base_name in self.known_coins:
                return self.known_coins[base_name]

        # Check for common DeFi naming patterns
        defi_indicators = ['TOKEN', 'COIN', 'FINANCE', 'PROTOCOL', 'DAO']
        for indicator in defi_indicators:
            if indicator in base_upper:
                return "DEFI"

        # Check for exchange-specific patterns
        if any(exchange in base_upper for exchange in ['BINANCE', 'HUOBI', 'OKX', 'KUCOIN', 'GATE']):
            return "EXCHANGE_TOKEN"

        # ✅ DYNAMIC: Check wrapped tokens with dynamic sectors
        if base_upper.startswith('W') and len(base_upper) > 2:
            wrapped_asset = base_upper[1:]

            # Check in dynamic sectors first
            if self.use_dynamic_sectors and hasattr(self, 'coin_to_sector'):
                if wrapped_asset in self.coin_to_sector:
                    return self.coin_to_sector[wrapped_asset]
            # Fallback to known coins
            elif hasattr(self, 'known_coins') and wrapped_asset in self.known_coins:
                return self.known_coins[wrapped_asset]

        # Default fallback
        return "OTHER"
    
    def _cache_result(self, cache_key: str, category: str):
        """Cache the categorization result."""
        self.category_cache[cache_key] = {
            'category': category,
            'timestamp': datetime.now().timestamp()
        }
        
        # Save cache periodically
        if len(self.category_cache) % 50 == 0:
            self._save_cache()
    
    def _load_cache(self):
        """Load cached categorization results."""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    data = json.load(f)
                    self.category_cache = data.get('cache', {})
                    print(f"Loaded {len(self.category_cache)} cached categorizations")
        except Exception as e:
            print(f"Error loading cache: {e}")
            self.category_cache = {}
        
        # Load known coins database
        self._load_known_coins()
    
    def _save_cache(self):
        """Save categorization cache to file."""
        try:
            # Clean old entries
            current_time = datetime.now().timestamp()
            cleaned_cache = {
                k: v for k, v in self.category_cache.items()
                if current_time - v['timestamp'] < self.cache_ttl * 7  # Keep for 7 days
            }
            
            cache_data = {
                'cache': cleaned_cache,
                'last_updated': current_time,
                'version': '2.0'
            }
            
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving cache: {e}")
    
    def get_category_stats(self) -> Dict[str, int]:
        """Get statistics of categorized coins."""
        stats = {}
        for entry in self.category_cache.values():
            category = entry['category']
            stats[category] = stats.get(category, 0) + 1
        return stats
    
    def bulk_categorize(self, symbols: List[str]) -> Dict[str, str]:
        """Categorize multiple symbols at once with auto-update."""
        results = {}
        for symbol in symbols:
            results[symbol] = self.get_coin_category(symbol)
        
        # Save cache after bulk operation
        self._save_cache()
        self._save_known_coins()
        return results
    
    def update_known_coin(self, symbol: str, category: str):
        """Add or update a known coin categorization."""
        base_asset = self._extract_base_asset(symbol)
        self.known_coins[base_asset.upper()] = category
        
        # Update cache
        cache_key = symbol.upper()
        self._cache_result(cache_key, category)
        
        print(f"Updated categorization: {symbol} -> {category}")
        self._save_known_coins()
    
    def get_all_categories(self) -> Dict[str, str]:
        """Get all available categories with descriptions."""
        return self.categories.copy()
    
    def __del__(self):
        """Save cache when object is destroyed."""
        try:
            self._save_cache()
            self._save_known_coins()
        except:
            pass
