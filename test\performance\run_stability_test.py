#!/usr/bin/env python3
"""
🚀 Run Stability Test - Script đơn giản để chạy test stability
"""

import os
import sys
import time
import requests
from datetime import datetime

def check_environment():
    """🔍 Check environment setup."""
    print(f"🔍 Checking environment setup...")
    
    # Check environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token:
        print(f"❌ TELEGRAM_BOT_TOKEN not set")
        return False
    
    if not chat_id:
        print(f"❌ TELEGRAM_CHAT_ID not set")
        return False
    
    print(f"✅ Environment variables set")
    
    # Check if main_bot.py exists
    if not os.path.exists("main_bot.py"):
        print(f"❌ main_bot.py not found")
        return False
    
    print(f"✅ main_bot.py found")
    
    # Test Telegram connection
    try:
        base_url = f"https://api.telegram.org/bot{bot_token}"
        test_message = f"🧪 <b>STABILITY TEST PREPARATION</b>\n\n⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>\n\n🔍 Testing Telegram connection before starting stability test..."
        
        data = {
            'chat_id': chat_id,
            'text': test_message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(f"{base_url}/sendMessage", data=data, timeout=10)
        if response.status_code == 200 and response.json().get('ok', False):
            print(f"✅ Telegram connection working")
            return True
        else:
            print(f"❌ Telegram connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Telegram test error: {e}")
        return False

def send_start_notification():
    """📱 Send start notification."""
    try:
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        base_url = f"https://api.telegram.org/bot{bot_token}"
        
        message = f"""🚀 <b>STABILITY TEST STARTING</b>

⏰ <b>Test Information:</b>
├ 🕐 Start Time: <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
├ 🕐 Duration: <code>1 hour</code>
├ 📊 Reports: <code>Every 10 minutes</code>
└ 🎯 Purpose: <b>Monitor bot for errors and stability</b>

🤖 <b>What will be tested:</b>
├ 🔄 Bot process stability
├ ❌ Error detection and logging
├ 📊 Analysis activity monitoring
├ 📱 Telegram communication
└ 💾 Memory and resource usage

🔍 <b>Monitoring will detect:</b>
├ ❌ Runtime errors and exceptions
├ 🚫 Process crashes or terminations
├ 📊 Analysis generation issues
├ 📱 Telegram sending problems
└ 🔄 System responsiveness issues

<b>🚀 STARTING BOT STABILITY TEST NOW...</b>

<i>The bot will be started and monitored for 1 hour. You will receive status reports every 10 minutes.</i>"""

        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(f"{base_url}/sendMessage", data=data, timeout=10)
        return response.status_code == 200 and response.json().get('ok', False)
        
    except Exception as e:
        print(f"❌ Error sending start notification: {e}")
        return False

def main():
    """🚀 Main function."""
    print(f"🚀 STABILITY TEST RUNNER")
    print(f"=" * 60)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    print(f"")
    
    # Step 1: Check environment
    print(f"Step 1: Environment Check")
    if not check_environment():
        print(f"❌ Environment check failed. Please fix issues and try again.")
        return
    print(f"✅ Environment check passed")
    print(f"")
    
    # Step 2: Send start notification
    print(f"Step 2: Send Start Notification")
    if send_start_notification():
        print(f"✅ Start notification sent to Telegram")
    else:
        print(f"⚠️ Failed to send start notification, but continuing...")
    print(f"")
    
    # Step 3: Run stability test
    print(f"Step 3: Run Stability Test")
    print(f"🚀 Starting bot stability test...")
    print(f"")
    
    try:
        # Import and run the stability tester
        from test_bot_stability import BotStabilityTester
        
        tester = BotStabilityTester()
        tester.run_stability_test()
        
        print(f"")
        print(f"✅ Stability test completed successfully")
        
    except ImportError as e:
        print(f"❌ Could not import stability tester: {e}")
        print(f"Make sure test_bot_stability.py is in the same directory")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        
    except Exception as e:
        print(f"❌ Error running stability test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"")
    print(f"🎯 Stability test runner completed")
    print(f"📱 Check your Telegram for detailed reports")

if __name__ == "__main__":
    main()
