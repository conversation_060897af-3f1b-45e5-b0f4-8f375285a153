#!/usr/bin/env python3
"""
🧪 TEST CONSENSUS DUPLICATE FIX
Test để kiểm tra việc sửa lỗi consensus signals bị gửi trùng lặp
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_consensus_duplicate_prevention():
    """Test consensus signal duplicate prevention."""
    print("🧪 === TESTING CONSENSUS DUPLICATE PREVENTION ===")
    
    try:
        # Test 1: Check main_bot.py fixes
        print(f"\n🧪 TEST 1: Check main_bot.py consensus duplicate prevention")
        
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            main_bot_content = f.read()
        
        fixes_found = 0
        
        # Check for single consensus send logic
        if 'consensus_sent = False' in main_bot_content:
            print(f"  ✅ Found consensus_sent tracking variable")
            fixes_found += 1
        else:
            print(f"  ❌ Missing consensus_sent tracking variable")
        
        # Check for primary/fallback logic
        if 'Primary consensus signal sent successfully' in main_bot_content:
            print(f"  ✅ Found primary consensus send logic")
            fixes_found += 1
        else:
            print(f"  ❌ Missing primary consensus send logic")
        
        # Check for chart generation prevention
        if 'not consensus_sent' in main_bot_content:
            print(f"  ✅ Found chart generation duplicate prevention")
            fixes_found += 1
        else:
            print(f"  ❌ Missing chart generation duplicate prevention")
        
        # Check for consensus sent marking
        if 'signal_data["consensus_sent"] = consensus_sent' in main_bot_content:
            print(f"  ✅ Found consensus sent marking")
            fixes_found += 1
        else:
            print(f"  ❌ Missing consensus sent marking")
        
        if fixes_found >= 3:
            print(f"  ✅ main_bot.py consensus fixes: GOOD ({fixes_found}/4)")
        else:
            print(f"  ❌ main_bot.py consensus fixes: INCOMPLETE ({fixes_found}/4)")
        
        return fixes_found >= 3
        
    except Exception as e:
        print(f"❌ Error testing consensus duplicate prevention: {e}")
        return False

def test_signal_integration_consensus_fix():
    """Test signal integration consensus duplicate fix."""
    print(f"\n🧪 TEST 2: Check main_bot_signal_integration.py consensus fixes")
    
    try:
        with open('main_bot_signal_integration.py', 'r', encoding='utf-8') as f:
            integration_content = f.read()
        
        integration_fixes = 0
        
        # Check for duplicate signal check in consensus
        if '_is_duplicate_signal("consensus"' in integration_content:
            print(f"  ✅ Found consensus duplicate signal check")
            integration_fixes += 1
        else:
            print(f"  ❌ Missing consensus duplicate signal check")
        
        # Check for chart_generator=None fix
        if 'chart_generator=None,  # ✅ FIX: Remove chart_generator to prevent duplicate charts' in integration_content:
            print(f"  ✅ Found chart_generator removal fix")
            integration_fixes += 1
        else:
            print(f"  ❌ Missing chart_generator removal fix")
        
        # Check for .env chat config usage
        if "target_chat=self.chat_configs.get('consensus')" in integration_content:
            print(f"  ✅ Found .env chat config usage")
            integration_fixes += 1
        else:
            print(f"  ❌ Missing .env chat config usage")
        
        # Check for duplicate prevention message
        if 'Duplicate consensus signal prevented' in integration_content:
            print(f"  ✅ Found duplicate prevention logging")
            integration_fixes += 1
        else:
            print(f"  ❌ Missing duplicate prevention logging")
        
        if integration_fixes >= 3:
            print(f"  ✅ signal integration consensus fixes: GOOD ({integration_fixes}/4)")
        else:
            print(f"  ❌ signal integration consensus fixes: INCOMPLETE ({integration_fixes}/4)")
        
        return integration_fixes >= 3
        
    except Exception as e:
        print(f"❌ Error testing signal integration consensus fix: {e}")
        return False

def test_consensus_flow_logic():
    """Test consensus signal flow logic."""
    print(f"\n🧪 TEST 3: Check consensus signal flow logic")
    
    try:
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        flow_checks = 0
        
        # Check for proper flow control
        flow_patterns = [
            'consensus_send_result = self.signal_integration.send_consensus_signal_with_tracking',
            'if consensus_send_result:',
            'consensus_sent = True',
            'else:',
            'if self.signal_integration.can_send_signal("consensus"):',
            'self._send_enhanced_signal_notification(signal_data, consensus_data)',
            'consensus_sent = True'
        ]
        
        for pattern in flow_patterns:
            if pattern in content:
                flow_checks += 1
            else:
                print(f"  ❌ Missing flow pattern: {pattern}")
        
        print(f"  📊 Flow patterns found: {flow_checks}/{len(flow_patterns)}")
        
        if flow_checks >= 6:
            print(f"  ✅ Consensus flow logic: GOOD")
            return True
        else:
            print(f"  ❌ Consensus flow logic: INCOMPLETE")
            return False
        
    except Exception as e:
        print(f"❌ Error testing consensus flow logic: {e}")
        return False

def test_duplicate_prevention_mechanisms():
    """Test all duplicate prevention mechanisms."""
    print(f"\n🧪 TEST 4: Check all duplicate prevention mechanisms")
    
    try:
        mechanisms_found = 0
        
        # Check main_bot.py mechanisms
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        main_mechanisms = [
            'consensus_sent = False',  # Tracking variable
            'consensus_sent = True',   # Success marking
            'not consensus_sent',      # Chart prevention
            'Consensus Sent:'          # Status logging
        ]
        
        for mechanism in main_mechanisms:
            if mechanism in main_content:
                mechanisms_found += 1
                print(f"  ✅ Found main_bot mechanism: {mechanism}")
            else:
                print(f"  ❌ Missing main_bot mechanism: {mechanism}")
        
        # Check signal integration mechanisms
        with open('main_bot_signal_integration.py', 'r', encoding='utf-8') as f:
            integration_content = f.read()
        
        integration_mechanisms = [
            '_is_duplicate_signal("consensus"',  # Duplicate check
            'chart_generator=None',              # Chart prevention
            'Duplicate consensus signal prevented'  # Prevention logging
        ]
        
        for mechanism in integration_mechanisms:
            if mechanism in integration_content:
                mechanisms_found += 1
                print(f"  ✅ Found integration mechanism: {mechanism}")
            else:
                print(f"  ❌ Missing integration mechanism: {mechanism}")
        
        total_mechanisms = len(main_mechanisms) + len(integration_mechanisms)
        
        if mechanisms_found >= total_mechanisms * 0.8:
            print(f"  ✅ Duplicate prevention mechanisms: GOOD ({mechanisms_found}/{total_mechanisms})")
            return True
        else:
            print(f"  ❌ Duplicate prevention mechanisms: INCOMPLETE ({mechanisms_found}/{total_mechanisms})")
            return False
        
    except Exception as e:
        print(f"❌ Error testing duplicate prevention mechanisms: {e}")
        return False

def test_consensus_signal_paths():
    """Test consensus signal sending paths."""
    print(f"\n🧪 TEST 5: Check consensus signal sending paths")
    
    try:
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        paths_found = 0
        
        # Path 1: Primary tracking method
        if 'send_consensus_signal_with_tracking' in content:
            print(f"  ✅ Found primary tracking path")
            paths_found += 1
        else:
            print(f"  ❌ Missing primary tracking path")
        
        # Path 2: Fallback enhanced notification
        if '_send_enhanced_signal_notification' in content:
            print(f"  ✅ Found fallback notification path")
            paths_found += 1
        else:
            print(f"  ❌ Missing fallback notification path")
        
        # Path 3: Chart generation path (should be conditional)
        if '_send_detailed_analysis_report' in content:
            print(f"  ✅ Found chart generation path")
            paths_found += 1
        else:
            print(f"  ❌ Missing chart generation path")
        
        # Check that paths are properly controlled
        if 'if not consensus_send_result:' in content:
            print(f"  ✅ Found proper path control logic")
            paths_found += 1
        else:
            print(f"  ❌ Missing proper path control logic")
        
        if paths_found >= 3:
            print(f"  ✅ Consensus signal paths: GOOD ({paths_found}/4)")
            return True
        else:
            print(f"  ❌ Consensus signal paths: INCOMPLETE ({paths_found}/4)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing consensus signal paths: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 === CONSENSUS DUPLICATE FIX TEST ===")
    print(f"⏰ Test started at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    results = []
    
    # Test 1: Consensus duplicate prevention
    print(f"\n" + "="*60)
    result1 = test_consensus_duplicate_prevention()
    results.append(("Consensus Duplicate Prevention", result1))
    
    # Test 2: Signal integration fixes
    print(f"\n" + "="*60)
    result2 = test_signal_integration_consensus_fix()
    results.append(("Signal Integration Consensus Fix", result2))
    
    # Test 3: Consensus flow logic
    print(f"\n" + "="*60)
    result3 = test_consensus_flow_logic()
    results.append(("Consensus Flow Logic", result3))
    
    # Test 4: Duplicate prevention mechanisms
    print(f"\n" + "="*60)
    result4 = test_duplicate_prevention_mechanisms()
    results.append(("Duplicate Prevention Mechanisms", result4))
    
    # Test 5: Consensus signal paths
    print(f"\n" + "="*60)
    result5 = test_consensus_signal_paths()
    results.append(("Consensus Signal Paths", result5))
    
    # Summary
    print(f"\n" + "="*60)
    print(f"📊 === TEST RESULTS SUMMARY ===")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Score: {passed}/{total} ({passed/total*100:.0f}%)")
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"✅ Consensus signals should now be sent only ONCE")
        print(f"✅ No more duplicate consensus notifications")
        print(f"✅ Proper flow control implemented")
    elif passed >= total * 0.8:
        print(f"✅ MOST TESTS PASSED!")
        print(f"🔧 Minor issues may remain")
    else:
        print(f"❌ SIGNIFICANT ISSUES REMAIN!")
        print(f"🚨 Consensus signals may still be duplicated")
    
    print(f"\n💡 FIXES IMPLEMENTED:")
    print(f"  1. ✅ Single consensus send tracking (consensus_sent variable)")
    print(f"  2. ✅ Primary/fallback flow control")
    print(f"  3. ✅ Chart generation duplicate prevention")
    print(f"  4. ✅ Signal integration duplicate check")
    print(f"  5. ✅ Removed chart_generator parameter")
    print(f"  6. ✅ Added .env chat routing")
    
    print(f"\n🚫 DUPLICATE PREVENTION STRATEGY:")
    print(f"  - Primary: send_consensus_signal_with_tracking()")
    print(f"  - Fallback: _send_enhanced_signal_notification() (only if primary fails)")
    print(f"  - Chart: _send_detailed_analysis_report() (only if consensus not sent)")
    print(f"  - Tracking: consensus_sent variable prevents multiple sends")
    print(f"  - Cooldown: 20-minute duplicate prevention per coin")
    
    print(f"\n⏰ Test completed at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 CONSENSUS DUPLICATE FIX TEST PASSED!")
        print(f"📊 Consensus signals should now be sent only once")
    else:
        print(f"\n❌ CONSENSUS DUPLICATE FIX TEST FAILED!")
        print(f"🚨 Consensus signals may still be duplicated")
    
    sys.exit(0 if success else 1)
