#!/usr/bin/env python3
"""
🔍 Debug Chart Test - Debug chart sending issue
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def debug_chart_test():
    """🔍 Debug chart test."""
    print(f"🔍 DEBUG CHART TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.02, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Create sample Fibonacci data
        fibonacci_data = {
            'trend_direction': 'UPTREND',
            'pivot_high': current_price * 1.05,
            'pivot_low': current_price * 0.95,
            'retracement_levels': [
                {'ratio': 0.236, 'price': current_price * 0.98, 'strength': 0.8},
                {'ratio': 0.382, 'price': current_price * 0.96, 'strength': 0.9},
                {'ratio': 0.618, 'price': current_price * 0.94, 'strength': 0.95}
            ],
            'extension_levels': [
                {'ratio': 1.618, 'price': current_price * 1.08, 'strength': 0.85},
                {'ratio': 2.618, 'price': current_price * 1.15, 'strength': 0.75}
            ]
        }
        
        print(f"🌀 Generating Fibonacci chart...")
        chart_path = chart_gen.generate_fibonacci_chart("BTC/USDT", fibonacci_data, ohlcv_data, current_price)
        
        if not chart_path:
            print(f"❌ Chart generation failed")
            return False
        
        print(f"✅ Chart generated: {chart_path}")
        
        # Check if file exists and get full path
        if not os.path.exists(chart_path):
            print(f"❌ Chart file does not exist: {chart_path}")
            return False
        
        full_path = os.path.abspath(chart_path)
        file_size = os.path.getsize(full_path) / (1024 * 1024)
        print(f"📁 Full path: {full_path}")
        print(f"📊 File size: {file_size:.2f} MB")
        
        # Create simple caption first
        simple_caption = f"""🌀 <b>FIBONACCI ANALYSIS - BTC/USDT</b>

💰 <b>Current Price:</b> <code>{current_price:.2f}</code>
📈 <b>Trend:</b> <b>UPTREND</b>
🎯 <b>Levels:</b> 3 retracement, 2 extension

⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>"""
        
        print(f"📝 Simple caption length: {len(simple_caption)} chars")
        print(f"📤 Testing with simple caption first...")
        
        success = notifier.send_photo(
            photo_path=full_path,
            caption=simple_caption,
            chat_id=chat_id,
            parse_mode="HTML"
        )
        
        if success:
            print(f"✅ SUCCESS! Chart with simple caption sent")
            
            # Now try with detailed caption
            print(f"📝 Creating detailed caption...")
            detailed_caption = chart_gen._create_fibonacci_caption("BTC/USDT", fibonacci_data, current_price)
            print(f"📝 Detailed caption length: {len(detailed_caption)} chars")
            
            # Wait a bit to avoid rate limiting
            import time
            time.sleep(3)
            
            print(f"📤 Testing with detailed caption...")
            success2 = notifier.send_photo(
                photo_path=full_path,
                caption=detailed_caption,
                chat_id=chat_id,
                parse_mode="HTML"
            )
            
            if success2:
                print(f"✅ SUCCESS! Chart with detailed caption sent")
                return True
            else:
                print(f"❌ FAILED! Detailed caption failed but simple worked")
                print(f"   This suggests the caption is too long or has formatting issues")
                return False
        else:
            print(f"❌ FAILED! Even simple caption failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_chart_test()
    if success:
        print(f"\n🎉 DEBUG CHART TEST PASSED!")
    else:
        print(f"\n💥 DEBUG CHART TEST FAILED!")
