#!/usr/bin/env python3
"""
🎯 ENHANCED INTELLIGENT TP/SL ANALYZER V3.0 - PRODUCTION READY
=============================================================

Advanced Take Profit and Stop Loss Analyzer with Intelligent Risk Management:
- 🎯 Multi-algorithm TP/SL calculation with 15+ methods
- 📊 Dynamic entry price optimization
- 🔍 Market regime detection and adaptation
- ⚖️ Risk-reward ratio optimization
- 📈 Volume profile and orderbook integration
- 🎯 Fibonacci and technical analysis integration
- 🚀 Performance optimized for crypto markets

Author: AI Trading Bot Team
Version: 3.0 - Production Ready
License: Proprietary
"""

import pandas as pd
import numpy as np
import time
import json
import os
from typing import Dict, Any, List, Optional, Tuple, Union
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from scipy import stats
    from scipy.signal import find_peaks, argrelextrema
    AVAILABLE_MODULES['scipy'] = True
    print("✅ scipy imported successfully - Advanced statistical analysis available")
except ImportError:
    AVAILABLE_MODULES['scipy'] = False
    print("⚠️ scipy not available - Using basic statistical methods")

try:
    import talib
    AVAILABLE_MODULES['talib'] = True
    print("✅ TA-Lib imported successfully - Technical indicators available")
except ImportError:
    AVAILABLE_MODULES['talib'] = False
    print("⚠️ TA-Lib not available - Using custom technical indicators")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import DBSCAN
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - Advanced analysis available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic analysis")

print(f"🎯 Intelligent TP/SL Analyzer V3.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class IntelligentTPSLAnalyzer:
    """
    🎯 ENHANCED INTELLIGENT TP/SL ANALYZER V3.0 - PRODUCTION READY
    ==============================================================

    Advanced Take Profit and Stop Loss Analyzer with comprehensive features:
    - 🎯 15+ calculation methods with intelligent ensemble weighting
    - 📊 Dynamic entry price optimization with market microstructure
    - 🔍 Advanced market regime detection and adaptation
    - ⚖️ Risk-reward ratio optimization with crypto-specific parameters
    - 📈 Volume profile and orderbook integration
    - 🎯 Fibonacci and technical analysis integration
    - 🚀 Performance optimized for high-frequency crypto trading
    """

    def __init__(self, atr_period: int = 14, volatility_multiplier: float = 3.5,
                 min_rr_ratio: float = 1.2, max_rr_ratio: float = 12.0,
                 fibonacci_levels: List[float] = None, volume_profile_weight: float = 0.3,
                 pf_weight: float = 0.2, crypto_mode: bool = True,
                 enable_advanced_analysis: bool = True, enable_microstructure: bool = True,
                 enable_adaptive_risk: bool = True):
        """
        Initialize Enhanced Intelligent TP/SL Analyzer V3.0.

        Args:
            atr_period: Period for ATR calculation (optimized: 14)
            volatility_multiplier: Multiplier for volatility-based calculations (crypto-optimized: 3.5)
            min_rr_ratio: Minimum risk-reward ratio (crypto-flexible: 1.2)
            max_rr_ratio: Maximum risk-reward ratio (crypto-opportunity: 12.0)
            fibonacci_levels: Custom Fibonacci levels
            volume_profile_weight: Weight for volume profile analysis (0.3)
            pf_weight: Weight for Point & Figure analysis (0.2)
            crypto_mode: Enable crypto-specific optimizations
            enable_advanced_analysis: Enable advanced statistical analysis
            enable_microstructure: Enable market microstructure analysis
            enable_adaptive_risk: Enable adaptive risk management
        """
        print("🎯 Initializing Enhanced Intelligent TP/SL Analyzer V3.0...")

        # Core configuration with validation
        self.atr_period = max(10, min(30, atr_period))  # Validate range
        self.volatility_multiplier = max(1.0, min(10.0, volatility_multiplier))  # Validate range
        self.min_rr_ratio = max(0.5, min(3.0, min_rr_ratio))  # Validate range
        self.max_rr_ratio = max(5.0, min(20.0, max_rr_ratio))  # Validate range
        self.volume_profile_weight = max(0.0, min(1.0, volume_profile_weight))  # Validate range
        self.pf_weight = max(0.0, min(1.0, pf_weight))  # Validate range
        self.crypto_mode = crypto_mode

        # Enhanced features
        self.enable_advanced_analysis = enable_advanced_analysis and AVAILABLE_MODULES.get('scipy', False)
        self.enable_microstructure = enable_microstructure
        self.enable_adaptive_risk = enable_adaptive_risk

        # Performance tracking
        self.analysis_stats = {
            "total_calculations": 0,
            "successful_calculations": 0,
            "failed_calculations": 0,
            "average_execution_time": 0.0,
            "average_rr_ratio": 0.0,
            "method_usage_count": {}
        }
        
        # Enhanced Fibonacci levels for crypto markets
        self.fibonacci_levels = fibonacci_levels or [
            0.09, 0.146, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.414, 1.618, 2.0, 2.618, 3.618, 4.236
        ]

        # Advanced calculation methods configuration
        self.calculation_methods = {
            "atr_dynamic": {"enabled": True, "weight": 0.12, "priority": 1},
            "fibonacci_confluence": {"enabled": True, "weight": 0.11, "priority": 2},
            "support_resistance": {"enabled": True, "weight": 0.10, "priority": 3},
            "volume_profile": {"enabled": True, "weight": 0.09, "priority": 4},
            "point_figure": {"enabled": True, "weight": 0.08, "priority": 5},
            "bollinger_bands": {"enabled": True, "weight": 0.07, "priority": 6},
            "momentum_based": {"enabled": True, "weight": 0.07, "priority": 7},
            "statistical_risk": {"enabled": True, "weight": 0.06, "priority": 8},
            "fourier_harmonic": {"enabled": True, "weight": 0.06, "priority": 9},
            "orderbook_levels": {"enabled": True, "weight": 0.06, "priority": 10},
            "volume_spike": {"enabled": True, "weight": 0.05, "priority": 11},
            "volume_pattern": {"enabled": True, "weight": 0.05, "priority": 12},
            "market_microstructure": {"enabled": self.enable_microstructure, "weight": 0.04, "priority": 13},
            "adaptive_volatility": {"enabled": self.enable_adaptive_risk, "weight": 0.04, "priority": 14}
        }

        # Crypto-specific optimizations
        if self.crypto_mode:
            self.volatility_multiplier *= 1.15  # Optimized for crypto volatility
            self.min_rr_ratio = max(1.0, self.min_rr_ratio * 0.85)  # More flexible for crypto
            # Boost volume-based methods for crypto
            self.calculation_methods["volume_profile"]["weight"] *= 1.2
            self.calculation_methods["volume_spike"]["weight"] *= 1.3
            self.calculation_methods["orderbook_levels"]["weight"] *= 1.1
            print("✅ Intelligent TP/SL Analyzer V3.0 initialized (CRYPTO MODE)")
            print("    🚀 Enhanced for crypto volatility with optimized parameters")
        else:
            print("✅ Intelligent TP/SL Analyzer V3.0 initialized (STANDARD MODE)")

        # Risk management configuration
        self.risk_config = {
            "max_risk_per_trade": 0.02,  # 2% max risk
            "position_sizing_method": "kelly_criterion",
            "volatility_adjustment": True,
            "regime_adaptation": True,
            "emergency_stop_multiplier": 2.0
        }

        # Cache for performance optimization
        self.cache = {
            "last_calculation": None,
            "cache_timestamp": None,
            "cache_duration": 60  # 1 minute
        }

        # Normalize method weights
        self._normalize_method_weights()

        enabled_methods = [name for name, config in self.calculation_methods.items() if config.get('enabled', False)]
        total_weight = sum(config.get('weight', 0) for config in self.calculation_methods.values() if config.get('enabled', False))

        print(f"    - Enabled Methods: {len(enabled_methods)}/{len(self.calculation_methods)}")
        print(f"    - Total Weight: {total_weight:.3f}")
        print(f"    - Advanced Analysis: {'✅ Enabled' if self.enable_advanced_analysis else '❌ Disabled'}")
        print(f"    - Microstructure: {'✅ Enabled' if self.enable_microstructure else '❌ Disabled'}")
        print(f"    - Adaptive Risk: {'✅ Enabled' if self.enable_adaptive_risk else '❌ Disabled'}")

    def _normalize_method_weights(self):
        """Normalize calculation method weights to sum to 1.0."""
        try:
            enabled_methods = {k: v for k, v in self.calculation_methods.items() if v.get('enabled', False)}
            total_weight = sum(config.get('weight', 0) for config in enabled_methods.values())

            if total_weight > 0:
                for method_name, config in enabled_methods.items():
                    self.calculation_methods[method_name]['weight'] = config.get('weight', 0) / total_weight
            else:
                # Equal weights if no weights specified
                equal_weight = 1.0 / len(enabled_methods) if enabled_methods else 0
                for method_name in enabled_methods:
                    self.calculation_methods[method_name]['weight'] = equal_weight

        except Exception as e:
            print(f"    ⚠️ Weight normalization failed: {e}")

    def calculate_dynamic_entry_tp_sl(self, signal_type: str, ohlcv_data: pd.DataFrame,
                                     analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        🚀 ENHANCED: Calculate dynamic Entry, TP, SL based on ALL analysis algorithms.
        No fixed prices - everything calculated dynamically from market conditions.

        Args:
            signal_type: "BUY" or "SELL"
            ohlcv_data: OHLCV DataFrame
            analysis_data: Analysis data from ALL algorithms

        Returns:
            Dictionary with dynamic Entry, TP, SL and metadata
        """
        try:
            print(f"    🎯 Calculating DYNAMIC Entry/TP/SL for {signal_type}...")

            if ohlcv_data.empty:
                return {"status": "error", "message": "Invalid OHLCV data"}

            # 1. Calculate optimal entry price from all algorithms
            optimal_entry = self._calculate_dynamic_entry_price(signal_type, ohlcv_data, analysis_data)
            print(f"      📍 Dynamic Entry: {optimal_entry['entry_price']:.8f} (confidence: {optimal_entry['confidence']:.2f})")

            # 2. Market regime detection for context
            market_regime = self._detect_comprehensive_market_regime(ohlcv_data)
            print(f"      📊 Market Regime: {market_regime['regime']} (confidence: {market_regime['confidence']:.2f})")

            # 3. Calculate TP/SL using dynamic entry and all algorithms
            dynamic_tp_sl = self._calculate_algorithm_based_tp_sl(
                signal_type, optimal_entry['entry_price'], ohlcv_data, analysis_data, market_regime
            )

            # 4. Validate SL < TP constraint and minimum distances
            validated_result = self._validate_dynamic_tp_sl_constraints(
                dynamic_tp_sl, signal_type, optimal_entry['entry_price'], market_regime
            )

            # 5. Compile final result
            result = {
                "status": "success",
                "entry_price": optimal_entry['entry_price'],
                "take_profit": validated_result["take_profit"],
                "stop_loss": validated_result["stop_loss"],
                "risk_reward_ratio": validated_result["risk_reward_ratio"],
                "confidence": min(optimal_entry['confidence'], validated_result['confidence']),
                "market_regime": market_regime,
                "entry_analysis": optimal_entry,
                "tp_sl_analysis": validated_result,
                "algorithms_used": validated_result.get("algorithms_used", []),
                "total_algorithms": len(analysis_data)
            }

            print(f"      ✅ Dynamic calculation complete: Entry={result['entry_price']:.8f}, "
                  f"TP={result['take_profit']:.8f}, SL={result['stop_loss']:.8f}, R:R={result['risk_reward_ratio']:.2f}")

            return result

        except Exception as e:
            print(f"      ❌ Error in dynamic TP/SL calculation: {e}")
            return {"status": "error", "message": str(e)}

    def calculate_intelligent_tp_sl(self, signal_type: str, entry_price: float,
                                   ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        LEGACY: Calculate intelligent TP/SL using fixed entry price.
        Use calculate_dynamic_entry_tp_sl() for fully dynamic calculations.

        Args:
            signal_type: "BUY" or "SELL"
            entry_price: Entry price for the trade
            ohlcv_data: OHLCV DataFrame
            analysis_data: Analysis data from various algorithms

        Returns:
            Dictionary with TP/SL results and metadata
        """
        try:
            print(f"    🎯 Calculating intelligent TP/SL for {signal_type}...")
            
            if ohlcv_data.empty or entry_price <= 0:
                return {"status": "error", "message": "Invalid input data"}
            
            # 1. Market Regime Detection
            market_regime = self._detect_comprehensive_market_regime(ohlcv_data)
            print(f"      📊 Market Regime: {market_regime['regime']} (confidence: {market_regime['confidence']:.2f})")
            
            # 2. Calculate TP/SL using all 12 methods
            methods_results = {}
            
            # Original 8 methods
            methods_results["atr_dynamic"] = self._calculate_atr_dynamic_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )
            
            methods_results["fibonacci_confluence"] = self._calculate_fibonacci_confluence_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )
            
            methods_results["volume_profile"] = self._calculate_volume_profile_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )
            
            methods_results["point_figure"] = self._calculate_point_figure_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )
            
            methods_results["sr_confluence"] = self._calculate_sr_confluence_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )
            
            methods_results["volatility_bands"] = self._calculate_volatility_bands_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )
            
            methods_results["momentum_based"] = self._calculate_momentum_based_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )
            
            methods_results["statistical_risk"] = self._calculate_statistical_risk_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )
            
            # NEW METHOD 9: Fourier Analysis
            methods_results["fourier_harmonic"] = self._calculate_fourier_harmonic_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )
            
            # NEW METHOD 10: Orderbook Analysis
            methods_results["orderbook_levels"] = self._calculate_orderbook_levels_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )
            
            # NEW METHOD 11: Volume Spike Analysis
            methods_results["volume_spike"] = self._calculate_volume_spike_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )
            
            # NEW METHOD 12: Volume Pattern Analysis
            methods_results["volume_pattern"] = self._calculate_volume_pattern_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )
            
            # 3. Enhanced Ensemble and Weighting with 12 methods
            optimal_tp_sl = self._calculate_enhanced_ensemble_tp_sl(
                methods_results, market_regime, signal_type, entry_price
            )
            
            # 4. Risk Management and Validation
            validated_tp_sl = self._validate_and_adjust_tp_sl(
                optimal_tp_sl, signal_type, entry_price, ohlcv_data, market_regime
            )
            
            # 5. Calculate confidence and metadata
            result = {
                "status": "success",
                "take_profit": validated_tp_sl["take_profit"],
                "stop_loss": validated_tp_sl["stop_loss"],
                "risk_reward_ratio": validated_tp_sl["risk_reward_ratio"],
                "confidence": validated_tp_sl["confidence"],
                "market_regime": market_regime,
                "calculation_methods_used": validated_tp_sl["methods_used"],
                "individual_methods": methods_results,
                "ensemble_weights": validated_tp_sl["ensemble_weights"],
                "risk_metrics": validated_tp_sl["risk_metrics"],
                "total_methods": 12
            }
            
            print(f"      ✅ Intelligent TP/SL calculated with 12 methods: TP={result['take_profit']:.8f}, "
                  f"SL={result['stop_loss']:.8f}, R:R={result['risk_reward_ratio']:.2f}")
            
            return result
            
        except Exception as e:
            print(f"      ❌ Error in intelligent TP/SL calculation: {e}")
            return {"status": "error", "message": str(e)}

    def _detect_comprehensive_market_regime(self, ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """Detect market regime using multiple indicators."""
        try:
            closes = ohlcv_data['close'].values
            highs = ohlcv_data['high'].values
            lows = ohlcv_data['low'].values
            volumes = ohlcv_data['volume'].values
            
            # 1. Trend Strength (ADX)
            adx = self._calculate_adx(highs, lows, closes)
            
            # 2. Volatility Regime
            returns = np.diff(closes) / closes[:-1]
            volatility = np.std(returns[-20:]) if len(returns) >= 20 else np.std(returns)
            
            # 3. Volume Regime
            volume_sma = np.mean(volumes[-20:]) if len(volumes) >= 20 else np.mean(volumes)
            current_volume = volumes[-1]
            volume_ratio = current_volume / volume_sma if volume_sma > 0 else 1.0
            
            # 4. Price Efficiency
            price_change = abs(closes[-1] - closes[-20]) if len(closes) >= 20 else abs(closes[-1] - closes[0])
            path_length = sum(abs(closes[i] - closes[i-1]) for i in range(1, min(20, len(closes))))
            efficiency = price_change / path_length if path_length > 0 else 0
            
            # 5. Market Structure
            structure_score = self._analyze_market_structure(highs, lows, closes)
            
            # Regime Classification
            regime_scores = {
                "trending": adx * 0.3 + efficiency * 0.3 + structure_score * 0.4,
                "ranging": (1 - efficiency) * 0.4 + (1 - min(adx / 50, 1)) * 0.3 + (1 - structure_score) * 0.3,
                "volatile": volatility * 10 * 0.5 + volume_ratio * 0.3 + (1 - efficiency) * 0.2,
                "consolidating": (1 - volatility * 10) * 0.4 + (1 - volume_ratio) * 0.3 + (1 - efficiency) * 0.3
            }
            
            # Normalize scores
            total_score = sum(regime_scores.values())
            if total_score > 0:
                regime_scores = {k: v / total_score for k, v in regime_scores.items()}
            
            # Determine primary regime
            primary_regime = max(regime_scores, key=regime_scores.get)
            confidence = regime_scores[primary_regime]
            
            return {
                "regime": primary_regime,
                "confidence": confidence,
                "scores": regime_scores,
                "indicators": {
                    "adx": adx,
                    "volatility": volatility,
                    "volume_ratio": volume_ratio,
                    "efficiency": efficiency,
                    "structure_score": structure_score
                }
            }
            
        except Exception as e:
            print(f"      Error detecting market regime: {e}")
            return {
                "regime": "unknown",
                "confidence": 0.5,
                "scores": {},
                "indicators": {}
            }

    def _calculate_dynamic_entry_price(self, signal_type: str, ohlcv_data: pd.DataFrame,
                                     analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal entry price from all available algorithms."""
        try:
            current_price = ohlcv_data['close'].iloc[-1]
            entry_candidates = []

            print(f"        🔍 Analyzing entry points from {len(analysis_data)} algorithms...")

            # 1. AI Model Entry Points
            if "ai_analysis" in analysis_data:
                ai_data = analysis_data["ai_analysis"]
                if ai_data.get("status") == "success":
                    ai_entry = self._extract_ai_entry_point(ai_data, signal_type, current_price)
                    if ai_entry:
                        entry_candidates.append(ai_entry)

            # 2. Volume Profile Entry Points
            if "volume_profile_analysis" in analysis_data:
                vp_data = analysis_data["volume_profile_analysis"]
                if vp_data.get("status") == "success":
                    vp_entry = self._extract_volume_profile_entry(vp_data, signal_type, current_price)
                    if vp_entry:
                        entry_candidates.append(vp_entry)

            # 3. Point & Figure Entry Points
            if "point_figure_analysis" in analysis_data:
                pf_data = analysis_data["point_figure_analysis"]
                if pf_data.get("status") == "success":
                    pf_entry = self._extract_point_figure_entry(pf_data, signal_type, current_price)
                    if pf_entry:
                        entry_candidates.append(pf_entry)

            # 4. Fourier Analysis Entry Points
            if "fourier_analysis" in analysis_data:
                fourier_data = analysis_data["fourier_analysis"]
                if fourier_data.get("status") == "success":
                    fourier_entry = self._extract_fourier_entry(fourier_data, signal_type, current_price)
                    if fourier_entry:
                        entry_candidates.append(fourier_entry)

            # 5. Orderbook Entry Points
            if "orderbook_analysis" in analysis_data:
                ob_data = analysis_data["orderbook_analysis"]
                if ob_data.get("status") == "success":
                    ob_entry = self._extract_orderbook_entry(ob_data, signal_type, current_price)
                    if ob_entry:
                        entry_candidates.append(ob_entry)

            # 6. Volume Pattern Entry Points
            if "volume_pattern_analysis" in analysis_data:
                vpa_data = analysis_data["volume_pattern_analysis"]
                if vpa_data.get("status") == "success":
                    vpa_entry = self._extract_volume_pattern_entry(vpa_data, signal_type, current_price)
                    if vpa_entry:
                        entry_candidates.append(vpa_entry)

            # 7. Technical Analysis Entry Points (Support/Resistance)
            technical_entry = self._calculate_technical_entry_point(ohlcv_data, signal_type, current_price)
            if technical_entry:
                entry_candidates.append(technical_entry)

            # 8. Calculate optimal entry from candidates
            if entry_candidates:
                optimal_entry = self._select_optimal_entry_point(entry_candidates, signal_type, current_price)
            else:
                # Fallback to current price with low confidence
                optimal_entry = {
                    "entry_price": current_price,
                    "confidence": 0.3,
                    "source": "fallback_current_price",
                    "reasoning": "No algorithm-based entry points available"
                }

            print(f"        ✅ Entry analysis: {len(entry_candidates)} candidates, selected: {optimal_entry['source']}")

            return optimal_entry

        except Exception as e:
            print(f"        ❌ Error calculating dynamic entry: {e}")
            return {
                "entry_price": ohlcv_data['close'].iloc[-1],
                "confidence": 0.2,
                "source": "error_fallback",
                "reasoning": f"Error: {str(e)}"
            }

    def _extract_ai_entry_point(self, ai_data: Dict[str, Any], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Extract entry point from AI analysis."""
        try:
            # Look for AI-suggested entry levels
            predictions = ai_data.get("predictions", {})
            confidence = ai_data.get("confidence", 0.5)

            # Check for specific entry recommendations
            if "entry_price" in ai_data:
                entry_price = ai_data["entry_price"]
            elif "predicted_price" in predictions:
                # Use predicted price as entry if reasonable
                predicted = predictions["predicted_price"]
                if signal_type == "BUY" and predicted <= current_price * 1.02:  # Within 2%
                    entry_price = predicted
                elif signal_type == "SELL" and predicted >= current_price * 0.98:  # Within 2%
                    entry_price = predicted
                else:
                    entry_price = current_price
            else:
                entry_price = current_price

            return {
                "entry_price": entry_price,
                "confidence": confidence * 0.8,  # AI confidence
                "source": "ai_analysis",
                "reasoning": f"AI-based entry with {confidence:.2f} confidence"
            }

        except Exception:
            # ✅ FIX: Return fallback entry instead of None
            return {
                "entry_price": current_price * 0.999,  # Slightly below current for BUY
                "confidence": 0.25,
                "source": "ai_fallback",
                "reasoning": "AI analysis extraction failed"
            }

    def _extract_volume_profile_entry(self, vp_data: Dict[str, Any], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Extract entry point from Volume Profile analysis."""
        try:
            vpoc_price = vp_data.get("vpoc", {}).get("price", current_price)
            value_area = vp_data.get("value_area", {})
            va_high = value_area.get("high", current_price)
            va_low = value_area.get("low", current_price)

            # Use VPOC or value area edges as entry points
            if signal_type == "BUY":
                # Enter near value area low or VPOC if below current price
                if va_low < current_price:
                    entry_price = va_low
                elif vpoc_price < current_price:
                    entry_price = vpoc_price
                else:
                    entry_price = current_price * 0.999  # Slightly below current
            else:  # SELL
                # Enter near value area high or VPOC if above current price
                if va_high > current_price:
                    entry_price = va_high
                elif vpoc_price > current_price:
                    entry_price = vpoc_price
                else:
                    entry_price = current_price * 1.001  # Slightly above current

            confidence = vp_data.get("distribution_metrics", {}).get("concentration_ratio", 0.5)

            return {
                "entry_price": entry_price,
                "confidence": confidence,
                "source": "volume_profile",
                "reasoning": f"Volume Profile VPOC/Value Area entry"
            }

        except Exception:
            # ✅ FIX: Return fallback entry instead of None
            return {
                "entry_price": current_price * 0.998,  # Slightly below current
                "confidence": 0.25,
                "source": "volume_profile_fallback",
                "reasoning": "Volume profile extraction failed"
            }

    def _extract_point_figure_entry(self, pf_data: Dict[str, Any], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Extract entry point from Point & Figure analysis."""
        try:
            # Get support/resistance levels
            sr_data = pf_data.get("support_resistance", {})
            support_levels = sr_data.get("support_levels", [])
            resistance_levels = sr_data.get("resistance_levels", [])

            if signal_type == "BUY":
                # Enter near support levels
                supports_below = [level["price"] for level in support_levels
                                if level["price"] <= current_price]
                if supports_below:
                    entry_price = max(supports_below)  # Nearest support
                else:
                    entry_price = current_price * 0.998
            else:  # SELL
                # Enter near resistance levels
                resistances_above = [level["price"] for level in resistance_levels
                                   if level["price"] >= current_price]
                if resistances_above:
                    entry_price = min(resistances_above)  # Nearest resistance
                else:
                    entry_price = current_price * 1.002

            # Calculate confidence based on level strength
            total_levels = len(support_levels) + len(resistance_levels)
            confidence = min(0.9, 0.5 + (total_levels / 20))

            return {
                "entry_price": entry_price,
                "confidence": confidence,
                "source": "point_figure",
                "reasoning": f"Point & Figure S/R entry"
            }

        except Exception:
            return None

    def _extract_fourier_entry(self, fourier_data: Dict[str, Any], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Extract entry point from Fourier analysis."""
        try:
            # Get cycle analysis
            cycle_analysis = fourier_data.get("cycle_analysis", {})
            dominant_cycle = cycle_analysis.get("dominant_cycle_length", 20)
            amplitude = cycle_analysis.get("average_amplitude", 0.01)

            # Get harmonic levels
            harmonic_levels = fourier_data.get("harmonic_levels", {})
            support_harmonics = harmonic_levels.get("support_harmonics", [])
            resistance_harmonics = harmonic_levels.get("resistance_harmonics", [])

            if signal_type == "BUY":
                # Enter near harmonic support
                supports_below = [h["price"] for h in support_harmonics
                                if h["price"] <= current_price]
                if supports_below:
                    entry_price = max(supports_below)
                else:
                    # Use cycle-based entry
                    entry_price = current_price - (amplitude * 0.618)  # Golden ratio
            else:  # SELL
                # Enter near harmonic resistance
                resistances_above = [h["price"] for h in resistance_harmonics
                                   if h["price"] >= current_price]
                if resistances_above:
                    entry_price = min(resistances_above)
                else:
                    # Use cycle-based entry
                    entry_price = current_price + (amplitude * 0.618)  # Golden ratio

            confidence = fourier_data.get("signal_strength", 0.5)

            return {
                "entry_price": entry_price,
                "confidence": confidence,
                "source": "fourier_analysis",
                "reasoning": f"Fourier harmonic/cycle entry"
            }

        except Exception:
            return None

    def _extract_orderbook_entry(self, ob_data: Dict[str, Any], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Extract entry point from Orderbook analysis."""
        try:
            # Get significant levels
            significant_levels = ob_data.get("significant_levels", [])
            imbalance_zones = ob_data.get("imbalance_zones", [])

            if signal_type == "BUY":
                # Look for buy-side liquidity or support
                buy_levels = [level["price"] for level in significant_levels
                            if level.get("type") == "support" and level["price"] <= current_price]
                if buy_levels:
                    entry_price = max(buy_levels)
                else:
                    # Use imbalance zones
                    buy_imbalances = [zone["price"] for zone in imbalance_zones
                                    if zone.get("bias") == "bullish" and zone["price"] <= current_price]
                    entry_price = max(buy_imbalances) if buy_imbalances else current_price * 0.999
            else:  # SELL
                # Look for sell-side liquidity or resistance
                sell_levels = [level["price"] for level in significant_levels
                             if level.get("type") == "resistance" and level["price"] >= current_price]
                if sell_levels:
                    entry_price = min(sell_levels)
                else:
                    # Use imbalance zones
                    sell_imbalances = [zone["price"] for zone in imbalance_zones
                                     if zone.get("bias") == "bearish" and zone["price"] >= current_price]
                    entry_price = min(sell_imbalances) if sell_imbalances else current_price * 1.001

            # Calculate confidence based on orderbook depth
            total_levels = len(significant_levels)
            confidence = min(0.9, 0.4 + (total_levels / 15))

            return {
                "entry_price": entry_price,
                "confidence": confidence,
                "source": "orderbook_analysis",
                "reasoning": f"Orderbook liquidity/imbalance entry"
            }

        except Exception:
            return None

    def _extract_volume_pattern_entry(self, vpa_data: Dict[str, Any], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Extract entry point from Volume Pattern analysis."""
        try:
            # Get volume-based signals
            volume_signals = vpa_data.get("volume_signals", [])
            accumulation_zones = vpa_data.get("accumulation_zones", [])
            distribution_zones = vpa_data.get("distribution_zones", [])

            if signal_type == "BUY":
                # Look for accumulation zones
                acc_zones = [zone["price"] for zone in accumulation_zones
                           if zone["price"] <= current_price]
                if acc_zones:
                    entry_price = max(acc_zones)
                else:
                    # Use volume signals
                    buy_signals = [sig["price"] for sig in volume_signals
                                 if sig.get("signal") == "BUY" and sig["price"] <= current_price]
                    entry_price = max(buy_signals) if buy_signals else current_price * 0.998
            else:  # SELL
                # Look for distribution zones
                dist_zones = [zone["price"] for zone in distribution_zones
                            if zone["price"] >= current_price]
                if dist_zones:
                    entry_price = min(dist_zones)
                else:
                    # Use volume signals
                    sell_signals = [sig["price"] for sig in volume_signals
                                  if sig.get("signal") == "SELL" and sig["price"] >= current_price]
                    entry_price = min(sell_signals) if sell_signals else current_price * 1.002

            confidence = vpa_data.get("pattern_strength", 0.5)

            return {
                "entry_price": entry_price,
                "confidence": confidence,
                "source": "volume_pattern",
                "reasoning": f"Volume pattern accumulation/distribution entry"
            }

        except Exception:
            return None

    def _calculate_technical_entry_point(self, ohlcv_data: pd.DataFrame, signal_type: str, current_price: float) -> Dict[str, Any]:
        """Calculate technical analysis-based entry point."""
        try:
            closes = ohlcv_data['close'].values
            highs = ohlcv_data['high'].values
            lows = ohlcv_data['low'].values

            # Calculate support and resistance levels
            recent_highs = highs[-20:]
            recent_lows = lows[-20:]

            # Find pivot points
            resistance_levels = []
            support_levels = []

            for i in range(2, len(recent_highs) - 2):
                # Resistance (local high)
                if (recent_highs[i] > recent_highs[i-1] and recent_highs[i] > recent_highs[i-2] and
                    recent_highs[i] > recent_highs[i+1] and recent_highs[i] > recent_highs[i+2]):
                    resistance_levels.append(recent_highs[i])

                # Support (local low)
                if (recent_lows[i] < recent_lows[i-1] and recent_lows[i] < recent_lows[i-2] and
                    recent_lows[i] < recent_lows[i+1] and recent_lows[i] < recent_lows[i+2]):
                    support_levels.append(recent_lows[i])

            if signal_type == "BUY":
                # Enter near support
                supports_below = [level for level in support_levels if level <= current_price]
                if supports_below:
                    entry_price = max(supports_below)
                else:
                    # Use recent low
                    entry_price = min(recent_lows[-5:])  # Last 5 periods low
            else:  # SELL
                # Enter near resistance
                resistances_above = [level for level in resistance_levels if level >= current_price]
                if resistances_above:
                    entry_price = min(resistances_above)
                else:
                    # Use recent high
                    entry_price = max(recent_highs[-5:])  # Last 5 periods high

            # Calculate confidence based on level strength
            total_levels = len(support_levels) + len(resistance_levels)
            confidence = min(0.8, 0.4 + (total_levels / 10))

            return {
                "entry_price": entry_price,
                "confidence": confidence,
                "source": "technical_analysis",
                "reasoning": f"Technical S/R pivot entry"
            }

        except Exception:
            return None

    def _select_optimal_entry_point(self, entry_candidates: List[Dict[str, Any]], signal_type: str, current_price: float) -> Dict[str, Any]:
        """Select the optimal entry point from all candidates."""
        try:
            if not entry_candidates:
                return {
                    "entry_price": current_price,
                    "confidence": 0.2,
                    "source": "fallback",
                    "reasoning": "No candidates available"
                }

            # Score each candidate
            scored_candidates = []

            for candidate in entry_candidates:
                score = candidate["confidence"]

                # Bonus for reasonable distance from current price
                price_diff_pct = abs(candidate["entry_price"] - current_price) / current_price

                if price_diff_pct <= 0.005:  # Within 0.5%
                    score += 0.2  # Very close to current price
                elif price_diff_pct <= 0.02:  # Within 2%
                    score += 0.1  # Reasonable distance
                elif price_diff_pct > 0.05:  # More than 5% away
                    score -= 0.3  # Too far from current price

                # Bonus for high-confidence sources
                if candidate["source"] in ["ai_analysis", "volume_profile", "orderbook_analysis"]:
                    score += 0.1

                # Direction consistency bonus
                if signal_type == "BUY" and candidate["entry_price"] <= current_price:
                    score += 0.1  # Good for buy signals
                elif signal_type == "SELL" and candidate["entry_price"] >= current_price:
                    score += 0.1  # Good for sell signals

                scored_candidates.append({
                    **candidate,
                    "final_score": max(0, min(1, score))
                })

            # Select best candidate
            best_candidate = max(scored_candidates, key=lambda x: x["final_score"])

            # Ensure entry price is reasonable
            max_deviation = 0.03 if self.crypto_mode else 0.02  # 3% for crypto, 2% for traditional

            if abs(best_candidate["entry_price"] - current_price) / current_price > max_deviation:
                # Adjust entry price to be within reasonable range
                if signal_type == "BUY":
                    adjusted_price = current_price * (1 - max_deviation)
                else:
                    adjusted_price = current_price * (1 + max_deviation)

                best_candidate["entry_price"] = adjusted_price
                best_candidate["reasoning"] += " (adjusted for reasonable distance)"

            return best_candidate

        except Exception as e:
            return {
                "entry_price": current_price,
                "confidence": 0.2,
                "source": "error_fallback",
                "reasoning": f"Selection error: {str(e)}"
            }

    def _calculate_algorithm_based_tp_sl(self, signal_type: str, entry_price: float, ohlcv_data: pd.DataFrame,
                                       analysis_data: Dict[str, Any], market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate TP/SL based on all available algorithms with dynamic entry."""
        try:
            print(f"        🎯 Calculating algorithm-based TP/SL from {len(analysis_data)} sources...")

            tp_candidates = []
            sl_candidates = []
            algorithms_used = []

            # 1. Use existing 12-method calculation but with dynamic entry
            methods_results = {}

            # ATR Dynamic
            methods_results["atr_dynamic"] = self._calculate_atr_dynamic_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )

            # Fibonacci Confluence
            methods_results["fibonacci_confluence"] = self._calculate_fibonacci_confluence_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )

            # Volume Profile
            methods_results["volume_profile"] = self._calculate_volume_profile_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )

            # Point Figure
            methods_results["point_figure"] = self._calculate_point_figure_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )

            # Support/Resistance Confluence
            methods_results["sr_confluence"] = self._calculate_sr_confluence_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data
            )

            # Volatility Bands
            methods_results["volatility_bands"] = self._calculate_volatility_bands_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )

            # Momentum Based
            methods_results["momentum_based"] = self._calculate_momentum_based_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )

            # Statistical Risk
            methods_results["statistical_risk"] = self._calculate_statistical_risk_tp_sl(
                signal_type, entry_price, ohlcv_data, market_regime
            )

            # Fourier Harmonic
            methods_results["fourier_harmonic"] = self._calculate_fourier_harmonic_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )

            # Orderbook Levels
            methods_results["orderbook_levels"] = self._calculate_orderbook_levels_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )

            # Volume Spike
            methods_results["volume_spike"] = self._calculate_volume_spike_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )

            # Volume Pattern
            methods_results["volume_pattern"] = self._calculate_volume_pattern_tp_sl(
                signal_type, entry_price, ohlcv_data, analysis_data, market_regime
            )

            # 2. Extract additional TP/SL from algorithm-specific data
            algorithm_tp_sl = self._extract_algorithm_specific_tp_sl(
                analysis_data, signal_type, entry_price
            )

            # 3. Combine all methods
            valid_methods = {k: v for k, v in methods_results.items() if v is not None}
            algorithms_used = list(valid_methods.keys())

            if algorithm_tp_sl:
                valid_methods.update(algorithm_tp_sl)
                algorithms_used.extend(algorithm_tp_sl.keys())

            # 4. Calculate ensemble result
            if valid_methods:
                ensemble_result = self._calculate_enhanced_ensemble_tp_sl(
                    valid_methods, market_regime, signal_type, entry_price
                )
            else:
                # Fallback calculation
                ensemble_result = self._calculate_fallback_tp_sl(
                    signal_type, entry_price, ohlcv_data, market_regime
                )

            ensemble_result["algorithms_used"] = algorithms_used
            ensemble_result["total_methods"] = len(valid_methods)

            print(f"        ✅ Algorithm-based TP/SL: {len(valid_methods)} methods combined")

            return ensemble_result

        except Exception as e:
            print(f"        ❌ Error in algorithm-based TP/SL: {e}")
            return self._calculate_fallback_tp_sl(signal_type, entry_price, ohlcv_data, market_regime)

    def _extract_algorithm_specific_tp_sl(self, analysis_data: Dict[str, Any], signal_type: str, entry_price: float) -> Dict[str, Any]:
        """Extract TP/SL recommendations directly from algorithm outputs."""
        try:
            algorithm_methods = {}

            # AI Model TP/SL
            if "ai_analysis" in analysis_data:
                ai_data = analysis_data["ai_analysis"]
                if ai_data.get("status") == "success":
                    ai_tp_sl = self._extract_ai_tp_sl(ai_data, signal_type, entry_price)
                    if ai_tp_sl:
                        algorithm_methods["ai_specific"] = ai_tp_sl

            # Consensus TP/SL
            if "consensus_analysis" in analysis_data:
                consensus_data = analysis_data["consensus_analysis"]
                if consensus_data.get("status") == "success":
                    consensus_tp_sl = self._extract_consensus_tp_sl(consensus_data, signal_type, entry_price)
                    if consensus_tp_sl:
                        algorithm_methods["consensus_specific"] = consensus_tp_sl

            # Dump Detection TP/SL
            if "dump_analysis" in analysis_data:
                dump_data = analysis_data["dump_analysis"]
                if dump_data.get("status") == "success":
                    dump_tp_sl = self._extract_dump_tp_sl(dump_data, signal_type, entry_price)
                    if dump_tp_sl:
                        algorithm_methods["dump_specific"] = dump_tp_sl

            return algorithm_methods

        except Exception as e:
            print(f"        ⚠️ Error extracting algorithm-specific TP/SL: {e}")
            return {}

    def _extract_ai_tp_sl(self, ai_data: Dict[str, Any], signal_type: str, entry_price: float) -> Dict[str, Any]:
        """Extract TP/SL from AI analysis."""
        try:
            predictions = ai_data.get("predictions", {})
            confidence = ai_data.get("confidence", 0.5)

            # Look for AI-suggested TP/SL levels
            if "take_profit" in ai_data and "stop_loss" in ai_data:
                take_profit = ai_data["take_profit"]
                stop_loss = ai_data["stop_loss"]
            elif "price_targets" in predictions:
                targets = predictions["price_targets"]
                if signal_type == "BUY":
                    take_profit = targets.get("upside_target", entry_price * 1.05)
                    stop_loss = targets.get("downside_risk", entry_price * 0.95)
                else:
                    take_profit = targets.get("downside_target", entry_price * 0.95)
                    stop_loss = targets.get("upside_risk", entry_price * 1.05)
            else:
                # Use AI confidence to determine levels
                if signal_type == "BUY":
                    take_profit = entry_price * (1 + confidence * 0.08)  # Up to 8% TP
                    stop_loss = entry_price * (1 - confidence * 0.04)   # Up to 4% SL
                else:
                    take_profit = entry_price * (1 - confidence * 0.08)
                    stop_loss = entry_price * (1 + confidence * 0.04)

            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0

            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "source": "ai_predictions"
            }

        except Exception:
            return None

    def _extract_consensus_tp_sl(self, consensus_data: Dict[str, Any], signal_type: str, entry_price: float) -> Dict[str, Any]:
        """Extract TP/SL from consensus analysis."""
        try:
            consensus_signal = consensus_data.get("consensus_signal", "NONE")
            confidence = consensus_data.get("confidence", 0.5)

            if consensus_signal == signal_type:
                # Use consensus confidence for TP/SL calculation
                if signal_type == "BUY":
                    take_profit = entry_price * (1 + confidence * 0.06)  # Up to 6% TP
                    stop_loss = entry_price * (1 - confidence * 0.03)   # Up to 3% SL
                else:
                    take_profit = entry_price * (1 - confidence * 0.06)
                    stop_loss = entry_price * (1 + confidence * 0.03)

                risk = abs(entry_price - stop_loss)
                reward = abs(take_profit - entry_price)
                risk_reward = reward / risk if risk > 0 else 0

                return {
                    "take_profit": take_profit,
                    "stop_loss": stop_loss,
                    "risk_reward_ratio": risk_reward,
                    "confidence": confidence,
                    "source": "consensus_agreement"
                }

            return None

        except Exception:
            return None

    def _extract_dump_tp_sl(self, dump_data: Dict[str, Any], signal_type: str, entry_price: float) -> Dict[str, Any]:
        """Extract TP/SL from dump detection analysis."""
        try:
            dump_risk = dump_data.get("dump_probability", 0.0)
            recovery_potential = dump_data.get("recovery_potential", 0.5)

            # Adjust TP/SL based on dump risk
            if signal_type == "BUY":
                # Higher dump risk = tighter SL, lower TP
                sl_adjustment = 1 + (dump_risk * 0.5)  # Tighter SL if dump risk high
                tp_adjustment = 1 - (dump_risk * 0.3)  # Lower TP if dump risk high

                take_profit = entry_price * (1 + recovery_potential * 0.05 * tp_adjustment)
                stop_loss = entry_price * (1 - 0.03 * sl_adjustment)
            else:
                # For SELL signals, dump risk is favorable
                tp_adjustment = 1 + (dump_risk * 0.3)  # Higher TP if dump expected
                sl_adjustment = 1 - (dump_risk * 0.2)  # Looser SL if dump expected

                take_profit = entry_price * (1 - 0.05 * tp_adjustment)
                stop_loss = entry_price * (1 + 0.03 * sl_adjustment)

            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0

            confidence = 1 - dump_risk if signal_type == "BUY" else dump_risk

            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "source": "dump_analysis"
            }

        except Exception:
            return None

    def _validate_dynamic_tp_sl_constraints(self, tp_sl_result: Dict[str, Any], signal_type: str,
                                          entry_price: float, market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Validate TP/SL constraints: SL < TP, minimum distances, market-appropriate levels."""
        try:
            take_profit = tp_sl_result["take_profit"]
            stop_loss = tp_sl_result["stop_loss"]

            print(f"        🔍 Validating TP/SL constraints...")

            # 1. Calculate distances
            if signal_type == "BUY":
                tp_distance = take_profit - entry_price
                sl_distance = entry_price - stop_loss
            else:  # SELL
                tp_distance = entry_price - take_profit
                sl_distance = stop_loss - entry_price

            # 2. Ensure SL distance < TP distance (SL should not be larger than TP)
            if sl_distance >= tp_distance:
                print(f"        ⚠️ SL distance ({sl_distance:.8f}) >= TP distance ({tp_distance:.8f}), adjusting...")

                # Adjust SL to be smaller than TP
                max_sl_distance = tp_distance * 0.8  # SL should be max 80% of TP distance

                if signal_type == "BUY":
                    stop_loss = entry_price - max_sl_distance
                else:
                    stop_loss = entry_price + max_sl_distance

                sl_distance = max_sl_distance
                print(f"        ✅ Adjusted SL distance to {sl_distance:.8f}")

            # 3. Ensure minimum distances (TP and SL not too small)
            min_distance_pct = 0.015 if self.crypto_mode else 0.01  # 1.5% for crypto, 1% for traditional
            min_distance = entry_price * min_distance_pct

            # Adjust TP if too small
            if tp_distance < min_distance:
                print(f"        ⚠️ TP distance too small ({tp_distance:.8f}), adjusting to minimum...")
                if signal_type == "BUY":
                    take_profit = entry_price + min_distance
                else:
                    take_profit = entry_price - min_distance
                tp_distance = min_distance

            # Adjust SL if too small
            if sl_distance < min_distance * 0.5:  # SL can be smaller than TP minimum
                print(f"        ⚠️ SL distance too small ({sl_distance:.8f}), adjusting to minimum...")
                min_sl_distance = min_distance * 0.5
                if signal_type == "BUY":
                    stop_loss = entry_price - min_sl_distance
                else:
                    stop_loss = entry_price + min_sl_distance
                sl_distance = min_sl_distance

            # 4. Ensure maximum distances (not too aggressive)
            max_distance_pct = 0.15 if self.crypto_mode else 0.08  # 15% for crypto, 8% for traditional
            max_distance = entry_price * max_distance_pct

            # Adjust TP if too large
            if tp_distance > max_distance:
                print(f"        ⚠️ TP distance too large ({tp_distance:.8f}), capping...")
                if signal_type == "BUY":
                    take_profit = entry_price + max_distance
                else:
                    take_profit = entry_price - max_distance
                tp_distance = max_distance

            # 5. Recalculate final metrics
            final_risk = abs(entry_price - stop_loss)
            final_reward = abs(take_profit - entry_price)
            final_rr = final_reward / final_risk if final_risk > 0 else 0

            # 6. Ensure minimum R:R ratio
            if final_rr < self.min_rr_ratio or final_risk == 0:
                print(f"        ⚠️ R:R ratio too low ({final_rr:.2f}) or zero risk, adjusting TP...")

                # If risk is zero, set minimum risk
                if final_risk == 0:
                    min_risk = entry_price * 0.02  # 2% minimum risk
                    if signal_type == "BUY":
                        stop_loss = entry_price - min_risk
                    else:
                        stop_loss = entry_price + min_risk
                    final_risk = min_risk

                target_reward = final_risk * self.min_rr_ratio

                if signal_type == "BUY":
                    take_profit = entry_price + target_reward
                else:
                    take_profit = entry_price - target_reward

                final_reward = target_reward
                final_rr = self.min_rr_ratio

            # 7. Cap maximum R:R ratio
            if final_rr > self.max_rr_ratio:
                print(f"        ⚠️ R:R ratio too high ({final_rr:.2f}), adjusting TP...")
                target_reward = final_risk * self.max_rr_ratio

                if signal_type == "BUY":
                    take_profit = entry_price + target_reward
                else:
                    take_profit = entry_price - target_reward

                final_reward = target_reward
                final_rr = self.max_rr_ratio

            # 8. Final validation - ensure no NaN values
            if not (take_profit > 0 and stop_loss > 0 and final_risk > 0 and final_reward > 0):
                print(f"        🚨 Invalid values detected, applying emergency fix...")
                # Emergency fallback calculation
                if signal_type == "BUY":
                    take_profit = entry_price * 1.05  # 5% TP
                    stop_loss = entry_price * 0.96   # 4% SL
                else:
                    take_profit = entry_price * 0.95  # 5% TP
                    stop_loss = entry_price * 1.04   # 4% SL

                final_risk = abs(entry_price - stop_loss)
                final_reward = abs(take_profit - entry_price)
                final_rr = final_reward / final_risk if final_risk > 0 else 1.25

            validated_result = tp_sl_result.copy()
            validated_result.update({
                "take_profit": float(take_profit),
                "stop_loss": float(stop_loss),
                "risk_reward_ratio": float(final_rr),
                "validation_applied": True,
                "risk_distance": float(final_risk),
                "reward_distance": float(final_reward),
                "risk_percentage": float((final_risk / entry_price) * 100),
                "reward_percentage": float((final_reward / entry_price) * 100)
            })

            print(f"        ✅ Validation complete: R:R={final_rr:.2f}, Risk={final_risk/entry_price*100:.2f}%, Reward={final_reward/entry_price*100:.2f}%")

            return validated_result

        except Exception as e:
            print(f"        ❌ Error in TP/SL validation: {e}")
            return tp_sl_result

    def _calculate_fallback_tp_sl(self, signal_type: str, entry_price: float, ohlcv_data: pd.DataFrame,
                                market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate fallback TP/SL when other methods fail."""
        try:
            # Use ATR-based calculation as fallback
            tr = self._calculate_true_range(ohlcv_data)
            atr = tr.rolling(self.atr_period).mean().iloc[-1]

            # Conservative multipliers for fallback
            if self.crypto_mode:
                tp_multiplier = 3.0
                sl_multiplier = 2.0
            else:
                tp_multiplier = 2.0
                sl_multiplier = 1.5

            # Adjust for market regime
            regime = market_regime.get("regime", "unknown")
            if regime == "volatile":
                sl_multiplier *= 1.5
            elif regime == "trending":
                tp_multiplier *= 1.2

            # Calculate levels
            if signal_type == "BUY":
                take_profit = entry_price + (atr * tp_multiplier)
                stop_loss = entry_price - (atr * sl_multiplier)
            else:
                take_profit = entry_price - (atr * tp_multiplier)
                stop_loss = entry_price + (atr * sl_multiplier)

            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0

            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": 0.4,  # Low confidence for fallback
                "source": "atr_fallback",
                "algorithms_used": ["atr_fallback"],
                "total_methods": 1
            }

        except Exception as e:
            print(f"        ❌ Error in fallback calculation: {e}")
            # Ultimate fallback with fixed percentages
            if signal_type == "BUY":
                take_profit = entry_price * 1.03
                stop_loss = entry_price * 0.98
            else:
                take_profit = entry_price * 0.97
                stop_loss = entry_price * 1.02

            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": 1.5,
                "confidence": 0.2,
                "source": "fixed_percentage_fallback",
                "algorithms_used": ["fixed_fallback"],
                "total_methods": 1
            }

    def _calculate_atr_dynamic_tp_sl(self, signal_type: str, entry_price: float,
                                   ohlcv_data: pd.DataFrame, market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate ATR-based dynamic TP/SL."""
        try:
            # Calculate True Range
            tr = self._calculate_true_range(ohlcv_data)
            
            # Multiple ATR periods for different timeframes
            atr_short = tr.rolling(self.atr_period // 2).mean().iloc[-1]
            atr_medium = tr.rolling(self.atr_period).mean().iloc[-1]
            atr_long = tr.rolling(self.atr_period * 2).mean().iloc[-1] if len(tr) >= self.atr_period * 2 else atr_medium
            
            # ✅ CRYPTO UPGRADE: Enhanced regime-based ATR selection and multipliers
            regime = market_regime["regime"]
            confidence = market_regime["confidence"]

            # Base multipliers for crypto volatility
            if self.crypto_mode:
                if regime == "trending":
                    atr_value = atr_medium
                    tp_multiplier = 4.0 + confidence  # Increased from 3.0
                    sl_multiplier = 2.5  # Increased from 1.5 (67% wider)
                elif regime == "volatile":
                    atr_value = atr_short
                    tp_multiplier = 3.0  # Increased from 2.0
                    sl_multiplier = 2.0  # Increased from 1.0 (100% wider)
                elif regime == "ranging":
                    atr_value = atr_long
                    tp_multiplier = 2.5  # Increased from 1.5
                    sl_multiplier = 1.8  # Increased from 0.8 (125% wider)
                else:  # consolidating
                    atr_value = atr_medium
                    tp_multiplier = 3.5  # Increased from 2.5
                    sl_multiplier = 2.2  # Increased from 1.2 (83% wider)
            else:
                # Original multipliers for traditional markets
                if regime == "trending":
                    atr_value = atr_medium
                    tp_multiplier = 3.0 + confidence
                    sl_multiplier = 1.5
                elif regime == "volatile":
                    atr_value = atr_short
                    tp_multiplier = 2.0
                    sl_multiplier = 1.0
                elif regime == "ranging":
                    atr_value = atr_long
                    tp_multiplier = 1.5
                    sl_multiplier = 0.8
                else:  # consolidating
                    atr_value = atr_medium
                    tp_multiplier = 2.5
                    sl_multiplier = 1.2
            
            # ✅ CRYPTO UPGRADE: Enhanced volatility adjustment
            volatility = market_regime["indicators"].get("volatility", 0.02)

            if self.crypto_mode:
                # More conservative volatility adjustment for crypto
                # Crypto baseline volatility is higher (~5% vs 2% for stocks)
                crypto_baseline = 0.05
                volatility_adjustment = min(3.0, max(0.8, 1 + (volatility - crypto_baseline) * 8))

                # Additional crypto-specific adjustments
                if volatility > 0.10:  # Very high volatility (>10%)
                    sl_multiplier *= 1.5  # Extra wide SL
                elif volatility > 0.07:  # High volatility (>7%)
                    sl_multiplier *= 1.3  # Wider SL

            else:
                # Original volatility adjustment for traditional markets
                volatility_adjustment = min(2.0, max(0.5, 1 + (volatility - 0.02) * 10))

            tp_multiplier *= volatility_adjustment
            sl_multiplier *= volatility_adjustment
            
            # Calculate levels
            if signal_type == "BUY":
                take_profit = entry_price + (atr_value * tp_multiplier)
                stop_loss = entry_price - (atr_value * sl_multiplier)
            else:
                take_profit = entry_price - (atr_value * tp_multiplier)
                stop_loss = entry_price + (atr_value * sl_multiplier)
            
            # Calculate metrics
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence * 0.8,  # High confidence for ATR method
                "atr_value": atr_value,
                "multipliers": {"tp": tp_multiplier, "sl": sl_multiplier}
            }
            
        except Exception as e:
            print(f"      Error in ATR dynamic calculation: {e}")
            # ✅ FIX: Return fallback TP/SL instead of None
            atr_fallback = (ohlcv_data['high'] - ohlcv_data['low']).mean() * 2.0
            if signal_type == "BUY":
                return {
                    "take_profit": entry_price + atr_fallback,
                    "stop_loss": entry_price - atr_fallback * 0.5,
                    "confidence": 0.25,
                    "method": "atr_fallback"
                }
            else:
                return {
                    "take_profit": entry_price - atr_fallback,
                    "stop_loss": entry_price + atr_fallback * 0.5,
                    "confidence": 0.25,
                    "method": "atr_fallback"
                }

    def _calculate_fibonacci_confluence_tp_sl(self, signal_type: str, entry_price: float,
                                            ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Fibonacci confluence-based TP/SL."""
        try:
            # Get Fibonacci levels from analysis data
            fib_data = analysis_data.get("fibonacci_levels", {})
            
            if fib_data.get("status") == "success":
                retracement_levels = fib_data.get("retracement_levels", [])
                extension_levels = fib_data.get("extension_levels", [])
            else:
                # Calculate our own Fibonacci levels
                retracement_levels, extension_levels = self._calculate_simple_fibonacci_levels(ohlcv_data)
            
            # Find confluence zones (multiple Fibonacci levels close together)
            confluence_zones = self._find_fibonacci_confluence_zones(
                retracement_levels + extension_levels, entry_price
            )
            
            if signal_type == "BUY":
                # Find resistance confluence above entry for TP
                tp_candidates = [zone for zone in confluence_zones if zone["price"] > entry_price]
                if tp_candidates:
                    tp_zone = min(tp_candidates, key=lambda x: x["price"])  # Nearest resistance
                    take_profit = tp_zone["price"]
                else:
                    # ✅ CRYPTO UPGRADE: Enhanced fallback for crypto volatility
                    extensions_above = [level for level in extension_levels if level.get("price", 0) > entry_price]
                    if extensions_above:
                        take_profit = extensions_above[0]["price"]
                    else:
                        fallback_tp = 1.06 if self.crypto_mode else 1.03  # 6% vs 3%
                        take_profit = entry_price * fallback_tp

                # Find support confluence below entry for SL
                sl_candidates = [zone for zone in confluence_zones if zone["price"] < entry_price]
                if sl_candidates:
                    sl_zone = max(sl_candidates, key=lambda x: x["price"])  # Nearest support
                    stop_loss = sl_zone["price"]
                else:
                    # ✅ CRYPTO UPGRADE: Enhanced fallback SL for crypto volatility
                    retracements_below = [level for level in retracement_levels if level.get("price", 0) < entry_price]
                    if retracements_below:
                        stop_loss = retracements_below[-1]["price"]
                    else:
                        fallback_sl = 0.95 if self.crypto_mode else 0.97  # 5% vs 3%
                        stop_loss = entry_price * fallback_sl
            else:
                # SELL signal - reverse logic
                tp_candidates = [zone for zone in confluence_zones if zone["price"] < entry_price]
                if tp_candidates:
                    tp_zone = max(tp_candidates, key=lambda x: x["price"])
                    take_profit = tp_zone["price"]
                else:
                    extensions_below = [level for level in extension_levels if level.get("price", 0) < entry_price]
                    take_profit = extensions_below[0]["price"] if extensions_below else entry_price * 0.97
                
                sl_candidates = [zone for zone in confluence_zones if zone["price"] > entry_price]
                if sl_candidates:
                    sl_zone = min(sl_candidates, key=lambda x: x["price"])
                    stop_loss = sl_zone["price"]
                else:
                    retracements_above = [level for level in retracement_levels if level.get("price", 0) > entry_price]
                    stop_loss = retracements_above[0]["price"] if retracements_above else entry_price * 1.03
            
            # Calculate confidence based on confluence strength
            confluence_strength = max([zone.get("strength", 0) for zone in confluence_zones] or [0.5])
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confluence_strength,
                "confluence_zones": len(confluence_zones),
                "fibonacci_levels_used": len(retracement_levels) + len(extension_levels)
            }
            
        except Exception as e:
            print(f"      Error in Fibonacci confluence calculation: {e}")
            # ✅ FIX: Return fallback TP/SL instead of None
            price_range = entry_price * 0.05  # 5% range
            if signal_type == "BUY":
                return {
                    "take_profit": entry_price + price_range,
                    "stop_loss": entry_price - price_range * 0.4,
                    "confidence": 0.25,
                    "method": "fibonacci_fallback"
                }
            else:
                return {
                    "take_profit": entry_price - price_range,
                    "stop_loss": entry_price + price_range * 0.4,
                    "confidence": 0.25,
                    "method": "fibonacci_fallback"
                }

    def _calculate_volume_profile_tp_sl(self, signal_type: str, entry_price: float,
                                      ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Volume Profile-based TP/SL."""
        try:
            vp_data = analysis_data.get("volume_profile_analysis", {})
            
            if vp_data.get("status") != "success":
                # ✅ FIX: Return fallback TP/SL instead of None
                price_range = entry_price * 0.04  # 4% range
                if signal_type == "BUY":
                    return {
                        "take_profit": entry_price + price_range,
                        "stop_loss": entry_price - price_range * 0.5,
                        "confidence": 0.25,
                        "method": "volume_profile_fallback"
                    }
                else:
                    return {
                        "take_profit": entry_price - price_range,
                        "stop_loss": entry_price + price_range * 0.5,
                        "confidence": 0.25,
                        "method": "volume_profile_fallback"
                    }
            
            # Get VPOC and Value Area
            vpoc_price = vp_data.get("vpoc", {}).get("price", 0)
            value_area = vp_data.get("value_area", {})
            va_high = value_area.get("high", 0)
            va_low = value_area.get("low", 0)
            
            # Get support and resistance levels
            support_levels = vp_data.get("support_resistance", {}).get("support_levels", [])
            resistance_levels = vp_data.get("support_resistance", {}).get("resistance_levels", [])
            
            if signal_type == "BUY":
                # Find next significant resistance for TP
                resistances_above = [level["price"] for level in resistance_levels 
                                   if level["price"] > entry_price]
                if resistances_above:
                    take_profit = min(resistances_above)
                elif va_high > entry_price:
                    take_profit = va_high
                else:
                    # ✅ CRYPTO UPGRADE: Wider fallback for crypto volatility
                    fallback_tp = 1.05 if self.crypto_mode else 1.025  # 5% vs 2.5%
                    take_profit = entry_price * fallback_tp

                # Find nearest support for SL
                supports_below = [level["price"] for level in support_levels
                                if level["price"] < entry_price]
                if supports_below:
                    stop_loss = max(supports_below)
                elif va_low < entry_price:
                    stop_loss = va_low
                else:
                    # ✅ CRYPTO UPGRADE: Wider fallback SL for crypto volatility
                    fallback_sl = 0.96 if self.crypto_mode else 0.985  # 4% vs 1.5%
                    stop_loss = entry_price * fallback_sl
            else:
                # SELL signal
                supports_below = [level["price"] for level in support_levels 
                                if level["price"] < entry_price]
                if supports_below:
                    take_profit = max(supports_below)
                elif va_low < entry_price:
                    take_profit = va_low
                else:
                    take_profit = entry_price * 0.975
                
                resistances_above = [level["price"] for level in resistance_levels 
                                   if level["price"] > entry_price]
                if resistances_above:
                    stop_loss = min(resistances_above)
                elif va_high > entry_price:
                    stop_loss = va_high
                else:
                    stop_loss = entry_price * 1.015
            
            # Calculate confidence based on volume profile quality
            profile_quality = vp_data.get("distribution_metrics", {}).get("concentration_ratio", 0.5)
            level_count = len(support_levels) + len(resistance_levels)
            confidence = min(1.0, profile_quality + (level_count / 20))
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "vpoc_price": vpoc_price,
                "value_area": {"high": va_high, "low": va_low},
                "levels_used": level_count
            }
            
        except Exception as e:
            print(f"      Error in Volume Profile calculation: {e}")
            return None

    def _calculate_point_figure_tp_sl(self, signal_type: str, entry_price: float,
                                    ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Point & Figure-based TP/SL."""
        try:
            pf_data = analysis_data.get("point_figure_analysis", {})
            
            if pf_data.get("status") != "success":
                return None
            
            # Get support and resistance from P&F
            sr_data = pf_data.get("support_resistance", {})
            support_levels = sr_data.get("support_levels", [])
            resistance_levels = sr_data.get("resistance_levels", [])
            
            # Get price objectives
            price_objectives = pf_data.get("price_objectives", {})
            bullish_targets = price_objectives.get("bullish_targets", [])
            bearish_targets = price_objectives.get("bearish_targets", [])
            
            if signal_type == "BUY":
                # Use bullish targets for TP
                if bullish_targets:
                    targets_above = [target["price"] for target in bullish_targets 
                                   if target["price"] > entry_price]
                    take_profit = min(targets_above) if targets_above else bullish_targets[0]["price"]
                else:
                    # ✅ CRYPTO UPGRADE: Enhanced fallback for crypto volatility
                    resistances = [level["price"] for level in resistance_levels
                                 if level["price"] > entry_price]
                    if resistances:
                        take_profit = min(resistances)
                    else:
                        fallback_tp = 1.06 if self.crypto_mode else 1.03  # 6% vs 3%
                        take_profit = entry_price * fallback_tp

                # Use support levels for SL
                supports = [level["price"] for level in support_levels
                          if level["price"] < entry_price]
                if supports:
                    stop_loss = max(supports)
                else:
                    fallback_sl = 0.95 if self.crypto_mode else 0.97  # 5% vs 3%
                    stop_loss = entry_price * fallback_sl
            else:
                # SELL signal
                if bearish_targets:
                    targets_below = [target["price"] for target in bearish_targets 
                                   if target["price"] < entry_price]
                    take_profit = max(targets_below) if targets_below else bearish_targets[0]["price"]
                else:
                    supports = [level["price"] for level in support_levels 
                              if level["price"] < entry_price]
                    take_profit = max(supports) if supports else entry_price * 0.97
                
                resistances = [level["price"] for level in resistance_levels 
                             if level["price"] > entry_price]
                stop_loss = min(resistances) if resistances else entry_price * 1.03
            
            # Calculate confidence based on P&F signal strength
            pf_signal = pf_data.get("signals", {})
            confidence = pf_signal.get("confidence", 0.5)
            
            # Adjust confidence based on pattern strength
            trend_analysis = pf_data.get("trend_analysis", {})
            trend_strength = trend_analysis.get("strength", 0.5)
            confidence = (confidence + trend_strength) / 2
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "targets_available": len(bullish_targets) + len(bearish_targets),
                "sr_levels_used": len(support_levels) + len(resistance_levels)
            }
            
        except Exception as e:
            print(f"      Error in Point & Figure calculation: {e}")
            return None

    def _calculate_sr_confluence_tp_sl(self, signal_type: str, entry_price: float,
                                     ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Support/Resistance confluence-based TP/SL."""
        try:
            # Collect S/R levels from multiple sources
            sr_levels = []
            
            # From ZigZag pivots
            zigzag_pivots = analysis_data.get("zigzag_pivots", [])
            for pivot in zigzag_pivots:
                if isinstance(pivot, dict) and "price" in pivot:
                    sr_levels.append({
                        "price": pivot["price"],
                        "type": pivot.get("type", "unknown"),
                        "strength": 0.7,
                        "source": "zigzag"
                    })
            
            # From moving averages
            ma_levels = self._calculate_ma_levels(ohlcv_data)
            sr_levels.extend(ma_levels)
            
            # From pivot points
            pivot_levels = self._calculate_pivot_levels(ohlcv_data)
            sr_levels.extend(pivot_levels)
            
            # Find confluence zones
            confluence_zones = self._find_sr_confluence_zones(sr_levels, entry_price)
            
            if signal_type == "BUY":
                # Find resistance confluence for TP
                resistance_zones = [zone for zone in confluence_zones 
                                  if zone["price"] > entry_price and zone["zone_type"] == "resistance"]
                if resistance_zones:
                    tp_zone = min(resistance_zones, key=lambda x: x["price"])
                    take_profit = tp_zone["price"]
                else:
                    # Fallback to nearest resistance level
                    resistances = [level["price"] for level in sr_levels 
                                 if level["price"] > entry_price]
                    take_profit = min(resistances) if resistances else entry_price * 1.025
                
                # Find support confluence for SL
                support_zones = [zone for zone in confluence_zones 
                               if zone["price"] < entry_price and zone["zone_type"] == "support"]
                if support_zones:
                    sl_zone = max(support_zones, key=lambda x: x["price"])
                    stop_loss = sl_zone["price"]
                else:
                    supports = [level["price"] for level in sr_levels 
                              if level["price"] < entry_price]
                    stop_loss = max(supports) if supports else entry_price * 0.985
            else:
                # SELL signal - reverse logic
                support_zones = [zone for zone in confluence_zones 
                               if zone["price"] < entry_price and zone["zone_type"] == "support"]
                if support_zones:
                    tp_zone = max(support_zones, key=lambda x: x["price"])
                    take_profit = tp_zone["price"]
                else:
                    supports = [level["price"] for level in sr_levels 
                              if level["price"] < entry_price]
                    take_profit = max(supports) if supports else entry_price * 0.975
                
                resistance_zones = [zone for zone in confluence_zones 
                                  if zone["price"] > entry_price and zone["zone_type"] == "resistance"]
                if resistance_zones:
                    sl_zone = min(resistance_zones, key=lambda x: x["price"])
                    stop_loss = sl_zone["price"]
                else:
                    resistances = [level["price"] for level in sr_levels 
                                 if level["price"] > entry_price]
                    stop_loss = min(resistances) if resistances else entry_price * 1.015
            
            # Calculate confidence based on confluence strength
            max_confluence = max([zone.get("strength", 0) for zone in confluence_zones] or [0.5])
            total_levels = len(sr_levels)
            confidence = min(1.0, max_confluence + (total_levels / 50))
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "confluence_zones": len(confluence_zones),
                "total_sr_levels": total_levels
            }
            
        except Exception as e:
            print(f"      Error in S/R confluence calculation: {e}")
            return None

    def _calculate_volatility_bands_tp_sl(self, signal_type: str, entry_price: float,
                                        ohlcv_data: pd.DataFrame, market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate volatility bands-based TP/SL."""
        try:
            closes = ohlcv_data['close'].values
            
            # Calculate Bollinger Bands
            sma_20 = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes)
            std_20 = np.std(closes[-20:]) if len(closes) >= 20 else np.std(closes)
            
            upper_band = sma_20 + (2 * std_20)
            lower_band = sma_20 - (2 * std_20)
            
            # Calculate Keltner Channels
            tr = self._calculate_true_range(ohlcv_data)
            atr_20 = tr.rolling(20).mean().iloc[-1] if len(tr) >= 20 else tr.mean()
            
            keltner_upper = sma_20 + (2 * atr_20)
            keltner_lower = sma_20 - (2 * atr_20)
            
            # Regime-based band selection
            regime = market_regime["regime"]
            volatility = market_regime["indicators"].get("volatility", 0.02)
            
            if regime == "volatile":
                # Use tighter bands for volatile markets
                tp_band = (upper_band + keltner_upper) / 2
                sl_band = (lower_band + keltner_lower) / 2
                band_multiplier = 0.8
            elif regime == "trending":
                # Use wider bands for trending markets
                tp_band = max(upper_band, keltner_upper)
                sl_band = min(lower_band, keltner_lower)
                band_multiplier = 1.2
            else:
                # Standard bands for ranging/consolidating
                tp_band = upper_band
                sl_band = lower_band
                band_multiplier = 1.0
            
            # Adjust for volatility
            volatility_adjustment = min(2.0, max(0.5, 1 + (volatility - 0.02) * 5))
            band_multiplier *= volatility_adjustment
            
            if signal_type == "BUY":
                take_profit = entry_price + ((tp_band - entry_price) * band_multiplier)
                stop_loss = entry_price - ((entry_price - sl_band) * band_multiplier)
            else:
                take_profit = entry_price - ((entry_price - sl_band) * band_multiplier)
                stop_loss = entry_price + ((tp_band - entry_price) * band_multiplier)
            
            # Calculate confidence based on band squeeze
            band_width = (upper_band - lower_band) / sma_20
            squeeze_factor = 1 / (1 + band_width * 10)  # Lower width = higher squeeze = higher confidence
            confidence = min(1.0, 0.5 + squeeze_factor)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "band_width": band_width,
                "squeeze_factor": squeeze_factor,
                "volatility_adjustment": volatility_adjustment
            }
            
        except Exception as e:
            print(f"      Error in volatility bands calculation: {e}")
            return None

    def _calculate_momentum_based_tp_sl(self, signal_type: str, entry_price: float,
                                      ohlcv_data: pd.DataFrame, market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate momentum-based TP/SL."""
        try:
            closes = ohlcv_data['close'].values
            highs = ohlcv_data['high'].values
            lows = ohlcv_data['low'].values
            
            # Calculate RSI
            closes_series = pd.Series(closes)
            rsi = self._calculate_rsi(closes_series)

            # Calculate MACD
            macd_line, signal_line, histogram = self._calculate_macd(closes_series)
            
            # Calculate momentum
            momentum = closes[-1] / closes[-10] - 1 if len(closes) >= 10 else 0
            
            # Calculate Stochastic
            stoch_k = self._calculate_stochastic_simple(highs, lows, closes)
            
            # Determine momentum strength
            # ✅ FIX: Extract scalar values from Series to avoid ambiguous truth value
            rsi_value = rsi.iloc[-1] if hasattr(rsi, 'iloc') and len(rsi) > 0 else 50
            macd_value = macd_line.iloc[-1] if hasattr(macd_line, 'iloc') and len(macd_line) > 0 else 0

            momentum_indicators = {
                "rsi": (rsi_value - 50) / 50,  # Normalized -1 to 1
                "macd": np.sign(macd_value) * min(1, abs(macd_value) / 0.1),
                "momentum": momentum * 10,  # Amplify for normalization
                "stochastic": (stoch_k - 50) / 50
            }
            
            # Weighted momentum score
            momentum_score = (
                momentum_indicators["rsi"] * 0.3 +
                momentum_indicators["macd"] * 0.3 +
                momentum_indicators["momentum"] * 0.25 +
                momentum_indicators["stochastic"] * 0.15
            )
            
            # Adjust for market regime
            regime = market_regime["regime"]
            if regime == "trending":
                momentum_multiplier = 1.5
            elif regime == "volatile":
                momentum_multiplier = 0.8
            else:
                momentum_multiplier = 1.0
            
            # Calculate TP/SL based on momentum
            base_percentage = 0.02  # 2% base
            momentum_adjustment = abs(momentum_score) * momentum_multiplier
            
            tp_percentage = base_percentage * (2 + momentum_adjustment)
            sl_percentage = base_percentage * (1 + momentum_adjustment * 0.5)
            
            if signal_type == "BUY":
                if momentum_score > 0:  # Strong bullish momentum
                    take_profit = entry_price * (1 + tp_percentage)
                    stop_loss = entry_price * (1 - sl_percentage * 0.7)  # Tighter SL
                else:  # Weak or bearish momentum
                    take_profit = entry_price * (1 + tp_percentage * 0.7)
                    stop_loss = entry_price * (1 - sl_percentage)
            else:
                if momentum_score < 0:  # Strong bearish momentum
                    take_profit = entry_price * (1 - tp_percentage)
                    stop_loss = entry_price * (1 + sl_percentage * 0.7)
                else:  # Weak or bullish momentum
                    take_profit = entry_price * (1 - tp_percentage * 0.7)
                    stop_loss = entry_price * (1 + sl_percentage)
            
            # Calculate confidence based on momentum alignment
            momentum_alignment = sum(1 for indicator in momentum_indicators.values() 
                                   if (indicator > 0.2 and signal_type == "BUY") or 
                                      (indicator < -0.2 and signal_type == "SELL"))
            confidence = min(1.0, momentum_alignment / len(momentum_indicators) + 0.3)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "momentum_score": momentum_score,
                "momentum_indicators": momentum_indicators,
                "momentum_alignment": momentum_alignment
            }
            
        except Exception as e:
            print(f"      Error in momentum-based calculation: {e}")
            return None

    def _calculate_statistical_risk_tp_sl(self, signal_type: str, entry_price: float,
                                        ohlcv_data: pd.DataFrame, market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistical risk model-based TP/SL."""
        try:
            closes = ohlcv_data['close'].values
            returns = np.diff(closes) / closes[:-1]
            
            # Calculate statistical measures
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            skewness = self._calculate_skewness(returns)
            kurtosis = self._calculate_kurtosis(returns)
            
            # Calculate Value at Risk (VaR)
            confidence_level = 0.95
            var_95 = np.percentile(returns, (1 - confidence_level) * 100)
            
            # Calculate Expected Shortfall (CVaR)
            cvar_95 = np.mean(returns[returns <= var_95]) if np.any(returns <= var_95) else var_95
            
            # Calculate maximum drawdown
            cumulative_returns = np.cumprod(1 + returns)
            rolling_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = np.min(drawdowns)
            
            # Regime-based risk adjustment
            regime = market_regime["regime"]
            volatility = market_regime["indicators"].get("volatility", 0.02)
            
            if regime == "volatile":
                risk_multiplier = 1.5
                conservative_factor = 0.8
            elif regime == "trending":
                risk_multiplier = 1.0
                conservative_factor = 1.2
            else:
                risk_multiplier = 1.2
                conservative_factor = 1.0
            
            # Calculate statistical TP/SL
            if signal_type == "BUY":
                # For bullish signals, use positive tail expectations
                expected_gain = mean_return + (std_return * 1.645)  # 95th percentile
                expected_loss = abs(cvar_95) * risk_multiplier
                
                take_profit = entry_price * (1 + expected_gain * conservative_factor)
                stop_loss = entry_price * (1 - expected_loss * conservative_factor)
            else:
                # For bearish signals, use negative tail expectations
                expected_gain = abs(mean_return - (std_return * 1.645))
                expected_loss = (mean_return + std_return) if mean_return > 0 else abs(mean_return - std_return)
                
                take_profit = entry_price * (1 - expected_gain * conservative_factor)
                stop_loss = entry_price * (1 + expected_loss * conservative_factor)
            
            # Calculate confidence based on statistical reliability
            sample_size = len(returns)
            reliability_factor = min(1.0, sample_size / 100)  # More data = higher reliability
            
            # Adjust for distribution characteristics
            if abs(skewness) < 0.5 and kurtosis < 3:  # Near-normal distribution
                distribution_confidence = 0.9
            elif abs(skewness) < 1.0 and kurtosis < 5:  # Moderately skewed
                distribution_confidence = 0.7
            else:  # Highly skewed or heavy-tailed
                distribution_confidence = 0.5
            
            confidence = reliability_factor * distribution_confidence
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "statistical_metrics": {
                    "var_95": var_95,
                    "cvar_95": cvar_95,
                    "max_drawdown": max_drawdown,
                    "skewness": skewness,
                    "kurtosis": kurtosis,
                    "std_return": std_return
                },
                "reliability_factor": reliability_factor,
                "distribution_confidence": distribution_confidence
            }
            
        except Exception as e:
            print(f"      Error in statistical risk calculation: {e}")
            return None

    def _calculate_ensemble_tp_sl(self, methods_results: Dict[str, Any], 
                                 market_regime: Dict[str, Any], signal_type: str, 
                                 entry_price: float) -> Dict[str, Any]:
        """Calculate ensemble TP/SL by combining all valid methods."""
        try:
            valid_methods = {k: v for k, v in methods_results.items() if v is not None}
            
            if not valid_methods:
                raise ValueError("No valid TP/SL methods available")
            
            # Dynamic weighting based on method confidence and market regime
            weights = self._calculate_dynamic_weights(valid_methods, market_regime)
            
            # Weighted average calculation
            weighted_tp = sum(method["take_profit"] * weights[name] 
                            for name, method in valid_methods.items())
            weighted_sl = sum(method["stop_loss"] * weights[name] 
                            for name, method in valid_methods.items())
            
            # Calculate ensemble confidence
            ensemble_confidence = sum(method["confidence"] * weights[name] 
                                    for name, method in valid_methods.items())
            
            # Calculate risk-reward ratio
            risk = abs(entry_price - weighted_sl)
            reward = abs(weighted_tp - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": weighted_tp,
                "stop_loss": weighted_sl,
                "risk_reward_ratio": risk_reward,
                "confidence": ensemble_confidence,
                "methods_used": list(valid_methods.keys()),
                "ensemble_weights": weights,
                "risk_metrics": {
                    "risk": risk,
                    "reward": reward,
                    "risk_percentage": (risk / entry_price) * 100,
                    "reward_percentage": (reward / entry_price) * 100
                }
            }
            
        except Exception as e:
            print(f"      Error in ensemble calculation: {e}")
            # ✅ FIX: Return fallback TP/SL instead of None
            fallback_range = 0.03  # 3% range
            if signal_type == "BUY":
                return {
                    "take_profit": entry_price * (1 + fallback_range),
                    "stop_loss": entry_price * (1 - fallback_range * 0.5),
                    "confidence": 0.25,
                    "method": "ensemble_fallback",
                    "risk_reward_ratio": 2.0
                }
            else:
                return {
                    "take_profit": entry_price * (1 - fallback_range),
                    "stop_loss": entry_price * (1 + fallback_range * 0.5),
                    "confidence": 0.25,
                    "method": "ensemble_fallback",
                    "risk_reward_ratio": 2.0
                }

    def _validate_and_adjust_tp_sl(self, tp_sl_result: Dict[str, Any], signal_type: str,
                                  entry_price: float, ohlcv_data: pd.DataFrame,
                                  market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and adjust TP/SL levels for risk management."""
        try:
            take_profit = tp_sl_result["take_profit"]
            stop_loss = tp_sl_result["stop_loss"]
            
            # Risk-reward ratio validation
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            # ✅ CRYPTO UPGRADE: Enhanced R:R adjustment for crypto
            if risk_reward < self.min_rr_ratio:
                # For crypto, prefer increasing TP over tightening SL
                if self.crypto_mode:
                    # Increase TP to improve R:R (don't tighten SL in crypto)
                    target_reward = risk * self.min_rr_ratio
                    if signal_type == "BUY":
                        take_profit = entry_price + target_reward
                    else:
                        take_profit = entry_price - target_reward
                else:
                    # Traditional approach: increase TP or tighten SL
                    adjustment_factor = self.min_rr_ratio / risk_reward
                    if signal_type == "BUY":
                        take_profit = entry_price + (reward * adjustment_factor)
                    else:
                        take_profit = entry_price - (reward * adjustment_factor)

                # Recalculate
                risk = abs(entry_price - stop_loss)
                reward = abs(take_profit - entry_price)
                risk_reward = reward / risk if risk > 0 else 0
                
            elif risk_reward > self.max_rr_ratio:
                # Reduce TP to more realistic levels
                max_reward = risk * self.max_rr_ratio
                if signal_type == "BUY":
                    take_profit = entry_price + max_reward
                else:
                    take_profit = entry_price - max_reward
                
                risk_reward = self.max_rr_ratio
            
            # Validate against recent price action
            recent_high = ohlcv_data['high'].tail(20).max()
            recent_low = ohlcv_data['low'].tail(20).min()
            
            # ✅ CRYPTO UPGRADE: Enhanced validation for crypto volatility
            if self.crypto_mode:
                # Crypto-specific validation with wider ranges
                if signal_type == "BUY":
                    # Allow TP further above recent highs for crypto opportunities
                    if take_profit > recent_high * 1.2:  # 20% vs 10%
                        take_profit = recent_high * 1.15  # 15% vs 5%
                    # Ensure SL has minimum distance from recent lows
                    min_sl_distance = recent_low * 0.95  # 5% vs 2%
                    if stop_loss > min_sl_distance:
                        stop_loss = min_sl_distance
                else:
                    # Allow TP further below recent lows for crypto opportunities
                    if take_profit < recent_low * 0.8:  # 20% vs 10%
                        take_profit = recent_low * 0.85  # 15% vs 5%
                    # Ensure SL has minimum distance from recent highs
                    max_sl_distance = recent_high * 1.05  # 5% vs 2%
                    if stop_loss < max_sl_distance:
                        stop_loss = max_sl_distance
            else:
                # Original validation for traditional markets
                if signal_type == "BUY":
                    # Ensure TP is not too far above recent highs
                    if take_profit > recent_high * 1.1:
                        take_profit = recent_high * 1.05
                    # Ensure SL is not too close to recent lows
                    if stop_loss > recent_low * 1.02:
                        stop_loss = recent_low * 0.98
                else:
                    # Ensure TP is not too far below recent lows
                    if take_profit < recent_low * 0.9:
                        take_profit = recent_low * 0.95
                    # Ensure SL is not too close to recent highs
                    if stop_loss < recent_high * 0.98:
                        stop_loss = recent_high * 1.02
            
            # Final validation
            final_risk = abs(entry_price - stop_loss)
            final_reward = abs(take_profit - entry_price)
            final_rr = final_reward / final_risk if final_risk > 0 else 0
            
            # Update result
            validated_result = tp_sl_result.copy()
            validated_result.update({
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": final_rr,
                "validation_adjustments": {
                    "rr_adjusted": risk_reward != tp_sl_result["risk_reward_ratio"],
                    "price_action_adjusted": True,
                    "original_rr": tp_sl_result["risk_reward_ratio"],
                    "final_rr": final_rr
                }
            })
            
            return validated_result
            
        except Exception as e:
            print(f"      Error in TP/SL validation: {e}")
            return tp_sl_result

    def _calculate_true_range(self, ohlcv_data: pd.DataFrame) -> pd.Series:
        """Calculate True Range."""
        high_low = ohlcv_data['high'] - ohlcv_data['low']
        high_close = np.abs(ohlcv_data['high'] - ohlcv_data['close'].shift(1))
        low_close = np.abs(ohlcv_data['low'] - ohlcv_data['close'].shift(1))
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return pd.Series(true_range, index=ohlcv_data.index)

    def _calculate_adx(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray, period: int = 14) -> float:
        """Calculate Average Directional Index with proper implementation."""
        try:
            # Calculate True Range
            tr1 = highs - lows
            tr2 = np.abs(highs - np.roll(closes, 1))
            tr3 = np.abs(lows - np.roll(closes, 1))
            tr = np.maximum(tr1, np.maximum(tr2, tr3))
            
            # Calculate Directional Movement
            dm_plus = np.where((highs - np.roll(highs, 1)) > (np.roll(lows, 1) - lows), 
                            np.maximum(highs - np.roll(highs, 1), 0), 0)
            dm_minus = np.where((np.roll(lows, 1) - lows) > (highs - np.roll(highs, 1)), 
                            np.maximum(np.roll(lows, 1) - lows, 0), 0)
            
            # Smooth the values
            tr_smooth = pd.Series(tr).rolling(window=period).mean().iloc[-1]
            dm_plus_smooth = pd.Series(dm_plus).rolling(window=period).mean().iloc[-1]
            dm_minus_smooth = pd.Series(dm_minus).rolling(window=period).mean().iloc[-1]
            
            # Calculate DI+ and DI-
            di_plus = 100 * (dm_plus_smooth / tr_smooth) if tr_smooth != 0 else 0
            di_minus = 100 * (dm_minus_smooth / tr_smooth) if tr_smooth != 0 else 0
            
            # Calculate ADX
            dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus) if (di_plus + di_minus) != 0 else 0
            
            # Return current ADX value (simplified - should use smoothed DX)
            return min(100, max(0, dx))
        
        except Exception as e:
            print(f"Error calculating ADX: {e}")
            return 25.0  # Default moderate value

    def _analyze_market_structure(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> float:
        """Analyze market structure for regime detection."""
        try:
            if len(closes) < 20:
                return 0.5
            
            # Calculate higher highs and lower lows
            recent_highs = highs[-10:]
            recent_lows = lows[-10:]
            
            # Count higher highs
            higher_highs = sum(1 for i in range(1, len(recent_highs)) 
                            if recent_highs[i] > recent_highs[i-1])
            
            # Count lower lows
            lower_lows = sum(1 for i in range(1, len(recent_lows)) 
                            if recent_lows[i] < recent_lows[i-1])
            
            # Structure score (0 = bearish structure, 1 = bullish structure)
            total_comparisons = len(recent_highs) - 1
            if total_comparisons == 0:
                return 0.5
            
            bullish_score = higher_highs / total_comparisons
            bearish_score = lower_lows / total_comparisons
            
            # Combined structure score
            structure_score = (bullish_score - bearish_score + 1) / 2  # Normalize to 0-1
            
            return max(0, min(1, structure_score))
            
        except Exception as e:
            print(f"Error analyzing market structure: {e}")
            return 0.5

    def _calculate_dynamic_weights(self, valid_methods: Dict[str, Any], market_regime: Dict[str, Any]) -> Dict[str, float]:
        """Calculate dynamic weights for ensemble method."""
        # ...existing code...
        num_methods = len(valid_methods)
        return {name: 1.0 / num_methods for name in valid_methods.keys()}

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD indicator."""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        macd_signal = macd_line.ewm(span=signal).mean()
        macd_histogram = macd_line - macd_signal
        return macd_line, macd_signal, macd_histogram

    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """Calculate skewness of the distribution."""
        try:
            if len(returns) < 3:
                return 0.0
            
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 0.0
            
            # Calculate skewness
            skewness = np.mean(((returns - mean_return) / std_return) ** 3)
            return skewness
            
        except Exception as e:
            return 0.0

    def _calculate_stochastic_simple(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray, period: int = 14) -> float:
        """Calculate simple Stochastic %K."""
        try:
            if len(closes) < period:
                return 50.0  # Neutral value

            # Get recent period data
            recent_highs = highs[-period:]
            recent_lows = lows[-period:]
            current_close = closes[-1]

            # Calculate %K
            highest_high = np.max(recent_highs)
            lowest_low = np.min(recent_lows)

            if highest_high == lowest_low:
                return 50.0  # Avoid division by zero

            stoch_k = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100
            return max(0, min(100, stoch_k))

        except Exception as e:
            return 50.0  # Default neutral value

    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """Calculate kurtosis of the distribution."""
        try:
            if len(returns) < 4:
                return 3.0  # Normal distribution kurtosis
            
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 3.0
            
            # Calculate excess kurtosis
            kurtosis = np.mean(((returns - mean_return) / std_return) ** 4)
            return kurtosis
            
        except Exception as e:
            return 3.0

    def _calculate_ml_adaptive_tp_sl(self, signal_type: str, entry_price: float,
                               ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                               market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """NEW METHOD 13: Machine Learning Adaptive TP/SL using historical performance."""
        try:
            # Load historical performance data
            historical_data = self._load_historical_tp_sl_performance()
            
            # Feature engineering for ML
            features = self._extract_ml_features(ohlcv_data, analysis_data, market_regime)
            
            # Predict optimal TP/SL using trained models
            if hasattr(self, 'tp_ml_model') and hasattr(self, 'sl_ml_model'):
                predicted_tp_ratio = self.tp_ml_model.predict([features])[0]
                predicted_sl_ratio = self.sl_ml_model.predict([features])[0]
                
                if signal_type == "BUY":
                    take_profit = entry_price * (1 + predicted_tp_ratio)
                    stop_loss = entry_price * (1 - predicted_sl_ratio)
                else:
                    take_profit = entry_price * (1 - predicted_tp_ratio)
                    stop_loss = entry_price * (1 + predicted_sl_ratio)
                
                # Calculate confidence based on model certainty
                confidence = self._calculate_ml_confidence(features, historical_data)
                
                return {
                    "take_profit": take_profit,
                    "stop_loss": stop_loss,
                    "risk_reward_ratio": predicted_tp_ratio / predicted_sl_ratio,
                    "confidence": confidence,
                    "ml_features_used": len(features),
                    "model_accuracy": self._get_model_accuracy(),
                    "prediction_certainty": confidence
                }
            else:
                # Train models if not available
                self._train_ml_models(historical_data)
                return None
                
        except Exception as e:
            print(f"      Error in ML adaptive calculation: {e}")
            return None

    def _calculate_reinforcement_learning_tp_sl(self, signal_type: str, entry_price: float,
                                            ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                            market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """NEW METHOD 14: Reinforcement Learning TP/SL using Q-Learning."""
        try:
            # Define state space
            state = self._encode_market_state(ohlcv_data, analysis_data, market_regime)
            
            # Get action from Q-table or neural network
            if hasattr(self, 'rl_agent'):
                action = self.rl_agent.get_action(state)
                tp_adjustment, sl_adjustment = self._decode_action(action)
                
                # Calculate base levels
                base_volatility = ohlcv_data['close'].pct_change().tail(20).std()
                
                if signal_type == "BUY":
                    take_profit = entry_price * (1 + base_volatility * tp_adjustment)
                    stop_loss = entry_price * (1 - base_volatility * sl_adjustment)
                else:
                    take_profit = entry_price * (1 - base_volatility * tp_adjustment)
                    stop_loss = entry_price * (1 + base_volatility * sl_adjustment)
                
                # Calculate confidence based on Q-value
                confidence = self._calculate_rl_confidence(state, action)
                
                return {
                    "take_profit": take_profit,
                    "stop_loss": stop_loss,
                    "risk_reward_ratio": tp_adjustment / sl_adjustment,
                    "confidence": confidence,
                    "rl_state": state,
                    "rl_action": action,
                    "q_value": self.rl_agent.get_q_value(state, action)
                }
            else:
                # Initialize RL agent
                self._initialize_rl_agent()
                return None
                
        except Exception as e:
            print(f"      Error in RL calculation: {e}")
            return None
    
    def _calculate_market_microstructure_tp_sl(self, signal_type: str, entry_price: float,
                                         ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                         market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """NEW METHOD 15: Market Microstructure TP/SL using tick-level analysis."""
        try:
            # Analyze tick data patterns
            tick_analysis = self._analyze_tick_patterns(ohlcv_data)
            
            # Calculate bid-ask spread dynamics
            spread_dynamics = self._analyze_spread_dynamics(analysis_data.get("orderbook_data"))
            
            # Market impact estimation
            market_impact = self._estimate_market_impact(ohlcv_data, analysis_data)
            
            # Liquidity analysis
            liquidity_metrics = self._analyze_liquidity_depth(analysis_data.get("orderbook_data"))
            
            # Calculate microstructure-based levels
            microstructure_adjustment = self._calculate_microstructure_adjustment(
                tick_analysis, spread_dynamics, market_impact, liquidity_metrics
            )
            
            base_range = ohlcv_data['high'].tail(10).max() - ohlcv_data['low'].tail(10).min()
            
            if signal_type == "BUY":
                take_profit = entry_price + (base_range * microstructure_adjustment["tp_multiplier"])
                stop_loss = entry_price - (base_range * microstructure_adjustment["sl_multiplier"])
            else:
                take_profit = entry_price - (base_range * microstructure_adjustment["tp_multiplier"])
                stop_loss = entry_price + (base_range * microstructure_adjustment["sl_multiplier"])
            
            confidence = microstructure_adjustment["confidence"]
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": microstructure_adjustment["tp_multiplier"] / microstructure_adjustment["sl_multiplier"],
                "confidence": confidence,
                "market_impact": market_impact,
                "liquidity_score": liquidity_metrics.get("liquidity_score", 0.5),
                "spread_quality": spread_dynamics.get("spread_quality", 0.5)
            }
            
        except Exception as e:
            print(f"      Error in microstructure calculation: {e}")
            return None
    
    def _calculate_neural_ensemble_tp_sl(self, signal_type: str, entry_price: float,
                                   ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                   market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """NEW METHOD 16: Neural Network Ensemble TP/SL using deep learning."""
        try:
            # Prepare neural network inputs
            nn_features = self._prepare_neural_features(ohlcv_data, analysis_data, market_regime)
            
            # Multiple neural network predictions
            predictions = {}
            
            if hasattr(self, 'lstm_model'):
                predictions['lstm'] = self.lstm_model.predict(nn_features['sequence_data'])
            
            if hasattr(self, 'cnn_model'):
                predictions['cnn'] = self.cnn_model.predict(nn_features['image_data'])
            
            if hasattr(self, 'transformer_model'):
                predictions['transformer'] = self.transformer_model.predict(nn_features['attention_data'])
            
            if hasattr(self, 'gru_model'):
                predictions['gru'] = self.gru_model.predict(nn_features['sequence_data'])
            
            # Ensemble neural predictions
            if predictions:
                ensemble_prediction = self._ensemble_neural_predictions(predictions)
                
                tp_ratio = ensemble_prediction['tp_ratio']
                sl_ratio = ensemble_prediction['sl_ratio']
                confidence = ensemble_prediction['confidence']
                
                if signal_type == "BUY":
                    take_profit = entry_price * (1 + tp_ratio)
                    stop_loss = entry_price * (1 - sl_ratio)
                else:
                    take_profit = entry_price * (1 - tp_ratio)
                    stop_loss = entry_price * (1 + sl_ratio)
                
                return {
                    "take_profit": take_profit,
                    "stop_loss": stop_loss,
                    "risk_reward_ratio": tp_ratio / sl_ratio,
                    "confidence": confidence,
                    "neural_models_used": list(predictions.keys()),
                    "ensemble_uncertainty": ensemble_prediction['uncertainty']
                }
            else:
                return None
                
        except Exception as e:
            print(f"      Error in neural ensemble calculation: {e}")
            return None

    def _calculate_adaptive_performance_tp_sl(self, signal_type: str, entry_price: float,
                                        ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                        market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """NEW METHOD 17: Adaptive Performance Learning TP/SL using historical success rates."""
        try:
            # Load historical performance for similar market conditions
            similar_conditions = self._find_similar_market_conditions(ohlcv_data, market_regime)
            
            # Analyze performance of different TP/SL ratios
            performance_analysis = self._analyze_tp_sl_performance(similar_conditions)
            
            # Find optimal ratios based on success rate and profitability
            optimal_ratios = self._find_optimal_ratios(performance_analysis, signal_type)
            
            # Calculate adaptive confidence based on sample size and consistency
            adaptive_confidence = self._calculate_adaptive_confidence(performance_analysis)
            
            # Apply learned ratios
            learned_tp_ratio = optimal_ratios['tp_ratio']
            learned_sl_ratio = optimal_ratios['sl_ratio']
            
            if signal_type == "BUY":
                take_profit = entry_price * (1 + learned_tp_ratio)
                stop_loss = entry_price * (1 - learned_sl_ratio)
            else:
                take_profit = entry_price * (1 - learned_tp_ratio)
                stop_loss = entry_price * (1 + learned_sl_ratio)
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": learned_tp_ratio / learned_sl_ratio,
                "confidence": adaptive_confidence,
                "historical_samples": len(similar_conditions),
                "success_rate": optimal_ratios['success_rate'],
                "avg_profitability": optimal_ratios['avg_profitability']
            }
            
        except Exception as e:
            print(f"      Error in adaptive performance calculation: {e}")
            return None

    def _calculate_simple_fibonacci_levels(self, ohlcv_data: pd.DataFrame) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Calculate simple Fibonacci retracement and extension levels."""
        try:
            highs = ohlcv_data['high'].values
            lows = ohlcv_data['low'].values

            # Find recent swing high and low
            recent_high = np.max(highs[-20:]) if len(highs) >= 20 else np.max(highs)
            recent_low = np.min(lows[-20:]) if len(lows) >= 20 else np.min(lows)

            swing_range = recent_high - recent_low

            # Fibonacci retracement levels
            fib_ratios = [0.236, 0.382, 0.5, 0.618, 0.786]
            retracement_levels = []

            for ratio in fib_ratios:
                price = recent_high - (swing_range * ratio)
                retracement_levels.append({
                    "price": price,
                    "ratio": ratio,
                    "type": "retracement"
                })

            # Fibonacci extension levels
            ext_ratios = [1.272, 1.414, 1.618, 2.0, 2.618]
            extension_levels = []

            for ratio in ext_ratios:
                price_up = recent_high + (swing_range * (ratio - 1))
                price_down = recent_low - (swing_range * (ratio - 1))

                extension_levels.extend([
                    {"price": price_up, "ratio": ratio, "type": "extension_up"},
                    {"price": price_down, "ratio": ratio, "type": "extension_down"}
                ])

            return retracement_levels, extension_levels

        except Exception as e:
            print(f"Error calculating Fibonacci levels: {e}")
            return [], []

    def _find_fibonacci_confluence_zones(self, levels: List[Dict[str, Any]], entry_price: float) -> List[Dict[str, Any]]:
        """Find confluence zones from Fibonacci levels."""
        try:
            if not levels:
                return []
            
            confluence_zones = []
            confluence_threshold = 0.005  # 0.5% price range for confluence
            
            # Group levels by price proximity
            sorted_levels = sorted(levels, key=lambda x: x.get("price", 0))
            
            i = 0
            while i < len(sorted_levels):
                current_level = sorted_levels[i]
                current_price = current_level.get("price", 0)
                
                if current_price <= 0:
                    i += 1
                    continue
                
                # Find all levels within confluence threshold
                confluence_group = [current_level]
                confluence_strength = current_level.get("ratio", 0.5)
                
                j = i + 1
                while j < len(sorted_levels):
                    next_level = sorted_levels[j]
                    next_price = next_level.get("price", 0)
                    
                    if next_price <= 0:
                        j += 1
                        continue
                    
                    # Check if within confluence range
                    price_diff = abs(next_price - current_price) / current_price
                    if price_diff <= confluence_threshold:
                        confluence_group.append(next_level)
                        confluence_strength += next_level.get("ratio", 0.5)
                        j += 1
                    else:
                        break
                
                # Create confluence zone if multiple levels found
                if len(confluence_group) >= 2:
                    avg_price = sum(level.get("price", 0) for level in confluence_group) / len(confluence_group)
                    
                    # Determine zone type based on proximity to entry price
                    zone_type = "resistance" if avg_price > entry_price else "support"
                    
                    # Calculate confluence strength score
                    strength_score = min(1.0, confluence_strength / len(confluence_group))
                    
                    confluence_zones.append({
                        "price": avg_price,
                        "zone_type": zone_type,
                        "strength": strength_score,
                        "levels_count": len(confluence_group),
                        "levels_detail": confluence_group,
                        "confidence": min(1.0, len(confluence_group) / 5),  # Max confidence at 5+ levels
                        "price_range": {
                            "min": min(level.get("price", 0) for level in confluence_group),
                            "max": max(level.get("price", 0) for level in confluence_group)
                        }
                    })
                
                # Move to next non-processed level
                i = j if j > i + 1 else i + 1
            
            # Sort by strength and filter out weak zones
            confluence_zones.sort(key=lambda x: x["strength"], reverse=True)
            
            # Filter zones with minimum strength
            strong_zones = [zone for zone in confluence_zones if zone["strength"] >= 0.3]
            
            print(f"        📐 Found {len(strong_zones)} Fibonacci confluence zones from {len(levels)} levels")
            
            return strong_zones[:10]  # Return top 10 strongest zones
            
        except Exception as e:
            print(f"Error finding Fibonacci confluence zones: {e}")
            return []

    def _find_sr_confluence_zones(self, sr_levels: List[Dict[str, Any]], entry_price: float) -> List[Dict[str, Any]]:
        """Find confluence zones from support/resistance levels."""
        try:
            if not sr_levels:
                return []
            
            confluence_zones = []
            confluence_threshold = 0.008  # 0.8% price range for S/R confluence (wider than Fib)
            
            # Separate support and resistance levels
            support_levels = [level for level in sr_levels if level.get("price", 0) < entry_price]
            resistance_levels = [level for level in sr_levels if level.get("price", 0) > entry_price]
            
            # Process support levels
            for level_type, levels_group in [("support", support_levels), ("resistance", resistance_levels)]:
                if not levels_group:
                    continue
                
                sorted_levels = sorted(levels_group, key=lambda x: x.get("price", 0))
                
                i = 0
                while i < len(sorted_levels):
                    current_level = sorted_levels[i]
                    current_price = current_level.get("price", 0)
                    
                    if current_price <= 0:
                        i += 1
                        continue
                    
                    # Find confluence group
                    confluence_group = [current_level]
                    total_strength = current_level.get("strength", 0.5)
                    total_touches = current_level.get("touches", 1)
                    
                    j = i + 1
                    while j < len(sorted_levels):
                        next_level = sorted_levels[j]
                        next_price = next_level.get("price", 0)
                        
                        if next_price <= 0:
                            j += 1
                            continue
                        
                        # Check confluence proximity
                        price_diff = abs(next_price - current_price) / current_price
                        if price_diff <= confluence_threshold:
                            confluence_group.append(next_level)
                            total_strength += next_level.get("strength", 0.5)
                            total_touches += next_level.get("touches", 1)
                            j += 1
                        else:
                            break
                    
                    # Create confluence zone
                    if len(confluence_group) >= 2:
                        # Weighted average price based on strength
                        weighted_price = sum(
                            level.get("price", 0) * level.get("strength", 0.5) 
                            for level in confluence_group
                        ) / sum(level.get("strength", 0.5) for level in confluence_group)
                        
                        # Calculate confluence strength
                        avg_strength = total_strength / len(confluence_group)
                        confluence_multiplier = min(2.0, len(confluence_group) / 2)  # Bonus for more levels
                        final_strength = min(1.0, avg_strength * confluence_multiplier)
                        
                        # Calculate confidence based on touches and consistency
                        consistency_score = 1 / (1 + np.std([level.get("price", 0) for level in confluence_group]) / weighted_price)
                        touch_score = min(1.0, total_touches / (len(confluence_group) * 3))  # Normalize by expected touches
                        confidence = (consistency_score * 0.6) + (touch_score * 0.4)
                        
                        confluence_zones.append({
                            "price": weighted_price,
                            "zone_type": level_type,
                            "strength": final_strength,
                            "confidence": confidence,
                            "levels_count": len(confluence_group),
                            "total_touches": total_touches,
                            "consistency_score": consistency_score,
                            "sources": list(set(level.get("source", "unknown") for level in confluence_group)),
                            "levels_detail": confluence_group,
                            "price_range": {
                                "min": min(level.get("price", 0) for level in confluence_group),
                                "max": max(level.get("price", 0) for level in confluence_group),
                                "spread_percentage": (max(level.get("price", 0) for level in confluence_group) - 
                                                    min(level.get("price", 0) for level in confluence_group)) / weighted_price * 100
                            }
                        })
                    
                    # Move to next group
                    i = j if j > i + 1 else i + 1
            
            # Sort by combined strength and confidence
            confluence_zones.sort(key=lambda x: (x["strength"] * x["confidence"]), reverse=True)
            
            # Filter significant zones
            significant_zones = [
                zone for zone in confluence_zones 
                if zone["strength"] >= 0.4 and zone["confidence"] >= 0.3
            ]
            
            print(f"        🎯 Found {len(significant_zones)} S/R confluence zones from {len(sr_levels)} levels")
            
            return significant_zones[:15]  # Return top 15 zones
            
        except Exception as e:
            print(f"Error finding S/R confluence zones: {e}")
            return []

    def _calculate_ma_levels(self, ohlcv_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Calculate support/resistance levels from moving averages."""
        try:
            closes = ohlcv_data['close'].values
            highs = ohlcv_data['high'].values
            lows = ohlcv_data['low'].values
            
            if len(closes) < 50:
                return []
            
            ma_levels = []
            current_price = closes[-1]
            
            # Different MA periods for multi-timeframe analysis
            ma_periods = [9, 21, 50, 100, 200]
            ma_types = {
                "SMA": lambda data, period: np.mean(data[-period:]) if len(data) >= period else None,
                "EMA": lambda data, period: self._calculate_ema(data, period),
                "WMA": lambda data, period: self._calculate_wma(data, period)
            }
            
            for period in ma_periods:
                if len(closes) < period:
                    continue
                
                for ma_name, ma_func in ma_types.items():
                    try:
                        ma_value = ma_func(closes, period)
                        if ma_value is None or ma_value <= 0:
                            continue
                        
                        # Determine if MA acts as support or resistance
                        ma_type = "support" if ma_value < current_price else "resistance"
                        
                        # Calculate MA strength based on recent price interaction
                        strength = self._calculate_ma_strength(closes, highs, lows, ma_value, period)
                        
                        # Calculate number of touches/bounces
                        touches = self._calculate_ma_touches(closes, highs, lows, ma_value, period)
                        
                        # Calculate slope and trend of MA
                        ma_slope = self._calculate_ma_slope(closes, period, ma_func)
                        trend_direction = "bullish" if ma_slope > 0.001 else "bearish" if ma_slope < -0.001 else "sideways"
                        
                        # Calculate distance from current price
                        distance_percentage = abs(ma_value - current_price) / current_price * 100
                        
                        # Relevance score (closer MAs are more relevant)
                        relevance_score = max(0.1, 1 / (1 + distance_percentage))
                        
                        # Dynamic resistance strength based on MA characteristics
                        if ma_type == "support":
                            dynamic_strength = strength * relevance_score * (1 + max(0, ma_slope * 100))
                        else:
                            dynamic_strength = strength * relevance_score * (1 + max(0, -ma_slope * 100))
                        
                        ma_levels.append({
                            "price": ma_value,
                            "type": ma_type,
                            "strength": min(1.0, dynamic_strength),
                            "source": f"{ma_name}_{period}",
                            "period": period,
                            "ma_type": ma_name,
                            "touches": touches,
                            "slope": ma_slope,
                            "trend_direction": trend_direction,
                            "distance_percentage": distance_percentage,
                            "relevance_score": relevance_score,
                            "last_interaction": self._get_last_ma_interaction(closes, highs, lows, ma_value),
                            "reliability": self._calculate_ma_reliability(closes, ma_value, period)
                        })
                        
                    except Exception as ma_error:
                        print(f"Error calculating {ma_name}_{period}: {ma_error}")
                        continue
            
            # Add dynamic support/resistance from recent MA crossovers
            crossover_levels = self._detect_ma_crossover_levels(closes, ma_periods)
            ma_levels.extend(crossover_levels)
            
            # Sort by strength and filter weak levels
            ma_levels.sort(key=lambda x: x["strength"], reverse=True)
            significant_levels = [level for level in ma_levels if level["strength"] >= 0.3]
            
            print(f"        📊 Calculated {len(significant_levels)} MA-based S/R levels from {len(ma_periods)} periods")
            
            return significant_levels[:20]  # Return top 20 levels
            
        except Exception as e:
            print(f"Error calculating MA levels: {e}")
            return []
    
    def _calculate_ema(self, data: np.ndarray, period: int) -> Optional[float]:
        """Calculate Exponential Moving Average."""
        try:
            if len(data) < period:
                return None
            
            alpha = 2 / (period + 1)
            ema = data[0]
            
            for price in data[1:]:
                ema = alpha * price + (1 - alpha) * ema
            
            return ema
            
        except Exception:
            return None

    def _calculate_wma(self, data: np.ndarray, period: int) -> Optional[float]:
        """Calculate Weighted Moving Average."""
        try:
            if len(data) < period:
                return None
            
            weights = np.arange(1, period + 1)
            recent_data = data[-period:]
            wma = np.sum(recent_data * weights) / np.sum(weights)
            
            return wma
            
        except Exception:
            return None

    def _calculate_ma_strength(self, closes: np.ndarray, highs: np.ndarray, 
                            lows: np.ndarray, ma_value: float, period: int) -> float:
        """Calculate MA strength based on price interaction."""
        try:
            recent_period = min(period, 20)
            recent_closes = closes[-recent_period:]
            recent_highs = highs[-recent_period:]
            recent_lows = lows[-recent_period:]
            
            # Count successful bounces/rejections
            bounces = 0
            total_interactions = 0
            
            for i in range(len(recent_closes)):
                close = recent_closes[i]
                high = recent_highs[i]
                low = recent_lows[i]
                
                # Check if price interacted with MA
                if low <= ma_value <= high:
                    total_interactions += 1
                    
                    # Check if it bounced (successful support/resistance)
                    if ma_value < close:  # Support bounce
                        if i < len(recent_closes) - 1 and recent_closes[i + 1] > ma_value:
                            bounces += 1
                    elif ma_value > close:  # Resistance rejection
                        if i < len(recent_closes) - 1 and recent_closes[i + 1] < ma_value:
                            bounces += 1
            
            # Calculate strength ratio
            strength = bounces / total_interactions if total_interactions > 0 else 0.5
            
            # Boost strength for longer periods (more significant)
            period_boost = min(1.5, 1 + (period - 20) / 100) if period > 20 else 1.0
            
            return min(1.0, strength * period_boost)
            
        except Exception:
            return 0.5

    def _calculate_ma_touches(self, closes: np.ndarray, highs: np.ndarray, 
                            lows: np.ndarray, ma_value: float, period: int) -> int:
        """Calculate number of MA touches/interactions."""
        try:
            lookback_period = min(period * 2, len(closes))
            recent_highs = highs[-lookback_period:]
            recent_lows = lows[-lookback_period:]
            
            touches = 0
            ma_threshold = ma_value * 0.002  # 0.2% threshold for "touch"
            
            for i in range(len(recent_highs)):
                high = recent_highs[i]
                low = recent_lows[i]
                
                # Check if candle touched MA within threshold
                if abs(high - ma_value) <= ma_threshold or abs(low - ma_value) <= ma_threshold:
                    touches += 1
                elif low <= ma_value <= high:  # MA crossed through candle
                    touches += 1
            
            return touches
            
        except Exception:
            return 0

    def _calculate_ma_slope(self, closes: np.ndarray, period: int, ma_func) -> float:
        """Calculate MA slope/trend."""
        try:
            if len(closes) < period + 5:
                return 0
            
            # Calculate MA for last few periods
            ma_current = ma_func(closes, period)
            ma_previous = ma_func(closes[:-5], period)
            
            if ma_current is None or ma_previous is None:
                return 0
            
            # Calculate slope as percentage change
            slope = (ma_current - ma_previous) / ma_previous if ma_previous != 0 else 0
            
            return slope
            
        except Exception:
            return 0

    def _get_last_ma_interaction(self, closes: np.ndarray, highs: np.ndarray, 
                                lows: np.ndarray, ma_value: float) -> int:
        """Get bars since last MA interaction."""
        try:
            for i in range(min(20, len(closes))):
                idx = len(closes) - 1 - i
                if idx < 0:
                    break
                
                high = highs[idx]
                low = lows[idx]
                
                # Check if price interacted with MA
                if low <= ma_value <= high:
                    return i
            
            return 20  # No recent interaction
            
        except Exception:
            return 20

    def _calculate_ma_reliability(self, closes: np.ndarray, ma_value: float, period: int) -> float:
        """Calculate MA reliability score."""
        try:
            if len(closes) < period:
                return 0.5
            
            # Check how well MA has worked as S/R historically
            test_period = min(period * 3, len(closes) - period)
            successful_tests = 0
            total_tests = 0
            
            for i in range(period, len(closes) - test_period):
                price = closes[i]
                future_prices = closes[i:i+5]  # Look 5 bars ahead
                
                if len(future_prices) < 5:
                    continue
                
                # Test if MA acted as support
                if price <= ma_value * 1.01 and price >= ma_value * 0.99:  # Near MA
                    total_tests += 1
                    
                    # Check if price bounced in expected direction
                    if price < ma_value and any(fp > ma_value for fp in future_prices):
                        successful_tests += 1
                    elif price > ma_value and any(fp < ma_value for fp in future_prices):
                        successful_tests += 1
            
            reliability = successful_tests / total_tests if total_tests > 0 else 0.5
            return min(1.0, reliability)
            
        except Exception:
            return 0.5

    def _detect_ma_crossover_levels(self, closes: np.ndarray, ma_periods: List[int]) -> List[Dict[str, Any]]:
        """Detect recent MA crossover levels that may act as S/R."""
        try:
            crossover_levels = []
            
            if len(closes) < max(ma_periods) + 10:
                return []
            
            # Calculate MAs
            mas = {}
            for period in ma_periods:
                if len(closes) >= period:
                    mas[period] = np.mean(closes[-period:])
            
            current_price = closes[-1]
            
            # Look for recent crossovers between different MA periods
            for i, short_period in enumerate(ma_periods[:-1]):
                for long_period in ma_periods[i+1:]:
                    if short_period in mas and long_period in mas:
                        short_ma = mas[short_period]
                        long_ma = mas[long_period]
                        
                        # Check for recent crossover
                        crossover_price = self._find_recent_crossover(closes, short_period, long_period)
                        
                        if crossover_price:
                            # Determine crossover type and strength
                            crossover_type = "bullish" if short_ma > long_ma else "bearish"
                            level_type = "support" if crossover_price < current_price else "resistance"
                            
                            # Calculate crossover strength
                            ma_separation = abs(short_ma - long_ma) / ((short_ma + long_ma) / 2)
                            crossover_strength = min(1.0, ma_separation * 50)  # Normalize
                            
                            crossover_levels.append({
                                "price": crossover_price,
                                "type": level_type,
                                "strength": crossover_strength,
                                "source": f"crossover_{short_period}_{long_period}",
                                "period": f"{short_period}x{long_period}",
                                "ma_type": "CROSSOVER",
                                "crossover_type": crossover_type,
                                "touches": 1,
                                "slope": 0,
                                "trend_direction": crossover_type,
                                "distance_percentage": abs(crossover_price - current_price) / current_price * 100,
                                "relevance_score": crossover_strength,
                                "last_interaction": 0,
                                "reliability": crossover_strength
                            })
            
            return crossover_levels
            
        except Exception as e:
            print(f"Error detecting MA crossover levels: {e}")
            return []

    def _find_recent_crossover(self, closes: np.ndarray, short_period: int, long_period: int) -> Optional[float]:
        """Find recent crossover point between two MAs."""
        try:
            lookback = min(20, len(closes) - max(short_period, long_period))
            
            for i in range(lookback):
                if len(closes) < max(short_period, long_period) + i + 1:
                    continue
                
                # Calculate MAs at different points
                data_current = closes[:-(i) if i > 0 else len(closes)]
                data_previous = closes[:-(i+1)]
                
                if len(data_current) < max(short_period, long_period) or len(data_previous) < max(short_period, long_period):
                    continue
                
                short_ma_current = np.mean(data_current[-short_period:])
                long_ma_current = np.mean(data_current[-long_period:])
                short_ma_previous = np.mean(data_previous[-short_period:])
                long_ma_previous = np.mean(data_previous[-long_period:])
                
                # Check for crossover
                if ((short_ma_previous <= long_ma_previous and short_ma_current > long_ma_current) or
                    (short_ma_previous >= long_ma_previous and short_ma_current < long_ma_current)):
                    
                    # Return approximate crossover price
                    return (short_ma_current + long_ma_current) / 2
            
            return None
            
        except Exception:
            return None

    def _calculate_pivot_levels(self, ohlcv_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Calculate pivot point levels as additional S/R."""
        try:
            if len(ohlcv_data) < 1:
                return []
            
            # Use yesterday's data for pivot calculation
            high = ohlcv_data['high'].iloc[-1]
            low = ohlcv_data['low'].iloc[-1]
            close = ohlcv_data['close'].iloc[-1]
            
            # Standard pivot point calculation
            pivot = (high + low + close) / 3
            
            # Calculate support and resistance levels
            r1 = 2 * pivot - low
            r2 = pivot + (high - low)
            r3 = high + 2 * (pivot - low)
            
            s1 = 2 * pivot - high
            s2 = pivot - (high - low)
            s3 = low - 2 * (high - pivot)
            
            current_price = close
            pivot_levels = []
            
            # Create level objects
            levels_data = [
                (pivot, "pivot", 0.8),
                (r1, "resistance", 0.7),
                (r2, "resistance", 0.6),
                (r3, "resistance", 0.5),
                (s1, "support", 0.7),
                (s2, "support", 0.6),
                (s3, "support", 0.5)
            ]
            
            for price, level_type, strength in levels_data:
                if price > 0:
                    pivot_levels.append({
                        "price": price,
                        "type": level_type,
                        "strength": strength,
                        "source": "pivot_point",
                        "period": 1,
                        "ma_type": "PIVOT",
                        "touches": 0,
                        "slope": 0,
                        "trend_direction": "neutral",
                        "distance_percentage": abs(price - current_price) / current_price * 100,
                        "relevance_score": strength,
                        "last_interaction": 0,
                        "reliability": strength
                    })
            
            print(f"        ⚓ Calculated {len(pivot_levels)} pivot point levels")
            return pivot_levels
            
        except Exception as e:
            print(f"Error calculating pivot levels: {e}")
            return []

    def _calculate_fourier_harmonic_tp_sl(self, signal_type: str, entry_price: float,
                                        ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                        market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Fourier harmonic-based TP/SL using frequency analysis."""
        try:
            # Get Fourier analysis data
            fourier_data = analysis_data.get("fourier_analysis", {})
            
            if fourier_data.get("status") != "success":
                # Perform basic Fourier analysis if not available
                fourier_data = self._perform_basic_fourier_analysis(ohlcv_data)
            
            if not fourier_data or fourier_data.get("status") != "success":
                return None
            
            # Extract harmonic levels and cycle information
            dominant_frequencies = fourier_data.get("dominant_frequencies", [])
            harmonic_levels_raw = fourier_data.get("harmonic_levels", {})

            # Handle different harmonic_levels formats
            if isinstance(harmonic_levels_raw, dict):
                # Extract from nested structure
                support_harmonics = harmonic_levels_raw.get("support_harmonics", [])
                resistance_harmonics = harmonic_levels_raw.get("resistance_harmonics", [])
                harmonic_levels = support_harmonics + resistance_harmonics
            elif isinstance(harmonic_levels_raw, list):
                harmonic_levels = harmonic_levels_raw
            else:
                harmonic_levels = []

            cycle_analysis = fourier_data.get("cycle_analysis", {})
            
            # Calculate harmonic-based TP/SL
            if signal_type == "BUY":
                # Find next harmonic resistance for TP
                resistance_harmonics = [level for level in harmonic_levels 
                                      if level.get("type") == "resistance" and level.get("price", 0) > entry_price]
                if resistance_harmonics:
                    # Sort by strength and proximity
                    resistance_harmonics.sort(key=lambda x: (x.get("strength", 0), -abs(x.get("price", 0) - entry_price)))
                    take_profit = resistance_harmonics[0]["price"]
                else:
                    # Use dominant frequency to project resistance
                    dominant_cycle = cycle_analysis.get("dominant_cycle_length", 20)
                    amplitude = cycle_analysis.get("average_amplitude", 0.01)
                    take_profit = entry_price + (amplitude * 1.618)  # Golden ratio extension
                
                # Find harmonic support for SL
                support_harmonics = [level for level in harmonic_levels 
                                   if level.get("type") == "support" and level.get("price", 0) < entry_price]
                if support_harmonics:
                    support_harmonics.sort(key=lambda x: (x.get("strength", 0), abs(x.get("price", 0) - entry_price)))
                    stop_loss = support_harmonics[0]["price"]
                else:
                    # Use harmonic support projection
                    amplitude = cycle_analysis.get("average_amplitude", 0.01)
                    stop_loss = entry_price - (amplitude * 0.618)  # Golden ratio retracement
            else:
                # SELL signal - reverse logic
                support_harmonics = [level for level in harmonic_levels 
                                   if level.get("type") == "support" and level.get("price", 0) < entry_price]
                if support_harmonics:
                    support_harmonics.sort(key=lambda x: (x.get("strength", 0), -abs(x.get("price", 0) - entry_price)))
                    take_profit = support_harmonics[0]["price"]
                else:
                    amplitude = cycle_analysis.get("average_amplitude", 0.01)
                    take_profit = entry_price - (amplitude * 1.618)
                
                resistance_harmonics = [level for level in harmonic_levels 
                                      if level.get("type") == "resistance" and level.get("price", 0) > entry_price]
                if resistance_harmonics:
                    resistance_harmonics.sort(key=lambda x: (x.get("strength", 0), abs(x.get("price", 0) - entry_price)))
                    stop_loss = resistance_harmonics[0]["price"]
                else:
                    amplitude = cycle_analysis.get("average_amplitude", 0.01)
                    stop_loss = entry_price + (amplitude * 0.618)
            
            # Calculate confidence based on harmonic strength
            harmonic_strength = cycle_analysis.get("dominant_frequency_strength", 0.5)
            frequency_count = len(dominant_frequencies)
            confidence = min(1.0, harmonic_strength + (frequency_count / 20))
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "harmonic_levels_used": len(harmonic_levels),
                "dominant_frequencies": len(dominant_frequencies),
                "harmonic_strength": harmonic_strength,
                "cycle_length": cycle_analysis.get("dominant_cycle_length", 0)
            }
            
        except Exception as e:
            print(f"      Error in Fourier harmonic calculation: {e}")
            return None

    def _calculate_orderbook_levels_tp_sl(self, signal_type: str, entry_price: float,
                                        ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                        market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate TP/SL based on orderbook level analysis."""
        try:
            # Get orderbook analysis data
            orderbook_data = analysis_data.get("orderbook_data", {})
            orderbook_analysis = analysis_data.get("orderbook_analysis", {})
            
            if not orderbook_data or orderbook_analysis.get("status") != "success":
                return None
            
            # Extract orderbook levels and imbalances
            bid_levels = self._extract_significant_bid_levels(orderbook_data, entry_price)
            ask_levels = self._extract_significant_ask_levels(orderbook_data, entry_price)
            
            # Get volume imbalance and whale levels
            imbalance_data = orderbook_analysis.get("imbalance_analysis", {})
            whale_levels = orderbook_analysis.get("whale_detection", {})
            
            if signal_type == "BUY":
                # Find significant ask levels for TP (resistance)
                ask_resistances = [level for level in ask_levels if level["price"] > entry_price]
                if ask_resistances:
                    # Prioritize levels with high volume or whale presence
                    ask_resistances.sort(key=lambda x: (x["volume_weight"], x["proximity_score"]), reverse=True)
                    take_profit = ask_resistances[0]["price"]
                else:
                    # Use volume-weighted average ask for estimation
                    volume_weighted_ask = self._calculate_volume_weighted_level(orderbook_data["asks"], entry_price, "above")
                    take_profit = volume_weighted_ask if volume_weighted_ask else entry_price * 1.02
                
                # Find significant bid levels for SL (support)
                bid_supports = [level for level in bid_levels if level["price"] < entry_price]
                if bid_supports:
                    bid_supports.sort(key=lambda x: (x["volume_weight"], -x["proximity_score"]), reverse=True)
                    stop_loss = bid_supports[0]["price"]
                else:
                    # Use volume-weighted average bid
                    volume_weighted_bid = self._calculate_volume_weighted_level(orderbook_data["bids"], entry_price, "below")
                    stop_loss = volume_weighted_bid if volume_weighted_bid else entry_price * 0.985
            else:
                # SELL signal
                bid_supports = [level for level in bid_levels if level["price"] < entry_price]
                if bid_supports:
                    bid_supports.sort(key=lambda x: (x["volume_weight"], x["proximity_score"]), reverse=True)
                    take_profit = bid_supports[0]["price"]
                else:
                    volume_weighted_bid = self._calculate_volume_weighted_level(orderbook_data["bids"], entry_price, "below")
                    take_profit = volume_weighted_bid if volume_weighted_bid else entry_price * 0.98
                
                ask_resistances = [level for level in ask_levels if level["price"] > entry_price]
                if ask_resistances:
                    ask_resistances.sort(key=lambda x: (x["volume_weight"], -x["proximity_score"]), reverse=True)
                    stop_loss = ask_resistances[0]["price"]
                else:
                    volume_weighted_ask = self._calculate_volume_weighted_level(orderbook_data["asks"], entry_price, "above")
                    stop_loss = volume_weighted_ask if volume_weighted_ask else entry_price * 1.015
            
            # Calculate confidence based on orderbook quality
            spread = self._calculate_orderbook_spread(orderbook_data)
            depth_quality = self._calculate_orderbook_depth_quality(orderbook_data)
            imbalance_strength = abs(imbalance_data.get("volume_imbalance", 0))
            
            confidence = min(1.0, (depth_quality * 0.4) + (imbalance_strength * 0.3) + ((1 - spread) * 0.3))
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "orderbook_spread": spread,
                "depth_quality": depth_quality,
                "volume_imbalance": imbalance_data.get("volume_imbalance", 0),
                "whale_levels_detected": len(whale_levels.get("whale_orders", []))
            }
            
        except Exception as e:
            print(f"      Error in orderbook levels calculation: {e}")
            return None

    def _calculate_volume_spike_tp_sl(self, signal_type: str, entry_price: float,
                                    ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                    market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate TP/SL based on volume spike analysis."""
        try:
            # Get volume spike information
            volume_spike_info = analysis_data.get("volume_spike_info", {})
            
            if not volume_spike_info or not volume_spike_info.get("is_spike"):
                # Analyze current volume conditions
                volume_analysis = self._analyze_volume_conditions(ohlcv_data)
                if volume_analysis["spike_potential"] < 0.3:
                    return None
                volume_spike_info = volume_analysis
            
            # Extract spike characteristics
            spike_factor = volume_spike_info.get("spike_factor", 1.0)
            spike_direction = volume_spike_info.get("direction", "UNKNOWN")
            spike_magnitude = volume_spike_info.get("magnitude", 1.0)
            
            # Calculate volume-based price movement expectations
            avg_volume = volume_spike_info.get("average_volume", 0)
            current_volume = volume_spike_info.get("current_volume", 0)
            
            # Estimate price impact based on volume spike
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            price_impact_multiplier = min(3.0, np.log(volume_ratio) * 0.5) if volume_ratio > 1 else 0.5
            
            # Calculate volatility expansion from volume spike
            recent_volatility = ohlcv_data['close'].pct_change().tail(20).std()
            volatility_expansion = recent_volatility * (1 + (spike_factor - 1) * 0.3)
            
            if signal_type == "BUY":
                # Volume spike supports upward movement
                if spike_direction in ["Tăng", "BUY", "BULLISH"] or spike_factor > 2.0:
                    # Aggressive TP for volume-supported moves
                    take_profit = entry_price * (1 + volatility_expansion * price_impact_multiplier * 2.0)
                    # Conservative SL as volume spike reduces downside risk
                    stop_loss = entry_price * (1 - volatility_expansion * 0.7)
                else:
                    # Conservative approach for uncertain volume spikes
                    take_profit = entry_price * (1 + volatility_expansion * price_impact_multiplier * 1.5)
                    stop_loss = entry_price * (1 - volatility_expansion * 1.0)
            else:
                # SELL signal
                if spike_direction in ["Giảm", "SELL", "BEARISH"] or spike_factor > 2.0:
                    take_profit = entry_price * (1 - volatility_expansion * price_impact_multiplier * 2.0)
                    stop_loss = entry_price * (1 + volatility_expansion * 0.7)
                else:
                    take_profit = entry_price * (1 - volatility_expansion * price_impact_multiplier * 1.5)
                    stop_loss = entry_price * (1 + volatility_expansion * 1.0)
            
            # Calculate confidence based on spike characteristics
            direction_alignment = 1.0 if spike_direction in ["Tăng", "BUY", "BULLISH"] and signal_type == "BUY" else \
                                 1.0 if spike_direction in ["Giảm", "SELL", "BEARISH"] and signal_type == "SELL" else 0.5
            
            spike_strength = min(1.0, spike_factor / 5.0)  # Normalize spike factor
            confidence = (direction_alignment * 0.6) + (spike_strength * 0.4)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "spike_factor": spike_factor,
                "volume_ratio": volume_ratio,
                "price_impact_multiplier": price_impact_multiplier,
                "direction_alignment": direction_alignment,
                "volatility_expansion": volatility_expansion
            }
            
        except Exception as e:
            print(f"      Error in volume spike calculation: {e}")
            return None

    def _calculate_volume_pattern_tp_sl(self, signal_type: str, entry_price: float,
                                      ohlcv_data: pd.DataFrame, analysis_data: Dict[str, Any],
                                      market_regime: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate TP/SL based on volume pattern analysis."""
        try:
            # Get volume pattern analysis
            volume_pattern_analysis = analysis_data.get("volume_pattern_analysis", {})
            
            if volume_pattern_analysis.get("status") != "success":
                # Perform basic volume pattern analysis
                volume_pattern_analysis = self._perform_basic_volume_pattern_analysis(ohlcv_data)
            
            if not volume_pattern_analysis or volume_pattern_analysis.get("status") != "success":
                return None
            
            # Extract pattern information
            patterns_detected = volume_pattern_analysis.get("patterns_detected", {})
            prediction = volume_pattern_analysis.get("prediction", {})
            volume_trend = volume_pattern_analysis.get("volume_trend", "neutral")
            
            # Analyze specific volume patterns
            accumulation_pattern = patterns_detected.get("accumulation", {})
            distribution_pattern = patterns_detected.get("distribution", {})
            breakout_pattern = patterns_detected.get("volume_breakout", {})
            
            # Calculate pattern-based price projections
            if signal_type == "BUY":
                # Accumulation patterns suggest upward movement
                if accumulation_pattern.get("detected", False):
                    accumulation_strength = accumulation_pattern.get("strength", 0.5)
                    base_move = self._calculate_accumulation_price_target(ohlcv_data, accumulation_pattern)
                    take_profit = entry_price + base_move * (1 + accumulation_strength)
                    stop_loss = entry_price - base_move * 0.3  # Tight SL for accumulation
                
                # Volume breakout patterns
                elif breakout_pattern.get("detected", False):
                    breakout_strength = breakout_pattern.get("strength", 0.5)
                    breakout_target = self._calculate_volume_breakout_target(ohlcv_data, breakout_pattern)
                    take_profit = entry_price + breakout_target * (1 + breakout_strength)
                    stop_loss = entry_price - breakout_target * 0.4
                
                # General volume trend support
                else:
                    volume_momentum = prediction.get("momentum_score", 0.5)
                    trend_strength = prediction.get("trend_strength", 0.5)
                    
                    base_volatility = ohlcv_data['close'].pct_change().tail(20).std()
                    volume_multiplier = 1 + (volume_momentum * trend_strength)
                    
                    take_profit = entry_price * (1 + base_volatility * volume_multiplier * 2.0)
                    stop_loss = entry_price * (1 - base_volatility * volume_multiplier * 0.8)
            else:
                # SELL signal
                if distribution_pattern.get("detected", False):
                    distribution_strength = distribution_pattern.get("strength", 0.5)
                    base_move = self._calculate_distribution_price_target(ohlcv_data, distribution_pattern)
                    take_profit = entry_price - base_move * (1 + distribution_strength)
                    stop_loss = entry_price + base_move * 0.3
                
                elif breakout_pattern.get("detected", False) and breakout_pattern.get("direction") == "down":
                    breakout_strength = breakout_pattern.get("strength", 0.5)
                    breakout_target = self._calculate_volume_breakout_target(ohlcv_data, breakout_pattern)
                    take_profit = entry_price - breakout_target * (1 + breakout_strength)
                    stop_loss = entry_price + breakout_target * 0.4
                
                else:
                    volume_momentum = prediction.get("momentum_score", 0.5)
                    trend_strength = prediction.get("trend_strength", 0.5)
                    
                    base_volatility = ohlcv_data['close'].pct_change().tail(20).std()
                    volume_multiplier = 1 + (volume_momentum * trend_strength)
                    
                    take_profit = entry_price * (1 - base_volatility * volume_multiplier * 2.0)
                    stop_loss = entry_price * (1 + base_volatility * volume_multiplier * 0.8)
            
            # Calculate confidence based on pattern strength
            pattern_count = sum(1 for pattern in patterns_detected.values() if pattern.get("detected", False))
            pattern_strength_avg = np.mean([pattern.get("strength", 0) for pattern in patterns_detected.values() 
                                          if pattern.get("detected", False)]) if pattern_count > 0 else 0.5
            
            prediction_confidence = prediction.get("confidence", 0.5)
            confidence = (pattern_strength_avg * 0.5) + (prediction_confidence * 0.3) + (pattern_count / 10 * 0.2)
            confidence = min(1.0, confidence)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "risk_reward_ratio": risk_reward,
                "confidence": confidence,
                "patterns_detected": pattern_count,
                "pattern_strength": pattern_strength_avg,
                "volume_trend": volume_trend,
                "prediction_confidence": prediction_confidence
            }
            
        except Exception as e:
            print(f"      Error in volume pattern calculation: {e}")
            return None

    def _calculate_enhanced_ensemble_tp_sl(self, methods_results: Dict[str, Any], 
                                         market_regime: Dict[str, Any], signal_type: str, 
                                         entry_price: float) -> Dict[str, Any]:
        """Calculate enhanced ensemble TP/SL by combining all 12 valid methods."""
        try:
            valid_methods = {k: v for k, v in methods_results.items() if v is not None}
            
            if not valid_methods:
                raise ValueError("No valid TP/SL methods available")
            
            print(f"      📊 Combining {len(valid_methods)}/12 methods for ensemble TP/SL")
            
            # Enhanced dynamic weighting based on method confidence, market regime, and method type
            weights = self._calculate_enhanced_dynamic_weights(valid_methods, market_regime)
            
            # Weighted average calculation with outlier filtering
            tp_values = []
            sl_values = []
            confidences = []
            
            for name, method in valid_methods.items():
                weight = weights[name]
                tp_values.append(method["take_profit"] * weight)
                sl_values.append(method["stop_loss"] * weight)
                confidences.append(method["confidence"] * weight)
            
            # Filter outliers before final calculation
            filtered_tp = self._filter_outliers(tp_values)
            filtered_sl = self._filter_outliers(sl_values)
            
            weighted_tp = sum(filtered_tp)
            weighted_sl = sum(filtered_sl)
            ensemble_confidence = sum(confidences)
            
            # Apply market regime adjustments
            regime_adjusted_tp, regime_adjusted_sl = self._apply_regime_adjustments(
                weighted_tp, weighted_sl, entry_price, market_regime, signal_type
            )
            
            # Calculate enhanced risk-reward ratio
            risk = abs(entry_price - regime_adjusted_sl)
            reward = abs(regime_adjusted_tp - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            return {
                "take_profit": regime_adjusted_tp,
                "stop_loss": regime_adjusted_sl,
                "risk_reward_ratio": risk_reward,
                "confidence": ensemble_confidence,
                "methods_used": list(valid_methods.keys()),
                "ensemble_weights": weights,
                "methods_count": len(valid_methods),
                "risk_metrics": {
                    "risk": risk,
                    "reward": reward,
                    "risk_percentage": (risk / entry_price) * 100,
                    "reward_percentage": (reward / entry_price) * 100
                }
            }
            
        except Exception as e:
            print(f"      Error in enhanced ensemble calculation: {e}")
            return None

    def _calculate_enhanced_dynamic_weights(self, valid_methods: Dict[str, Any], 
                                          market_regime: Dict[str, Any]) -> Dict[str, float]:
        """Calculate enhanced dynamic weights for all 12 methods based on market conditions."""
        try:
            regime = market_regime["regime"]
            confidence = market_regime["confidence"]
            
            # Base weights for different market regimes
            regime_weights = {
                "trending": {
                    "atr_dynamic": 0.12, "fibonacci_confluence": 0.15, "volume_profile": 0.10,
                    "point_figure": 0.08, "sr_confluence": 0.08, "volatility_bands": 0.06,
                    "momentum_based": 0.12, "statistical_risk": 0.07, "fourier_harmonic": 0.10,
                    "orderbook_levels": 0.04, "volume_spike": 0.04, "volume_pattern": 0.04
                },
                "ranging": {
                    "atr_dynamic": 0.08, "fibonacci_confluence": 0.12, "volume_profile": 0.15,
                    "point_figure": 0.12, "sr_confluence": 0.15, "volatility_bands": 0.10,
                    "momentum_based": 0.05, "statistical_risk": 0.08, "fourier_harmonic": 0.05,
                    "orderbook_levels": 0.08, "volume_spike": 0.01, "volume_pattern": 0.01
                },
                "volatile": {
                    "atr_dynamic": 0.18, "fibonacci_confluence": 0.08, "volume_profile": 0.08,
                    "point_figure": 0.06, "sr_confluence": 0.06, "volatility_bands": 0.15,
                    "momentum_based": 0.08, "statistical_risk": 0.12, "fourier_harmonic": 0.04,
                    "orderbook_levels": 0.05, "volume_spike": 0.05, "volume_pattern": 0.05
                },
                "consolidating": {
                    "atr_dynamic": 0.10, "fibonacci_confluence": 0.10, "volume_profile": 0.12,
                    "point_figure": 0.10, "sr_confluence": 0.12, "volatility_bands": 0.08,
                    "momentum_based": 0.06, "statistical_risk": 0.10, "fourier_harmonic": 0.08,
                    "orderbook_levels": 0.06, "volume_spike": 0.04, "volume_pattern": 0.04
                }
            }
            
            # Get base weights for current regime
            base_weights = regime_weights.get(regime, regime_weights["consolidating"])
            
            # Adjust weights based on individual method confidence
            final_weights = {}
            total_weight = 0
            
            for method_name, method_result in valid_methods.items():
                base_weight = base_weights.get(method_name, 1.0 / len(valid_methods))
                method_confidence = method_result.get("confidence", 0.5)
                
                # Enhance weight based on method-specific factors
                if method_name == "fourier_harmonic":
                    harmonic_strength = method_result.get("harmonic_strength", 0.5)
                    confidence_multiplier = 1 + (harmonic_strength * 0.5)
                elif method_name == "orderbook_levels":
                    depth_quality = method_result.get("depth_quality", 0.5)
                    confidence_multiplier = 1 + (depth_quality * 0.3)
                elif method_name == "volume_spike":
                    spike_factor = method_result.get("spike_factor", 1.0)
                    confidence_multiplier = 1 + (min(spike_factor - 1, 2.0) * 0.2)
                elif method_name == "volume_pattern":
                    pattern_count = method_result.get("patterns_detected", 0)
                    confidence_multiplier = 1 + (pattern_count * 0.1)
                else:
                    confidence_multiplier = 1.0
                
                # Final weight calculation
                adjusted_weight = base_weight * method_confidence * confidence_multiplier
                final_weights[method_name] = adjusted_weight
                total_weight += adjusted_weight
            
            # Normalize weights to sum to 1.0
            if total_weight > 0:
                final_weights = {k: v / total_weight for k, v in final_weights.items()}
            else:
                # Equal weights fallback
                equal_weight = 1.0 / len(valid_methods)
                final_weights = {k: equal_weight for k in valid_methods.keys()}
            
            return final_weights
            
        except Exception as e:
            print(f"      Error calculating enhanced dynamic weights: {e}")
            # Fallback to equal weights
            equal_weight = 1.0 / len(valid_methods)
            return {k: equal_weight for k in valid_methods.keys()}

    # ...existing code...

    # Helper methods for new calculation methods
    def _perform_basic_fourier_analysis(self, ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """Perform basic Fourier analysis on price data."""
        try:
            closes = ohlcv_data['close'].values
            n = len(closes)
            
            # Apply FFT to price data
            fft_values = np.fft.fft(closes)
            frequencies = np.fft.fftfreq(n)
            
            # Find dominant frequencies
            magnitude = np.abs(fft_values)
            dominant_indices = np.argsort(magnitude)[-10:]  # Top 10 frequencies
            
            dominant_frequencies = []
            for idx in dominant_indices:
                if frequencies[idx] != 0:  # Avoid DC component
                    dominant_frequencies.append({
                        "frequency": frequencies[idx],
                        "magnitude": magnitude[idx],
                        "phase": np.angle(fft_values[idx])
                    })
            
            # Calculate harmonic levels
            harmonic_levels = []
            current_price = closes[-1]
            price_range = np.max(closes) - np.min(closes)
            
            for freq_data in dominant_frequencies[:5]:  # Top 5 frequencies
                magnitude_norm = freq_data["magnitude"] / np.max(magnitude)
                
                # Project harmonic support and resistance
                harmonic_support = current_price - (price_range * magnitude_norm * 0.618)
                harmonic_resistance = current_price + (price_range * magnitude_norm * 0.618)
                
                harmonic_levels.extend([
                    {"type": "support", "price": harmonic_support, "strength": magnitude_norm},
                    {"type": "resistance", "price": harmonic_resistance, "strength": magnitude_norm}
                ])
            
            return {
                "status": "success",
                "dominant_frequencies": dominant_frequencies,
                "harmonic_levels": harmonic_levels,
                "cycle_analysis": {
                    "dominant_cycle_length": 1 / abs(dominant_frequencies[0]["frequency"]) if dominant_frequencies else 20,
                    "average_amplitude": price_range * 0.1,
                    "dominant_frequency_strength": dominant_frequencies[0]["magnitude"] / np.max(magnitude) if dominant_frequencies else 0.5
                }
            }
            
        except Exception as e:
            print(f"Error in basic Fourier analysis: {e}")
            return {"status": "failed"}

    def _extract_significant_bid_levels(self, orderbook_data: Dict, entry_price: float) -> List[Dict]:
        """Extract significant bid levels from orderbook data."""
        try:
            bids = orderbook_data.get("bids", [])
            if not bids:
                return []
            
            significant_levels = []
            total_volume = sum(float(bid[1]) for bid in bids)
            
            for bid in bids:
                price = float(bid[0])
                volume = float(bid[1])
                
                if price < entry_price:  # Only consider bids below entry price
                    volume_weight = volume / total_volume if total_volume > 0 else 0
                    proximity_score = 1 / (1 + abs(price - entry_price) / entry_price)
                    
                    if volume_weight > 0.05:  # Significant volume threshold
                        significant_levels.append({
                            "price": price,
                            "volume": volume,
                            "volume_weight": volume_weight,
                            "proximity_score": proximity_score
                        })
            
            return significant_levels[:10]  # Top 10 levels
            
        except Exception as e:
            print(f"Error extracting bid levels: {e}")
            return []

    def _extract_significant_ask_levels(self, orderbook_data: Dict, entry_price: float) -> List[Dict]:
        """Extract significant ask levels from orderbook data."""
        try:
            asks = orderbook_data.get("asks", [])
            if not asks:
                return []
            
            significant_levels = []
            total_volume = sum(float(ask[1]) for ask in asks)
            
            for ask in asks:
                price = float(ask[0])
                volume = float(ask[1])
                
                if price > entry_price:  # Only consider asks above entry price
                    volume_weight = volume / total_volume if total_volume > 0 else 0
                    proximity_score = 1 / (1 + abs(price - entry_price) / entry_price)
                    
                    if volume_weight > 0.05:  # Significant volume threshold
                        significant_levels.append({
                            "price": price,
                            "volume": volume,
                            "volume_weight": volume_weight,
                            "proximity_score": proximity_score
                        })
            
            return significant_levels[:10]  # Top 10 levels
            
        except Exception as e:
            print(f"Error extracting ask levels: {e}")
            return []

    def _calculate_volume_weighted_level(self, orders: List, entry_price: float, direction: str) -> Optional[float]:
        """Calculate volume-weighted price level from orderbook orders."""
        try:
            if not orders:
                return None
            
            total_volume = 0
            weighted_price = 0
            
            for order in orders:
                price = float(order[0])
                volume = float(order[1])
                
                if direction == "above" and price > entry_price:
                    total_volume += volume
                    weighted_price += price * volume
                elif direction == "below" and price < entry_price:
                    total_volume += volume
                    weighted_price += price * volume
            
            return weighted_price / total_volume if total_volume > 0 else None
            
        except Exception as e:
            print(f"Error calculating volume weighted level: {e}")
            return None

    def _calculate_orderbook_spread(self, orderbook_data: Dict) -> float:
        """Calculate orderbook spread."""
        try:
            bids = orderbook_data.get("bids", [])
            asks = orderbook_data.get("asks", [])
            
            if not bids or not asks:
                return 1.0  # High spread indicates poor quality
            
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            
            spread = (best_ask - best_bid) / best_bid
            return spread
            
        except Exception as e:
            return 1.0

    def _calculate_orderbook_depth_quality(self, orderbook_data: Dict) -> float:
        """Calculate orderbook depth quality."""
        try:
            bids = orderbook_data.get("bids", [])
            asks = orderbook_data.get("asks", [])
            
            if not bids or not asks:
                return 0.0
            
            # Calculate depth as cumulative volume in top levels
            bid_depth = sum(float(bid[1]) for bid in bids[:10])
            ask_depth = sum(float(ask[1]) for ask in asks[:10])
            
            # Quality based on balance and total depth
            total_depth = bid_depth + ask_depth
            balance = min(bid_depth, ask_depth) / max(bid_depth, ask_depth) if max(bid_depth, ask_depth) > 0 else 0
            
            # Normalize quality score
            quality = min(1.0, (total_depth / 1000000) * balance)  # Adjust denominator based on typical volumes
            return quality
            
        except Exception as e:
            return 0.0

    def _analyze_volume_conditions(self, ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze current volume conditions for spike potential."""
        try:
            volumes = ohlcv_data['volume'].values
            n = len(volumes)
            
            if n < 20:
                return {"spike_potential": 0.0}
            
            current_volume = volumes[-1]
            avg_volume = np.mean(volumes[-20:])
            volume_std = np.std(volumes[-20:])
            
            # Calculate spike potential
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            z_score = (current_volume - avg_volume) / volume_std if volume_std > 0 else 0
            
            spike_potential = min(1.0, max(0.0, (volume_ratio - 1.0) + (z_score / 3.0)))
            
            return {
                "spike_potential": spike_potential,
                "volume_ratio": volume_ratio,
                "z_score": z_score,
                "current_volume": current_volume,
                "average_volume": avg_volume
            }
            
        except Exception as e:
            return {"spike_potential": 0.0}

    def _perform_basic_volume_pattern_analysis(self, ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """Perform basic volume pattern analysis."""
        try:
            volumes = ohlcv_data['volume'].values
            closes = ohlcv_data['close'].values
            n = len(volumes)
            
            if n < 20:
                return {"status": "failed"}
            
            # Analyze volume trends
            volume_ma_short = np.mean(volumes[-5:])
            volume_ma_long = np.mean(volumes[-20:])
            volume_trend = "increasing" if volume_ma_short > volume_ma_long else "decreasing"
            
            # Detect basic patterns
            patterns_detected = {
                "accumulation": {
                    "detected": self._detect_accumulation_pattern(volumes, closes),
                    "strength": 0.5
                },
                "distribution": {
                    "detected": self._detect_distribution_pattern(volumes, closes),
                    "strength": 0.5
                },
                "volume_breakout": {
                    "detected": self._detect_volume_breakout_pattern(volumes),
                    "strength": 0.5
                }
            }
            
            return {
                "status": "success",
                "patterns_detected": patterns_detected,
                "volume_trend": volume_trend,
                "prediction": {
                    "momentum_score": 0.5,
                    "trend_strength": 0.5,
                    "confidence": 0.5
                }
            }
            
        except Exception as e:
            return {"status": "failed"}

    def _detect_accumulation_pattern(self, volumes: np.ndarray, closes: np.ndarray) -> bool:
        """Detect accumulation pattern (increasing volume, stable/rising price)."""
        try:
            if len(volumes) < 10 or len(closes) < 10:
                return False
            
            volume_trend = np.polyfit(range(len(volumes[-10:])), volumes[-10:], 1)[0]
            price_trend = np.polyfit(range(len(closes[-10:])), closes[-10:], 1)[0]
            
            return volume_trend > 0 and price_trend >= 0
            
        except Exception:
            return False

    def _detect_distribution_pattern(self, volumes: np.ndarray, closes: np.ndarray) -> bool:
        """Detect distribution pattern (high volume, declining price)."""
        try:
            if len(volumes) < 10 or len(closes) < 10:
                return False
            
            volume_trend = np.polyfit(range(len(volumes[-10:])), volumes[-10:], 1)[0]
            price_trend = np.polyfit(range(len(closes[-10:])), closes[-10:], 1)[0]
            
            return volume_trend > 0 and price_trend < 0
            
        except Exception:
            return False

    def _detect_volume_breakout_pattern(self, volumes: np.ndarray) -> bool:
        """Detect volume breakout pattern."""
        try:
            if len(volumes) < 20:
                return False
            
            current_volume = volumes[-1]
            avg_volume = np.mean(volumes[-20:-1])
            
            return current_volume > avg_volume * 2.0
            
        except Exception:
            return False

    def _calculate_accumulation_price_target(self, ohlcv_data: pd.DataFrame, pattern: Dict) -> float:
        """Calculate price target for accumulation pattern."""
        try:
            price_range = ohlcv_data['high'].tail(20).max() - ohlcv_data['low'].tail(20).min()
            return price_range * 0.5  # Conservative target
        except Exception:
            return ohlcv_data['close'].iloc[-1] * 0.02  # 2% fallback

    def _calculate_distribution_price_target(self, ohlcv_data: pd.DataFrame, pattern: Dict) -> float:
        """Calculate price target for distribution pattern."""
        try:
            price_range = ohlcv_data['high'].tail(20).max() - ohlcv_data['low'].tail(20).min()
            return price_range * 0.5  # Conservative target
        except Exception:
            return ohlcv_data['close'].iloc[-1] * 0.02  # 2% fallback

    def _calculate_volume_breakout_target(self, ohlcv_data: pd.DataFrame, pattern: Dict) -> float:
        """Calculate price target for volume breakout pattern."""
        try:
            price_range = ohlcv_data['high'].tail(10).max() - ohlcv_data['low'].tail(10).min()
            return price_range * 0.8  # Aggressive target for breakouts
        except Exception:
            return ohlcv_data['close'].iloc[-1] * 0.03  # 3% fallback

    def _filter_outliers(self, values: List[float]) -> List[float]:
        """Filter outliers from a list of values."""
        try:
            if len(values) < 3:
                return values
            
            q1 = np.percentile(values, 25)
            q3 = np.percentile(values, 75)
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            filtered = [v for v in values if lower_bound <= v <= upper_bound]
            return filtered if filtered else values  # Return original if all filtered out
            
        except Exception:
            return values

    def _apply_regime_adjustments(self, take_profit: float, stop_loss: float, 
                                entry_price: float, market_regime: Dict[str, Any], 
                                signal_type: str) -> Tuple[float, float]:
        """Apply market regime adjustments to TP/SL levels."""
        try:
            regime = market_regime["regime"]
            confidence = market_regime["confidence"]
            
            # Adjustment factors based on regime
            if regime == "trending":
                tp_adjustment = 1.1  # More aggressive TP
                sl_adjustment = 0.9  # Tighter SL
            elif regime == "volatile":
                tp_adjustment = 0.9  # Conservative TP
                sl_adjustment = 1.1  # Wider SL
            elif regime == "ranging":
                tp_adjustment = 0.8  # Very conservative TP
                sl_adjustment = 1.0  # Normal SL
            else:  # consolidating
                tp_adjustment = 1.0  # Normal TP
                sl_adjustment = 0.95  # Slightly tighter SL
            
            # Apply confidence-based adjustments
            confidence_factor = 0.5 + (confidence * 0.5)  # 0.5 to 1.0 range
            
            if signal_type == "BUY":
                adjusted_tp = entry_price + (take_profit - entry_price) * tp_adjustment * confidence_factor
                adjusted_sl = entry_price - (entry_price - stop_loss) * sl_adjustment * confidence_factor
            else:
                adjusted_tp = entry_price - (entry_price - take_profit) * tp_adjustment * confidence_factor
                adjusted_sl = entry_price + (stop_loss - entry_price) * sl_adjustment * confidence_factor
            
            return adjusted_tp, adjusted_sl
            
        except Exception as e:
            print(f"Error applying regime adjustments: {e}")
            return take_profit, stop_loss
