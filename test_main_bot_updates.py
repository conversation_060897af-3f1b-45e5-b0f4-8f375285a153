#!/usr/bin/env python3
"""
🔧 MAIN_BOT.PY UPDATES TEST SCRIPT
=================================

Test script để kiểm tra các cập nhật và tối ưu hóa trong main_bot.py:
- Wavelet Analysis trong Fourier Analyzer
- Trade Tracker V3.0 Ultra features
- Specialized Telegram Chat Routing
- Module compatibility và interface changes
"""

import os
import sys
import time
from datetime import datetime

def test_imports():
    """Test importing main_bot and all dependencies"""
    print("📦 === TESTING MAIN_BOT IMPORTS ===")
    print("=" * 50)
    
    try:
        print("🔧 Testing main_bot import...")
        import main_bot
        print("  ✅ main_bot imported successfully")
        
        # Test TradingBot class
        print("🤖 Testing TradingBot class...")
        bot_class = main_bot.TradingBot
        print("  ✅ TradingBot class accessible")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fourier_analyzer_wavelet():
    """Test Fourier Analyzer wavelet analysis features"""
    print("\n🌊 === TESTING FOURIER ANALYZER WAVELET FEATURES ===")
    print("=" * 50)
    
    try:
        import fourier_analyzer
        print("  ✅ fourier_analyzer module imported")
        
        # Test FourierAnalyzer class
        analyzer = fourier_analyzer.FourierAnalyzer(
            enable_wavelet_analysis=True,
            enable_cycle_detection=True
        )
        print("  ✅ FourierAnalyzer initialized with wavelet analysis")
        
        # Check for wavelet methods
        wavelet_methods = ['_analyze_with_wavelet']
        available_methods = [method for method in dir(analyzer)
                           if not method.startswith('__') and callable(getattr(analyzer, method))]
        
        has_wavelet = any(method in available_methods for method in wavelet_methods)
        print(f"  🌊 Wavelet Analysis Available: {'✅ Yes' if has_wavelet else '❌ No'}")
        
        if hasattr(analyzer, 'enable_wavelet_analysis'):
            print("  ✅ enable_wavelet_analysis attribute found")
        
        return True
        
    except Exception as e:
        print(f"❌ Fourier Analyzer wavelet test failed: {e}")
        return False

def test_trade_tracker_v3():
    """Test Trade Tracker V3.0 features"""
    print("\n🚀 === TESTING TRADE TRACKER V3.0 FEATURES ===")
    print("=" * 50)
    
    try:
        import trade_tracker
        print("  ✅ trade_tracker module imported")
        
        # Check TradeTracker class
        tracker_class = trade_tracker.TradeTracker
        print("  ✅ TradeTracker class accessible")
        
        # Check for V3.0 methods
        v3_methods = [
            'get_ultra_tracker_status',
            'send_ultra_tracker_status_report',
            '_check_and_update_tp_sl_realtime',
            'get_tp_sl_tracking_summary',
            'send_tp_sl_tracking_report'
        ]
        
        available_methods = [method for method in dir(tracker_class)
                           if not method.startswith('__')]
        
        v3_features_available = [method for method in v3_methods if method in available_methods]
        
        print(f"  🚀 V3.0 Features Available: {len(v3_features_available)}/{len(v3_methods)}")
        for method in v3_features_available:
            print(f"    ✅ {method}")
        
        missing_features = [method for method in v3_methods if method not in available_methods]
        for method in missing_features:
            print(f"    ❌ {method}")
        
        return len(v3_features_available) >= 3  # At least 3 V3.0 features should be available
        
    except Exception as e:
        print(f"❌ Trade Tracker V3.0 test failed: {e}")
        return False

def test_telegram_specialized_chats():
    """Test specialized Telegram chat configuration"""
    print("\n📱 === TESTING SPECIALIZED TELEGRAM CHATS ===")
    print("=" * 50)
    
    try:
        import telegram_notifier
        print("  ✅ telegram_notifier module imported")
        
        # Check EnhancedTelegramNotifier class
        notifier_class = telegram_notifier.EnhancedTelegramNotifier
        print("  ✅ EnhancedTelegramNotifier class accessible")
        
        # Check for specialized chat methods
        specialized_methods = [
            'send_fibonacci_analysis',
            'send_ai_analysis_report',
            'send_volume_profile_analysis',
            'send_consensus_signal'
        ]
        
        available_methods = [method for method in dir(notifier_class)
                           if not method.startswith('__')]
        
        specialized_features = [method for method in specialized_methods if method in available_methods]
        
        print(f"  📱 Specialized Chat Features: {len(specialized_features)}/{len(specialized_methods)}")
        for method in specialized_features:
            print(f"    ✅ {method}")
        
        return len(specialized_features) >= 2  # At least 2 specialized features should be available
        
    except Exception as e:
        print(f"❌ Specialized Telegram chats test failed: {e}")
        return False

def test_module_compatibility():
    """Test module compatibility and interface consistency"""
    print("\n🔗 === TESTING MODULE COMPATIBILITY ===")
    print("=" * 50)
    
    compatibility_results = {}
    
    # Test core modules
    core_modules = [
        'data_fetcher',
        'signal_processor', 
        'ai_model_manager',
        'backup_manager',
        'data_logger',
        'trade_tracker'
    ]
    
    for module_name in core_modules:
        try:
            module = __import__(module_name)
            compatibility_results[module_name] = True
            print(f"  ✅ {module_name}: Compatible")
        except Exception as e:
            compatibility_results[module_name] = False
            print(f"  ❌ {module_name}: Error - {e}")
    
    # Test analyzer modules
    analyzer_modules = [
        'volume_profile_analyzer',
        'point_figure_analyzer',
        'fourier_analyzer',
        'orderbook_analyzer',
        'consensus_analyzer'
    ]
    
    for module_name in analyzer_modules:
        try:
            module = __import__(module_name)
            compatibility_results[module_name] = True
            print(f"  ✅ {module_name}: Compatible")
        except Exception as e:
            compatibility_results[module_name] = False
            print(f"  ❌ {module_name}: Error - {e}")
    
    compatible_count = sum(compatibility_results.values())
    total_count = len(compatibility_results)
    
    print(f"\n📊 Compatibility Summary: {compatible_count}/{total_count} modules compatible")
    
    return compatible_count >= (total_count * 0.8)  # At least 80% should be compatible

def test_configuration_values():
    """Test configuration values and constants"""
    print("\n⚙️ === TESTING CONFIGURATION VALUES ===")
    print("=" * 50)
    
    try:
        import main_bot
        
        # Test key configuration constants
        config_tests = [
            ('DYNAMIC_TP_SL_ENABLED', 'Dynamic TP/SL'),
            ('TRAILING_STOP_ENABLED', 'Trailing Stop'),
            ('CHART_GENERATION_ENABLED', 'Chart Generation'),
            ('AI_ENSEMBLE_REPORTS_ENABLED', 'AI Ensemble Reports'),
            ('SIGNAL_QUALITY_FILTER_ENABLED', 'Signal Quality Filter')
        ]
        
        for config_name, description in config_tests:
            if hasattr(main_bot, config_name):
                value = getattr(main_bot, config_name)
                print(f"  ✅ {description}: {value}")
            else:
                print(f"  ❌ {description}: Not found")
        
        # Test specialized chats configuration
        if hasattr(main_bot, 'TELEGRAM_SPECIALIZED_CHATS'):
            chats = main_bot.TELEGRAM_SPECIALIZED_CHATS
            print(f"  📱 Specialized Chats: {len(chats)} configured")
            for chat_type in ['ai_analysis', 'consensus_signals', 'fibonacci_zigzag_fourier']:
                if chat_type in chats:
                    print(f"    ✅ {chat_type}")
                else:
                    print(f"    ❌ {chat_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 MAIN_BOT.PY UPDATES TEST SUITE")
    print("=" * 80)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = {}
    
    # Run all tests
    test_results['imports'] = test_imports()
    test_results['fourier_wavelet'] = test_fourier_analyzer_wavelet()
    test_results['trade_tracker_v3'] = test_trade_tracker_v3()
    test_results['telegram_chats'] = test_telegram_specialized_chats()
    test_results['module_compatibility'] = test_module_compatibility()
    test_results['configuration'] = test_configuration_values()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Main bot updates are working correctly.")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("⚠️ MOSTLY SUCCESSFUL! Some minor issues detected.")
        return True
    else:
        print("❌ MULTIPLE FAILURES! Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
