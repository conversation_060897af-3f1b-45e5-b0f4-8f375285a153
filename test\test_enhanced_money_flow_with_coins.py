#!/usr/bin/env python3
"""
🧪 TEST: Enhanced Money Flow with Top Coins Information
Test để kiểm tra money flow signal với thông tin top coins trong sector
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_sample_market_data_with_coins():
    """Tạo sample market data với coin performance data"""
    print("📊 Creating sample market data with coin performance...")
    
    market_data = {}
    
    # Layer1 coins (hot sector)
    layer1_coins = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT']
    
    for i, coin in enumerate(layer1_coins):
        # Generate 50 hours of data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=50), periods=50, freq='1H')
        
        # Base price
        base_price = np.random.uniform(20, 100)
        
        # Different performance for each coin
        if coin == 'BTCUSDT':
            trend = 0.12  # BTC is leading with 12% gain
            volume_multiplier = 3.0
        elif coin == 'ETHUSDT':
            trend = 0.08  # ETH is second with 8% gain
            volume_multiplier = 2.5
        elif coin == 'SOLUSDT':
            trend = 0.06  # SOL is third with 6% gain
            volume_multiplier = 2.0
        elif coin == 'BNBUSDT':
            trend = 0.03  # BNB is moderate
            volume_multiplier = 1.5
        else:
            trend = 0.01  # ADA is lagging
            volume_multiplier = 1.2
        
        prices = []
        volumes = []
        
        for j in range(50):
            # Apply trend in recent hours (last 25 hours)
            if j >= 25:
                price_change = trend * (j - 25) / 25
                price = base_price * (1 + price_change + np.random.normal(0, 0.005))
            else:
                price = base_price * (1 + np.random.normal(0, 0.01))
            
            # Volume surge in recent hours
            base_volume = np.random.uniform(1000, 10000)
            if j >= 40:
                volume = base_volume * volume_multiplier
            else:
                volume = base_volume
            
            prices.append(max(0.01, price))
            volumes.append(max(100, volume))
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        market_data[coin] = {
            'ohlcv_data': ohlcv_data,
            'current_price': prices[-1]
        }
    
    print(f"✅ Created market data for {len(market_data)} Layer1 coins")
    return market_data

def test_enhanced_money_flow_with_coins():
    """Test enhanced money flow với thông tin top coins"""
    print("\n🧪 === TESTING ENHANCED MONEY FLOW WITH TOP COINS ===")
    
    try:
        from money_flow_analyzer import MoneyFlowAnalyzer
        
        # Initialize analyzer
        analyzer = MoneyFlowAnalyzer(
            tracking_pairs=['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'],
            sector_analysis=True
        )
        
        print(f"✅ MoneyFlowAnalyzer initialized")
        
        # Create sample market data with coin performance
        market_data = create_sample_market_data_with_coins()
        
        # Run money flow analysis
        print("\n🔍 Running money flow analysis...")
        analysis_result = analyzer.analyze_market_money_flow(market_data)
        
        # Generate money flow signals
        print("\n🌊 Generating money flow signals...")
        money_flow_signals = analyzer.get_money_flow_signals(analysis_result)
        
        print(f"\n📤 MONEY FLOW SIGNALS ({len(money_flow_signals)}):")
        
        for i, signal in enumerate(money_flow_signals, 1):
            print(f"\n📊 Signal {i}:")
            print(f"  Type: {signal['type']}")
            print(f"  Subtype: {signal.get('subtype', 'N/A')}")
            
            if 'hot_sector' in signal:
                print(f"  🏢 Hot Sector: {signal['hot_sector']}")
                print(f"  💪 Strength: {signal['strength']}")
                
                # Display formatted message with coin information
                if 'formatted_message' in signal:
                    print(f"\n📝 ENHANCED FORMATTED MESSAGE WITH TOP COINS:")
                    print("=" * 60)
                    print(signal['formatted_message'])
                    print("=" * 60)
                    
                    # Check if message contains coin information
                    if "Top Coins đang được chú ý" in signal['formatted_message']:
                        print("\n✅ SUCCESS: Message contains top coins information!")
                    else:
                        print("\n⚠️ WARNING: Message missing top coins information")
        
        # Test direct signal formatting
        print(f"\n🧪 Testing direct signal formatting...")
        
        test_signal = {
            'sector': 'Layer1',
            'signal': 'BUY_SECTOR',
            'strength': 'MODERATE',
            'sector_strength': 0.75,
            'money_flow_score': 0.65,
            'reason': 'Money rotating into Layer1 sector'
        }
        
        # Add market data to analysis result for coin analysis
        test_analysis_result = {
            'total_flow_score': 0.075,
            'sector_rotation': {
                'market_data': market_data
            },
            'market_data': market_data
        }
        
        formatted_message = analyzer._format_sector_rotation_signal(test_signal, test_analysis_result)
        
        print(f"\n📝 DIRECT FORMATTED MESSAGE TEST:")
        print("=" * 60)
        print(formatted_message)
        print("=" * 60)
        
        # Verify enhanced features
        enhanced_features = [
            "🌊 **MONEY FLOW SIGNAL**",
            "🔄 **SECTOR ROTATION DETECTED**",
            "🎯 Hot Sector: Layer1",
            "💰 **Top Coins đang được chú ý:**",
            "📊 **Market Flow Score:"
        ]
        
        missing_features = []
        for feature in enhanced_features:
            if feature not in formatted_message:
                missing_features.append(feature)
        
        if missing_features:
            print(f"\n❌ Missing enhanced features:")
            for feature in missing_features:
                print(f"  - {feature}")
            return False
        else:
            print(f"\n✅ All enhanced features present!")
            
            # Check for coin names
            layer1_coins = ['BTC', 'ETH', 'SOL', 'BNB', 'ADA']
            coins_found = []
            for coin in layer1_coins:
                if coin in formatted_message:
                    coins_found.append(coin)
            
            print(f"✅ Found {len(coins_found)} Layer1 coins in message: {coins_found}")
            
            return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced money flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run enhanced money flow test"""
    print("🧪 === ENHANCED MONEY FLOW WITH TOP COINS TEST ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_enhanced_money_flow_with_coins()
    
    if success:
        print(f"\n🎉 === TEST SUCCESSFUL ===")
        print("\n📋 ENHANCED FEATURES CONFIRMED:")
        print("✅ Sector rotation detection")
        print("✅ Top coins identification in hot sector")
        print("✅ Coin performance analysis (price change + volume)")
        print("✅ Enhanced message formatting with coin details")
        print("✅ Vietnamese language support")
        
        print("\n🌊 ENHANCED SIGNAL FORMAT:")
        print("🔄 SECTOR ROTATION DETECTED")
        print("🎯 Hot Sector: Layer1")
        print("💰 Top Coins đang được chú ý:")
        print("├ 1. **BTC** 📈 +12.0% 🔥 Vol: 3.0x")
        print("├ 2. **ETH** 📈 +8.0% 🔥 Vol: 2.5x")
        print("├ 3. **SOL** 📈 +6.0% 📊 Vol: 2.0x")
        print("└ 🎯 **Đây là những coin hot nhất trong sector**")
        print("📊 Market Flow Score: 0.075 đang được chú ý")
        
    else:
        print(f"\n❌ Test failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
