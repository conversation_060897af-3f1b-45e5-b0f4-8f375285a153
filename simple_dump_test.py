#!/usr/bin/env python3
"""
🚨 SIMPLE DUMP DETECTOR TEST
Quick test to verify dump detector fix
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_dump_detector():
    """Simple test for dump detector"""
    print("🚨 SIMPLE DUMP DETECTOR TEST")
    print("=" * 50)
    
    try:
        print("🔍 Testing DumpDetector import...")
        from dump_detector import DumpDetector
        print("✅ DumpDetector imported successfully")
        
        print("\n🔍 Testing DumpDetector initialization...")
        dump_detector = DumpDetector(
            sensitivity=0.6,
            min_volume_threshold=2.0,
            whale_threshold=50000,
            lookback_period=60
        )
        print("✅ DumpDetector initialized successfully")
        
        print("\n🔍 Testing UltraEarlyDumpDetector import...")
        from dump_detector import UltraEarlyDumpDetector
        print("✅ UltraEarlyDumpDetector imported successfully")
        
        print("\n🔍 Testing UltraEarlyDumpDetector initialization...")
        ultra_detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=0.75,
            pre_dump_lookback=60,
            whale_threshold=100000,
            min_confidence=0.70
        )
        print("✅ UltraEarlyDumpDetector initialized successfully")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Dump detector initialization fix is working")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_dump_detector()
    print(f"\n🎯 Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
