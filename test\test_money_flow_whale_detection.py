#!/usr/bin/env python3
"""
🧪 TEST SUITE: Advanced Money Flow & Whale Detection System
Test các components mới: MoneyFlowAnalyzer, WhaleActivityTracker, MarketManipulationDetector, CrossAssetAnalyzer
"""

import sys
import os
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the new components
from money_flow_analyzer import MoneyFlowAnalyzer
from whale_activity_tracker import WhaleActivityTracker, Whale<PERSON>lert
from market_manipulation_detector import MarketManipulationDetector, ManipulationAlert
from cross_asset_analyzer import CrossAssetAnalyzer

def create_test_ohlcv_data(periods=100, base_price=50000):
    """Create test OHLCV data"""
    dates = pd.date_range(start='2024-01-01', periods=periods, freq='1H')
    
    # Generate realistic price movements
    price_changes = np.random.normal(0, 0.02, periods)  # 2% volatility
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, base_price * 0.5))  # Prevent negative prices
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': dates[i],
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    return pd.DataFrame(data).set_index('timestamp')

def create_test_whale_transactions():
    """Create test whale transaction data"""
    current_time = time.time()
    transactions = []
    
    for i in range(10):
        tx_time = current_time - (i * 3600)  # Last 10 hours
        transactions.append({
            'timestamp': tx_time,
            'type': np.random.choice(['buy', 'sell', 'exchange_deposit', 'exchange_withdrawal']),
            'value_usd': np.random.uniform(100000, 50000000),  # $100K to $50M
            'address': f'whale_address_{i}',
            'exchange': np.random.choice(['binance', 'coinbase', 'kraken'])
        })
    
    return transactions

def create_test_orderbook():
    """Create test orderbook data"""
    base_price = 50000
    
    bids = []
    asks = []
    
    for i in range(20):
        bid_price = base_price * (1 - (i + 1) * 0.001)  # 0.1% steps down
        ask_price = base_price * (1 + (i + 1) * 0.001)  # 0.1% steps up
        
        bid_size = np.random.uniform(0.1, 10.0)
        ask_size = np.random.uniform(0.1, 10.0)
        
        bids.append([bid_price, bid_size])
        asks.append([ask_price, ask_size])
    
    return {
        'bids': bids,
        'asks': asks,
        'timestamp': time.time()
    }

def test_money_flow_analyzer():
    """Test MoneyFlowAnalyzer"""
    print("\n🌊 === TESTING MONEY FLOW ANALYZER ===")
    
    try:
        # Initialize analyzer
        analyzer = MoneyFlowAnalyzer(
            tracking_pairs=['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
            flow_threshold=1000000,
            correlation_window=24
        )
        
        # Create test market data
        market_data = {}
        for pair in ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']:
            market_data[pair] = {
                'ohlcv_data': create_test_ohlcv_data(periods=50),
                'whale_transactions': create_test_whale_transactions()
            }
        
        # Run analysis
        result = analyzer.analyze_market_money_flow(market_data)
        
        # Check results
        assert 'total_flow_score' in result
        assert 'volume_flows' in result
        assert 'correlation_analysis' in result
        assert 'sector_rotation' in result
        
        print(f"✅ Money Flow Analysis completed")
        print(f"  - Total Flow Score: {result['total_flow_score']:.3f}")
        print(f"  - Volume Flows: {len(result['volume_flows'].get('individual_flows', {}))}")
        print(f"  - Top Inflow Coins: {len(result.get('top_inflow_coins', []))}")
        
        # Test signal generation
        signals = analyzer.get_money_flow_signals(result)
        print(f"  - Generated Signals: {len(signals)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Money Flow Analyzer test failed: {e}")
        return False

def test_whale_activity_tracker():
    """Test WhaleActivityTracker"""
    print("\n🐋 === TESTING WHALE ACTIVITY TRACKER ===")
    
    try:
        # Initialize tracker
        tracker = WhaleActivityTracker(
            whale_threshold_small=100000,
            whale_threshold_medium=1000000,
            whale_threshold_large=10000000
        )
        
        # Create test market data
        market_data = {
            'ohlcv_data': create_test_ohlcv_data(),
            'whale_transactions': create_test_whale_transactions(),
            'orderbook_data': create_test_orderbook()
        }
        
        # Run analysis
        whale_alert = tracker.analyze_whale_activity('BTCUSDT', market_data)
        
        if whale_alert:
            print(f"✅ Whale Activity detected")
            print(f"  - Activity Type: {whale_alert.activity_type}")
            print(f"  - Whale Size: {whale_alert.whale_size}")
            print(f"  - Confidence: {whale_alert.confidence:.1%}")
            print(f"  - Risk Level: {whale_alert.risk_level}")
            
            # Test signal generation
            signals = tracker.get_whale_signals(whale_alert)
            print(f"  - Generated Signals: {len(signals)}")
        else:
            print(f"✅ No whale activity detected (normal)")
        
        return True
        
    except Exception as e:
        print(f"❌ Whale Activity Tracker test failed: {e}")
        return False

def test_market_manipulation_detector():
    """Test MarketManipulationDetector"""
    print("\n🕵️ === TESTING MARKET MANIPULATION DETECTOR ===")
    
    try:
        # Initialize detector
        detector = MarketManipulationDetector(
            wash_trading_threshold=0.7,
            spoofing_threshold=0.8,
            pump_dump_threshold=0.75
        )
        
        # Create test market data
        market_data = {
            'ohlcv_data': create_test_ohlcv_data(),
            'orderbook_data': create_test_orderbook(),
            'trade_data': [],  # Empty for now
            'whale_transactions': create_test_whale_transactions()
        }
        
        # Run analysis
        manipulation_alert = detector.detect_manipulation('BTCUSDT', market_data)
        
        if manipulation_alert:
            print(f"✅ Manipulation detected")
            print(f"  - Type: {manipulation_alert.manipulation_type}")
            print(f"  - Confidence: {manipulation_alert.confidence:.1%}")
            print(f"  - Severity: {manipulation_alert.severity}")
            print(f"  - Risk Level: {manipulation_alert.risk_level}")
            
            # Test signal generation
            signals = detector.get_manipulation_signals(manipulation_alert)
            print(f"  - Generated Signals: {len(signals)}")
        else:
            print(f"✅ No manipulation detected (normal)")
        
        return True
        
    except Exception as e:
        print(f"❌ Market Manipulation Detector test failed: {e}")
        return False

def test_cross_asset_analyzer():
    """Test CrossAssetAnalyzer"""
    print("\n🔄 === TESTING CROSS ASSET ANALYZER ===")
    
    try:
        # Initialize analyzer
        analyzer = CrossAssetAnalyzer(
            correlation_window=24,
            rotation_threshold=0.15,
            correlation_threshold=0.7
        )
        
        # Create test market data for multiple assets
        market_data = {}
        for asset in ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT']:
            market_data[asset] = {
                'ohlcv_data': create_test_ohlcv_data(periods=50),
                'whale_transactions': create_test_whale_transactions()
            }
        
        # Run analysis
        result = analyzer.analyze_cross_asset_opportunities(market_data)
        
        # Check results
        assert 'cross_asset_score' in result
        assert 'correlation_analysis' in result
        assert 'rotation_analysis' in result
        assert 'divergence_analysis' in result
        
        print(f"✅ Cross Asset Analysis completed")
        print(f"  - Cross Asset Score: {result['cross_asset_score']:.3f}")
        print(f"  - Correlations: {len(result['correlation_analysis'].get('high_correlations', []))}")
        print(f"  - Divergences: {len(result['divergence_analysis'].get('divergences', []))}")
        print(f"  - Pair Opportunities: {len(result.get('pair_opportunities', []))}")
        
        # Test signal generation
        signals = analyzer.get_cross_asset_signals(result)
        print(f"  - Generated Signals: {len(signals)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cross Asset Analyzer test failed: {e}")
        return False

def test_integration():
    """Test integration of all components"""
    print("\n🔗 === TESTING INTEGRATION ===")
    
    try:
        # Initialize all components
        money_flow = MoneyFlowAnalyzer()
        whale_tracker = WhaleActivityTracker()
        manipulation_detector = MarketManipulationDetector()
        cross_asset = CrossAssetAnalyzer()
        
        # Create comprehensive test data
        market_data = {}
        for pair in ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']:
            market_data[pair] = {
                'ohlcv_data': create_test_ohlcv_data(),
                'whale_transactions': create_test_whale_transactions(),
                'orderbook_data': create_test_orderbook()
            }
        
        # Run all analyses
        money_flow_result = money_flow.analyze_market_money_flow(market_data)
        cross_asset_result = cross_asset.analyze_cross_asset_opportunities(market_data)
        
        # Test individual coin analysis
        for coin in ['BTCUSDT', 'ETHUSDT']:
            whale_alert = whale_tracker.analyze_whale_activity(coin, market_data[coin])
            manipulation_alert = manipulation_detector.detect_manipulation(coin, market_data[coin])
        
        print(f"✅ Integration test completed")
        print(f"  - Money Flow Score: {money_flow_result.get('total_flow_score', 0):.3f}")
        print(f"  - Cross Asset Score: {cross_asset_result.get('cross_asset_score', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 === ADVANCED MONEY FLOW & WHALE DETECTION TEST SUITE ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Money Flow Analyzer", test_money_flow_analyzer),
        ("Whale Activity Tracker", test_whale_activity_tracker),
        ("Market Manipulation Detector", test_market_manipulation_detector),
        ("Cross Asset Analyzer", test_cross_asset_analyzer),
        ("Integration Test", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append({
            'name': test_name,
            'success': success,
            'duration': end_time - start_time
        })
        
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"\n{test_name}: {status} ({end_time - start_time:.2f}s)")
    
    # Summary
    print(f"\n{'='*60}")
    print("🧪 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['name']}: {result['duration']:.2f}s")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Advanced Money Flow & Whale Detection System is ready!")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
