#!/usr/bin/env python3
"""
🚨 ULTRA EARLY DUMP DETECTOR V4.0 - ADVANCED WARNING SYSTEM
===========================================================

Enhanced dump detection with ultra-early warning capabilities:
- 📊 Pre-dump pattern recognition (5-15 minutes advance warning)
- 🔍 Multi-timeframe analysis (1m, 3m, 5m, 15m)
- 🐋 Whale activity monitoring and order flow analysis
- 📈 Volume divergence and accumulation/distribution patterns
- 🎯 Smart money detection and institutional selling pressure
- ⚡ Real-time orderbook monitoring for early signals
- 🧠 Machine learning pattern prediction
- 📱 Instant Telegram alerts with risk levels

Author: Enhanced Trading Bot Team
Version: 4.0 - Ultra Early Detection
License: Proprietary
"""

import numpy as np
import pandas as pd
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

@dataclass
class UltraEarlyDumpAlert:
    """Ultra early dump alert data structure"""
    # Required fields (no default values)
    coin: str
    detection_time: datetime
    dump_probability: float
    risk_level: str
    confidence_score: float
    estimated_dump_time: datetime
    estimated_dump_magnitude: float
    warning_stage: str  # "PRE_DUMP", "EARLY_DUMP", "ACTIVE_DUMP"

    # Enhanced detection metrics
    volume_anomaly_score: float
    orderbook_imbalance: float
    whale_selling_pressure: float
    price_momentum_decay: float
    smart_money_flow: float

    # Technical indicators
    rsi_divergence: float
    macd_bearish_signal: float
    volume_profile_breakdown: float
    support_level_weakness: float

    # Early warning indicators
    pre_dump_signals: List[str]
    institutional_flow: Dict[str, float]
    market_structure_break: bool
    liquidity_drain_detected: bool

    # Risk assessment
    severity_level: int  # 1-10 scale
    estimated_duration: str
    recovery_probability: float
    suggested_action: str

    # Timing estimates
    minutes_to_dump: int
    confidence_window: str

    # Optional fields (with default values) - MUST BE LAST
    current_price: float = 0.0  # ✅ FIXED: Add missing current_price attribute
    indicators: dict = None  # ✅ FIX: Add indicators attribute for compatibility

    def __post_init__(self):
        """Initialize indicators dict if not provided"""
        if self.indicators is None:
            self.indicators = {
                "volume_divergence": getattr(self, 'volume_anomaly_score', 0.0),
                "price_deterioration": getattr(self, 'price_momentum_decay', 0.0),
                "rsi_divergence": getattr(self, 'rsi_divergence', 0.0),
                "macd_bearish": getattr(self, 'macd_bearish_signal', 0.0),
                "orderbook_imbalance": getattr(self, 'orderbook_imbalance', 0.0),
                "whale_selling": getattr(self, 'whale_selling_pressure', 0.0),
                "smart_money_flow": getattr(self, 'smart_money_flow', 0.0),
                "support_weakness": getattr(self, 'support_level_weakness', 0.0),
                "volume_profile_breakdown": getattr(self, 'volume_profile_breakdown', 0.0)
            }

class UltraEarlyDumpDetector:
    """
    🚨 Ultra Early Dump Detector V4.0
    
    Advanced system for detecting market dumps 5-15 minutes before they occur
    using multi-timeframe analysis, whale monitoring, and pattern recognition.
    """
    
    def __init__(self, 
                 ultra_early_sensitivity: float = 0.75,
                 pre_dump_lookback: int = 60,
                 whale_threshold: float = 100000,
                 min_confidence: float = 0.70):
        """
        Initialize Ultra Early Dump Detector
        
        Args:
            ultra_early_sensitivity: Sensitivity for pre-dump detection (0.5-1.0)
            pre_dump_lookback: Minutes to look back for pattern analysis
            whale_threshold: Minimum USD value for whale transactions
            min_confidence: Minimum confidence for alerts
        """
        self.ultra_early_sensitivity = ultra_early_sensitivity
        self.pre_dump_lookback = pre_dump_lookback
        self.whale_threshold = whale_threshold
        self.min_confidence = min_confidence
        
        # Enhanced detection parameters
        self.volume_spike_threshold = 2.0  # 2x normal volume
        self.orderbook_imbalance_threshold = 0.15  # 15% imbalance
        self.rsi_overbought_threshold = 70
        self.price_momentum_threshold = -0.008  # -0.8% momentum decay
        
        # Pre-dump pattern database
        self.pre_dump_patterns = {
            "volume_divergence": {"weight": 0.25, "threshold": 0.7},
            "whale_accumulation_stop": {"weight": 0.20, "threshold": 0.8},
            "orderbook_stacking": {"weight": 0.15, "threshold": 0.75},
            "smart_money_exit": {"weight": 0.20, "threshold": 0.8},
            "technical_breakdown": {"weight": 0.20, "threshold": 0.7}
        }
          # Historical data storage
        self.alert_history: List[UltraEarlyDumpAlert] = []
        self.performance_metrics = {
            "total_alerts": 0,
            "accurate_predictions": 0,
            "false_positives": 0,
            "average_advance_warning": 0
        }        
        print("🚨 Ultra Early Dump Detector V4.0 initialized")
        print(f"  ⚡ Sensitivity: {ultra_early_sensitivity}")
        print(f"  🔍 Lookback: {pre_dump_lookback} minutes")
        print(f"  🐋 Whale threshold: ${whale_threshold:,.0f}")
        print(f"  🎯 Min confidence: {min_confidence:.0%}")

    def analyze_ultra_early_dump_signals(self, 
                                       coin: str,
                                       market_data: Dict[str, Any]) -> Optional[UltraEarlyDumpAlert]:
        """
        🚨 Analyze market data for ultra-early dump signals
        
        Returns dump alert 5-15 minutes before actual dump occurs
        """
        try:
            print(f"\n🚨 ULTRA EARLY DUMP ANALYSIS: {coin}")
            
            # Extract market data
            ohlcv_1m = market_data.get("ohlcv_1m")
            ohlcv_5m = market_data.get("ohlcv_5m") 
            ohlcv_15m = market_data.get("ohlcv_15m")
            orderbook = market_data.get("orderbook_data")
            whale_transactions = market_data.get("whale_transactions", [])
            current_price = market_data.get("current_price", 0)
            
            if not self._validate_market_data(ohlcv_1m, ohlcv_5m, current_price):
                print(f"  ❌ Insufficient market data for {coin} - creating fallback alert")
                # ✅ FIX: Return fallback alert instead of None
                return self._create_fallback_dump_alert(coin, "insufficient_data")
                
            # Ensure data is not None after validation
            if ohlcv_1m is None or ohlcv_5m is None:
                print(f"  ❌ Critical data missing for {coin} - creating fallback alert")
                # ✅ FIX: Return fallback alert instead of None
                return self._create_fallback_dump_alert(coin, "critical_data_missing")
            
            # Multi-stage analysis
            stage_1 = self._analyze_pre_dump_patterns(ohlcv_1m, ohlcv_5m, ohlcv_15m)
            stage_2 = self._analyze_whale_and_smart_money(whale_transactions, ohlcv_1m)
            stage_3 = self._analyze_orderbook_deterioration(orderbook, current_price) if orderbook else {}
            stage_4 = self._analyze_technical_breakdown(ohlcv_5m, ohlcv_15m) if ohlcv_15m is not None else {}
            stage_5 = self._analyze_volume_and_momentum(ohlcv_1m, ohlcv_5m)
            
            # Calculate composite dump probability
            dump_signals = {
                "pre_dump_patterns": stage_1,
                "whale_smart_money": stage_2,
                "orderbook_deterioration": stage_3,
                "technical_breakdown": stage_4,
                "volume_momentum": stage_5
            }
            
            # Enhanced scoring algorithm
            dump_probability = self._calculate_ultra_early_dump_probability(dump_signals)
            confidence_score = self._calculate_confidence_score(dump_signals)
            
            print(f"  📊 Ultra Early Analysis Results:")
            print(f"    🎯 Dump Probability: {dump_probability:.1%}")
            print(f"    💪 Confidence Score: {confidence_score:.1%}")
            
            # Check if alert threshold is met
            if dump_probability >= self.ultra_early_sensitivity and confidence_score >= self.min_confidence:
                
                # Create ultra early dump alert
                alert = self._create_ultra_early_dump_alert(
                    coin, dump_probability, confidence_score, dump_signals, current_price
                )
                  # Store in history
                self.alert_history.append(alert)
                self.performance_metrics["total_alerts"] += 1
                
                print(f"  🚨 ULTRA EARLY DUMP ALERT GENERATED!")
                print(f"    ⏰ Estimated dump in: {alert.minutes_to_dump} minutes")
                print(f"    📉 Estimated magnitude: {alert.estimated_dump_magnitude:.1%}")
                print(f"    🎯 Risk level: {alert.risk_level}")
                
                return alert                
            else:
                print(f"  ✅ No significant early dump signals detected - creating low-risk alert")
                # ✅ FIX: Return low-risk alert instead of None for monitoring purposes
                return self._create_fallback_dump_alert(coin, "no_significant_signals", dump_probability, confidence_score)
                
        except Exception as e:
            print(f"  ❌ Error in ultra early dump analysis: {e} - creating emergency alert")
            # ✅ FIX: Return emergency alert instead of None
            return self._create_fallback_dump_alert(coin, "analysis_error", error=str(e))

    def _validate_market_data(self, ohlcv_1m: Optional[pd.DataFrame], ohlcv_5m: Optional[pd.DataFrame], 
                            current_price: float) -> bool:
        """Validate required market data availability"""
        try:
            if ohlcv_1m is None or len(ohlcv_1m) < 30:
                return False
            if ohlcv_5m is None or len(ohlcv_5m) < 20:
                return False
            if current_price <= 0:
                return False
            return True
        except:
            return False

    def _analyze_pre_dump_patterns(self, ohlcv_1m: pd.DataFrame, 
                                 ohlcv_5m: pd.DataFrame, 
                                 ohlcv_15m: Optional[pd.DataFrame]) -> Dict[str, float]:
        """🔍 Analyze pre-dump patterns across multiple timeframes"""
        try:
            patterns = {}
            
            # 1. Volume divergence analysis
            patterns["volume_divergence"] = self._detect_volume_divergence(ohlcv_1m, ohlcv_5m)
            
            # 2. Price action deterioration
            patterns["price_deterioration"] = self._detect_price_deterioration(ohlcv_5m)
            
            # 3. Momentum decay
            patterns["momentum_decay"] = self._detect_momentum_decay(ohlcv_1m, ohlcv_5m)
            
            # 4. Support level weakness (only if 15m data available)
            if ohlcv_15m is not None and len(ohlcv_15m) > 10:
                patterns["support_weakness"] = self._detect_support_weakness(ohlcv_15m)
            else:
                patterns["support_weakness"] = 0.0
            
            # 5. Distribution patterns
            patterns["distribution_detected"] = self._detect_distribution_patterns(ohlcv_5m)
            
            print(f"    🔍 Pre-dump Patterns:")
            for pattern, score in patterns.items():
                print(f"      - {pattern}: {score:.3f}")
                
            return patterns
            
        except Exception as e:
            print(f"    ❌ Error analyzing pre-dump patterns: {e}")
            return {}

    def _detect_volume_divergence(self, ohlcv_1m: pd.DataFrame, ohlcv_5m: pd.DataFrame) -> float:
        """Detect volume divergence - price up but volume declining"""
        try:
            # Get recent data
            recent_1m = ohlcv_1m.tail(20)
            recent_5m = ohlcv_5m.tail(10)

            if len(recent_1m) < 20:
                return 0.0

            # Calculate price trend (last 10 minutes)
            price_change = (recent_1m['close'].iloc[-1] - recent_1m['close'].iloc[-10]) / recent_1m['close'].iloc[-10]

            # Calculate volume trend (last 10 minutes vs previous 10)
            recent_volume = recent_1m['volume'].tail(10).mean()
            previous_volume = recent_1m['volume'].iloc[-20:-10].mean()
            volume_change = (recent_volume - previous_volume) / previous_volume if previous_volume > 0 else 0

            # Enhanced divergence detection - not just extreme cases
            divergence_score = 0.0

            # Strong divergence: price up but volume down significantly
            if price_change > 0 and volume_change < -0.2:  # Price up 0%+, volume down 20%+
                divergence_score = min(1.0, abs(volume_change) * 2)
                print(f"      🚨 Strong volume divergence: Price +{price_change:.1%}, Volume {volume_change:.1%}")

            # Moderate divergence: price flat/slightly up but volume declining
            elif price_change >= -0.01 and volume_change < -0.1:  # Price flat, volume down 10%+
                divergence_score = min(0.6, abs(volume_change) * 3)
                print(f"      ⚠️ Moderate volume divergence: Price {price_change:.1%}, Volume {volume_change:.1%}")

            # Weak divergence: any negative volume trend with non-negative price
            elif price_change >= 0 and volume_change < -0.05:  # Price up/flat, volume down 5%+
                divergence_score = min(0.3, abs(volume_change) * 4)
                print(f"      📊 Weak volume divergence: Price {price_change:.1%}, Volume {volume_change:.1%}")

            # Even if no divergence, return small score for any volume decline
            elif volume_change < 0:
                divergence_score = min(0.1, abs(volume_change))

            return divergence_score

        except Exception as e:
            print(f"      ❌ Volume divergence error: {e}")
            return 0.0

    def _detect_price_deterioration(self, ohlcv_5m: pd.DataFrame) -> float:
        """Detect gradual price deterioration patterns"""
        try:
            recent_data = ohlcv_5m.tail(12)  # Last hour

            if len(recent_data) < 5:
                return 0.0

            # Calculate successive lower highs
            highs = recent_data['high'].values
            lower_highs = 0
            for i in range(1, len(highs)):
                if highs[i] < highs[i-1]:
                    lower_highs += 1

            # Calculate weakening closes
            closes = recent_data['close'].values
            weak_closes = 0
            for i in range(len(closes)):
                candle_range = recent_data['high'].iloc[i] - recent_data['low'].iloc[i]
                close_position = (closes[i] - recent_data['low'].iloc[i]) / candle_range if candle_range > 0 else 0.5
                if close_position < 0.3:  # Close in lower 30% of candle
                    weak_closes += 1

            # Enhanced deterioration calculation
            lower_highs_ratio = lower_highs / max(1, len(highs) - 1)  # Avoid division by zero
            weak_closes_ratio = weak_closes / len(closes)

            # Base deterioration score
            deterioration_score = (lower_highs_ratio + weak_closes_ratio) / 2

            # Additional factors
            # Check for declining price trend
            price_trend = (closes[-1] - closes[0]) / closes[0] if closes[0] > 0 else 0
            if price_trend < -0.01:  # Declining more than 1%
                deterioration_score += min(0.3, abs(price_trend) * 10)

            # Check for decreasing volume on rallies
            volumes = recent_data['volume'].values
            rally_volume_decline = 0
            for i in range(1, len(closes)):
                if closes[i] > closes[i-1] and volumes[i] < volumes[i-1]:  # Price up, volume down
                    rally_volume_decline += 1

            if rally_volume_decline > 0:
                deterioration_score += min(0.2, rally_volume_decline / len(closes))

            deterioration_score = min(1.0, deterioration_score)

            if deterioration_score > 0.3:  # Lower threshold for reporting
                print(f"      📉 Price deterioration: {deterioration_score:.3f} (Lower highs: {lower_highs}, Weak closes: {weak_closes}, Trend: {price_trend:.1%})")

            return deterioration_score

        except Exception as e:
            print(f"      ❌ Price deterioration error: {e}")
            return 0.0

    def _detect_momentum_decay(self, ohlcv_1m: pd.DataFrame, ohlcv_5m: pd.DataFrame) -> float:
        """Detect momentum decay in buying pressure"""
        try:
            if len(ohlcv_1m) < 20:
                return 0.0

            # 🔧 ENHANCED: RSI momentum analysis with None handling
            closes_1m = ohlcv_1m['close'].tail(20)
            rsi = self._calculate_rsi(closes_1m, period=14)

            # Handle None/NaN RSI values
            if len(rsi) > 0 and not pd.isna(rsi.iloc[-1]):
                current_rsi = float(rsi.iloc[-1])
            else:
                current_rsi = 50.0  # Neutral RSI
                print(f"      ⚠️ RSI calculation failed, using neutral value: {current_rsi}")

            # 🔧 ENHANCED: Price momentum with None/zero handling
            price_momentum = 0.0
            try:
                if len(closes_1m) >= 6:
                    momentum_calc = closes_1m.pct_change(periods=5).iloc[-1]
                else:
                    momentum_calc = closes_1m.pct_change().iloc[-1]

                # Handle None/NaN/inf values
                if pd.isna(momentum_calc) or np.isinf(momentum_calc):
                    price_momentum = 0.0
                    print(f"      ⚠️ Price momentum calculation failed, using 0.0")
                else:
                    price_momentum = float(momentum_calc)
            except Exception as e:
                price_momentum = 0.0
                print(f"      ❌ Price momentum error: {e}")

            # 🔧 ENHANCED: Volume momentum with None/zero handling
            volume_momentum = 0.0
            try:
                volumes = ohlcv_1m['volume'].tail(10)
                if len(volumes) >= 6:
                    vol_momentum_calc = volumes.pct_change(periods=5).iloc[-1]
                else:
                    vol_momentum_calc = volumes.pct_change().iloc[-1]

                # Handle None/NaN/inf values
                if pd.isna(vol_momentum_calc) or np.isinf(vol_momentum_calc):
                    volume_momentum = 0.0
                    print(f"      ⚠️ Volume momentum calculation failed, using 0.0")
                else:
                    volume_momentum = float(vol_momentum_calc)
            except Exception as e:
                volume_momentum = 0.0
                print(f"      ❌ Volume momentum error: {e}")

            # 🔧 ENHANCED: Combine momentum indicators with safe calculations
            momentum_score = 0.0

            # 🔧 ENHANCED: RSI analysis with safe value handling
            try:
                if current_rsi > 60:  # Lowered from 70
                    rsi_decline = max(0.0, (80 - current_rsi) / 20)  # Normalize over wider range
                    rsi_contribution = rsi_decline * 0.3
                    momentum_score += rsi_contribution
                    print(f"      📊 RSI momentum factor: {rsi_decline:.3f} (RSI: {current_rsi:.1f})")
            except Exception as e:
                print(f"      ❌ RSI momentum calculation error: {e}")

            # 🔧 ENHANCED: Price momentum analysis with safe handling
            try:
                if price_momentum != 0.0 and not pd.isna(price_momentum) and not np.isinf(price_momentum):
                    if price_momentum < 0:  # Any negative momentum
                        price_contribution = min(0.4, abs(price_momentum) * 20)  # Scale and weight
                        momentum_score += price_contribution
                        print(f"      📉 Price momentum decay: {price_momentum:.3f}")
            except Exception as e:
                print(f"      ❌ Price momentum analysis error: {e}")

            # 🔧 ENHANCED: Volume momentum analysis with safe handling
            try:
                if volume_momentum != 0.0 and not pd.isna(volume_momentum) and not np.isinf(volume_momentum):
                    if volume_momentum < -0.05:  # Lowered from -0.1
                        volume_contribution = min(0.3, abs(volume_momentum) * 3)
                        momentum_score += volume_contribution
                        print(f"      📊 Volume momentum decay: {volume_momentum:.3f}")
            except Exception as e:
                print(f"      ❌ Volume momentum analysis error: {e}")

            # 🔧 ENHANCED: Additional momentum factors with safe calculations
            # Check for velocity decline with None/zero handling
            try:
                if len(closes_1m) >= 3:
                    recent_price = closes_1m.iloc[-1]
                    prev_price_1 = closes_1m.iloc[-2]
                    prev_price_2 = closes_1m.iloc[-3]

                    # Handle None/NaN values
                    if pd.isna(recent_price) or pd.isna(prev_price_1) or pd.isna(prev_price_2):
                        print(f"      ⚠️ Price data contains NaN values, skipping velocity calculation")
                    else:
                        recent_velocity = abs(float(recent_price) - float(prev_price_1))
                        previous_velocity = abs(float(prev_price_1) - float(prev_price_2))

                        if previous_velocity > 0:
                            velocity_change = (recent_velocity - previous_velocity) / previous_velocity
                            if velocity_change < -0.3:  # 30% velocity decline
                                velocity_contribution = 0.2
                                momentum_score += velocity_contribution
                                print(f"      ⚡ Momentum exhaustion: velocity declined {velocity_change:.1%}")
                        else:
                            print(f"      ⚠️ Previous velocity is zero, cannot calculate velocity change")
            except Exception as e:
                print(f"      ❌ Velocity calculation error: {e}")

            # 🔧 ENHANCED: Ensure momentum score is valid
            try:
                momentum_score = max(0.0, min(1.0, float(momentum_score)))
            except (ValueError, TypeError):
                momentum_score = 0.0
                print(f"      ❌ Invalid momentum score, reset to 0.0")

            if momentum_score > 0.1:  # Lower threshold for reporting
                print(f"      ⚡ Momentum decay detected: {momentum_score:.3f}")

            return momentum_score

        except Exception as e:
            print(f"      ❌ Momentum decay error: {e}")
            return 0.0

    def _detect_support_weakness(self, ohlcv_15m: pd.DataFrame) -> float:
        """Detect weakness in key support levels"""
        try:
            recent_data = ohlcv_15m.tail(20)  # Last 5 hours
            
            # Identify key support levels
            lows = recent_data['low'].values
            support_level = np.min(lows[-10:])  # Recent support
            
            # Check for support tests
            support_tests = 0
            below_support = 0
            
            for low in lows[-5:]:  # Last 5 candles
                if abs(low - support_level) / support_level < 0.005:  # Within 0.5% of support
                    support_tests += 1
                elif low < support_level:
                    below_support += 1
            
            # Calculate weakness score
            weakness_score = 0
            if support_tests >= 2:  # Multiple tests = weakness
                weakness_score += 0.4
            if below_support >= 1:  # Breaking below support
                weakness_score += 0.6
                
            # Check for declining volume on bounces
            if support_tests > 0:
                bounce_volumes = []
                for i in range(len(recent_data)):
                    if abs(recent_data['low'].iloc[i] - support_level) / support_level < 0.01:
                        bounce_volumes.append(recent_data['volume'].iloc[i])
                
                if len(bounce_volumes) >= 2:
                    if bounce_volumes[-1] < bounce_volumes[0] * 0.7:  # 30% volume decline
                        weakness_score += 0.3
            
            weakness_score = min(1.0, weakness_score)

            # ✅ ENHANCED: Add baseline score to avoid 0 values
            if weakness_score == 0:
                weakness_score = 0.02  # Small baseline for market uncertainty

            if weakness_score > 0.1:  # Lower threshold for reporting
                print(f"      🏗️ Support weakness: {weakness_score:.1%} (Tests: {support_tests}, Breaks: {below_support})")

            return weakness_score
            
        except Exception as e:
            print(f"      ❌ Support weakness error: {e}")
            return 0.0

    def _detect_distribution_patterns(self, ohlcv_5m: pd.DataFrame) -> float:
        """Detect smart money distribution patterns"""
        try:
            recent_data = ohlcv_5m.tail(24)  # Last 2 hours
            
            # Calculate On-Balance Volume (OBV)
            obv = self._calculate_obv(recent_data)
            
            # 🔧 ENHANCED: Check for OBV divergence with safe calculations
            try:
                close_current = recent_data['close'].iloc[-1]
                close_past = recent_data['close'].iloc[-12]
                obv_current = obv.iloc[-1]
                obv_past = obv.iloc[-12]

                # Handle None/NaN values
                if pd.isna(close_current) or pd.isna(close_past) or pd.isna(obv_current) or pd.isna(obv_past):
                    price_trend = 0.0
                    obv_trend = 0.0
                    print(f"      ⚠️ Price or OBV data contains NaN values")
                elif close_past == 0:
                    price_trend = 0.0
                    obv_trend = 0.0
                    print(f"      ⚠️ Past close price is zero, cannot calculate trend")
                else:
                    price_trend = (float(close_current) - float(close_past)) / float(close_past)

                    # Safe OBV trend calculation
                    if abs(obv_past) > 1e-10:  # Avoid division by very small numbers
                        obv_trend = (float(obv_current) - float(obv_past)) / abs(float(obv_past))
                    else:
                        obv_trend = 0.0
                        print(f"      ⚠️ Past OBV is too small, cannot calculate trend")

            except Exception as e:
                price_trend = 0.0
                obv_trend = 0.0
                print(f"      ❌ OBV divergence calculation error: {e}")
            
            # Distribution pattern: price stable/up but OBV declining
            distribution_score = 0
            
            if price_trend >= -0.02 and obv_trend < -0.1:  # Price flat/up, OBV down 10%+
                distribution_score = min(1.0, abs(obv_trend) * 2)
                print(f"      📦 Distribution detected: Price {price_trend:.1%}, OBV {obv_trend:.1%}")
            
            # 🔧 ENHANCED: Check for high volume selling on upticks with safe calculations
            uptick_selling = 0
            try:
                for i in range(1, len(recent_data)):
                    current_close = recent_data['close'].iloc[i]
                    prev_close = recent_data['close'].iloc[i-1]
                    current_volume = recent_data['volume'].iloc[i]
                    prev_volume = recent_data['volume'].iloc[i-1]

                    # Handle None/NaN values
                    if pd.isna(current_close) or pd.isna(prev_close) or pd.isna(current_volume) or pd.isna(prev_volume):
                        continue

                    if float(current_close) > float(prev_close):  # Uptick
                        if float(prev_volume) > 0:  # Avoid division by zero
                            volume_ratio = float(current_volume) / float(prev_volume)
                            if volume_ratio > 1.5:  # High volume on uptick
                                uptick_selling += 1
                        else:
                            print(f"      ⚠️ Previous volume is zero at index {i}")
            except Exception as e:
                print(f"      ❌ Uptick selling analysis error: {e}")
            
            if uptick_selling >= 3:  # Multiple high-volume upticks (distribution)
                distribution_score += 0.3

            # ✅ ENHANCED: Add baseline score even if no clear distribution detected
            distribution_score = max(0.02, distribution_score)  # Minimum baseline for uncertainty
            distribution_score = min(1.0, distribution_score)

            if distribution_score > 0.1:  # Lower threshold for reporting
                print(f"      📦 Distribution analysis: {distribution_score:.3f}")

            return distribution_score

        except Exception as e:
            print(f"      ❌ Distribution pattern error: {e}")
            return 0.03  # Return baseline uncertainty score instead of 0

    def _analyze_whale_and_smart_money(self, whale_transactions: List[Dict[str, Any]], 
                                     ohlcv_1m: pd.DataFrame) -> Dict[str, float]:
        """🐋 Analyze whale activity and smart money flows"""
        try:
            whale_signals = {}
            
            # 1. Recent whale selling activity
            whale_signals["whale_selling"] = self._detect_whale_selling(whale_transactions)
            
            # 2. Smart money flow analysis
            whale_signals["smart_money_exit"] = self._detect_smart_money_exit(whale_transactions, ohlcv_1m)
            
            # 3. Institutional selling pressure
            whale_signals["institutional_pressure"] = self._detect_institutional_selling(whale_transactions)
            
            print(f"    🐋 Whale & Smart Money Analysis:")
            for signal, score in whale_signals.items():
                print(f"      - {signal}: {score:.3f}")
                
            return whale_signals
            
        except Exception as e:
            print(f"    ❌ Error analyzing whale activity: {e}")
            return {}

    def _detect_whale_selling(self, whale_transactions: List[Dict[str, Any]]) -> float:
        """Detect recent whale selling activity"""
        try:
            # Even without whale data, return small baseline score
            if not whale_transactions:
                print(f"      📊 No whale transaction data available")
                return 0.05  # Small baseline score instead of 0

            current_time = time.time()
            recent_threshold = current_time - 1800  # Last 30 minutes

            total_whale_selling = 0
            total_whale_buying = 0
            whale_sell_count = 0
            whale_buy_count = 0

            for tx in whale_transactions:
                tx_time = tx.get('timestamp', current_time)
                if tx_time >= recent_threshold:
                    amount = tx.get('amount', 0)
                    side = tx.get('side', 'neutral')  # ✅ ENHANCED: Meaningful default

                    if side == 'sell' and amount >= self.whale_threshold:
                        total_whale_selling += amount
                        whale_sell_count += 1
                    elif side == 'buy' and amount >= self.whale_threshold:
                        total_whale_buying += amount
                        whale_buy_count += 1

            # Calculate selling pressure
            total_volume = total_whale_buying + total_whale_selling
            if total_volume > 0:
                selling_ratio = total_whale_selling / total_volume

                # Weight by number of sell transactions
                sell_pressure = selling_ratio * min(1.0, whale_sell_count / 3)  # Normalize by 3 transactions

                if sell_pressure > 0.2:
                    print(f"      🐋 Whale selling detected: {sell_pressure:.3f} (${total_whale_selling:,.0f})")
                elif whale_sell_count > 0:
                    print(f"      📊 Minor whale selling: {sell_pressure:.3f} ({whale_sell_count} sells)")

                return sell_pressure
            else:
                # No whale activity detected, but return small score for uncertainty
                print(f"      📊 No recent whale activity detected")
                return 0.02

        except Exception as e:
            print(f"      ❌ Whale selling detection error: {e}")
            return 0.03  # Return small score on error

    def _detect_smart_money_exit(self, whale_transactions: List[Dict[str, Any]],
                               ohlcv_1m: pd.DataFrame) -> float:
        """Detect smart money exit patterns"""
        try:
            # ✅ ENHANCED: Alternative analysis when no whale data
            if not whale_transactions or len(ohlcv_1m) < 10:
                # Use price action patterns to detect potential smart money exit
                return self._detect_smart_money_exit_from_price_action(ohlcv_1m)
            
            current_time = time.time()
            recent_threshold = current_time - 900  # Last 15 minutes
            
            smart_money_exits = 0
            total_exits = 0
            
            for tx in whale_transactions:
                tx_time = tx.get('timestamp', current_time)
                if tx_time >= recent_threshold and tx.get('side') == 'sell':
                    total_exits += 1
                    
                    # Find corresponding price action
                    price_at_tx = self._get_price_at_time(ohlcv_1m, tx_time)
                    current_price = ohlcv_1m['close'].iloc[-1]
                    
                    # Smart money = selling before price drops
                    if price_at_tx > current_price * 1.005:  # Sold at 0.5%+ higher price
                        smart_money_exits += 1
            
            if total_exits > 0:
                smart_exit_ratio = smart_money_exits / total_exits
                if smart_exit_ratio > 0.6:  # 60%+ of whale sales were "smart"
                    print(f"      🧠 Smart money exit: {smart_exit_ratio:.1%} ({smart_money_exits}/{total_exits})")
                return smart_exit_ratio
            
            return 0.0
            
        except Exception as e:
            print(f"      ❌ Smart money exit error: {e}")
            return 0.0

    def _detect_institutional_selling(self, whale_transactions: List[Dict[str, Any]]) -> float:
        """Detect coordinated institutional selling"""
        try:
            if not whale_transactions:
                return 0.0
            
            current_time = time.time()
            recent_threshold = current_time - 3600  # Last hour
            
            # Group transactions by time windows (5-minute buckets)
            time_buckets = {}
            
            for tx in whale_transactions:
                tx_time = tx.get('timestamp', current_time)
                if tx_time >= recent_threshold and tx.get('side') == 'sell':
                    bucket = int(tx_time // 300) * 300  # 5-minute buckets
                    if bucket not in time_buckets:
                        time_buckets[bucket] = []
                    time_buckets[bucket].append(tx)
            
            # Look for coordinated selling (multiple large sells in short time)
            coordination_score = 0
            
            for bucket, transactions in time_buckets.items():
                if len(transactions) >= 2:  # Multiple sells in 5 minutes
                    total_amount = sum(tx.get('amount', 0) for tx in transactions)
                    if total_amount >= self.whale_threshold * 3:  # 3x whale threshold
                        coordination_score += min(0.3, len(transactions) * 0.1)
            
            coordination_score = min(1.0, coordination_score)
            
            if coordination_score > 0.3:
                print(f"      🏛️ Institutional selling: {coordination_score:.1%}")
            
            return coordination_score
            
        except Exception as e:
            print(f"      ❌ Institutional selling error: {e}")
            return 0.0

    def _detect_smart_money_exit_from_price_action(self, ohlcv_1m: pd.DataFrame) -> float:
        """✅ ENHANCED: Detect smart money exit patterns from price action when no whale data"""
        try:
            if len(ohlcv_1m) < 20:
                return 0.02  # Baseline uncertainty score

            recent_data = ohlcv_1m.tail(20)
            smart_money_score = 0.0

            # 1. High volume selling at resistance levels
            highs = recent_data['high']
            volumes = recent_data['volume']
            closes = recent_data['close']

            # Find recent high
            recent_high = highs.tail(10).max()
            avg_volume = volumes.tail(20).mean()

            # Check for high volume near recent highs with weak closes
            for i in range(len(recent_data)):
                if highs.iloc[i] >= recent_high * 0.98:  # Within 2% of recent high
                    if volumes.iloc[i] > avg_volume * 1.5:  # High volume
                        # Check if close is weak relative to high
                        close_position = (closes.iloc[i] - recent_data['low'].iloc[i]) / (highs.iloc[i] - recent_data['low'].iloc[i])
                        if close_position < 0.4:  # Closed in lower 40% of range
                            smart_money_score += 0.15
                            print(f"      🧠 Smart money exit pattern: High volume weak close at resistance")

            # 2. Volume divergence at highs
            price_trend = (closes.iloc[-1] - closes.iloc[-10]) / closes.iloc[-10]
            volume_trend = (volumes.tail(5).mean() - volumes.iloc[-15:-10].mean()) / volumes.iloc[-15:-10].mean()

            if price_trend > 0 and volume_trend < -0.1:  # Price up, volume down
                divergence_strength = min(0.3, abs(volume_trend) * 2)
                smart_money_score += divergence_strength
                print(f"      🧠 Volume divergence suggests smart money exit: Price +{price_trend:.1%}, Volume {volume_trend:.1%}")

            # 3. Distribution pattern (higher highs with lower volume)
            if len(highs) >= 10:
                recent_highs = []
                recent_volumes_at_highs = []

                for i in range(5, len(recent_data)):
                    if highs.iloc[i] == highs.iloc[i-2:i+3].max():  # Local high
                        recent_highs.append(highs.iloc[i])
                        recent_volumes_at_highs.append(volumes.iloc[i])

                if len(recent_highs) >= 2:
                    # Check if higher highs have lower volume
                    if recent_highs[-1] > recent_highs[0] and recent_volumes_at_highs[-1] < recent_volumes_at_highs[0]:
                        distribution_score = 0.2
                        smart_money_score += distribution_score
                        print(f"      🧠 Distribution pattern: Higher highs with lower volume")

            # 4. Selling pressure indicators
            # Check for consecutive red candles after volume spikes
            red_candles_after_volume = 0
            for i in range(1, len(recent_data)):
                if volumes.iloc[i-1] > avg_volume * 1.3:  # Volume spike
                    if closes.iloc[i] < closes.iloc[i-1]:  # Red candle after
                        red_candles_after_volume += 1

            if red_candles_after_volume >= 2:
                smart_money_score += 0.1
                print(f"      🧠 Selling pressure: {red_candles_after_volume} red candles after volume spikes")

            # Add baseline uncertainty even if no clear patterns
            smart_money_score = max(0.03, smart_money_score)

            return min(1.0, smart_money_score)

        except Exception as e:
            print(f"      ❌ Smart money price action analysis error: {e}")
            return 0.04  # Return baseline uncertainty score

    def _analyze_orderbook_deterioration(self, orderbook: Dict[str, Any], current_price: float) -> Dict[str, float]:
        """📋 Analyze orderbook for deterioration signals"""
        try:
            # Return baseline scores if no orderbook data
            if not orderbook or 'bids' not in orderbook or 'asks' not in orderbook:
                print(f"    📋 No orderbook data available - using baseline scores")
                return {
                    "orderbook_imbalance": 0.05,  # Small baseline instead of 0
                    "bid_support_weakness": 0.03,
                    "ask_wall_building": 0.02
                }

            orderbook_signals = {}

            # 1. Bid/Ask imbalance analysis
            orderbook_signals["orderbook_imbalance"] = self._calculate_orderbook_imbalance(orderbook)

            # 2. Bid support weakness
            orderbook_signals["bid_support_weakness"] = self._analyze_bid_support_weakness(orderbook, current_price)

            # 3. Ask wall building
            orderbook_signals["ask_wall_building"] = self._detect_ask_wall_building(orderbook, current_price)

            print(f"    📋 Orderbook Analysis:")
            for signal, score in orderbook_signals.items():
                print(f"      - {signal}: {score:.3f}")

            return orderbook_signals

        except Exception as e:
            print(f"    ❌ Error analyzing orderbook: {e}")
            # Return small baseline scores on error
            return {
                "orderbook_imbalance": 0.04,
                "bid_support_weakness": 0.03,
                "ask_wall_building": 0.02
            }

    def _calculate_orderbook_imbalance(self, orderbook: Dict) -> float:
        """Calculate bid/ask imbalance"""
        try:
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])

            if not bids or not asks:
                print(f"      📊 Empty orderbook - returning baseline imbalance")
                return 0.1  # Baseline score instead of 0

            # Calculate total bid and ask volumes
            bid_volume = sum(float(bid[1]) for bid in bids[:20])  # Top 20 bids
            ask_volume = sum(float(ask[1]) for ask in asks[:20])  # Top 20 asks

            total_volume = bid_volume + ask_volume
            if total_volume == 0:
                return 0.08  # Small score for empty volume

            # Calculate imbalance (negative = more asks than bids = bearish)
            imbalance = (bid_volume - ask_volume) / total_volume

            # Convert to dump signal (higher when more asks)
            dump_signal = max(0, -imbalance * 2)  # Amplify ask dominance

            # Add baseline score even for balanced orderbook
            dump_signal = max(0.02, dump_signal)  # Minimum baseline

            if dump_signal > 0.2:  # Lower threshold for reporting
                print(f"      📋 Orderbook imbalance: {dump_signal:.1%} (Bid: {bid_volume:,.0f}, Ask: {ask_volume:,.0f})")
            
            return min(1.0, dump_signal)
            
        except Exception as e:
            print(f"      ❌ Orderbook imbalance error: {e}")
            return 0.0

    def _analyze_bid_support_weakness(self, orderbook: Dict, current_price: float) -> float:
        """Analyze weakness in bid support levels"""
        try:
            bids = orderbook.get('bids', [])
            if not bids:
                return 0.0
            
            # Analyze bid depth at key levels
            support_levels = [0.995, 0.99, 0.985, 0.98, 0.975]  # -0.5%, -1%, -1.5%, -2%, -2.5%
            
            total_support = 0
            weak_support_levels = 0
            
            for level in support_levels:
                target_price = current_price * level
                support_at_level = 0
                
                for bid in bids:
                    bid_price = float(bid[0])
                    bid_volume = float(bid[1])
                    
                    if bid_price >= target_price:
                        support_at_level += bid_volume * bid_price  # USD value
                
                total_support += support_at_level
                
                # Weak support = less than $10k at each level
                if support_at_level < 10000:
                    weak_support_levels += 1
            
            # Calculate weakness score
            weakness_ratio = weak_support_levels / len(support_levels)
            
            if weakness_ratio > 0.6:
                print(f"      🏗️ Bid support weakness: {weakness_ratio:.1%} ({weak_support_levels}/{len(support_levels)} levels weak)")
            
            return weakness_ratio
            
        except Exception as e:
            print(f"      ❌ Bid support analysis error: {e}")
            return 0.0

    def _detect_ask_wall_building(self, orderbook: Dict, current_price: float) -> float:
        """Detect building of ask walls above current price"""
        try:
            asks = orderbook.get('asks', [])
            if not asks:
                return 0.0
            
            # Check for unusual ask concentration
            resistance_levels = [1.005, 1.01, 1.015, 1.02, 1.025]  # +0.5%, +1%, +1.5%, +2%, +2.5%
            
            wall_score = 0
            
            for level in resistance_levels:
                target_price = current_price * level
                ask_volume_at_level = 0
                
                # Sum asks within 0.1% of target level
                for ask in asks:
                    ask_price = float(ask[0])
                    ask_volume = float(ask[1])
                    
                    if abs(ask_price - target_price) / target_price < 0.001:  # Within 0.1%
                        ask_volume_at_level += ask_volume * ask_price  # USD value
                
                # Large ask wall = $50k+ at level
                if ask_volume_at_level > 50000:
                    wall_score += 0.2
                    print(f"      🧱 Ask wall at +{(level-1)*100:.1f}%: ${ask_volume_at_level:,.0f}")
            
            return min(1.0, wall_score)
            
        except Exception as e:
            print(f"      ❌ Ask wall detection error: {e}")
            return 0.0

    def _analyze_technical_breakdown(self, ohlcv_5m: pd.DataFrame, ohlcv_15m: pd.DataFrame) -> Dict[str, float]:
        """📈 Analyze technical indicators for breakdown signals"""
        try:
            technical_signals = {}

            # 1. RSI divergence and overbought conditions
            technical_signals["rsi_signals"] = self._analyze_rsi_signals(ohlcv_5m)

            # 2. MACD bearish signals
            if ohlcv_15m is not None and len(ohlcv_15m) > 20:
                technical_signals["macd_bearish"] = self._analyze_macd_bearish(ohlcv_15m)
            else:
                technical_signals["macd_bearish"] = 0.05  # Baseline score

            # 3. Moving average breakdown
            if ohlcv_15m is not None and len(ohlcv_15m) > 20:
                technical_signals["ma_breakdown"] = self._analyze_ma_breakdown(ohlcv_15m)
            else:
                technical_signals["ma_breakdown"] = 0.03  # Baseline score

            # 4. Bollinger Band signals
            technical_signals["bb_signals"] = self._analyze_bollinger_signals(ohlcv_5m)

            print(f"    📈 Technical Analysis:")
            for signal, score in technical_signals.items():
                print(f"      - {signal}: {score:.3f}")

            # Add additional context
            if technical_signals["bb_signals"] > 0.3:
                print(f"      📊 Price near Bollinger upper band")

            return technical_signals

        except Exception as e:
            print(f"    ❌ Error in technical analysis: {e}")
            # Return baseline scores on error
            return {
                "rsi_signals": 0.05,
                "macd_bearish": 0.04,
                "ma_breakdown": 0.03,
                "bb_signals": 0.06
            }

    def _analyze_rsi_signals(self, ohlcv_5m: pd.DataFrame) -> float:
        """Analyze RSI for overbought and divergence signals"""
        try:
            closes = ohlcv_5m['close']
            rsi = self._calculate_rsi(closes, period=14)
            
            if len(rsi) < 10:
                return 0.0
            
            current_rsi = rsi.iloc[-1]
            rsi_signal = 0
            
            # Overbought condition
            if current_rsi > self.rsi_overbought_threshold:
                overbought_score = (current_rsi - 70) / 30  # Normalize 70-100 to 0-1
                rsi_signal += overbought_score * 0.4
            
            # RSI bearish divergence
            price_change = (closes.iloc[-1] - closes.iloc[-10]) / closes.iloc[-10]
            rsi_change = (rsi.iloc[-1] - rsi.iloc[-10]) / rsi.iloc[-10]
            
            if price_change > 0 and rsi_change < -0.05:  # Price up, RSI down 5%+
                divergence_score = min(1.0, abs(rsi_change) * 10)
                rsi_signal += divergence_score * 0.6
                print(f"      📊 RSI bearish divergence: Price +{price_change:.1%}, RSI {rsi_change:.1%}")
            
            return min(1.0, rsi_signal)
            
        except Exception as e:
            print(f"      ❌ RSI analysis error: {e}")
            return 0.0

    def _analyze_macd_bearish(self, ohlcv_15m: pd.DataFrame) -> float:
        """Analyze MACD for bearish signals"""
        try:
            closes = ohlcv_15m['close']
            
            if len(closes) < 35:
                return 0.0
            
            # Calculate MACD
            exp12 = closes.ewm(span=12).mean()
            exp26 = closes.ewm(span=26).mean()
            macd_line = exp12 - exp26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line
            
            macd_signal = 0
            
            # MACD bearish crossover
            if len(macd_line) >= 2 and len(signal_line) >= 2:
                if macd_line.iloc[-2] > signal_line.iloc[-2] and macd_line.iloc[-1] < signal_line.iloc[-1]:
                    macd_signal += 0.5
                    print(f"      📊 MACD bearish crossover detected")
            
            # MACD histogram declining
            if len(histogram) >= 5:
                recent_histogram = histogram.tail(5)
                if all(recent_histogram.iloc[i] < recent_histogram.iloc[i-1] for i in range(1, len(recent_histogram))):
                    macd_signal += 0.3
                    print(f"      📊 MACD histogram declining")
            
            # MACD below zero and declining
            if macd_line.iloc[-1] < 0 and macd_line.iloc[-1] < macd_line.iloc[-3]:
                macd_signal += 0.2
            
            return min(1.0, macd_signal)
            
        except Exception as e:
            print(f"      ❌ MACD analysis error: {e}")
            return 0.0

    def _analyze_ma_breakdown(self, ohlcv_15m: pd.DataFrame) -> float:
        """Analyze moving average breakdown"""
        try:
            closes = ohlcv_15m['close']
            
            if len(closes) < 50:
                return 0.0
            
            # Calculate moving averages
            ma20 = closes.rolling(window=20).mean()
            ma50 = closes.rolling(window=50).mean()
            
            current_price = closes.iloc[-1]
            ma_signal = 0
            
            # Price below key MAs
            if current_price < ma20.iloc[-1]:
                ma_signal += 0.3
            
            if current_price < ma50.iloc[-1]:
                ma_signal += 0.2
            
            # MA bearish crossover
            if ma20.iloc[-1] < ma50.iloc[-1] and ma20.iloc[-2] >= ma50.iloc[-2]:
                ma_signal += 0.5
                print(f"      📊 MA bearish crossover: MA20 below MA50")
            
            return min(1.0, ma_signal)
            
        except Exception as e:
            print(f"      ❌ MA analysis error: {e}")
            return 0.0

    def _analyze_bollinger_signals(self, ohlcv_5m: pd.DataFrame) -> float:
        """Analyze Bollinger Bands for breakdown signals"""
        try:
            closes = ohlcv_5m['close']

            if len(closes) < 20:
                return 0.08  # Baseline score instead of 0

            # Calculate Bollinger Bands
            ma20 = closes.rolling(window=20).mean()
            std20 = closes.rolling(window=20).std()
            bb_upper = ma20 + (std20 * 2)
            bb_lower = ma20 - (std20 * 2)
            bb_middle = ma20

            current_price = closes.iloc[-1]
            bb_signal = 0.05  # Start with baseline

            # Calculate position within bands
            if not pd.isna(bb_upper.iloc[-1]) and not pd.isna(bb_lower.iloc[-1]):
                band_width = bb_upper.iloc[-1] - bb_lower.iloc[-1]
                if band_width > 0:
                    position_in_band = (current_price - bb_lower.iloc[-1]) / band_width

                    # Price at upper band (potential reversal)
                    if position_in_band >= 0.95:  # In top 5% of band
                        bb_signal += 0.4
                        print(f"      📊 Price at Bollinger upper band ({position_in_band:.1%})")
                    elif position_in_band >= 0.85:  # In top 15% of band
                        bb_signal += 0.25
                        print(f"      📊 Price near Bollinger upper band ({position_in_band:.1%})")
                    elif position_in_band >= 0.7:  # Above middle
                        bb_signal += 0.15
                        print(f"      📊 Price in upper Bollinger range ({position_in_band:.1%})")
                    else:
                        bb_signal += 0.05  # Small signal for any position

            # Check for band squeeze (low volatility before breakout)
            if len(std20) >= 10:
                current_std = std20.iloc[-1]
                avg_std = std20.tail(10).mean()
                if current_std < avg_std * 0.8:  # 20% below average volatility
                    bb_signal += 0.1
                    print(f"      📊 Bollinger band squeeze detected")

            # Check for price rejection at upper band
            highs = ohlcv_5m['high'].tail(5)
            for i, high in enumerate(highs):
                if not pd.isna(bb_upper.iloc[-(5-i)]) and high >= bb_upper.iloc[-(5-i)] * 0.99:
                    if closes.iloc[-(5-i)] < high * 0.98:  # Closed below high
                        bb_signal += 0.1
                        print(f"      📊 Upper band rejection detected")
                        break
            
            # Band squeeze followed by expansion
            recent_band_width = (bb_upper - bb_lower) / ma20
            if len(recent_band_width) >= 10:
                current_width = recent_band_width.iloc[-1]
                avg_width = recent_band_width.tail(10).mean()
                
                if current_width > avg_width * 1.2:  # 20% wider than average
                    bb_signal += 0.3
            
            return min(1.0, bb_signal)
            
        except Exception as e:
            print(f"      ❌ Bollinger Bands error: {e}")
            return 0.0

    def _analyze_volume_and_momentum(self, ohlcv_1m: pd.DataFrame, ohlcv_5m: pd.DataFrame) -> Dict[str, float]:
        """📊 Analyze volume patterns and momentum decay"""
        try:
            volume_signals = {}
            
            # 1. Volume spike without price follow-through
            volume_signals["volume_no_followthrough"] = self._detect_volume_no_followthrough(ohlcv_1m)
            
            # 2. Declining volume on rallies
            volume_signals["declining_rally_volume"] = self._detect_declining_rally_volume(ohlcv_5m)
            
            # 3. Momentum exhaustion
            volume_signals["momentum_exhaustion"] = self._detect_momentum_exhaustion(ohlcv_1m, ohlcv_5m)
            
            print(f"    📊 Volume & Momentum Analysis:")
            for signal, score in volume_signals.items():
                print(f"      - {signal}: {score:.3f}")
                
            return volume_signals
            
        except Exception as e:
            print(f"    ❌ Error analyzing volume/momentum: {e}")
            return {}

    def _detect_volume_no_followthrough(self, ohlcv_1m: pd.DataFrame) -> float:
        """Detect high volume without price follow-through"""
        try:
            recent_data = ohlcv_1m.tail(15)  # Last 15 minutes

            if len(recent_data) < 10:
                return 0.03  # ✅ ENHANCED: Return baseline uncertainty instead of 0
            
            # Find volume spikes
            avg_volume = recent_data['volume'].rolling(window=10).mean()
            volume_spikes = recent_data['volume'] > avg_volume * 2  # 2x volume spike
            
            no_followthrough_score = 0
            
            for i in range(len(recent_data)):
                if volume_spikes.iloc[i]:
                    # Check price action after volume spike
                    current_close = recent_data['close'].iloc[i]
                    
                    # Check next 3 candles for follow-through
                    if i < len(recent_data) - 3:
                        future_closes = recent_data['close'].iloc[i+1:i+4]
                        max_gain = (future_closes.max() - current_close) / current_close
                        
                        # High volume but low price gain = distribution
                        if max_gain < 0.003:  # Less than 0.3% gain
                            no_followthrough_score += 0.3
                            print(f"      📊 Volume spike without follow-through at {current_close:.6f}")
            
            # ✅ ENHANCED: Add baseline score even if no clear patterns
            no_followthrough_score = max(0.02, no_followthrough_score)  # Minimum baseline
            return min(1.0, no_followthrough_score)

        except Exception as e:
            print(f"      ❌ Volume follow-through error: {e}")
            return 0.03  # Return baseline uncertainty instead of 0

    def _detect_declining_rally_volume(self, ohlcv_5m: pd.DataFrame) -> float:
        """Detect declining volume on price rallies"""
        try:
            recent_data = ohlcv_5m.tail(12)  # Last hour

            if len(recent_data) < 8:
                return 0.03  # ✅ ENHANCED: Return baseline uncertainty instead of 0
            
            # Identify rally periods (consecutive higher closes)
            rally_periods = []
            current_rally = []
            
            for i in range(1, len(recent_data)):
                if recent_data['close'].iloc[i] > recent_data['close'].iloc[i-1]:
                    current_rally.append(i)
                else:
                    if len(current_rally) >= 2:  # Rally of at least 2 candles
                        rally_periods.append(current_rally)
                    current_rally = []
            
            # Check last rally if ongoing
            if len(current_rally) >= 2:
                rally_periods.append(current_rally)
            
            declining_volume_score = 0
            
            for rally in rally_periods:
                if len(rally) >= 3:  # Rally of at least 3 candles
                    rally_volumes = [recent_data['volume'].iloc[i] for i in rally]
                    
                    # Check if volume is declining during rally
                    volume_declining = True
                    for i in range(1, len(rally_volumes)):
                        if rally_volumes[i] >= rally_volumes[i-1]:
                            volume_declining = False
                            break
                    
                    if volume_declining:
                        declining_volume_score += 0.4
                        print(f"      📊 Declining volume on rally: {len(rally)} candles")
            
            # ✅ ENHANCED: Add baseline score even if no clear patterns
            declining_volume_score = max(0.02, declining_volume_score)  # Minimum baseline
            return min(1.0, declining_volume_score)

        except Exception as e:
            print(f"      ❌ Rally volume error: {e}")
            return 0.03  # Return baseline uncertainty instead of 0

    def _detect_momentum_exhaustion(self, ohlcv_1m: pd.DataFrame, ohlcv_5m: pd.DataFrame) -> float:
        """Detect momentum exhaustion patterns"""
        try:
            # Rate of Change analysis
            closes_1m = ohlcv_1m['close']
            closes_5m = ohlcv_5m['close']

            if len(closes_1m) < 20 or len(closes_5m) < 10:
                return 0.04  # ✅ ENHANCED: Return baseline uncertainty instead of 0
            
            # Calculate momentum indicators
            roc_1m = closes_1m.pct_change(periods=5)  # 5-minute ROC on 1m chart
            roc_5m = closes_5m.pct_change(periods=3)  # 15-minute ROC on 5m chart
            
            momentum_exhaustion = 0
            
            # Declining rate of change
            if len(roc_1m) >= 5:
                recent_roc_1m = roc_1m.tail(5)
                if recent_roc_1m.iloc[-1] < recent_roc_1m.iloc[-3] and recent_roc_1m.iloc[-1] > 0:
                    momentum_exhaustion += 0.3
            
            if len(roc_5m) >= 3:
                recent_roc_5m = roc_5m.tail(3)
                if recent_roc_5m.iloc[-1] < recent_roc_5m.iloc[-2] and recent_roc_5m.iloc[-1] > 0:
                    momentum_exhaustion += 0.4
            
            # Check for velocity slowdown
            price_velocity = abs(closes_1m.pct_change())
            if len(price_velocity) >= 10:
                recent_velocity = price_velocity.tail(5).mean()
                previous_velocity = price_velocity.iloc[-10:-5].mean()
                
                if recent_velocity < previous_velocity * 0.7:  # 30% velocity decline
                    momentum_exhaustion += 0.3
                    print(f"      ⚡ Momentum exhaustion: velocity declined {((recent_velocity/previous_velocity-1)*100):.1f}%")
            
            # ✅ ENHANCED: Add baseline score even if no clear patterns
            momentum_exhaustion = max(0.03, momentum_exhaustion)  # Minimum baseline
            return min(1.0, momentum_exhaustion)

        except Exception as e:
            print(f"      ❌ Momentum exhaustion error: {e}")
            return 0.04  # Return baseline uncertainty instead of 0

    def _calculate_ultra_early_dump_probability(self, dump_signals: Dict[str, Dict[str, float]]) -> float:
        """🔧 ENHANCED: Calculate composite ultra-early dump probability with safe handling"""
        try:
            # Advanced weighted scoring
            weights = {
                "pre_dump_patterns": 0.30,
                "whale_smart_money": 0.25,
                "orderbook_deterioration": 0.20,
                "technical_breakdown": 0.15,
                "volume_momentum": 0.10
            }

            total_score = 0.0
            total_weight = 0.0

            for category, signals in dump_signals.items():
                if category in weights:
                    category_weight = weights[category]

                    # 🔧 ENHANCED: Calculate category score with None/empty handling
                    if signals and isinstance(signals, dict):
                        # Filter out None values and ensure all values are numeric
                        valid_values = []
                        for signal_name, score in signals.items():
                            try:
                                if score is not None and not pd.isna(score) and not np.isinf(score):
                                    numeric_score = float(score)
                                    if 0.0 <= numeric_score <= 1.0:  # Valid range
                                        valid_values.append(numeric_score)
                                    else:
                                        print(f"      ⚠️ {category}.{signal_name}: score {score} out of range [0,1]")
                                else:
                                    print(f"      ⚠️ {category}.{signal_name}: invalid score {score}")
                            except (ValueError, TypeError) as e:
                                print(f"      ❌ {category}.{signal_name}: conversion error {e}")

                        if valid_values:
                            category_score = np.mean(valid_values)
                            weighted_score = category_score * category_weight
                            total_score += weighted_score
                            total_weight += category_weight
                        else:
                            print(f"      ⚠️ {category}: no valid signals, using baseline score")
                            # Use small baseline score for categories with no valid signals
                            baseline_score = 0.02 * category_weight
                            total_score += baseline_score
                            total_weight += category_weight
                    else:
                        print(f"      ⚠️ {category}: empty or invalid signals dict")
                        # Use small baseline score for empty categories
                        baseline_score = 0.02 * category_weight
                        total_score += baseline_score
                        total_weight += category_weight
            
            # Normalize
            if total_weight > 0:
                base_probability = total_score / total_weight
            else:
                base_probability = 0.0
            
            # Apply sensitivity adjustment
            adjusted_probability = base_probability * (0.5 + self.ultra_early_sensitivity)
            
            return min(1.0, adjusted_probability)
            
        except Exception as e:
            print(f"    ❌ Error calculating dump probability: {e}")
            return 0.0

    def _calculate_confidence_score(self, dump_signals: Dict[str, Dict[str, float]]) -> float:
        """🔧 ENHANCED: Calculate confidence score with safe handling"""
        try:
            # Count non-zero signals with safe handling
            total_signals = 0
            active_signals = 0

            for category, signals in dump_signals.items():
                if signals and isinstance(signals, dict):
                    for signal_name, score in signals.items():
                        try:
                            # Validate score
                            if score is not None and not pd.isna(score) and not np.isinf(score):
                                numeric_score = float(score)
                                total_signals += 1
                                if numeric_score > 0.3:  # Signal threshold
                                    active_signals += 1
                        except (ValueError, TypeError):
                            print(f"      ⚠️ Invalid score for {category}.{signal_name}: {score}")
                            continue

            if total_signals == 0:
                print(f"      ⚠️ No valid signals found for confidence calculation")
                return 0.0

            # Base confidence from signal coverage
            signal_coverage = active_signals / total_signals

            # 🔧 ENHANCED: Boost confidence if multiple categories are active with safe handling
            active_categories = 0
            total_categories = len(dump_signals)

            for signals in dump_signals.values():
                if signals and isinstance(signals, dict):
                    category_has_active_signal = False
                    for score in signals.values():
                        try:
                            if score is not None and not pd.isna(score) and not np.isinf(score):
                                if float(score) > 0.3:
                                    category_has_active_signal = True
                                    break
                        except (ValueError, TypeError):
                            continue
                    if category_has_active_signal:
                        active_categories += 1

            if total_categories > 0:
                category_diversity = active_categories / total_categories
            else:
                category_diversity = 0.0

            # Combined confidence with bounds checking
            confidence = (signal_coverage * 0.7) + (category_diversity * 0.3)
            confidence = max(0.0, min(1.0, confidence))  # Ensure valid range

            return confidence

        except Exception as e:
            print(f"    ❌ Error calculating confidence: {e}")
            return 0.0

    def _create_ultra_early_dump_alert(self, coin: str, dump_probability: float, 
                                     confidence_score: float, dump_signals: Dict[str, Dict[str, float]],
                                     current_price: float) -> UltraEarlyDumpAlert:
        """Create comprehensive ultra-early dump alert"""
        try:
            current_time = datetime.now()
            
            # Estimate dump timing based on signal strength
            minutes_to_dump = self._estimate_dump_timing(dump_probability, dump_signals)
            estimated_dump_time = current_time + timedelta(minutes=minutes_to_dump)
            
            # Estimate dump magnitude
            estimated_magnitude = self._estimate_dump_magnitude(dump_probability, dump_signals)
            
            # Determine risk level
            risk_level = self._determine_risk_level(dump_probability, confidence_score)
            
            # Determine warning stage
            warning_stage = self._determine_warning_stage(minutes_to_dump, dump_probability)
            
            # Extract signal details
            pre_dump_signals = self._extract_pre_dump_signals(dump_signals)
            
            # Create alert
            alert = UltraEarlyDumpAlert(
                coin=coin,
                detection_time=current_time,
                dump_probability=dump_probability,
                risk_level=risk_level,
                confidence_score=confidence_score,
                estimated_dump_time=estimated_dump_time,
                estimated_dump_magnitude=estimated_magnitude,
                warning_stage=warning_stage,
                current_price=current_price,  # ✅ FIXED: Add current_price
                
                # 🔧 ENHANCED: Enhanced metrics with safe value extraction
                volume_anomaly_score=self._safe_mean_extraction(dump_signals.get("volume_momentum", {})),
                orderbook_imbalance=self._safe_mean_extraction(dump_signals.get("orderbook_deterioration", {})),
                whale_selling_pressure=self._safe_mean_extraction(dump_signals.get("whale_smart_money", {})),
                price_momentum_decay=self._safe_value_extraction(dump_signals.get("pre_dump_patterns", {}), "momentum_decay", 0.0),
                smart_money_flow=self._safe_value_extraction(dump_signals.get("whale_smart_money", {}), "smart_money_exit", 0.0),

                # 🔧 ENHANCED: Technical indicators with safe extraction
                rsi_divergence=self._safe_value_extraction(dump_signals.get("technical_breakdown", {}), "rsi_signals", 0.0),
                macd_bearish_signal=self._safe_value_extraction(dump_signals.get("technical_breakdown", {}), "macd_bearish", 0.0),
                volume_profile_breakdown=self._safe_value_extraction(dump_signals.get("volume_momentum", {}), "volume_no_followthrough", 0.0),
                support_level_weakness=self._safe_value_extraction(dump_signals.get("pre_dump_patterns", {}), "support_weakness", 0.0),
                
                # Early warning indicators
                pre_dump_signals=pre_dump_signals,
                institutional_flow=self._calculate_institutional_flow(dump_signals),
                market_structure_break=dump_probability > 0.8,
                liquidity_drain_detected=dump_signals.get("orderbook_deterioration", {}).get("bid_support_weakness", 0) > 0.7,
                
                # Risk assessment
                severity_level=min(10, int(dump_probability * 10) + 1),
                estimated_duration=self._estimate_dump_duration(estimated_magnitude),
                recovery_probability=self._estimate_recovery_probability(dump_signals),
                suggested_action=self._suggest_action(risk_level, dump_probability),
                
                # Timing
                minutes_to_dump=minutes_to_dump,
                confidence_window=f"{minutes_to_dump-2}-{minutes_to_dump+5} minutes"
            )
            
            return alert
            
        except Exception as e:
            print(f"    ❌ Error creating dump alert: {e} - creating emergency fallback")
            # ✅ FIX: Return emergency fallback instead of None
            return self._create_fallback_dump_alert(coin, "alert_creation_error", error=str(e))

    def _safe_mean_extraction(self, signals_dict: Dict[str, float]) -> float:
        """🔧 ENHANCED: Safely extract mean value from signals dictionary"""
        try:
            if not signals_dict or not isinstance(signals_dict, dict):
                return 0.0

            valid_values = []
            for value in signals_dict.values():
                try:
                    if value is not None and not pd.isna(value) and not np.isinf(value):
                        numeric_value = float(value)
                        if 0.0 <= numeric_value <= 1.0:  # Valid range
                            valid_values.append(numeric_value)
                except (ValueError, TypeError):
                    continue

            if valid_values:
                return np.mean(valid_values)
            else:
                return 0.0

        except Exception as e:
            print(f"      ❌ Safe mean extraction error: {e}")
            return 0.0

    def _safe_value_extraction(self, signals_dict: Dict[str, float], key: str, default: float = 0.0) -> float:
        """🔧 ENHANCED: Safely extract specific value from signals dictionary"""
        try:
            if not signals_dict or not isinstance(signals_dict, dict):
                return default

            value = signals_dict.get(key, default)

            if value is None or pd.isna(value) or np.isinf(value):
                return default

            try:
                numeric_value = float(value)
                if 0.0 <= numeric_value <= 1.0:  # Valid range
                    return numeric_value
                else:
                    print(f"      ⚠️ Value {key}={value} out of range [0,1], using default")
                    return default
            except (ValueError, TypeError):
                print(f"      ⚠️ Cannot convert {key}={value} to float, using default")
                return default

        except Exception as e:
            print(f"      ❌ Safe value extraction error for {key}: {e}")
            return default

    def _estimate_dump_timing(self, dump_probability: float, dump_signals: Dict) -> int:
        """Estimate how many minutes until dump occurs"""
        try:
            # Base timing on signal urgency
            base_minutes = 10  # Default 10 minutes
            
            # Urgent signals reduce time
            urgency_factors = {
                "orderbook_deterioration": -3,  # Very urgent
                "whale_smart_money": -2,        # Urgent
                "volume_momentum": -1,          # Somewhat urgent
                "technical_breakdown": 0,       # Less urgent
                "pre_dump_patterns": 1          # Early warning
            }
            
            timing_adjustment = 0
            for category, signals in dump_signals.items():
                if category in urgency_factors and signals:
                    category_strength = np.mean(list(signals.values()))
                    timing_adjustment += urgency_factors[category] * category_strength
            
            # High probability = sooner
            probability_adjustment = -5 * (dump_probability - 0.5)  # -2.5 to +2.5 minutes
            
            estimated_minutes = base_minutes + timing_adjustment + probability_adjustment
            
            # Clamp to reasonable range
            return max(2, min(20, int(estimated_minutes)))
            
        except Exception as e:
            print(f"    ❌ Error estimating timing: {e}")
            return 10

    def _estimate_dump_magnitude(self, dump_probability: float, dump_signals: Dict) -> float:
        """Estimate expected dump magnitude as percentage"""
        try:
            # Base magnitude on probability
            base_magnitude = 0.03 + (dump_probability * 0.15)  # 3-18% range
            
            # Adjust based on signal types
            magnitude_multipliers = {
                "whale_smart_money": 1.5,      # Large whale selling = bigger dumps
                "orderbook_deterioration": 1.3, # Liquidity issues = bigger moves
                "technical_breakdown": 1.2,     # Technical breaks = follow-through
                "volume_momentum": 1.1,         # Volume confirms = more conviction
                "pre_dump_patterns": 1.0        # Early signals = base magnitude
            }
            
            total_multiplier = 1.0
            active_categories = 0
            
            for category, signals in dump_signals.items():
                if category in magnitude_multipliers and signals:
                    category_strength = np.mean(list(signals.values()))
                    if category_strength > 0.3:
                        total_multiplier *= (1 + (magnitude_multipliers[category] - 1) * category_strength)
                        active_categories += 1
            
            # Apply multiplier
            estimated_magnitude = base_magnitude * total_multiplier
            
            # Clamp to reasonable range
            return min(0.5, max(0.02, estimated_magnitude))  # 2-50% range
            
        except Exception as e:
            print(f"    ❌ Error estimating magnitude: {e}")
            return 0.05

    def _determine_risk_level(self, dump_probability: float, confidence_score: float) -> str:
        """Determine risk level based on probability and confidence"""
        try:
            combined_score = (dump_probability + confidence_score) / 2
            
            if combined_score >= 0.85:
                return "CRITICAL"
            elif combined_score >= 0.75:
                return "HIGH"
            elif combined_score >= 0.65:
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            return "UNKNOWN"

    def _determine_warning_stage(self, minutes_to_dump: int, dump_probability: float) -> str:
        """Determine the warning stage"""
        try:
            if minutes_to_dump <= 3:
                return "ACTIVE_DUMP"
            elif minutes_to_dump <= 8:
                return "EARLY_DUMP"
            else:
                return "PRE_DUMP"
                
        except Exception as e:
            return "UNKNOWN"

    def _extract_pre_dump_signals(self, dump_signals: Dict) -> List[str]:
        """Extract list of detected pre-dump signals"""
        try:
            signals = []
            
            signal_names = {
                "volume_divergence": "Volume Divergence",
                "price_deterioration": "Price Deterioration",
                "momentum_decay": "Momentum Decay",
                "support_weakness": "Support Weakness",
                "distribution_detected": "Distribution Pattern",
                "whale_selling": "Whale Selling",
                "smart_money_exit": "Smart Money Exit",
                "institutional_pressure": "Institutional Selling",
                "orderbook_imbalance": "Orderbook Imbalance",
                "bid_support_weakness": "Bid Support Weakness",
                "ask_wall_building": "Ask Wall Building",
                "rsi_signals": "RSI Bearish Signals",
                "macd_bearish": "MACD Bearish",
                "ma_breakdown": "MA Breakdown",
                "bb_signals": "Bollinger Band Signals",
                "volume_no_followthrough": "Volume No Follow-through",
                "declining_rally_volume": "Declining Rally Volume",
                "momentum_exhaustion": "Momentum Exhaustion"
            }
            
            for category, category_signals in dump_signals.items():
                for signal_key, score in category_signals.items():
                    if score > 0.3 and signal_key in signal_names:
                        signals.append(f"{signal_names[signal_key]} ({score:.1%})")
            
            return signals
            
        except Exception as e:
            print(f"    ❌ Error extracting signals: {e}")
            return []

    def _calculate_institutional_flow(self, dump_signals: Dict) -> Dict[str, float]:
        """Calculate institutional flow metrics"""
        try:
            whale_signals = dump_signals.get("whale_smart_money", {})
            
            return {
                "whale_selling_flow": whale_signals.get("whale_selling", 0),
                "smart_money_exit_flow": whale_signals.get("smart_money_exit", 0),
                "institutional_coordination": whale_signals.get("institutional_pressure", 0)
            }
            
        except Exception as e:
            return {"error": "calculation_failed"}

    def _estimate_dump_duration(self, magnitude: float) -> str:
        """Estimate how long the dump will last"""
        try:
            if magnitude < 0.05:
                return "5-15 minutes"
            elif magnitude < 0.10:
                return "15-30 minutes"
            elif magnitude < 0.20:
                return "30-60 minutes"
            else:
                return "1-3 hours"
                
        except Exception as e:
            return "Unknown"

    def _estimate_recovery_probability(self, dump_signals: Dict) -> float:
        """Estimate probability of quick recovery"""
        try:
            # Lower recovery if fundamentals are weak
            technical_weakness = np.mean(list(dump_signals.get("technical_breakdown", {}).values()))
            whale_pressure = np.mean(list(dump_signals.get("whale_smart_money", {}).values()))
            
            base_recovery = 0.6  # 60% base recovery probability
            
            # Reduce if strong selling pressure
            if whale_pressure > 0.7:
                base_recovery -= 0.2
            if technical_weakness > 0.7:
                base_recovery -= 0.1
            
            return max(0.1, min(0.9, base_recovery))
            
        except Exception as e:
            return 0.5

    def _calculate_dump_severity(self, signals: Dict) -> str:
        """Calculate dump severity level"""
        try:
            severity_score = 0
            total_signals = len(signals)
            
            for category, signal_data in signals.items():
                if isinstance(signal_data, dict):
                    category_strength = np.mean(list(signal_data.values()))
                    weight = self.signal_weights.get(category, 1.0)
                    severity_score += category_strength * weight
            
            # Normalize score
            if total_signals > 0:
                severity_score /= total_signals
            
            # Classify severity
            if severity_score >= 0.8:
                return "CRITICAL"  # 🔴 Severe dump imminent
            elif severity_score >= 0.6:
                return "HIGH"      # 🟠 Strong dump likely
            elif severity_score >= 0.4:
                return "MEDIUM"    # 🟡 Dump possible
            else:
                return "LOW"       # 🟢 Minimal dump risk
                
        except Exception as e:
            return "UNKNOWN"

    def _get_protection_strategies(self, severity: str, recovery_prob: float) -> List[str]:
        """Get protection strategies based on dump analysis"""
        strategies = []
        
        try:
            if severity == "CRITICAL":
                strategies.extend([
                    "🚨 EMERGENCY EXIT if holding positions",
                    "❌ AVOID all new entries for 2-4 hours",
                    "💰 Consider SHORT positions (experienced traders)",
                    "⏳ Wait for capitulation bottom signals",
                    "📊 Monitor 15min/5min charts for reversal"
                ])
            elif severity == "HIGH":
                strategies.extend([
                    "⚠️ REDUCE position sizes by 50-70%",
                    "🛡️ TIGHTEN stop losses immediately",
                    "⏸️ PAUSE new entries for 1-2 hours",
                    "📈 Look for oversold bounce opportunities",
                    "👀 Watch for whale accumulation"
                ])
            elif severity == "MEDIUM":
                strategies.extend([
                    "🎯 PARTIAL profit taking recommended",
                    "📊 MONITOR closely for escalation",
                    "⚖️ BALANCED approach - some entries OK",
                    "🔄 PREPARE for potential volatility"
                ])
            else:  # LOW
                strategies.extend([
                    "✅ NORMAL trading conditions",
                    "🛍️ DIP buying opportunities may arise",
                    "📈 CONTINUE regular strategy"
                ])
            
            # Add recovery-based strategies
            if recovery_prob < 0.3:
                strategies.append("⏳ EXTENDED dump likely - prepare for days/weeks")
            elif recovery_prob > 0.7:
                strategies.append("🚀 QUICK recovery expected - watch for bounce")
            
            return strategies
            
        except Exception as e:
            return ["⚠️ Unable to determine protection strategies"]

    async def _send_dump_warning_alert(self, coin: str, dump_data: Dict):
        """Send advanced dump warning alert with strategies"""
        try:
            severity = dump_data.get('severity', 'MODERATE')  # ✅ ENHANCED: Meaningful default
            confidence = dump_data.get('confidence', 0)
            time_estimate = dump_data.get('time_estimate', '15-30 minutes')  # ✅ ENHANCED: Meaningful default
            recovery_prob = dump_data.get('recovery_probability', 0)
            
            # Determine alert emoji and urgency
            severity_emoji = {
                "CRITICAL": "🚨🔴",
                "HIGH": "⚠️🟠", 
                "MEDIUM": "⚠️🟡",
                "LOW": "ℹ️🟢"
            }
            
            emoji = severity_emoji.get(severity, "⚠️")
            
            # Build comprehensive alert message
            message = f"{emoji} **DUMP WARNING ALERT** {emoji}\n\n"
            message += f"📈 **Coin:** {coin}\n"
            message += f"🚨 **Severity:** {severity}\n"
            message += f"🎯 **Confidence:** {confidence:.1%}\n"
            message += f"⏰ **Time Estimate:** {time_estimate}\n"
            message += f"🔄 **Recovery Probability:** {recovery_prob:.1%}\n\n"
            
            # Add technical breakdown
            technical_signals = dump_data.get('signals', {})
            if technical_signals:
                message += "📊 **Technical Breakdown:**\n"
                for category, signals in technical_signals.items():
                    if isinstance(signals, dict) and signals:
                        avg_strength = np.mean(list(signals.values()))
                        strength_bar = "🔴" * int(avg_strength * 5)
                        message += f"  • {category.replace('_', ' ').title()}: {strength_bar} {avg_strength:.1%}\n"
            
            # Add protection strategies
            strategies = self._get_protection_strategies(severity, recovery_prob)
            if strategies:
                message += f"\n🛡️ **PROTECTION STRATEGIES:**\n"
                for i, strategy in enumerate(strategies[:5], 1):  # Limit to 5 strategies
                    message += f"{i}. {strategy}\n"
            
            # Add timing information
            message += f"\n⏱️ **Analysis Time:** {datetime.now().strftime('%H:%M:%S')}\n"
            message += f"🔄 **Next Update:** {(datetime.now() + timedelta(minutes=5)).strftime('%H:%M:%S')}\n"
            
            # Add warning footer
            message += f"\n⚠️ **Risk Warning:** This is algorithmic analysis. Always use proper risk management and never invest more than you can afford to lose.\n"
            
            # Send to dump detection chat
            await self.notifier.send_message(
                message, 
                chat_id=DUMP_NOTIFICATION_CHAT_ID,
                parse_mode='Markdown'
            )
            
            # Also send to main chat if severity is HIGH or CRITICAL
            if severity in ["HIGH", "CRITICAL"]:
                summary_message = f"{emoji} **{severity} DUMP WARNING** - {coin}\n"
                summary_message += f"🎯 Confidence: {confidence:.1%} | ⏰ Time: {time_estimate}\n"
                summary_message += f"🛡️ Action: {strategies[0] if strategies else 'Monitor closely'}"
                
                await self.notifier.send_message(
                    summary_message,
                    chat_id=TELEGRAM_CHAT_ID,
                    parse_mode='Markdown'                )
            
            print(f"🚨 Dump warning sent for {coin} - Severity: {severity}, Confidence: {confidence:.1%}")
            return True
            
        except Exception as e:
            print(f"❌ Error sending dump warning for {coin}: {e}")
            return False

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)
        
        Args:
            prices: Series of closing prices
            period: RSI calculation period (default 14)
            
        Returns:
            Series of RSI values (0-100)
        """
        try:
            if len(prices) < period + 1:
                return pd.Series([50.0] * len(prices), index=prices.index)
            
            # Calculate price changes
            delta = prices.diff()
            
            # Separate gains and losses
            gains = delta.where(delta > 0, 0)
            losses = -delta.where(delta < 0, 0)
            
            # Calculate average gains and losses using exponential moving average
            avg_gains = gains.ewm(span=period, adjust=False).mean()
            avg_losses = losses.ewm(span=period, adjust=False).mean()
            
            # Calculate RS and RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))
            
            # Handle division by zero
            rsi = rsi.fillna(50.0)
            
            return rsi
            
        except Exception as e:
            print(f"      ❌ RSI calculation error: {e}")
            # Return neutral RSI values
            return pd.Series([50.0] * len(prices), index=prices.index)

    def _calculate_obv(self, ohlcv_data: pd.DataFrame) -> pd.Series:
        """
        Calculate On-Balance Volume (OBV)
        
        Args:
            ohlcv_data: DataFrame with 'close' and 'volume' columns
            
        Returns:
            Series of OBV values
        """
        try:
            if len(ohlcv_data) < 2:
                return pd.Series([0.0] * len(ohlcv_data), index=ohlcv_data.index)
            
            closes = ohlcv_data['close']
            volumes = ohlcv_data['volume']
            
            # Calculate price direction
            price_direction = np.where(closes > closes.shift(1), 1, 
                                     np.where(closes < closes.shift(1), -1, 0))
            
            # Calculate OBV
            obv_values = []
            obv = 0
            
            for i in range(len(ohlcv_data)):
                if i == 0:
                    obv = volumes.iloc[i]
                else:
                    if price_direction[i] == 1:  # Price up
                        obv += volumes.iloc[i]
                    elif price_direction[i] == -1:  # Price down
                        obv -= volumes.iloc[i]
                    # If price unchanged, OBV stays same
                
                obv_values.append(obv)
            
            return pd.Series(obv_values, index=ohlcv_data.index)
            
        except Exception as e:
            print(f"      ❌ OBV calculation error: {e}")
            # Return zero OBV values
            return pd.Series([0.0] * len(ohlcv_data), index=ohlcv_data.index)

    def update_market_conditions(self, market_data: Dict):
        """Update market conditions for better dump detection"""
        try:
            # Update BTC dominance and fear & greed if available
            btc_dominance = market_data.get('btc_dominance')
            if btc_dominance:
                self.market_conditions['btc_dominance'] = btc_dominance
            
            # Update overall market trend
            market_trend = market_data.get('market_trend', 'NEUTRAL')
            self.market_conditions['trend'] = market_trend
            
            # Update volatility index
            vix = market_data.get('volatility_index', 0.5)
            self.market_conditions['volatility'] = vix
            
            # Store timestamp
            self.market_conditions['last_update'] = time.time()
            
        except Exception as e:
            print(f"⚠️ Error updating market conditions: {e}")

    def _create_fallback_dump_alert(self, coin: str, reason: str,
                                  dump_probability: float = 0.15,
                                  confidence_score: float = 0.25,
                                  error: str = None) -> UltraEarlyDumpAlert:
        """
        ✅ FIX: Create fallback dump alert instead of returning None
        Always provides actionable alert data for monitoring purposes
        """
        try:
            current_time = datetime.now()

            # Create fallback alert with reasonable defaults
            fallback_alert = UltraEarlyDumpAlert(
                coin=coin,
                detection_time=current_time,
                dump_probability=dump_probability,
                risk_level="LOW" if dump_probability < 0.3 else "MEDIUM",
                confidence_score=confidence_score,
                estimated_dump_time=current_time + timedelta(minutes=30),  # Default 30 min
                estimated_dump_magnitude=0.05,  # Default 5% drop
                warning_stage="MONITORING",
                current_price=0.0,  # ✅ FIXED: Add current_price

                # Enhanced detection metrics with fallback values
                volume_anomaly_score=0.1,
                orderbook_imbalance=0.05,
                whale_selling_pressure=0.03,
                price_momentum_decay=0.02,
                smart_money_flow=0.04,

                # Technical indicators with fallback values
                rsi_divergence=0.1,
                macd_bearish_signal=0.05,
                volume_profile_breakdown=0.03,
                support_level_weakness=0.02,

                # Early warning indicators
                pre_dump_signals=[f"fallback_alert_{reason}"],
                institutional_flow={"net_flow": -0.02, "confidence": 0.25},
                market_structure_break=False,
                liquidity_drain_detected=False,

                # Risk assessment
                severity_level=2 if dump_probability < 0.3 else 4,
                estimated_duration="30-60 minutes",
                recovery_probability=0.75,
                suggested_action="MONITOR" if dump_probability < 0.3 else "CAUTION",

                # Timing estimates
                minutes_to_dump=30,
                confidence_window="LOW"
            )

            # Add error information if provided
            if error:
                fallback_alert.pre_dump_signals.append(f"error: {error[:50]}")

            print(f"    ✅ Created fallback dump alert for {coin} (reason: {reason})")
            return fallback_alert

        except Exception as e:
            print(f"    ❌ Critical error creating fallback alert: {e}")
            # Ultimate fallback - create minimal alert
            return UltraEarlyDumpAlert(
                coin=coin,
                detection_time=datetime.now(),
                dump_probability=0.1,
                risk_level="LOW",
                confidence_score=0.2,
                estimated_dump_time=datetime.now() + timedelta(minutes=60),
                estimated_dump_magnitude=0.03,
                warning_stage="EMERGENCY_FALLBACK",
                current_price=0.0,  # ✅ FIXED: Add current_price
                volume_anomaly_score=0.05,
                orderbook_imbalance=0.02,
                whale_selling_pressure=0.01,
                price_momentum_decay=0.01,
                smart_money_flow=0.01,
                rsi_divergence=0.05,
                macd_bearish_signal=0.02,
                volume_profile_breakdown=0.01,
                support_level_weakness=0.01,
                pre_dump_signals=["emergency_fallback"],
                institutional_flow={"net_flow": 0.0, "confidence": 0.1},
                market_structure_break=False,
                liquidity_drain_detected=False,
                severity_level=1,
                estimated_duration="unknown",
                recovery_probability=0.8,
                suggested_action="MONITOR",
                minutes_to_dump=60,
                confidence_window="VERY_LOW"
            )

    def _adjust_thresholds_for_market(self) -> Dict[str, float]:
        """Adjust detection thresholds based on market conditions"""
        try:
            base_thresholds = {
                'price_threshold': 0.05,      # 5% price drop
                'volume_threshold': 2.0,      # 2x volume
                'momentum_threshold': 0.7,    # 70% momentum
                'confidence_threshold': 0.6   # 60% confidence
            }
            
            # Adjust based on market volatility
            volatility = self.market_conditions.get('volatility', 0.5)
            if volatility > 0.7:  # High volatility market
                base_thresholds['price_threshold'] *= 1.3  # Need bigger moves
                base_thresholds['volume_threshold'] *= 0.8  # Lower volume requirement
            elif volatility < 0.3:  # Low volatility market
                base_thresholds['price_threshold'] *= 0.7  # Smaller moves significant
                base_thresholds['volume_threshold'] *= 1.2  # Higher volume requirement
            
            # Adjust based on market trend
            trend = self.market_conditions.get('trend', 'NEUTRAL')
            if trend == 'BEARISH':
                base_thresholds['confidence_threshold'] *= 0.8  # Lower confidence needed
            elif trend == 'BULLISH':
                base_thresholds['confidence_threshold'] *= 1.2  # Higher confidence needed
            
            return base_thresholds
            
        except Exception as e:
            print(f"⚠️ Error adjusting thresholds: {e}")
            return {                'price_threshold': 0.05,
                'volume_threshold': 2.0, 
                'momentum_threshold': 0.7,
                'confidence_threshold': 0.6
            }

# ============================================================================
# 🔄 COMPATIBILITY CLASS FOR MAIN BOT INTEGRATION
# ============================================================================

class DumpDetector:
    """
    🚨 Dump Detector V4.0 - Compatibility wrapper for DumpDetector

    This class provides compatibility with the main bot's expected interface
    while using the enhanced DumpDetector internally.
    """

    def __init__(self,
                 sensitivity: float = 0.75,
                 min_volume_threshold: float = 2.0,
                 whale_threshold: float = 50000,
                 lookback_period: int = 50,
                 enable_ml_detection: bool = True,
                 enable_pattern_recognition: bool = True,
                 enable_real_time_monitoring: bool = True):
        """
        Initialize Dump Detector with compatibility parameters
        """
        print("🚨 Initializing Dump Detector V4.0 (Enhanced)...")

        # Initialize the enhanced detector
        self.ultra_detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=sensitivity,
            pre_dump_lookback=lookback_period,
            whale_threshold=whale_threshold,
            min_confidence=0.6
        )

        # Store configuration
        self.sensitivity = sensitivity
        self.min_volume_threshold = min_volume_threshold
        self.whale_threshold = whale_threshold
        self.lookback_period = lookback_period
        self.enable_ml_detection = enable_ml_detection
        self.enable_pattern_recognition = enable_pattern_recognition
        self.enable_real_time_monitoring = enable_real_time_monitoring

        # Compatibility tracking
        self.dump_alerts_history = []

        print(f"  ✅ Enhanced Dump Detector initialized")
        print(f"  🎯 Sensitivity: {sensitivity}")
        print(f"  📊 Volume Threshold: {min_volume_threshold}x")
        print(f"  🐋 Whale Threshold: ${whale_threshold:,.0f}")
        print(f"  🔍 Lookback Period: {lookback_period}")
        print(f"  🧠 ML Detection: {'✅' if enable_ml_detection else '❌'}")
        print(f"  🔍 Pattern Recognition: {'✅' if enable_pattern_recognition else '❌'}")
        print(f"  ⚡ Real-time Monitoring: {'✅' if enable_real_time_monitoring else '❌'}")

    def analyze_dump_probability(self, coin: str, market_data: Dict[str, Any]) -> Optional[UltraEarlyDumpAlert]:
        """
        🚨 Main method for dump probability analysis

        This method provides compatibility with the main bot's expected interface
        while using the enhanced UltraEarlyDumpDetector internally.
        """
        try:
            print(f"\n🚨 ENHANCED DUMP ANALYSIS: {coin}")

            # Convert market_data format if needed
            enhanced_market_data = self._convert_market_data_format(market_data)

            # Use the ultra detector for analysis
            dump_alert = self.ultra_detector.analyze_ultra_early_dump_signals(
                coin, enhanced_market_data
            )

            if dump_alert:
                # Store in history for compatibility
                self.dump_alerts_history.append(dump_alert)

                print(f"  🚨 DUMP ALERT GENERATED!")
                print(f"    🎯 Risk Level: {dump_alert.risk_level}")
                print(f"    📊 Confidence: {dump_alert.confidence_score:.1%}")
                print(f"    📉 Dump Probability: {dump_alert.dump_probability:.1%}")
                print(f"    ⏰ Time Estimate: {dump_alert.minutes_to_dump} minutes")

                return dump_alert
            else:
                print(f"  ✅ No significant dump risk detected for {coin}")
                return None

        except Exception as e:
            print(f"  ❌ Error in dump analysis for {coin}: {e}")
            return None

    def _convert_market_data_format(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert market data to enhanced format"""
        try:
            # Extract data from the input format
            ohlcv_data = market_data.get("ohlcv_data")
            orderbook_data = market_data.get("orderbook_data")
            current_price = market_data.get("current_price", 0)

            # Create enhanced format
            enhanced_data = {
                "ohlcv_1m": ohlcv_data,  # Use main OHLCV as 1m data
                "ohlcv_5m": ohlcv_data,  # Use same data for 5m (will be resampled if needed)
                "ohlcv_15m": ohlcv_data, # Use same data for 15m (will be resampled if needed)
                "orderbook_data": orderbook_data,
                "whale_transactions": market_data.get("whale_transactions", []),
                "current_price": current_price,
                "funding_rate": market_data.get("funding_rate"),
                "volume_profile": market_data.get("volume_profile"),
                "market_sentiment": market_data.get("market_sentiment")
            }

            return enhanced_data

        except Exception as e:
            print(f"    ⚠️ Error converting market data format: {e}")
            return market_data

    def log_dump_detection(self, dump_alert: UltraEarlyDumpAlert, current_price: float):
        """Log dump detection for compatibility"""
        try:
            print(f"📝 Logging dump detection:")
            print(f"  🪙 Coin: {dump_alert.coin}")
            print(f"  💰 Price: ${current_price:.6f}")
            print(f"  🎯 Risk Level: {dump_alert.risk_level}")
            print(f"  📊 Confidence: {dump_alert.confidence_score:.1%}")
            print(f"  📉 Dump Probability: {dump_alert.dump_probability:.1%}")
            print(f"  ⏰ Detection Time: {dump_alert.detection_time}")

            # Store in history
            if dump_alert not in self.dump_alerts_history:
                self.dump_alerts_history.append(dump_alert)

        except Exception as e:
            print(f"❌ Error logging dump detection: {e}")

    def get_dump_statistics(self) -> Dict[str, Any]:
        """Get dump detection statistics for compatibility"""
        try:
            total_alerts = len(self.dump_alerts_history)

            if total_alerts == 0:
                return {
                    "total_alerts": 0,
                    "accuracy_rate": 0.0,
                    "average_advance_warning": 0,
                    "risk_level_distribution": {}
                }

            # Calculate statistics
            risk_levels = {}
            advance_warnings = []

            for alert in self.dump_alerts_history:
                risk_level = alert.risk_level
                risk_levels[risk_level] = risk_levels.get(risk_level, 0) + 1
                advance_warnings.append(alert.minutes_to_dump)

            return {
                "total_alerts": total_alerts,
                "accuracy_rate": self.ultra_detector.performance_metrics.get("accurate_predictions", 0) / max(1, total_alerts),
                "average_advance_warning": sum(advance_warnings) / len(advance_warnings) if advance_warnings else 0,
                "risk_level_distribution": risk_levels
            }

        except Exception as e:
            print(f"❌ Error getting dump statistics: {e}")
            return {"total_alerts": 0, "accuracy_rate": 0.0}

    def _create_fallback_dump_alert(self, coin: str, reason: str,
                                  dump_probability: float = 0.15,
                                  confidence_score: float = 0.25,
                                  error: str = None) -> UltraEarlyDumpAlert:
        """
        ✅ FIX: Create fallback dump alert instead of returning None
        Always provides actionable alert data for monitoring purposes
        """
        try:
            current_time = datetime.now()

            # Create fallback alert with reasonable defaults
            fallback_alert = UltraEarlyDumpAlert(
                coin=coin,
                detection_time=current_time,
                dump_probability=dump_probability,
                risk_level="LOW" if dump_probability < 0.3 else "MEDIUM",
                confidence_score=confidence_score,
                estimated_dump_time=current_time + timedelta(minutes=30),  # Default 30 min
                estimated_dump_magnitude=0.05,  # Default 5% drop
                warning_stage="MONITORING",
                current_price=0.0,  # ✅ FIXED: Add current_price

                # Enhanced detection metrics with fallback values
                volume_anomaly_score=0.1,
                orderbook_imbalance=0.05,
                whale_selling_pressure=0.03,
                price_momentum_decay=0.02,
                smart_money_flow=0.04,

                # Technical indicators with fallback values
                rsi_divergence=0.1,
                macd_bearish_signal=0.05,
                volume_profile_breakdown=0.03,
                support_level_weakness=0.02,

                # Early warning indicators
                pre_dump_signals=[f"fallback_alert_{reason}"],
                institutional_flow={"net_flow": -0.02, "confidence": 0.25},
                market_structure_break=False,
                liquidity_drain_detected=False,

                # Risk assessment
                severity_level=2 if dump_probability < 0.3 else 4,
                estimated_duration="30-60 minutes",
                recovery_probability=0.75,
                suggested_action="MONITOR" if dump_probability < 0.3 else "CAUTION",

                # Timing estimates
                minutes_to_dump=30,
                confidence_window="LOW"
            )

            # Add error information if provided
            if error:
                fallback_alert.pre_dump_signals.append(f"error: {error[:50]}")

            print(f"    ✅ Created fallback dump alert for {coin} (reason: {reason})")
            return fallback_alert

        except Exception as e:
            print(f"    ❌ Critical error creating fallback alert: {e}")
            # Ultimate fallback - create minimal alert
            return UltraEarlyDumpAlert(
                coin=coin,
                detection_time=datetime.now(),
                dump_probability=0.1,
                risk_level="LOW",
                confidence_score=0.2,
                estimated_dump_time=datetime.now() + timedelta(minutes=60),
                estimated_dump_magnitude=0.03,
                warning_stage="EMERGENCY_FALLBACK",
                current_price=0.0,  # ✅ FIXED: Add current_price
                volume_anomaly_score=0.05,
                orderbook_imbalance=0.02,
                whale_selling_pressure=0.01,
                price_momentum_decay=0.01,
                smart_money_flow=0.01,
                rsi_divergence=0.05,
                macd_bearish_signal=0.02,
                volume_profile_breakdown=0.01,
                support_level_weakness=0.01,
                pre_dump_signals=["emergency_fallback"],
                institutional_flow={"net_flow": 0.0, "confidence": 0.1},
                market_structure_break=False,
                liquidity_drain_detected=False,
                severity_level=1,
                estimated_duration="unknown",
                recovery_probability=0.8,
                suggested_action="MONITOR",
                minutes_to_dump=60,
                confidence_window="VERY_LOW"
            )
