#!/usr/bin/env python3
"""
🚨 IMPROVED DUMP DETECTOR V5.0
Simplified, more accurate dump detection algorithm focused on reducing false positives
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
import time
from dataclasses import dataclass

@dataclass
class UltraEarlyDumpAlert:
    """Simplified dump alert structure for compatibility"""
    coin: str
    dump_probability: float
    confidence_score: float
    risk_level: str
    stage: str
    indicators: Dict[str, float]
    estimated_dump_magnitude: float
    minutes_to_dump: int
    current_price: float = 0.0

    # Additional compatibility fields
    detection_time: str = ""
    estimated_dump_time: str = ""
    warning_stage: str = ""
    volume_anomaly_score: float = 0.0
    orderbook_imbalance: float = 0.0
    whale_selling_pressure: float = 0.0
    price_momentum_decay: float = 0.0
    smart_money_flow: float = 0.0
    rsi_divergence: float = 0.0
    macd_bearish_signal: float = 0.0
    volume_profile_breakdown: float = 0.0
    support_level_weakness: float = 0.0
    pre_dump_signals: List[str] = None
    institutional_flow: Dict[str, float] = None
    market_structure_break: bool = False
    liquidity_drain_detected: bool = False
    severity_level: int = 1
    estimated_duration: str = ""
    recovery_probability: float = 0.0
    suggested_action: str = ""
    confidence_window: str = ""

    def __post_init__(self):
        """Initialize default values"""
        if self.pre_dump_signals is None:
            self.pre_dump_signals = []
        if self.institutional_flow is None:
            self.institutional_flow = {}

class ImprovedDumpDetector:
    """
    🚨 Improved Dump Detector V5.0
    
    Simplified algorithm focused on:
    - Reducing false positives
    - Higher accuracy
    - Clear, interpretable signals
    - Fewer baseline assumptions
    """
    
    def __init__(self, 
                 sensitivity: float = 0.55,
                 min_volume_threshold: float = 2.0,
                 whale_threshold: float = 50000,
                 lookback_period: int = 30,
                 strict_mode: bool = True):
        """
        Initialize Improved Dump Detector
        
        Args:
            sensitivity: Detection sensitivity (0.3-0.8, lower = fewer false positives)
            min_volume_threshold: Minimum volume spike for consideration
            whale_threshold: Minimum USD for whale transactions
            lookback_period: Minutes to analyze
            strict_mode: If True, requires multiple confirmations
        """
        self.sensitivity = sensitivity
        self.min_volume_threshold = min_volume_threshold
        self.whale_threshold = whale_threshold
        self.lookback_period = lookback_period
        self.strict_mode = strict_mode
        
        # ✅ BALANCED: Reasonable thresholds to reduce false positives while catching real dumps
        self.min_dump_probability = 0.35  # Require 35% minimum (balanced)
        self.min_confidence = 0.40        # Require 40% confidence (balanced)
        self.min_indicators = 2           # Require at least 2 indicators
        
        print(f"🚨 Improved Dump Detector V5.0 initialized")
        print(f"  🎯 Sensitivity: {sensitivity} (lower = fewer false positives)")
        print(f"  📊 Min Volume: {min_volume_threshold}x")
        print(f"  🐋 Whale Threshold: ${whale_threshold:,.0f}")
        print(f"  ⏰ Lookback: {lookback_period} minutes")
        print(f"  🔒 Strict Mode: {'✅ Enabled' if strict_mode else '❌ Disabled'}")
        print(f"  🎯 Min Dump Probability: {self.min_dump_probability:.0%}")
        print(f"  🎯 Min Confidence: {self.min_confidence:.0%}")
        print(f"  📊 Min Indicators Required: {self.min_indicators}")
    
    def analyze_dump_probability(self, coin: str, market_data: Dict[str, Any]) -> Optional[UltraEarlyDumpAlert]:
        """
        🚨 Main dump analysis method - simplified and more accurate
        """
        try:
            print(f"\n🚨 IMPROVED DUMP ANALYSIS: {coin}")
            
            # Extract data
            ohlcv_data = market_data.get('ohlcv_data')
            current_price = market_data.get('current_price', 0)
            
            if ohlcv_data is None or len(ohlcv_data) < 20:
                print(f"  ❌ Insufficient data for {coin}")
                return None
            
            # ✅ SIMPLIFIED: Only 4 key indicators (instead of 20+)
            indicators = {}
            
            # 1. Price momentum breakdown (most important)
            indicators['price_momentum'] = self._analyze_price_momentum(ohlcv_data)
            
            # 2. Volume selling pressure
            indicators['volume_pressure'] = self._analyze_volume_pressure(ohlcv_data)
            
            # 3. Support level breakdown
            indicators['support_breakdown'] = self._analyze_support_breakdown(ohlcv_data, current_price)
            
            # 4. Market structure deterioration
            indicators['market_structure'] = self._analyze_market_structure(ohlcv_data)
            
            print(f"  📊 Dump Indicators:")
            for name, value in indicators.items():
                print(f"    - {name}: {value:.3f}")
            
            # ✅ STRICT: Calculate probability with high standards
            dump_probability = self._calculate_strict_dump_probability(indicators)
            confidence = self._calculate_confidence(indicators, dump_probability)
            
            print(f"  🎯 Dump Probability: {dump_probability:.1%}")
            print(f"  🎯 Confidence: {confidence:.1%}")
            print(f"  🎯 Required: prob≥{self.min_dump_probability:.0%}, conf≥{self.min_confidence:.0%}")
            
            # ✅ STRICT: Only create alert if meets high standards
            if dump_probability >= self.min_dump_probability and confidence >= self.min_confidence:
                # Count significant indicators
                significant_indicators = sum(1 for v in indicators.values() if v > 0.3)
                
                if significant_indicators >= self.min_indicators:
                    print(f"  ✅ DUMP ALERT CRITERIA MET:")
                    print(f"    - Probability: {dump_probability:.1%} ≥ {self.min_dump_probability:.0%}")
                    print(f"    - Confidence: {confidence:.1%} ≥ {self.min_confidence:.0%}")
                    print(f"    - Indicators: {significant_indicators} ≥ {self.min_indicators}")
                    
                    # Create high-quality alert
                    alert = UltraEarlyDumpAlert(
                        coin=coin,
                        dump_probability=dump_probability,
                        confidence_score=confidence,
                        risk_level=self._calculate_risk_level(dump_probability),
                        stage="ACTIVE_DUMP",
                        indicators=indicators,
                        estimated_dump_magnitude=self._estimate_magnitude(indicators),
                        minutes_to_dump=self._estimate_timing(indicators),
                        current_price=current_price
                    )
                    
                    print(f"  🚨 HIGH-QUALITY DUMP ALERT CREATED!")
                    print(f"    📉 Estimated magnitude: {alert.estimated_dump_magnitude:.1%}")
                    print(f"    ⏰ Estimated timing: {alert.minutes_to_dump} minutes")
                    print(f"    🎯 Risk level: {alert.risk_level}")
                    
                    return alert
                else:
                    print(f"  🚫 INSUFFICIENT INDICATORS: {significant_indicators} < {self.min_indicators}")
                    return None
            else:
                print(f"  🚫 DUMP CRITERIA NOT MET:")
                print(f"    - Probability: {dump_probability:.1%} < {self.min_dump_probability:.0%}")
                print(f"    - Confidence: {confidence:.1%} < {self.min_confidence:.0%}")
                return None
                
        except Exception as e:
            print(f"  ❌ Error in improved dump analysis: {e}")
            return None
    
    def _analyze_price_momentum(self, ohlcv_data: pd.DataFrame) -> float:
        """Analyze price momentum breakdown - most critical indicator"""
        try:
            if len(ohlcv_data) < 10:
                return 0.0
            
            # Calculate momentum indicators
            recent_prices = ohlcv_data['close'].tail(5)
            older_prices = ohlcv_data['close'].tail(15).head(10)
            
            recent_avg = recent_prices.mean()
            older_avg = older_prices.mean()
            
            # Price momentum change
            momentum_change = (recent_avg - older_avg) / older_avg if older_avg > 0 else 0
            
            # RSI-like momentum
            price_changes = ohlcv_data['close'].pct_change().tail(10)
            negative_changes = price_changes[price_changes < 0].abs().mean()
            positive_changes = price_changes[price_changes > 0].mean()
            
            if positive_changes > 0:
                momentum_ratio = negative_changes / positive_changes
            else:
                momentum_ratio = 2.0  # High selling pressure
            
            # Combine indicators
            momentum_score = 0.0
            
            # Negative momentum
            if momentum_change < -0.02:  # 2% decline
                momentum_score += 0.3
            if momentum_change < -0.05:  # 5% decline
                momentum_score += 0.2
            
            # High selling vs buying ratio
            if momentum_ratio > 1.5:
                momentum_score += 0.3
            if momentum_ratio > 2.0:
                momentum_score += 0.2
            
            return min(1.0, momentum_score)
            
        except Exception:
            return 0.0
    
    def _analyze_volume_pressure(self, ohlcv_data: pd.DataFrame) -> float:
        """Analyze volume selling pressure"""
        try:
            if len(ohlcv_data) < 10:
                return 0.0

            # Volume analysis
            recent_volume = ohlcv_data['volume'].tail(5).mean()
            avg_volume = ohlcv_data['volume'].tail(20).mean()

            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

            # Price vs volume relationship
            recent_prices = ohlcv_data['close'].tail(5)
            price_change = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0] if len(recent_prices) > 0 else 0

            volume_score = 0.0

            # ✅ IMPROVED: More sensitive volume detection
            # High volume with any price decline
            if volume_ratio > 1.5 and price_change < -0.005:  # 0.5% decline
                volume_score += 0.3

            # Very high volume with price decline
            if volume_ratio > self.min_volume_threshold and price_change < -0.01:
                volume_score += 0.4

            # Extreme volume with significant decline
            if volume_ratio > 3.0 and price_change < -0.02:
                volume_score += 0.4

            # Volume spike without price recovery
            if volume_ratio > 2.0 and price_change < 0:
                volume_score += 0.2

            # Sustained high volume
            if volume_ratio > 2.5:
                volume_score += 0.2

            return min(1.0, volume_score)

        except Exception:
            return 0.0
    
    def _analyze_support_breakdown(self, ohlcv_data: pd.DataFrame, current_price: float) -> float:
        """Analyze support level breakdown"""
        try:
            if len(ohlcv_data) < 15 or current_price <= 0:
                return 0.0

            # Find recent support levels
            lows = ohlcv_data['low'].tail(20)
            support_level = lows.quantile(0.2)  # 20th percentile as support

            # Also check recent lows as support
            recent_low = lows.tail(10).min()
            key_support = min(support_level, recent_low)

            # Check if current price broke support
            support_distance = (current_price - key_support) / key_support if key_support > 0 else 0

            support_score = 0.0

            # ✅ IMPROVED: More sensitive support breakdown detection
            # Any support breakdown
            if support_distance < -0.005:  # 0.5% below support
                support_score += 0.2

            # Clear support breakdown
            if support_distance < -0.01:  # 1% below support
                support_score += 0.3

            # Significant breakdown
            if support_distance < -0.03:  # 3% below support
                support_score += 0.3

            # Major breakdown
            if support_distance < -0.05:  # 5% below support
                support_score += 0.2

            # Check if price is approaching support (early warning)
            if -0.005 <= support_distance < 0.005:  # Near support
                support_score += 0.1

            return min(1.0, support_score)

        except Exception:
            return 0.0
    
    def _analyze_market_structure(self, ohlcv_data: pd.DataFrame) -> float:
        """Analyze market structure deterioration"""
        try:
            if len(ohlcv_data) < 10:
                return 0.0
            
            # Lower highs and lower lows pattern
            highs = ohlcv_data['high'].tail(10)
            lows = ohlcv_data['low'].tail(10)
            
            # Check for declining pattern
            recent_high = highs.tail(3).max()
            older_high = highs.head(7).max()
            
            recent_low = lows.tail(3).min()
            older_low = lows.head(7).min()
            
            structure_score = 0.0
            
            # Lower highs
            if recent_high < older_high * 0.98:  # 2% lower high
                structure_score += 0.3
            
            # Lower lows
            if recent_low < older_low * 0.98:  # 2% lower low
                structure_score += 0.4
            
            # Consistent decline
            if recent_high < older_high and recent_low < older_low:
                structure_score += 0.3
            
            return min(1.0, structure_score)
            
        except Exception:
            return 0.0
    
    def _calculate_strict_dump_probability(self, indicators: Dict[str, float]) -> float:
        """Calculate dump probability with strict standards"""
        # ✅ STRICT: Higher weights for most reliable indicators
        weights = {
            'price_momentum': 0.40,    # Most important
            'volume_pressure': 0.30,   # Second most important
            'support_breakdown': 0.20, # Third
            'market_structure': 0.10   # Supporting evidence
        }
        
        probability = 0.0
        for indicator, value in indicators.items():
            if indicator in weights:
                probability += value * weights[indicator]
        
        # ✅ STRICT: Apply penalty for weak signals
        significant_indicators = sum(1 for v in indicators.values() if v > 0.3)
        if significant_indicators < 2:
            probability *= 0.5  # Reduce probability if too few indicators
        
        return min(1.0, probability)
    
    def _calculate_confidence(self, indicators: Dict[str, float], probability: float) -> float:
        """Calculate confidence in the dump prediction"""
        # Base confidence on indicator agreement
        strong_indicators = sum(1 for v in indicators.values() if v > 0.5)
        moderate_indicators = sum(1 for v in indicators.values() if 0.3 <= v <= 0.5)
        
        confidence = 0.0
        
        # Strong indicators boost confidence
        confidence += strong_indicators * 0.25
        
        # Moderate indicators add some confidence
        confidence += moderate_indicators * 0.15
        
        # High probability boosts confidence
        if probability > 0.7:
            confidence += 0.2
        
        # Consistency bonus
        if all(v > 0.2 for v in indicators.values()):
            confidence += 0.15
        
        return min(1.0, confidence)
    
    def _calculate_risk_level(self, probability: float) -> str:
        """Calculate risk level based on probability"""
        if probability >= 0.8:
            return "CRITICAL"
        elif probability >= 0.7:
            return "HIGH"
        elif probability >= 0.6:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _estimate_magnitude(self, indicators: Dict[str, float]) -> float:
        """Estimate dump magnitude"""
        # Base magnitude on indicator strength
        avg_strength = np.mean(list(indicators.values()))
        magnitude = 0.02 + (avg_strength * 0.08)  # 2-10% range
        return min(0.15, magnitude)  # Cap at 15%
    
    def _estimate_timing(self, indicators: Dict[str, float]) -> int:
        """Estimate timing until dump"""
        # Urgent indicators reduce time
        urgency = indicators.get('price_momentum', 0) * 0.4 + indicators.get('volume_pressure', 0) * 0.3
        
        base_minutes = 15
        urgent_reduction = int(urgency * 10)
        
        return max(5, base_minutes - urgent_reduction)

# ✅ COMPATIBILITY: Create wrapper for existing interface
class DumpDetector:
    """Compatibility wrapper for improved dump detector"""
    
    def __init__(self, sensitivity: float = 0.55, **kwargs):
        self.improved_detector = ImprovedDumpDetector(
            sensitivity=sensitivity,
            strict_mode=True  # Always use strict mode for accuracy
        )
    
    def analyze_dump_probability(self, coin: str, market_data: Dict[str, Any]) -> Optional[UltraEarlyDumpAlert]:
        """Main interface method"""
        return self.improved_detector.analyze_dump_probability(coin, market_data)
    
    def log_dump_detection(self, alert, current_price):
        """Compatibility method for logging"""
        if alert:
            print(f"📝 Logged dump detection: {alert.coin} at ${current_price:.4f}")
