#!/usr/bin/env python3
"""
📊 Chart Test - Test chart generation with detailed report
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

def chart_test():
    """📊 Chart test."""
    print(f"📊 CHART TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Import modules
        from telegram_notifier import EnhancedTelegramNotifier
        from chart_generator import EnhancedChartGenerator
        
        print(f"📱 Initializing notifier...")
        notifier = EnhancedTelegramNotifier(bot_token, chat_id)
        
        print(f"🎨 Initializing chart generator...")
        chart_gen = EnhancedChartGenerator(output_dir="charts", telegram_notifier=notifier)
        
        print(f"📊 Creating sample data...")
        # Create sample OHLCV data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 50000
        price_changes = np.random.normal(0, 0.02, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # Create OHLCV DataFrame
        ohlcv_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        current_price = prices[-1]
        print(f"  📊 Sample data created, current price: {current_price:.2f}")
        
        # Create sample Fibonacci data
        fibonacci_data = {
            'trend_direction': 'UPTREND',
            'pivot_high': current_price * 1.05,
            'pivot_low': current_price * 0.95,
            'retracement_levels': [
                {'ratio': 0.236, 'price': current_price * 0.98, 'strength': 0.8},
                {'ratio': 0.382, 'price': current_price * 0.96, 'strength': 0.9},
                {'ratio': 0.618, 'price': current_price * 0.94, 'strength': 0.95}
            ],
            'extension_levels': [
                {'ratio': 1.618, 'price': current_price * 1.08, 'strength': 0.85},
                {'ratio': 2.618, 'price': current_price * 1.15, 'strength': 0.75}
            ],
            'confluence_zones': [
                {'price': current_price * 0.97, 'strength': 0.9, 'methods': ['Fibonacci', 'Support']}
            ],
            'signals': {
                'primary_signal': 'BUY',
                'confidence': 0.85
            }
        }
        
        print(f"🌀 Generating Fibonacci chart...")
        chart_path = chart_gen.generate_fibonacci_chart("BTC/USDT", fibonacci_data, ohlcv_data, current_price)
        
        if not chart_path:
            print(f"❌ Chart generation failed")
            return False
        
        print(f"✅ Chart generated: {chart_path}")
        
        # Create detailed caption using chart_generator method
        print(f"📝 Creating detailed caption...")
        detailed_caption = chart_gen._create_fibonacci_caption("BTC/USDT", fibonacci_data, current_price)
        
        print(f"📤 Sending chart with detailed caption...")
        success = notifier.send_photo(
            photo_path=chart_path,
            caption=detailed_caption,
            chat_id=chat_id,
            parse_mode="HTML"
        )
        
        if success:
            print(f"✅ SUCCESS! Chart with detailed report sent to Telegram")
            print(f"📱 Check your Telegram for the Fibonacci chart with detailed analysis")
            return True
        else:
            print(f"❌ FAILED! Could not send chart with detailed report")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = chart_test()
    if success:
        print(f"\n🎉 CHART TEST PASSED!")
        print(f"📱 You should see a Fibonacci chart with detailed analysis in Telegram")
    else:
        print(f"\n💥 CHART TEST FAILED!")
