#!/usr/bin/env python3
"""
🚀 Test Main Simple - Simple test for main bot
"""

import os
import sys
from datetime import datetime

def test_main_simple():
    """🚀 Simple test for main bot."""
    print(f"🚀 MAIN BOT SIMPLE TEST STARTED")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")
    
    # Get environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if not bot_token or not chat_id:
        print(f"❌ Missing environment variables")
        return False
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        print(f"📦 Importing main_bot...")
        from main_bot import TradingBot
        
        print(f"🚀 Initializing TradingBot...")
        bot = TradingBot()
        
        print(f"✅ Bot initialized successfully")
        
        # Force enable chart system
        print(f"🔧 Force enabling chart system...")
        bot.force_enable_chart_system()
        
        # Debug chart system status
        print(f"🔍 Debugging chart system status...")
        bot.debug_chart_system_status()
        
        print(f"✅ Main bot test completed successfully")
        return True
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main_simple()
    if success:
        print(f"\n🎉 MAIN BOT SIMPLE TEST PASSED!")
        print(f"🚀 Main bot is working correctly")
    else:
        print(f"\n💥 MAIN BOT SIMPLE TEST FAILED!")
