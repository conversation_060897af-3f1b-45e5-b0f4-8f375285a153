#!/usr/bin/env python3
"""
🧪 SIMPLE TEST: CoinCategorizer
Test đơn giản để kiểm tra CoinCategorizer
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """Run simple test"""
    print("🧪 === SIMPLE COIN CATEGORIZER TEST ===")
    
    try:
        print("🏷️ Testing CoinCategorizer import...")
        import coin_categorizer
        print("✅ Import successful")
        
        print("🏷️ Creating CoinCategorizer instance...")
        categorizer = coin_categorizer.CoinCategorizer(
            auto_update=False  # Disable auto-update for faster testing
        )
        print("✅ CoinCategorizer created")
        
        # Check attributes
        print("🔍 Checking attributes...")
        has_dynamic = hasattr(categorizer, 'use_dynamic_sectors')
        has_known_coins = hasattr(categorizer, 'known_coins')
        has_dynamic_sectors = hasattr(categorizer, 'dynamic_sectors')
        
        print(f"  use_dynamic_sectors: {has_dynamic}")
        print(f"  known_coins: {has_known_coins}")
        print(f"  dynamic_sectors: {has_dynamic_sectors}")
        
        if has_dynamic and categorizer.use_dynamic_sectors:
            if has_dynamic_sectors:
                total_coins = len(categorizer.dynamic_sectors.get('all_coins', []))
                print(f"✅ Dynamic mode: {total_coins} coins")
            else:
                print("⚠️ Dynamic mode but no dynamic_sectors")
        elif has_known_coins:
            total_coins = len(categorizer.known_coins)
            print(f"✅ Static mode: {total_coins} coins")
        else:
            print("❌ No coin data found")
        
        # Test categorization
        print("🔍 Testing categorization...")
        test_result = categorizer.get_coin_category('BTC')
        print(f"  BTC category: {test_result}")
        
        print("🎉 SUCCESS: CoinCategorizer is working!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
