
import os
import sys

# Test emergency notifier creation
def test_emergency_notifier():
    try:
        # Mock environment
        os.environ['TELEGRAM_BOT_TOKEN'] = 'test_token'
        os.environ['TELEGRAM_CHAT_ID'] = 'test_chat'
        
        # Import and test
        from main_bot import EnhancedTradingBot
        
        # Create bot instance (this should not fail due to None notifier)
        print("🧪 Testing bot initialization...")
        
        # This is a minimal test - we don't want to fully initialize
        # Just test that the emergency notifier creation works
        bot = EnhancedTradingBot.__new__(EnhancedTradingBot)
        
        # Test emergency notifier creation directly
        emergency_notifier = bot._create_emergency_notifier()
        
        if emergency_notifier is not None:
            print("✅ Emergency notifier creation successful")
            
            # Test basic methods
            if hasattr(emergency_notifier, 'send_message'):
                result = emergency_notifier.send_message("Test message")
                print(f"✅ send_message test: {result}")
            
            if hasattr(emergency_notifier, 'send_photo'):
                result = emergency_notifier.send_photo("test.jpg", "Test caption")
                print(f"✅ send_photo test: {result}")
                
            return True
        else:
            print("❌ Emergency notifier creation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_emergency_notifier()
    print(f"\n🎯 Test result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
