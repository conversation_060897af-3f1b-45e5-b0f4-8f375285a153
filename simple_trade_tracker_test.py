#!/usr/bin/env python3
"""
🔍 SIMPLE TRADE TRACKER TEST
Quick test to verify Trade Tracker components
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic imports"""
    print("🔍 Testing basic imports...")
    
    try:
        from trade_tracker import TradeTracker
        print("  ✅ TradeTracker imported")
        
        from signal_manager_integration import SignalManagerIntegration
        print("  ✅ SignalManagerIntegration imported")
        
        from main_bot_signal_integration import MainBotSignalIntegration
        print("  ✅ MainBotSignalIntegration imported")
        
        from telegram_notifier import TelegramNotifier
        print("  ✅ TelegramNotifier imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        return False

def test_telegram_notifier_methods():
    """Test TelegramNotifier methods"""
    print("\n🔍 Testing TelegramNotifier methods...")
    
    try:
        from telegram_notifier import TelegramNotifier
        
        notifier = TelegramNotifier()
        
        # Check required methods
        required_methods = [
            'send_volume_profile_report',
            'send_ai_analysis_report',
            'send_fibonacci_analysis_report',
            'send_orderbook_analysis_report',
            'send_fourier_analysis_report'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if hasattr(notifier, method_name):
                print(f"  ✅ Method exists: {method_name}")
            else:
                missing_methods.append(method_name)
                print(f"  ❌ Method missing: {method_name}")
        
        if missing_methods:
            print(f"  ❌ Missing methods: {missing_methods}")
            return False
        else:
            print("  ✅ All required methods exist")
            return True
        
    except Exception as e:
        print(f"  ❌ TelegramNotifier test failed: {e}")
        return False

def test_trade_tracker_basic():
    """Test Trade Tracker basic functionality"""
    print("\n🔍 Testing Trade Tracker basic functionality...")
    
    try:
        from trade_tracker import TradeTracker
        
        tracker = TradeTracker()
        
        # Check basic attributes
        if hasattr(tracker, 'active_signals'):
            print(f"  ✅ Active signals: {len(tracker.active_signals)}")
        else:
            print("  ❌ No active_signals attribute")
        
        if hasattr(tracker, 'completed_signals'):
            print(f"  ✅ Completed signals: {len(tracker.completed_signals)}")
        else:
            print("  ❌ No completed_signals attribute")
        
        if hasattr(tracker, 'signal_management'):
            print("  ✅ Signal management exists")
            max_signals = tracker.signal_management.get('max_signals', 'Unknown')
            print(f"    Max signals: {max_signals}")
        else:
            print("  ❌ No signal_management attribute")
        
        # Test can_send_new_signal method
        if hasattr(tracker, 'can_send_new_signal'):
            can_send = tracker.can_send_new_signal()
            print(f"  ✅ Can send new signal: {can_send}")
        else:
            print("  ❌ No can_send_new_signal method")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Trade Tracker test failed: {e}")
        return False

def run_simple_test():
    """Run simple Trade Tracker test"""
    print("🚀 STARTING SIMPLE TRADE TRACKER TEST")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_telegram_notifier_methods,
        test_trade_tracker_basic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 SIMPLE TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ ALL TESTS PASSED - Trade Tracker components are working!")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = run_simple_test()
    print(f"\n🎯 Final result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
