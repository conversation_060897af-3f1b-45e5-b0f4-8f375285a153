#!/usr/bin/env python3
"""
🔧 TEST DUMP DETECTOR FIX
=========================

Quick test để kiểm tra dump detector initialization fix.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dump_detector_fix():
    """Test dump detector with correct parameters."""
    print("🔧 TESTING DUMP DETECTOR FIX")
    print("=" * 50)
    
    try:
        from dump_detector import UltraEarlyDumpDetector
        
        # Test with correct parameter name
        print("📊 Testing UltraEarlyDumpDetector with correct parameters...")
        
        dump_detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=0.7,  # ✅ CORRECT parameter name
            whale_threshold=50000,
            pre_dump_lookback=60,
            min_confidence=0.70
        )
        
        print(f"  ✅ Dump Detector: Initialized successfully!")
        print(f"    - Type: {type(dump_detector)}")
        print(f"    - Has analyze method: {hasattr(dump_detector, 'analyze')}")
        print(f"    - Has detect_ultra_early_dump: {hasattr(dump_detector, 'detect_ultra_early_dump')}")
        
        return dump_detector
        
    except Exception as e:
        print(f"  ❌ Dump Detector: Still failed - {e}")
        return None

def test_consensus_with_both_detectors():
    """Test consensus with both PUMP and DUMP detectors."""
    print("\n🔧 TESTING CONSENSUS WITH BOTH DETECTORS")
    print("=" * 50)
    
    try:
        from dump_detector import UltraEarlyDumpDetector
        from volume_spike_detector import VolumeSpikeDetector
        from consensus_analyzer import ConsensusAnalyzer
        
        # Initialize both detectors
        dump_detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=0.7,
            whale_threshold=50000
        )
        
        volume_detector = VolumeSpikeDetector(
            spike_threshold_multiplier=2.5,
            moving_avg_period=20,
            min_data_points=30
        )
        
        print("✅ Both detectors initialized successfully")
        
        # Setup external analyzers
        external_analyzers = {
            "volume_profile_analyzer": None,
            "point_figure_analyzer": None,
            "fourier_analyzer": None,
            "volume_pattern_analyzer": None,
            "volume_spike_detector": volume_detector,
            "ai_manager": None,
            "orderbook_analyzer": None,
            "dump_detector": dump_detector,  # ✅ Now should work
            "pump_detector": volume_detector
        }
        
        # Initialize consensus analyzer
        consensus_analyzer = ConsensusAnalyzer(
            min_consensus_score=0.55,
            external_analyzers=external_analyzers
        )
        
        # Check connections
        connections = consensus_analyzer.analyzer_connections
        dump_connected = connections.get('dump_detector') is not None
        pump_connected = connections.get('pump_detector') is not None
        
        print(f"\n📊 Connection Status:")
        print(f"  🚀 Pump Detector: {'✅ Connected' if pump_connected else '❌ Not Connected'}")
        print(f"  📉 Dump Detector: {'✅ Connected' if dump_connected else '❌ Not Connected'}")
        
        connected_count = sum(1 for conn in connections.values() if conn is not None)
        print(f"  📊 Total Connected: {connected_count}/{len(connections)}")
        
        if dump_connected and pump_connected:
            print(f"\n🎉 SUCCESS! Both PUMP and DUMP detectors are connected!")
            
            # Test sample analysis
            sample_input = {
                'coin': 'TESTCOIN/USDT',
                'ai_prediction': {'ensemble_signal': 'BUY', 'ensemble_confidence': 0.7},
                'dump_analysis': {'dump_probability': 0.6, 'confidence': 0.6},
                'pump_analysis': {'pump_probability': 0.4, 'confidence': 0.4}
            }
            
            print(f"\n🧪 Testing sample analysis with both detectors...")
            result = consensus_analyzer.analyze_consensus(sample_input)
            
            if result.get('status') == 'success':
                consensus = result.get('consensus', {})
                contributing = consensus.get('signal_count', 0)
                print(f"  ✅ Analysis Success: {contributing} algorithms contributing")
                
                # Check if both detectors contributed
                algorithms = consensus.get('contributing_algorithms', [])
                pump_contrib = any('Pump' in alg.get('name', '') for alg in algorithms)
                dump_contrib = any('Dump' in alg.get('name', '') for alg in algorithms)
                
                print(f"  🚀 Pump Detector contributed: {'✅' if pump_contrib else '❌'}")
                print(f"  📉 Dump Detector contributed: {'✅' if dump_contrib else '❌'}")
                
                return True
            else:
                print(f"  ❌ Analysis failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"\n❌ Connection failed - detectors not properly connected")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run dump detector fix test."""
    print("🔧 DUMP DETECTOR FIX TEST")
    print("=" * 70)
    
    # Test 1: Dump detector initialization
    dump_detector = test_dump_detector_fix()
    
    # Test 2: Consensus with both detectors
    if dump_detector:
        success = test_consensus_with_both_detectors()
        
        if success:
            print(f"\n🎉 ALL TESTS PASSED!")
            print(f"✅ Dump detector parameter fix successful")
            print(f"✅ Both PUMP and DUMP detectors working")
            print(f"✅ Consensus analyzer can use both detectors")
            print(f"\n🚀 READY FOR PRODUCTION!")
            print(f"When you run the main bot, you should now see:")
            print(f"  ✅ Pump Detector: BUY/SELL (XX%) - Weight: 0.08")
            print(f"  ✅ Dump Detector: BUY/SELL (XX%) - Weight: 0.08")
            print(f"  📊 Total algorithms: 8 (instead of 6)")
        else:
            print(f"\n⚠️ Some tests failed - check output above")
    else:
        print(f"\n❌ Dump detector initialization still failing")
        print(f"🔧 Check dump_detector.py constructor parameters")

if __name__ == "__main__":
    main()
