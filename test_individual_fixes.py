#!/usr/bin/env python3
"""
🧪 TEST INDIVIDUAL FIXES
=======================

Test từng fix riêng lẻ.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_consensus_analyzer_fix():
    """Test consensus analyzer weight threshold fix."""
    print("⚖️ TESTING CONSENSUS ANALYZER FIX")
    print("=" * 50)
    
    try:
        import consensus_analyzer
        
        # Test weight threshold
        analyzer = consensus_analyzer.ConsensusAnalyzer()
        weight_threshold = analyzer.quality_control.get('min_weight_threshold', 0.6)
        
        print(f"⚖️ Weight threshold: {weight_threshold}")
        print(f"⚖️ Expected: <= 0.5")
        print(f"⚖️ Result: {'✅ FIXED' if weight_threshold <= 0.5 else '❌ NOT FIXED'}")
        
        return weight_threshold <= 0.5
        
    except Exception as e:
        print(f"❌ Consensus analyzer test failed: {e}")
        return False

def test_signal_manager_fix():
    """Test signal manager integration fix."""
    print("\n📡 TESTING SIGNAL MANAGER FIX")
    print("=" * 50)
    
    try:
        from signal_manager_integration import SignalManagerIntegration
        
        # Test with no Ultra Tracker (should create signal_manager)
        integration = SignalManagerIntegration(
            telegram_notifier=None,
            data_fetcher=None,
            trade_tracker=None  # No Ultra Tracker
        )
        
        has_signal_manager = hasattr(integration, 'signal_manager')
        signal_manager_value = getattr(integration, 'signal_manager', None)
        
        print(f"📡 Has signal_manager attribute: {has_signal_manager}")
        print(f"📡 Signal manager is not None: {signal_manager_value is not None}")
        print(f"📡 Result: {'✅ FIXED' if has_signal_manager else '❌ NOT FIXED'}")
        
        return has_signal_manager
        
    except Exception as e:
        print(f"❌ Signal manager test failed: {e}")
        return False

def test_chart_generator_fix():
    """Test chart generator fix."""
    print("\n📊 TESTING CHART GENERATOR FIX")
    print("=" * 50)
    
    try:
        import chart_generator
        
        # Test chart generator initialization
        chart_gen = chart_generator.EnhancedChartGenerator(
            output_dir="test_charts",
            telegram_notifier=None,
            enable_auto_delete=False,
            enable_advanced_styling=True,
            enable_interactive_charts=False,
            max_storage_mb=100
        )
        
        # Test methods
        methods = [
            'generate_fibonacci_chart',
            'generate_volume_profile_chart', 
            'generate_point_figure_chart',
            'generate_fourier_chart',
            'generate_ai_analysis_chart'
        ]
        
        all_methods_exist = True
        for method in methods:
            has_method = hasattr(chart_gen, method)
            print(f"  📊 {method}: {'✅' if has_method else '❌'}")
            if not has_method:
                all_methods_exist = False
        
        print(f"📊 Result: {'✅ FIXED' if all_methods_exist else '❌ NOT FIXED'}")
        return all_methods_exist
        
    except Exception as e:
        print(f"❌ Chart generator test failed: {e}")
        return False

def test_type_checking_fix():
    """Test type checking fix."""
    print("\n🎯 TESTING TYPE CHECKING FIX")
    print("=" * 50)
    
    try:
        # Test the type checking logic
        def test_tp_sl_check(take_profit, stop_loss):
            """Test TP/SL type checking logic."""
            return (isinstance(take_profit, (int, float)) and take_profit > 0 and 
                    isinstance(stop_loss, (int, float)) and stop_loss > 0)
        
        # Test cases
        test_cases = [
            (105.0, 95.0, True),  # Valid floats
            (105, 95, True),      # Valid ints
            ({"invalid": "dict"}, 95.0, False),  # Invalid take_profit
            (105.0, {"invalid": "dict"}, False), # Invalid stop_loss
            (-105.0, 95.0, False), # Negative take_profit
            (105.0, -95.0, False), # Negative stop_loss
        ]
        
        all_passed = True
        for tp, sl, expected in test_cases:
            result = test_tp_sl_check(tp, sl)
            status = "✅" if result == expected else "❌"
            print(f"  🎯 TP={tp}, SL={sl} -> {result} (expected {expected}) {status}")
            if result != expected:
                all_passed = False
        
        print(f"🎯 Result: {'✅ FIXED' if all_passed else '❌ NOT FIXED'}")
        return all_passed
        
    except Exception as e:
        print(f"❌ Type checking test failed: {e}")
        return False

def test_whale_activity_fix():
    """Test whale activity fix."""
    print("\n🐋 TESTING WHALE ACTIVITY FIX")
    print("=" * 50)
    
    try:
        from whale_activity_tracker import WhaleActivityTracker, WhaleAlert
        
        # Test WhaleAlert dataclass
        whale_alert = WhaleAlert(
            coin="BTCUSDT",
            whale_type="MONITORING",
            activity_type="BASELINE",
            confidence=0.25,
            estimated_impact=0.02,
            time_window="1-6 hours",
            whale_size="SMALL",
            coordination_level="LOW",
            risk_level="LOW"
        )
        
        # Test attribute access
        has_activity_type = hasattr(whale_alert, 'activity_type')
        has_whale_size = hasattr(whale_alert, 'whale_size')
        
        print(f"🐋 WhaleAlert has activity_type: {has_activity_type}")
        print(f"🐋 WhaleAlert has whale_size: {has_whale_size}")
        print(f"🐋 activity_type value: {whale_alert.activity_type}")
        print(f"🐋 whale_size value: {whale_alert.whale_size}")
        
        result = has_activity_type and has_whale_size
        print(f"🐋 Result: {'✅ FIXED' if result else '❌ NOT FIXED'}")
        return result
        
    except Exception as e:
        print(f"❌ Whale activity test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 TESTING INDIVIDUAL FIXES")
    print("=" * 70)
    
    # Test each fix individually
    fix1 = test_consensus_analyzer_fix()
    fix2 = test_signal_manager_fix()
    fix3 = test_chart_generator_fix()
    fix4 = test_type_checking_fix()
    fix5 = test_whale_activity_fix()
    
    print("\n🎯 SUMMARY OF FIXES:")
    print("=" * 50)
    print(f"⚖️ Consensus Analyzer Weight: {'✅ FIXED' if fix1 else '❌ NOT FIXED'}")
    print(f"📡 Signal Manager Integration: {'✅ FIXED' if fix2 else '❌ NOT FIXED'}")
    print(f"📊 Chart Generator: {'✅ FIXED' if fix3 else '❌ NOT FIXED'}")
    print(f"🎯 Type Checking: {'✅ FIXED' if fix4 else '❌ NOT FIXED'}")
    print(f"🐋 Whale Activity: {'✅ FIXED' if fix5 else '❌ NOT FIXED'}")
    
    total_fixed = sum([fix1, fix2, fix3, fix4, fix5])
    print(f"\n🏆 TOTAL FIXES: {total_fixed}/5 ({'✅ ALL FIXED' if total_fixed == 5 else '⚠️ PARTIAL'})")
    
    if total_fixed == 5:
        print("\n🎉 ALL ISSUES HAVE BEEN SUCCESSFULLY FIXED!")
        print("The trading bot should now run without the reported errors.")
    else:
        print(f"\n⚠️ {5-total_fixed} issues still need attention.")
