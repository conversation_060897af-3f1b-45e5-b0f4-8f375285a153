#!/usr/bin/env python3
"""
🔧 QR METHOD FIX TEST
=====================

Test script để kiểm tra method fix cho QR generator
"""

def test_qr_method_exists():
    """Test QR generator method exists"""
    print("📱 === TESTING QR METHOD FIX ===")
    print("=" * 50)
    
    try:
        from qr_code_generator import DonationQRGenerator
        
        # Create QR generator instance
        qr_gen = DonationQRGenerator()
        print("✅ DonationQRGenerator imported and created")
        
        # Check if method exists
        if hasattr(qr_gen, 'generate_all_formats'):
            print("✅ generate_all_formats method exists")
        else:
            print("❌ generate_all_formats method missing")
            return False
        
        # Check if old method doesn't exist
        if hasattr(qr_gen, 'generate_all_qr_codes'):
            print("⚠️ generate_all_qr_codes method still exists (should be removed)")
        else:
            print("✅ generate_all_qr_codes method correctly removed")
        
        # Check method signature
        import inspect
        sig = inspect.signature(qr_gen.generate_all_formats)
        print(f"📋 Method signature: generate_all_formats{sig}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing QR method: {e}")
        return False

def test_main_bot_method_fix():
    """Test main bot method fix"""
    print("\n🤖 === TESTING MAIN BOT METHOD FIX ===")
    print("=" * 50)
    
    try:
        # Check main_bot.py source code for correct method call
        with open('main_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for correct method call
        if 'generate_all_formats()' in content:
            print("✅ main_bot.py uses correct method: generate_all_formats()")
        else:
            print("❌ main_bot.py missing correct method call")
            return False
        
        # Check for incorrect method call
        if 'generate_all_qr_codes()' in content:
            print("❌ main_bot.py still uses incorrect method: generate_all_qr_codes()")
            return False
        else:
            print("✅ main_bot.py doesn't use incorrect method")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking main_bot.py: {e}")
        return False

def test_method_compatibility():
    """Test method compatibility"""
    print("\n🔧 === TESTING METHOD COMPATIBILITY ===")
    print("=" * 50)
    
    try:
        from qr_code_generator import DonationQRGenerator
        
        qr_gen = DonationQRGenerator()
        
        # Test method call (without actually generating)
        print("🔧 Testing method call compatibility...")
        
        # Check if method is callable
        if callable(getattr(qr_gen, 'generate_all_formats', None)):
            print("✅ generate_all_formats is callable")
        else:
            print("❌ generate_all_formats is not callable")
            return False
        
        # Check return type annotation
        import inspect
        sig = inspect.signature(qr_gen.generate_all_formats)
        return_annotation = sig.return_annotation
        
        if return_annotation == dict:
            print("✅ Method returns dict as expected")
        else:
            print(f"⚠️ Method return type: {return_annotation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing method compatibility: {e}")
        return False

def show_fix_summary():
    """Show fix summary"""
    print("\n📋 === FIX SUMMARY ===")
    print("=" * 50)
    
    print("🔧 ISSUE IDENTIFIED:")
    print("  ❌ main_bot.py called: qr_generator.generate_all_qr_codes()")
    print("  ✅ Correct method is: qr_generator.generate_all_formats()")
    
    print("\n🔧 FIX APPLIED:")
    print("  📝 Updated main_bot.py line 547:")
    print("  OLD: qr_files = self.qr_generator.generate_all_qr_codes()")
    print("  NEW: qr_files = self.qr_generator.generate_all_formats()")
    
    print("\n✅ EXPECTED RESULT:")
    print("  🚀 Bot initialization should work without AttributeError")
    print("  📱 QR codes should generate successfully")
    print("  🎯 All 4 QR formats should be created")

def main():
    """Main test function"""
    print("🔧 === QR METHOD FIX TEST ===")
    print("🎯 Testing fix for generate_all_qr_codes() method error")
    print()
    
    # Run tests
    test1 = test_qr_method_exists()
    test2 = test_main_bot_method_fix()
    test3 = test_method_compatibility()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("QR Method Exists", test1),
        ("Main Bot Method Fix", test2),
        ("Method Compatibility", test3)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed")
    
    # Show fix summary
    show_fix_summary()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ QR METHOD FIX SUCCESSFUL!")
        print("  • QR generator method: CORRECT")
        print("  • Main bot method call: FIXED")
        print("  • Method compatibility: VERIFIED")
        
        print("\n🚀 READY TO TEST:")
        print("  1. Try bot initialization: python -c \"from main_bot import TradingBot; TradingBot()\"")
        print("  2. Test QR generation: python qr_code_generator.py")
        print("  3. Test full integration: python main_bot.py --telegram")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the method names and try again.")

if __name__ == "__main__":
    main()
