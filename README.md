# TradingBotV2

An AI-powered trading bot for cryptocurrency markets.

This project aims to leverage various technical indicators and machine learning models
to generate trading signals for assets on Binance.

## Features (Planned/Implemented)
- Data fetching from Binance
- Volume spike detection
- Signal processing (<PERSON>ig<PERSON>ag, <PERSON>bon<PERSON><PERSON>, Fourier)
- Multiple AI model integration (LSTM, XGBoost, RandomForest, etc.)
- Telegram notifications for signals and bot status
- Trade tracking and performance logging

## Setup
1. Ensure Python 3.9+ is installed.
2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   # On Windows:
   # venv\Scripts\activate
   # On Linux/macOS:
   # source venv/bin/activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   # Or, if using setup.py for development:
   # pip install -e .
   ```
4. Create a `.env` file in the root directory (e.g., `e:\BOT-2\.env`) and populate it with your API keys and configurations. Refer to the required environment variables in `main_bot.py`. Example:
   ```env
   # Binance API Credentials (Optional - if you implement live trading)
   # BINANCE_API_KEY="YOUR_BINANCE_API_KEY"
   # BINANCE_SECRET_KEY="YOUR_BINANCE_SECRET_KEY"

   # Telegram Bot Credentials
   TELEGRAM_TOKEN="YOUR_TELEGRAM_BOT_TOKEN"
   TELEGRAM_CHAT_ID_SPOT="YOUR_TELEGRAM_CHAT_ID"

   # Bot Configuration
   # SYMBOLS_TO_EXCLUDE="UP,DOWN,BEAR,BULL"
   # TIMEFRAMES_FOR_ANALYSIS="4h,1d"
   # PRIMARY_SIGNAL_TIMEFRAME="1h"
   # ... other env variables used by the bot
   ```

## Running the Bot
```bash
python main_bot.py
```
