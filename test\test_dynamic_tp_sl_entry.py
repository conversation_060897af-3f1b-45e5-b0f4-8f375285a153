#!/usr/bin/env python3
"""
🧪 TEST: Dynamic TP/SL/Entry System
Test để kiểm tra hệ thống tính toán Entry, TP, SL động theo tất cả thuật toán
"""

import sys
import os
import pandas as pd
import numpy as np

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_mock_analysis_data():
    """Create comprehensive mock analysis data from all algorithms"""
    return {
        "ai_analysis": {
            "status": "success",
            "confidence": 0.85,
            "predictions": {
                "predicted_price": 51000.0,
                "price_targets": {
                    "upside_target": 53000.0,
                    "downside_risk": 48000.0
                }
            },
            "entry_price": 50500.0,
            "take_profit": 52500.0,
            "stop_loss": 49000.0
        },
        "volume_profile_analysis": {
            "status": "success",
            "vpoc": {"price": 50200.0},
            "value_area": {"high": 51000.0, "low": 49500.0},
            "support_resistance": {
                "support_levels": [
                    {"price": 49000.0, "strength": 0.8},
                    {"price": 49500.0, "strength": 0.6}
                ],
                "resistance_levels": [
                    {"price": 51500.0, "strength": 0.7},
                    {"price": 52000.0, "strength": 0.9}
                ]
            },
            "distribution_metrics": {"concentration_ratio": 0.75}
        },
        "point_figure_analysis": {
            "status": "success",
            "support_resistance": {
                "support_levels": [
                    {"price": 49200.0, "strength": 0.8},
                    {"price": 49800.0, "strength": 0.6}
                ],
                "resistance_levels": [
                    {"price": 51200.0, "strength": 0.7},
                    {"price": 51800.0, "strength": 0.8}
                ]
            },
            "price_objectives": {
                "bullish_targets": [
                    {"price": 52000.0, "confidence": 0.8}
                ],
                "bearish_targets": [
                    {"price": 48500.0, "confidence": 0.7}
                ]
            }
        },
        "fourier_analysis": {
            "status": "success",
            "cycle_analysis": {
                "dominant_cycle_length": 24,
                "average_amplitude": 0.025
            },
            "harmonic_levels": {
                "support_harmonics": [
                    {"price": 49300.0, "strength": 0.7}
                ],
                "resistance_harmonics": [
                    {"price": 51700.0, "strength": 0.8}
                ]
            },
            "signal_strength": 0.72
        },
        "orderbook_analysis": {
            "status": "success",
            "significant_levels": [
                {"price": 49400.0, "type": "support", "strength": 0.8},
                {"price": 51600.0, "type": "resistance", "strength": 0.9}
            ],
            "imbalance_zones": [
                {"price": 49600.0, "bias": "bullish", "strength": 0.6},
                {"price": 51400.0, "bias": "bearish", "strength": 0.7}
            ]
        },
        "volume_pattern_analysis": {
            "status": "success",
            "volume_signals": [
                {"price": 49700.0, "signal": "BUY", "strength": 0.7}
            ],
            "accumulation_zones": [
                {"price": 49600.0, "strength": 0.8}
            ],
            "distribution_zones": [
                {"price": 51300.0, "strength": 0.6}
            ],
            "pattern_strength": 0.65
        },
        "consensus_analysis": {
            "status": "success",
            "consensus_signal": "BUY",
            "confidence": 0.78
        },
        "dump_analysis": {
            "status": "success",
            "dump_probability": 0.15,
            "recovery_potential": 0.75
        }
    }

def create_mock_ohlcv_data():
    """Create mock OHLCV data"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    
    np.random.seed(42)
    base_price = 50000
    
    # Create realistic price movement
    returns = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    data = []
    for i in range(len(dates)):
        price = prices[i]
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': dates[i],
            'open': price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume
        })
    
    return pd.DataFrame(data).set_index('timestamp')

def test_dynamic_entry_calculation():
    """Test dynamic entry price calculation"""
    print("🧪 === TESTING DYNAMIC ENTRY CALCULATION ===")
    
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        analyzer = IntelligentTPSLAnalyzer(crypto_mode=True)
        ohlcv_data = create_mock_ohlcv_data()
        analysis_data = create_mock_analysis_data()
        current_price = ohlcv_data['close'].iloc[-1]
        
        print(f"📊 Current Price: ${current_price:,.2f}")
        print(f"📊 Analysis Data Sources: {len(analysis_data)}")
        
        # Test BUY signal
        print(f"\n🔍 Testing BUY Signal Entry Calculation...")
        buy_result = analyzer.calculate_dynamic_entry_tp_sl("BUY", ohlcv_data, analysis_data)
        
        if buy_result["status"] == "success":
            entry_price = buy_result["entry_price"]
            take_profit = buy_result["take_profit"]
            stop_loss = buy_result["stop_loss"]
            
            print(f"✅ BUY Signal Results:")
            print(f"  Entry: ${entry_price:,.2f}")
            print(f"  Take Profit: ${take_profit:,.2f}")
            print(f"  Stop Loss: ${stop_loss:,.2f}")
            print(f"  R:R Ratio: {buy_result['risk_reward_ratio']:.2f}")
            print(f"  Confidence: {buy_result['confidence']:.2f}")
            print(f"  Algorithms Used: {len(buy_result.get('algorithms_used', []))}")
            
            # Validate constraints
            if stop_loss < entry_price < take_profit:
                print(f"  ✅ Price order correct: SL < Entry < TP")
            else:
                print(f"  ❌ Price order incorrect")
            
            sl_distance = entry_price - stop_loss
            tp_distance = take_profit - entry_price
            
            if sl_distance < tp_distance:
                print(f"  ✅ SL distance < TP distance")
            else:
                print(f"  ❌ SL distance >= TP distance")
            
            return True
        else:
            print(f"❌ BUY calculation failed: {buy_result.get('message', 'Unknown error')}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sell_signal_dynamic():
    """Test SELL signal dynamic calculation"""
    print("\n🧪 === TESTING SELL SIGNAL DYNAMIC CALCULATION ===")
    
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        analyzer = IntelligentTPSLAnalyzer(crypto_mode=True)
        ohlcv_data = create_mock_ohlcv_data()
        analysis_data = create_mock_analysis_data()
        
        # Modify analysis data for SELL signal
        analysis_data["consensus_analysis"]["consensus_signal"] = "SELL"
        analysis_data["ai_analysis"]["predictions"]["price_targets"]["downside_target"] = 48000.0
        analysis_data["ai_analysis"]["predictions"]["price_targets"]["upside_risk"] = 52000.0
        
        print(f"🔍 Testing SELL Signal Entry Calculation...")
        sell_result = analyzer.calculate_dynamic_entry_tp_sl("SELL", ohlcv_data, analysis_data)
        
        if sell_result["status"] == "success":
            entry_price = sell_result["entry_price"]
            take_profit = sell_result["take_profit"]
            stop_loss = sell_result["stop_loss"]
            
            print(f"✅ SELL Signal Results:")
            print(f"  Entry: ${entry_price:,.2f}")
            print(f"  Take Profit: ${take_profit:,.2f}")
            print(f"  Stop Loss: ${stop_loss:,.2f}")
            print(f"  R:R Ratio: {sell_result['risk_reward_ratio']:.2f}")
            print(f"  Confidence: {sell_result['confidence']:.2f}")
            
            # Validate SELL constraints
            if take_profit < entry_price < stop_loss:
                print(f"  ✅ SELL price order correct: TP < Entry < SL")
            else:
                print(f"  ❌ SELL price order incorrect")
            
            return True
        else:
            print(f"❌ SELL calculation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_constraint_validation():
    """Test TP/SL constraint validation"""
    print("\n🧪 === TESTING CONSTRAINT VALIDATION ===")
    
    try:
        from intelligent_tp_sl_analyzer import IntelligentTPSLAnalyzer
        
        analyzer = IntelligentTPSLAnalyzer(crypto_mode=True)
        ohlcv_data = create_mock_ohlcv_data()
        
        # Test with minimal analysis data
        minimal_data = {
            "ai_analysis": {
                "status": "success",
                "confidence": 0.5
            }
        }
        
        result = analyzer.calculate_dynamic_entry_tp_sl("BUY", ohlcv_data, minimal_data)
        
        if result["status"] == "success":
            entry = result["entry_price"]
            tp = result["take_profit"]
            sl = result["stop_loss"]
            rr = result["risk_reward_ratio"]
            
            print(f"✅ Constraint Validation Results:")
            print(f"  Entry: ${entry:,.2f}")
            print(f"  TP: ${tp:,.2f}")
            print(f"  SL: ${sl:,.2f}")
            print(f"  R:R: {rr:.2f}")
            
            # Check minimum R:R ratio
            if rr >= analyzer.min_rr_ratio:
                print(f"  ✅ R:R ratio >= minimum ({analyzer.min_rr_ratio})")
            else:
                print(f"  ❌ R:R ratio < minimum")
            
            # Check maximum R:R ratio
            if rr <= analyzer.max_rr_ratio:
                print(f"  ✅ R:R ratio <= maximum ({analyzer.max_rr_ratio})")
            else:
                print(f"  ❌ R:R ratio > maximum")
            
            return True
        else:
            print(f"❌ Constraint validation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all dynamic TP/SL/Entry tests"""
    print("🧪 === DYNAMIC TP/SL/ENTRY SYSTEM TEST ===")
    
    test1 = test_dynamic_entry_calculation()
    test2 = test_sell_signal_dynamic()
    test3 = test_constraint_validation()
    
    if test1 and test2 and test3:
        print("\n🎉 SUCCESS: Dynamic TP/SL/Entry system working!")
        print("✅ Entry prices calculated dynamically from algorithms")
        print("✅ TP/SL constraints properly validated")
        print("✅ SL < TP constraint enforced")
        print("✅ Minimum/maximum distances enforced")
        print("✅ Market-adaptive calculations working")
    else:
        print("\n❌ FAILED: Some tests failed")
    
    return test1 and test2 and test3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
