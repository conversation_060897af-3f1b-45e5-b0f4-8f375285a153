#!/usr/bin/env python3
"""
👑 ADMIN SYSTEM MANAGER V4.0
============================

Comprehensive admin system management for the Enhanced Trading Bot.
Extracted from main_bot.py for better organization and maintainability.

Features:
- Admin system initialization and management
- System integration and cross-referencing
- Health monitoring and status reporting
- Performance analytics and statistics
"""

import time
from typing import Dict, Any, Optional

class AdminSystemManager:
    """👑 Comprehensive admin system manager."""
    
    def __init__(self, bot_instance=None):
        """Initialize admin system manager."""
        self.bot = bot_instance
        self.member_manager = None
        self.admin_commands = None
        self.hidden_admin_csv = None
        self.message_handler = None
        
        # Performance tracking
        self.admin_performance_stats = {
            "initialization_time": 0,
            "systems_loaded": 0,
            "systems_failed": 0,
            "integration_success": False,
            "last_health_check": 0
        }
        
    def initialize_admin_systems(self) -> bool:
        """👑 Initialize comprehensive admin systems."""
        try:
            start_time = time.time()
            print("👑 Initializing Enhanced Admin Systems V4.0...")
            
            # Initialize member manager
            self.member_manager = self._initialize_member_manager()
            if self.member_manager:
                self.admin_performance_stats["systems_loaded"] += 1
                print("    ✅ Member Manager initialized")
            else:
                self.admin_performance_stats["systems_failed"] += 1
                print("    ❌ Member Manager failed")
            
            # Initialize admin commands
            self.admin_commands = self._initialize_admin_commands()
            if self.admin_commands:
                self.admin_performance_stats["systems_loaded"] += 1
                print("    ✅ Admin Commands initialized")
            else:
                self.admin_performance_stats["systems_failed"] += 1
                print("    ❌ Admin Commands failed")
            
            # Initialize hidden CSV system
            self.hidden_admin_csv = self._initialize_hidden_csv_system()
            if self.hidden_admin_csv:
                self.admin_performance_stats["systems_loaded"] += 1
                print("    ✅ Hidden CSV System initialized")
            else:
                self.admin_performance_stats["systems_failed"] += 1
                print("    ❌ Hidden CSV System failed")
            
            # Initialize message handler
            self.message_handler = self._initialize_message_handler()
            if self.message_handler:
                self.admin_performance_stats["systems_loaded"] += 1
                print("    ✅ Message Handler initialized")
            else:
                self.admin_performance_stats["systems_failed"] += 1
                print("    ❌ Message Handler failed")
            
            # Integrate systems
            integration_success = self.integrate_admin_systems()
            self.admin_performance_stats["integration_success"] = integration_success
            
            # Calculate initialization time
            self.admin_performance_stats["initialization_time"] = time.time() - start_time
            
            # Determine overall success
            systems_loaded = self.admin_performance_stats["systems_loaded"]
            total_systems = 4  # member_manager, admin_commands, hidden_csv, message_handler
            
            success_rate = systems_loaded / total_systems
            overall_success = success_rate >= 0.5 and integration_success
            
            print(f"👑 Admin Systems Initialization: {'✅ SUCCESS' if overall_success else '⚠️ PARTIAL'}")
            print(f"    📊 Systems loaded: {systems_loaded}/{total_systems}")
            print(f"    🔗 Integration: {'✅' if integration_success else '❌'}")
            print(f"    ⏱️ Time: {self.admin_performance_stats['initialization_time']:.2f}s")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ Admin systems initialization failed: {e}")
            return False
    
    def _initialize_member_manager(self) -> Optional[Any]:
        """Initialize member manager system."""
        try:
            if not self.bot:
                return None
                
            # Try to get from bot's communication modules
            comm_modules = getattr(self.bot, 'COMMUNICATION_MODULES', {})
            member_manager_class = comm_modules.get('telegram_member_manager')
            
            if member_manager_class and self.bot.notifier:
                return member_manager_class(
                    telegram_notifier=self.bot.notifier,
                    bot_token=getattr(self.bot.notifier, 'bot_token', ''),
                    main_chat_id=getattr(self.bot.notifier, 'chat_id', '')
                )
            else:
                print("    ⚠️ Member manager dependencies not available")
                return None
                
        except Exception as e:
            print(f"    ❌ Member manager initialization error: {e}")
            return None
    
    def _initialize_admin_commands(self) -> Optional[Any]:
        """Initialize admin commands system."""
        try:
            if not self.bot:
                return None
                
            # Try to get from bot's communication modules
            comm_modules = getattr(self.bot, 'COMMUNICATION_MODULES', {})
            admin_commands_class = comm_modules.get('member_admin_commands')
            
            if admin_commands_class and self.bot.notifier:
                return admin_commands_class(
                    telegram_notifier=self.bot.notifier,
                    main_bot=self.bot
                )
            else:
                print("    ⚠️ Admin commands dependencies not available")
                return None
                
        except Exception as e:
            print(f"    ❌ Admin commands initialization error: {e}")
            return None
    
    def _initialize_hidden_csv_system(self) -> Optional[Any]:
        """Initialize hidden CSV system."""
        try:
            if not self.bot:
                return None
                
            # Try to get from bot's communication modules
            comm_modules = getattr(self.bot, 'COMMUNICATION_MODULES', {})
            csv_system_class = comm_modules.get('hidden_admin_csv_system')
            
            if csv_system_class and self.bot.notifier:
                return csv_system_class(
                    telegram_notifier=self.bot.notifier,
                    main_bot=self.bot
                )
            else:
                print("    ⚠️ Hidden CSV system dependencies not available")
                return None
                
        except Exception as e:
            print(f"    ❌ Hidden CSV system initialization error: {e}")
            return None
    
    def _initialize_message_handler(self) -> Optional[Any]:
        """Initialize message handler system."""
        try:
            if not self.bot:
                return None
                
            # Try to get from bot's communication modules
            comm_modules = getattr(self.bot, 'COMMUNICATION_MODULES', {})
            handler_class = comm_modules.get('telegram_message_handler')
            
            if handler_class and self.bot.notifier:
                return handler_class(
                    telegram_notifier=self.bot.notifier,
                    member_manager=self.member_manager,
                    admin_commands=self.admin_commands,
                    hidden_csv_system=self.hidden_admin_csv
                )
            else:
                print("    ⚠️ Message handler dependencies not available")
                return None
                
        except Exception as e:
            print(f"    ❌ Message handler initialization error: {e}")
            return None
    
    def integrate_admin_systems(self) -> bool:
        """🔗 Integrate admin systems for seamless operation."""
        try:
            print("    🔗 Integrating admin systems...")

            # Cross-reference systems
            if self.member_manager and self.admin_commands:
                # Link member manager to admin commands
                if hasattr(self.admin_commands, 'set_member_manager'):
                    self.admin_commands.set_member_manager(self.member_manager)
                    print("    ✅ Member manager linked to admin commands")

            if self.hidden_admin_csv and self.member_manager:
                # Link CSV system to member manager
                if hasattr(self.hidden_admin_csv, 'set_member_manager'):
                    self.hidden_admin_csv.set_member_manager(self.member_manager)
                    print("    ✅ CSV system linked to member manager")

            # Setup shared resources
            shared_resources = {
                "backup_manager": getattr(self.bot, 'backup_mgr', None),
                "data_logger": getattr(self.bot, 'logger', None),
                "telegram_notifier": getattr(self.bot, 'notifier', None),
                "chart_generator": getattr(self.bot, 'chart_generator', None)
            }

            for system_name, system in [
                ("member_manager", self.member_manager),
                ("admin_commands", self.admin_commands),
                ("hidden_admin_csv", self.hidden_admin_csv)
            ]:
                if system and hasattr(system, 'set_shared_resources'):
                    try:
                        system.set_shared_resources(shared_resources)
                        print(f"    ✅ Shared resources configured for {system_name}")
                    except Exception as resource_error:
                        print(f"    ⚠️ Resource sharing failed for {system_name}: {resource_error}")

            print("    ✅ Admin system integration completed")
            return True

        except Exception as e:
            print(f"    ❌ Admin system integration failed: {e}")
            return False
    
    def get_admin_system_status(self) -> Dict[str, Any]:
        """👑 Get comprehensive admin system status."""
        try:
            admin_systems = {
                "member_manager": self.member_manager,
                "admin_commands": self.admin_commands,
                "hidden_admin_csv": self.hidden_admin_csv,
                "message_handler": self.message_handler
            }

            system_status = {}
            total_health_score = 0.0

            for system_name, system in admin_systems.items():
                if system is not None:
                    # Check if system has health reporting
                    if hasattr(system, 'get_system_health'):
                        health = system.get_system_health()
                    else:
                        # Basic health check
                        health = {
                            "status": "operational",
                            "health_score": 0.8 if not getattr(system, 'fallback_mode', False) else 0.5,
                            "fallback_mode": getattr(system, 'fallback_mode', False)
                        }

                    system_status[system_name] = health
                    total_health_score += health.get('health_score', 0.0)
                else:
                    system_status[system_name] = {
                        "status": "unavailable",
                        "health_score": 0.0,
                        "fallback_mode": False
                    }

            # Calculate overall health
            overall_health = total_health_score / len(admin_systems)

            # Determine overall status
            if overall_health >= 0.8:
                overall_status = "excellent"
            elif overall_health >= 0.6:
                overall_status = "good"
            elif overall_health >= 0.4:
                overall_status = "limited"
            else:
                overall_status = "poor"

            return {
                "overall_status": overall_status,
                "overall_health_score": overall_health,
                "systems": system_status,
                "performance_stats": self.admin_performance_stats,
                "integration_status": all(system is not None for system in admin_systems.values())
            }

        except Exception as e:
            return {
                "overall_status": "error",
                "overall_health_score": 0.0,
                "systems": {},
                "error": str(e)
            }
