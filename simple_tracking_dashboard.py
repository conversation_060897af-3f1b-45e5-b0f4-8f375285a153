#!/usr/bin/env python3
"""
📊 SIMPLE TRACKING DASHBOARD
===========================
Simple dashboard to view tracking statistics without Flask dependency.
"""

import os
import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

def load_ultra_tracker_data():
    """🚀 Load Ultra Tracker data from backup files."""
    try:
        # Try different backup file locations
        backup_files = [
            "backup/ultra_tracker_state.json",
            "backup/trade_tracker_state.json", 
            "backup/tracker_backup.json",
            "ultra_tracker_backup.json"
        ]
        
        for backup_file in backup_files:
            if os.path.exists(backup_file):
                with open(backup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"✅ Loaded Ultra Tracker data from: {backup_file}")
                    return data
        
        print("⚠️ No Ultra Tracker backup files found")
        return {}
        
    except Exception as e:
        print(f"❌ Error loading Ultra Tracker data: {e}")
        return {}

def load_pnl_data():
    """💰 Load P&L data from database."""
    try:
        db_files = ["trading_performance.db", "demo_trading_performance.db"]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                with sqlite3.connect(db_file) as conn:
                    cursor = conn.cursor()
                    
                    # Get recent trades
                    cursor.execute('''
                        SELECT signal_type, signal_source, COUNT(*) as total_trades,
                               SUM(CASE WHEN profit_loss_pct > 0 THEN 1 ELSE 0 END) as winning_trades,
                               AVG(profit_loss_pct) as avg_pnl,
                               SUM(profit_loss_pct) as total_pnl
                        FROM trades 
                        WHERE entry_time >= date('now', '-30 days')
                        GROUP BY signal_type, signal_source
                        ORDER BY total_pnl DESC
                    ''')
                    
                    results = cursor.fetchall()
                    pnl_data = {}
                    
                    for row in results:
                        signal_type, signal_source, total_trades, winning_trades, avg_pnl, total_pnl = row
                        key = f"{signal_type}_{signal_source}"
                        
                        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                        
                        pnl_data[key] = {
                            'signal_type': signal_type,
                            'signal_source': signal_source,
                            'total_trades': total_trades,
                            'winning_trades': winning_trades,
                            'win_rate': round(win_rate, 1),
                            'avg_pnl': round(avg_pnl or 0, 2),
                            'total_pnl': round(total_pnl or 0, 2)
                        }
                    
                    print(f"✅ Loaded P&L data from: {db_file} ({len(pnl_data)} entries)")
                    return pnl_data
        
        print("⚠️ No P&L database files found")
        return {}
        
    except Exception as e:
        print(f"❌ Error loading P&L data: {e}")
        return {}

def load_signal_log():
    """📊 Load signal log data."""
    try:
        log_files = ["trade_signals_log_v3.csv", "signals_log.csv", "trading_signals.csv"]
        
        for log_file in log_files:
            if os.path.exists(log_file):
                # Simple CSV reading without pandas
                signals = []
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if len(lines) > 1:  # Has header + data
                        header = lines[0].strip().split(',')
                        for line in lines[1:]:
                            values = line.strip().split(',')
                            if len(values) >= len(header):
                                signal = dict(zip(header, values))
                                signals.append(signal)
                
                print(f"✅ Loaded signal log from: {log_file} ({len(signals)} signals)")
                return signals
        
        print("⚠️ No signal log files found")
        return []
        
    except Exception as e:
        print(f"❌ Error loading signal log: {e}")
        return []

def analyze_ultra_tracker_data(tracker_data):
    """🚀 Analyze Ultra Tracker data."""
    if not tracker_data:
        return {}
    
    active_signals = tracker_data.get('active_signals', [])
    completed_signals = tracker_data.get('completed_signals', [])
    
    # Group by analyzer type
    analysis = {
        'total_active': len(active_signals),
        'total_completed': len(completed_signals),
        'by_analyzer': {}
    }
    
    # Analyze active signals
    for signal in active_signals:
        analyzer = signal.get('analyzer_type', 'unknown')
        if analyzer not in analysis['by_analyzer']:
            analysis['by_analyzer'][analyzer] = {'active': 0, 'completed': 0, 'success_rate': 0}
        analysis['by_analyzer'][analyzer]['active'] += 1
    
    # Analyze completed signals
    for signal in completed_signals:
        analyzer = signal.get('analyzer_type', 'unknown')
        if analyzer not in analysis['by_analyzer']:
            analysis['by_analyzer'][analyzer] = {'active': 0, 'completed': 0, 'success_rate': 0}
        analysis['by_analyzer'][analyzer]['completed'] += 1
    
    # Calculate success rates
    for analyzer, data in analysis['by_analyzer'].items():
        completed = data['completed']
        if completed > 0:
            successful = len([s for s in completed_signals 
                            if s.get('analyzer_type') == analyzer and 
                            s.get('close_reason') in ['TAKE_PROFIT', 'TP']])
            data['success_rate'] = round((successful / completed) * 100, 1)
    
    return analysis

def generate_dashboard_report():
    """📊 Generate comprehensive dashboard report."""
    print("🚀 ENHANCED TRACKING DASHBOARD")
    print("=" * 60)
    print(f"⏰ Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Load all data
    print("📂 Loading tracking data...")
    ultra_tracker_data = load_ultra_tracker_data()
    pnl_data = load_pnl_data()
    signal_log = load_signal_log()
    
    print()
    print("🚀 ULTRA TRACKER STATUS")
    print("-" * 30)
    
    if ultra_tracker_data:
        tracker_analysis = analyze_ultra_tracker_data(ultra_tracker_data)
        
        print(f"📊 Total Active Signals: {tracker_analysis['total_active']}")
        print(f"✅ Total Completed Signals: {tracker_analysis['total_completed']}")
        print()
        
        if tracker_analysis['by_analyzer']:
            print("📈 By Algorithm:")
            for analyzer, data in tracker_analysis['by_analyzer'].items():
                print(f"  🎯 {analyzer.upper()}:")
                print(f"    📊 Active: {data['active']}")
                print(f"    ✅ Completed: {data['completed']}")
                print(f"    📈 Success Rate: {data['success_rate']}%")
                print()
    else:
        print("⚠️ No Ultra Tracker data available")
        print("   💡 Start main_bot.py to generate tracking data")
    
    print()
    print("💰 P&L TRACKING STATISTICS")
    print("-" * 30)
    
    if pnl_data:
        total_trades = sum(data['total_trades'] for data in pnl_data.values())
        total_winning = sum(data['winning_trades'] for data in pnl_data.values())
        total_pnl = sum(data['total_pnl'] for data in pnl_data.values())
        overall_win_rate = (total_winning / total_trades * 100) if total_trades > 0 else 0
        
        print(f"📊 Overall Statistics:")
        print(f"  📈 Total Trades: {total_trades}")
        print(f"  🏆 Winning Trades: {total_winning}")
        print(f"  📊 Win Rate: {overall_win_rate:.1f}%")
        print(f"  💰 Total P&L: {total_pnl:.2f}%")
        print()
        
        print("📈 By Algorithm:")
        for key, data in sorted(pnl_data.items(), key=lambda x: x[1]['total_pnl'], reverse=True):
            print(f"  🎯 {data['signal_type'].upper()} - {data['signal_source']}:")
            print(f"    📊 Trades: {data['total_trades']}")
            print(f"    🏆 Win Rate: {data['win_rate']}%")
            print(f"    💰 Total P&L: {data['total_pnl']:.2f}%")
            print(f"    📈 Avg P&L: {data['avg_pnl']:.2f}%")
            print()
    else:
        print("⚠️ No P&L data available")
        print("   💡 P&L tracking will start when signals are generated")
    
    print()
    print("📊 SIGNAL LOG ANALYSIS")
    print("-" * 30)
    
    if signal_log:
        print(f"📈 Total Signals in Log: {len(signal_log)}")
        
        # Analyze by signal type
        signal_types = {}
        for signal in signal_log[-50:]:  # Last 50 signals
            signal_type = signal.get('signal_type', 'unknown')
            if signal_type not in signal_types:
                signal_types[signal_type] = 0
            signal_types[signal_type] += 1
        
        print("📊 Recent Signal Types:")
        for signal_type, count in sorted(signal_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  🎯 {signal_type}: {count} signals")
    else:
        print("⚠️ No signal log data available")
        print("   💡 Signal log will be created when bot starts generating signals")
    
    print()
    print("🔧 SYSTEM INTEGRATION STATUS")
    print("-" * 30)
    print("✅ Enhanced P&L Tracker: Ready")
    print("✅ Ultra Tracker Integration: Ready") 
    print("✅ Algorithm-Specific Tracking: Ready")
    print("✅ Dashboard Data Aggregation: Ready")
    print("✅ Real-time Statistics: Ready")
    print()
    
    print("🎯 NEXT STEPS")
    print("-" * 30)
    print("1. 🚀 Start main bot: python main_bot.py")
    print("2. 📊 Signals will be automatically tracked")
    print("3. 🔄 Run this dashboard again to see live data")
    print("4. 🌐 Install Flask for web dashboard: pip install flask")
    print()
    
    print("✅ Enhanced tracking system ready!")
    print("=" * 60)

def create_html_dashboard():
    """🌐 Create HTML dashboard file."""
    try:
        # Load data
        ultra_tracker_data = load_ultra_tracker_data()
        pnl_data = load_pnl_data()
        
        # Generate HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced Tracking Dashboard</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .card {{
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        .stat-card {{
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }}
        .stat-value {{ font-size: 2em; font-weight: bold; margin: 10px 0; }}
        .refresh-btn {{
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Enhanced Tracking Dashboard</h1>
            <p>Real-time Performance Monitoring</p>
            <p>Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="card">
            <h2>🚀 Ultra Tracker Status</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>📊 Active Signals</h3>
                    <div class="stat-value">{len(ultra_tracker_data.get('active_signals', []))}</div>
                </div>
                <div class="stat-card">
                    <h3>✅ Completed</h3>
                    <div class="stat-value">{len(ultra_tracker_data.get('completed_signals', []))}</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>💰 P&L Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>📈 Total Trades</h3>
                    <div class="stat-value">{sum(data['total_trades'] for data in pnl_data.values()) if pnl_data else 0}</div>
                </div>
                <div class="stat-card">
                    <h3>🏆 Win Rate</h3>
                    <div class="stat-value">{round(sum(data['winning_trades'] for data in pnl_data.values()) / sum(data['total_trades'] for data in pnl_data.values()) * 100, 1) if pnl_data and sum(data['total_trades'] for data in pnl_data.values()) > 0 else 0}%</div>
                </div>
                <div class="stat-card">
                    <h3>💰 Total P&L</h3>
                    <div class="stat-value">{round(sum(data['total_pnl'] for data in pnl_data.values()), 2) if pnl_data else 0}%</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🎯 System Status</h2>
            <p>✅ Enhanced P&L Tracker: Ready</p>
            <p>✅ Ultra Tracker Integration: Ready</p>
            <p>✅ Algorithm-Specific Tracking: Ready</p>
            <p>✅ Real-time Statistics: Ready</p>
        </div>
        
        <div style="text-align: center;">
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
        </div>
    </div>
    
    <script>
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>
"""
        
        with open("tracking_dashboard.html", 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ HTML dashboard created: tracking_dashboard.html")
        
        # Try to open in browser
        import webbrowser
        file_path = os.path.abspath("tracking_dashboard.html")
        webbrowser.open(f"file://{file_path}")
        print(f"🌐 Dashboard opened: file://{file_path}")
        
    except Exception as e:
        print(f"❌ Error creating HTML dashboard: {e}")

if __name__ == "__main__":
    try:
        # Generate console report
        generate_dashboard_report()
        
        # Create HTML dashboard
        print("\n🌐 Creating HTML dashboard...")
        create_html_dashboard()
        
        print("\n🎯 Dashboard options:")
        print("  📊 Console report: python simple_tracking_dashboard.py")
        print("  🌐 HTML dashboard: tracking_dashboard.html")
        print("  🚀 Full dashboard: python dashboard.py (after installing Flask)")
        
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
