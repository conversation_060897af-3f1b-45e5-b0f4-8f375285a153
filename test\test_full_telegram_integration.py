#!/usr/bin/env python3
"""
🚀 FULL TELEGRAM INTEGRATION TEST
=================================

Test script để kiểm tra tích hợp hoàn chỉnh tất cả Telegram modules vào main_bot.py
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_telegram_modules_import():
    """Test import của tất cả Telegram modules"""
    print("📦 === TESTING TELEGRAM MODULES IMPORT ===")
    print("=" * 50)
    
    try:
        # Test basic Telegram modules
        print("🔧 Testing basic Telegram modules...")
        
        import telegram_notifier
        print("  ✅ telegram_notifier")
        
        import telegram_member_manager
        print("  ✅ telegram_member_manager")
        
        import telegram_message_handler
        print("  ✅ telegram_message_handler")
        
        import member_admin_commands
        print("  ✅ member_admin_commands")
        
        import hidden_admin_csv_system
        print("  ✅ hidden_admin_csv_system")
        
        import qr_code_generator
        print("  ✅ qr_code_generator")
        
        import bot_warning_message
        print("  ✅ bot_warning_message")
        
        import admin_config
        print("  ✅ admin_config")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing Telegram modules: {e}")
        return False

def test_main_bot_integration():
    """Test main_bot integration với Telegram modules"""
    print("\n🤖 === TESTING MAIN BOT INTEGRATION ===")
    print("=" * 50)
    
    try:
        print("🔧 Testing main_bot imports...")
        
        # Test main bot import
        import main_bot
        print("  ✅ main_bot")
        
        # Test TradingBot class
        bot_class = main_bot.TradingBot
        print("  ✅ TradingBot class")
        
        # Check new methods
        new_methods = [
            'add_warning_to_signal',
            'send_signal_with_warning',
            'start_telegram_integration',
            'run_with_telegram_integration'
        ]
        
        print("🔧 Checking new Telegram methods...")
        for method in new_methods:
            if hasattr(bot_class, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} - Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing main bot integration: {e}")
        return False

def test_bot_initialization():
    """Test bot initialization với tất cả Telegram components"""
    print("\n🚀 === TESTING BOT INITIALIZATION ===")
    print("=" * 50)
    
    try:
        print("🔧 Testing bot initialization...")
        
        # Import and create bot instance
        from main_bot import TradingBot
        
        print("  🤖 Creating TradingBot instance...")
        bot = TradingBot()
        
        # Check Telegram components
        telegram_components = [
            ('notifier', 'Telegram Notifier'),
            ('qr_generator', 'QR Code Generator'),
            ('member_manager', 'Member Manager'),
            ('admin_commands', 'Admin Commands'),
            ('hidden_admin_csv', 'Hidden Admin CSV'),
            ('message_handler', 'Message Handler'),
            ('warning_config', 'Warning Config')
        ]
        
        print("🔧 Checking Telegram components...")
        for attr, name in telegram_components:
            if hasattr(bot, attr):
                print(f"  ✅ {name}: INITIALIZED")
            else:
                print(f"  ❌ {name}: MISSING")
        
        print("✅ Bot initialization test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing bot initialization: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_telegram_integration_methods():
    """Test Telegram integration methods"""
    print("\n📱 === TESTING TELEGRAM INTEGRATION METHODS ===")
    print("=" * 50)
    
    try:
        from main_bot import TradingBot
        
        # Create bot instance
        bot = TradingBot()
        
        # Test warning methods
        print("🔧 Testing warning methods...")
        if hasattr(bot, 'add_warning_to_signal'):
            test_message = "🎯 Test signal for BTC/USDT"
            warning_message = bot.add_warning_to_signal(test_message, "general")
            if len(warning_message) > len(test_message):
                print(f"  ✅ add_warning_to_signal: +{len(warning_message) - len(test_message)} characters")
            else:
                print(f"  ❌ add_warning_to_signal: No warning added")
        else:
            print(f"  ❌ add_warning_to_signal: Method missing")
        
        # Test Telegram integration start method
        print("🔧 Testing Telegram integration methods...")
        if hasattr(bot, 'start_telegram_integration'):
            print(f"  ✅ start_telegram_integration: Available")
        else:
            print(f"  ❌ start_telegram_integration: Missing")
        
        if hasattr(bot, 'run_with_telegram_integration'):
            print(f"  ✅ run_with_telegram_integration: Available")
        else:
            print(f"  ❌ run_with_telegram_integration: Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Telegram integration methods: {e}")
        return False

def test_configuration():
    """Test configuration và environment variables"""
    print("\n⚙️ === TESTING CONFIGURATION ===")
    print("=" * 50)
    
    try:
        # Test environment variables
        print("🔧 Testing environment variables...")
        
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        print(f"  📱 TELEGRAM_BOT_TOKEN: {'✅ SET' if bot_token else '❌ MISSING'}")
        print(f"  💬 TELEGRAM_CHAT_ID: {'✅ SET' if chat_id else '❌ MISSING'}")
        
        # Test admin config
        print("🔧 Testing admin configuration...")
        import admin_config
        
        admin_users = getattr(admin_config, 'ADMIN_USERS', [])
        print(f"  👑 Admin users: {len(admin_users)} configured")
        
        # Test warning config
        print("🔧 Testing warning configuration...")
        from bot_warning_message import WARNING_CONFIG
        
        warning_features = [
            'show_warning_on_signals',
            'show_footer_on_all',
            'startup_warning'
        ]
        
        for feature in warning_features:
            status = WARNING_CONFIG.get(feature, False)
            print(f"  🚨 {feature}: {'✅ ENABLED' if status else '❌ DISABLED'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def show_integration_summary():
    """Show integration summary"""
    print("\n📋 === INTEGRATION SUMMARY ===")
    print("=" * 50)
    
    print("🚀 MAIN BOT TELEGRAM INTEGRATION:")
    print("  📱 Telegram Notifier: Enhanced with VPN support")
    print("  👥 Member Manager: Auto-welcome + 60-day trial")
    print("  👑 Admin Commands: /help_admin, /stats, /members, /extend")
    print("  🔒 Hidden Commands: /export (super admin only)")
    print("  📱 Message Handler: Polling + command processing")
    print("  📱 QR Code System: Auto-generation + donation")
    print("  🚨 Warning System: Startup + signal + member warnings")
    print("  📊 CSV Export: Admin-only member data export")
    
    print("\n🎯 USAGE OPTIONS:")
    print("  🔧 Basic Bot: python main_bot.py")
    print("  📱 Full Integration: python main_bot.py --telegram")
    print("  👑 Admin Only: python start_bot_with_admin.py")
    
    print("\n📱 TELEGRAM FEATURES:")
    print("  🤖 User Commands: /start, /help, /donate")
    print("  👑 Admin Commands: /help_admin, /stats, /members")
    print("  🔒 Hidden Commands: /export all, /export group")
    print("  📱 QR Codes: Auto-send with donation info")
    print("  🚨 Warnings: All messages include disclaimers")
    print("  👥 Member Management: Auto-welcome + trial tracking")

def main():
    """Main test function"""
    print("🚀 === FULL TELEGRAM INTEGRATION TEST ===")
    print("🎯 Testing complete integration of all Telegram modules into main_bot.py")
    print()
    
    # Run tests
    test1 = test_telegram_modules_import()
    test2 = test_main_bot_integration()
    test3 = test_bot_initialization()
    test4 = test_telegram_integration_methods()
    test5 = test_configuration()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Telegram Modules Import", test1),
        ("Main Bot Integration", test2),
        ("Bot Initialization", test3),
        ("Integration Methods", test4),
        ("Configuration", test5)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed")
    
    # Show integration summary
    show_integration_summary()
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ FULL TELEGRAM INTEGRATION SUCCESSFUL!")
        print("  • All Telegram modules: IMPORTED")
        print("  • Main bot integration: COMPLETE")
        print("  • Bot initialization: WORKING")
        print("  • Integration methods: FUNCTIONAL")
        print("  • Configuration: VALID")
        
        print("\n🚀 READY TO USE:")
        print("  1. Basic bot: python main_bot.py")
        print("  2. Full Telegram: python main_bot.py --telegram")
        print("  3. Admin only: python start_bot_with_admin.py")
        
        print("\n📱 TELEGRAM FEATURES AVAILABLE:")
        print("  • Trading signals with warnings")
        print("  • Admin commands and management")
        print("  • Member management with trials")
        print("  • QR code donation system")
        print("  • Warning system for all messages")
        print("  • CSV export for admins")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the configuration and dependencies.")

if __name__ == "__main__":
    main()
