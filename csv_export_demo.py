#!/usr/bin/env python3
"""
📊 CSV EXPORT DEMO
==================

Demo script để test CSV export functionality
"""

import os
from datetime import datetime
from telegram_member_manager import TelegramMemberManager

def demo_csv_exports():
    """Demo CSV export functions"""
    print("📊 === CSV EXPORT DEMO ===")
    print("=" * 50)
    
    # Initialize member manager
    print("🔧 Initializing Member Manager...")
    manager = TelegramMemberManager()
    
    print("\n📋 TESTING CSV EXPORTS:")
    print("=" * 50)
    
    # Test 1: Export all members
    print("\n🔄 Test 1: Export All Members")
    all_file = manager.export_all_members_csv()
    if all_file and os.path.exists(all_file):
        size = os.path.getsize(all_file)
        print(f"✅ All members exported: {all_file} ({size} bytes)")
    else:
        print("❌ All members export failed")
    
    # Test 2: Export by group
    print("\n🔄 Test 2: Export Trading Signals Group")
    group_file = manager.export_group_members_csv("-1002301937119")
    if group_file and os.path.exists(group_file):
        size = os.path.getsize(group_file)
        print(f"✅ Group members exported: {group_file} ({size} bytes)")
    else:
        print("❌ Group members export failed")
    
    # Test 3: Export new members today
    print("\n🔄 Test 3: Export New Members Today")
    new_file = manager.export_new_members_today_csv()
    if new_file and os.path.exists(new_file):
        size = os.path.getsize(new_file)
        print(f"✅ New members today exported: {new_file} ({size} bytes)")
    else:
        print("❌ New members today export failed")
    
    # Test 4: Export expiring members
    print("\n🔄 Test 4: Export Expiring Members (7 days)")
    expiring_file = manager.export_expiring_members_csv(7)
    if expiring_file and os.path.exists(expiring_file):
        size = os.path.getsize(expiring_file)
        print(f"✅ Expiring members exported: {expiring_file} ({size} bytes)")
    else:
        print("❌ Expiring members export failed")
    
    # Test 5: Export by status
    print("\n🔄 Test 5: Export Active Members")
    active_file = manager.export_members_by_status_csv("active")
    if active_file and os.path.exists(active_file):
        size = os.path.getsize(active_file)
        print(f"✅ Active members exported: {active_file} ({size} bytes)")
    else:
        print("❌ Active members export failed")
    
    # Test 6: Get export summary
    print("\n🔄 Test 6: Export Summary")
    summary = manager.get_csv_export_summary()
    if summary and "error" not in summary:
        print("✅ Export summary retrieved:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
    else:
        print(f"❌ Export summary failed: {summary}")
    
    return True

def show_csv_files():
    """Hiển thị các file CSV đã tạo"""
    print("\n📁 CSV FILES CREATED:")
    print("=" * 50)
    
    exports_dir = "exports"
    if os.path.exists(exports_dir):
        csv_files = [f for f in os.listdir(exports_dir) if f.endswith('.csv')]
        
        if csv_files:
            for csv_file in sorted(csv_files):
                filepath = os.path.join(exports_dir, csv_file)
                size = os.path.getsize(filepath)
                mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                print(f"📄 {csv_file}")
                print(f"   Size: {size} bytes")
                print(f"   Modified: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
        else:
            print("❌ No CSV files found")
    else:
        print("❌ Exports directory not found")

def show_csv_content_sample():
    """Hiển thị sample nội dung CSV"""
    print("\n📋 CSV CONTENT SAMPLE:")
    print("=" * 50)
    
    exports_dir = "exports"
    if os.path.exists(exports_dir):
        csv_files = [f for f in os.listdir(exports_dir) if f.endswith('.csv')]
        
        if csv_files:
            # Lấy file CSV đầu tiên
            sample_file = os.path.join(exports_dir, csv_files[0])
            
            try:
                with open(sample_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                print(f"📄 Sample from: {csv_files[0]}")
                print(f"📊 Total lines: {len(lines)}")
                print()
                
                # Hiển thị header và vài dòng đầu
                for i, line in enumerate(lines[:5]):
                    if i == 0:
                        print(f"Header: {line.strip()}")
                    else:
                        print(f"Row {i}: {line.strip()}")
                
                if len(lines) > 5:
                    print(f"... and {len(lines) - 5} more rows")
                    
            except Exception as e:
                print(f"❌ Error reading CSV file: {e}")
        else:
            print("❌ No CSV files to sample")
    else:
        print("❌ Exports directory not found")

def show_admin_commands_info():
    """Hiển thị thông tin admin commands"""
    print("\n👑 ADMIN COMMANDS FOR CSV EXPORT:")
    print("=" * 50)
    
    commands = [
        ("/export all", "Export tất cả thành viên"),
        ("/export group <chat_id>", "Export theo nhóm"),
        ("/export new", "Export thành viên mới hôm nay"),
        ("/export expiring [days]", "Export sắp hết hạn"),
        ("/export status <status>", "Export theo trạng thái"),
        ("/export summary", "Tổng kết export")
    ]
    
    for command, description in commands:
        print(f"📋 {command}")
        print(f"   {description}")
        print()
    
    print("💡 Examples:")
    print("   /export all")
    print("   /export group -1002301937119")
    print("   /export expiring 7")
    print("   /export status active")

def show_csv_structure():
    """Hiển thị cấu trúc CSV"""
    print("\n📊 CSV STRUCTURE:")
    print("=" * 50)
    
    headers = [
        'ID', 'User ID', 'Username', 'First Name', 'Last Name',
        'Chat ID', 'Group Name', 'Join Date', 'Trial End Date',
        'Days Remaining', 'Status', 'Warnings Sent', 'Last Warning Date',
        'Notes', 'Created At', 'Updated At'
    ]
    
    print("📋 CSV Headers:")
    for i, header in enumerate(headers, 1):
        print(f"  {i:2d}. {header}")
    
    print(f"\n📊 Total columns: {len(headers)}")
    print("💾 Encoding: UTF-8")
    print("📁 Directory: exports/")
    print("🕐 Auto export: Daily at 23:00")

def main():
    """Main demo function"""
    print("🎯 === CSV EXPORT SYSTEM DEMO ===")
    print("🎉 Demonstrating CSV export functionality")
    print()
    
    # Run CSV export demo
    success = demo_csv_exports()
    
    # Show created files
    show_csv_files()
    
    # Show CSV content sample
    show_csv_content_sample()
    
    # Show CSV structure
    show_csv_structure()
    
    # Show admin commands
    show_admin_commands_info()
    
    print("\n" + "=" * 50)
    print("🎯 DEMO SUMMARY")
    print("=" * 50)
    
    if success:
        print("✅ CSV Export System: Fully operational")
        print("✅ Multiple Export Types: All, Group, New, Expiring, Status")
        print("✅ Auto Scheduling: Daily exports at 23:00")
        print("✅ Admin Commands: /export with various options")
        print("✅ File Management: Organized in exports/ directory")
        print("✅ UTF-8 Encoding: Support Vietnamese characters")
        print()
        print("📊 CSV EXPORT FEATURES:")
        print("  • Export all members to CSV")
        print("  • Export by group (Trading/Premium)")
        print("  • Export new members daily")
        print("  • Export expiring members")
        print("  • Export by status (active/expired)")
        print("  • Automatic daily exports")
        print("  • Admin commands integration")
        print("  • File size and timestamp tracking")
        print()
        print("👑 ADMIN USAGE:")
        print("  • Use /export commands in Telegram")
        print("  • Files saved in exports/ directory")
        print("  • UTF-8 encoding for Vietnamese")
        print("  • Automatic daily scheduling")
        print()
        print("🚀 CSV EXPORT SYSTEM READY FOR USE!")
    else:
        print("❌ CSV Export Demo Failed")
        print("  Check member_csv_exporter.py")
        print("  Check database connection")
        print("  Check file permissions")

if __name__ == "__main__":
    main()
