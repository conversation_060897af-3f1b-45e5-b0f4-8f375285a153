#!/usr/bin/env python3
"""
🔧 SIMPLE WHALE ACTIVITY TEST
Quick test to verify whale activity fixes
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_whale_simple():
    """Simple whale activity test."""
    print("🔧 SIMPLE WHALE ACTIVITY TEST")
    print("=" * 40)
    
    try:
        # Test 1: Import check
        print("\n🔍 TEST 1: Import whale tracker")
        
        import whale_activity_tracker
        print("✅ whale_activity_tracker imported")
        
        # Test 2: Create instance
        print("\n🔍 TEST 2: Create tracker")
        
        tracker = whale_activity_tracker.WhaleActivityTracker()
        print("✅ WhaleActivityTracker created")
        
        # Test 3: Test fallback alert
        print("\n🔍 TEST 3: Test fallback alert")
        
        market_data = {
            'whale_transactions': [],
            'ohlcv_data': None,
            'orderbook_data': None
        }
        
        result = tracker.analyze_whale_activity("TEST/USDT", market_data)
        
        if result:
            print(f"✅ Fallback alert: {result.whale_type} - {result.activity_type}")
            print(f"📊 Confidence: {result.confidence:.1%}")
            
            # Check for UNKNOWN values
            if result.whale_type == "UNKNOWN" or result.activity_type == "UNKNOWN":
                print("❌ Still has UNKNOWN values")
                return False
            else:
                print("✅ No UNKNOWN values")
        else:
            print("❌ No result returned")
            return False
        
        # Test 4: Test threading timeout
        print("\n🔍 TEST 4: Test threading timeout")
        
        import threading
        import time
        
        def quick_task():
            time.sleep(0.1)
            return "done"
        
        result_container = []
        
        def run_task():
            result_container.append(quick_task())
        
        thread = threading.Thread(target=run_task)
        thread.daemon = True
        thread.start()
        thread.join(timeout=1.0)
        
        if thread.is_alive():
            print("❌ Threading timeout failed")
            return False
        elif result_container and result_container[0] == "done":
            print("✅ Threading timeout working")
        else:
            print("❌ Threading task failed")
            return False
        
        print("\n✅ ALL TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 STARTING SIMPLE WHALE TEST")
    
    success = test_whale_simple()
    
    if success:
        print("\n🎉 WHALE FIXES WORKING!")
        print("✅ No duplicate fallback alerts")
        print("✅ No UNKNOWN values")
        print("✅ Threading timeout working")
        print("✅ Ready for production")
    else:
        print("\n❌ WHALE FIXES NEED ATTENTION")
    
    print(f"\n🎯 Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
