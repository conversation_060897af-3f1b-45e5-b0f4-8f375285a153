#!/usr/bin/env python3
"""
📱 TEST TELEGRAM NOTIFIER WITH .ENV
====================================

Test script để kiểm tra telegram_notifier đọc cấu hình từ .env
"""

import os
from dotenv import load_dotenv

def test_env_loading():
    """Test loading environment variables"""
    print("📋 === TESTING .ENV LOADING ===")
    print("=" * 50)
    
    # Load .env
    load_dotenv()
    
    # Test basic Telegram config
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    print(f"🤖 Bot Token: {'✅ Loaded' if bot_token else '❌ Missing'}")
    print(f"💬 Chat ID: {chat_id if chat_id else '❌ Missing'}")
    
    # Test specialized chats
    specialized_chats = {
        "fibonacci_zigzag_fourier": os.getenv("TELEGRAM_FIBONACCI_ZIGZAG_FOURIER"),
        "volume_profile_point_figure": os.getenv("TELEGRAM_VOLUME_PROFILE_POINT_FIGURE"),
        "ai_analysis": os.getenv("TELEGRAM_AI_ANALYSIS"),
        "pump_detection": os.getenv("TELEGRAM_PUMP_DETECTION"),
        "dump_detection": os.getenv("TELEGRAM_DUMP_DETECTION"),
        "orderbook_analysis": os.getenv("TELEGRAM_ORDERBOOK_ANALYSIS"),
        "consensus_signals": os.getenv("TELEGRAM_CONSENSUS_SIGNALS"),
        "money_flow": os.getenv("TELEGRAM_MONEY_FLOW"),
        "whale_detection": os.getenv("TELEGRAM_WHALE_DETECTION"),
        "manipulation_detection": os.getenv("TELEGRAM_MANIPULATION_DETECTION"),
        "cross_asset": os.getenv("TELEGRAM_CROSS_ASSET")
    }
    
    print(f"\n📊 Specialized Chats:")
    for name, chat_id in specialized_chats.items():
        status = "✅" if chat_id else "❌"
        print(f"  {status} {name}: {chat_id}")
    
    return all(specialized_chats.values())

def test_telegram_notifier_initialization():
    """Test telegram notifier initialization with .env"""
    print("\n🤖 === TESTING TELEGRAM NOTIFIER INITIALIZATION ===")
    print("=" * 50)
    
    try:
        # Load .env
        load_dotenv()
        
        # Import telegram notifier
        import telegram_notifier
        
        # Get config from .env
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not bot_token or not chat_id:
            print("❌ Missing required Telegram config in .env")
            return False
        
        # Initialize notifier
        notifier = telegram_notifier.EnhancedTelegramNotifier(
            bot_token=bot_token,
            chat_id=chat_id,
            rate_limit_delay=1.0,
            max_retries=3
        )
        
        print("✅ Telegram notifier initialized successfully")
        
        # Check specialized chats
        print(f"\n📊 Specialized Chats Configuration:")
        for name, chat_id in notifier.specialized_chats.items():
            print(f"  📱 {name}: {chat_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing telegram notifier: {e}")
        return False

def test_chat_routing():
    """Test chat routing functionality"""
    print("\n🎯 === TESTING CHAT ROUTING ===")
    print("=" * 50)
    
    try:
        # Load .env
        load_dotenv()
        
        # Import telegram notifier
        import telegram_notifier
        
        # Get config from .env
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        # Initialize notifier
        notifier = telegram_notifier.EnhancedTelegramNotifier(
            bot_token=bot_token,
            chat_id=chat_id
        )
        
        # Test algorithm routing
        test_algorithms = [
            "fibonacci", "ai_analysis", "pump_detection", 
            "dump_detection", "consensus", "orderbook"
        ]
        
        print("🧪 Testing algorithm routing:")
        for algorithm in test_algorithms:
            target_chat = notifier._get_target_chat_for_algorithm(algorithm)
            print(f"  📊 {algorithm} → {target_chat}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat routing: {e}")
        return False

def compare_old_vs_new_config():
    """Compare old hardcoded vs new .env config"""
    print("\n🔄 === COMPARING OLD VS NEW CONFIG ===")
    print("=" * 50)
    
    # Old hardcoded config
    old_config = {
        "fibonacci_zigzag_fourier": "-1002608968097_619",
        "volume_profile_point_figure": "-1002608968097_621", 
        "ai_analysis": "-1002608968097_620",
        "pump_detection": "-1002608968097_616",
        "dump_detection": "-1002608968097_617",
        "orderbook_analysis": "-1002608968097_1",
        "consensus_signals": "-*************"
    }
    
    # New .env config
    load_dotenv()
    new_config = {
        "fibonacci_zigzag_fourier": os.getenv("TELEGRAM_FIBONACCI_ZIGZAG_FOURIER"),
        "volume_profile_point_figure": os.getenv("TELEGRAM_VOLUME_PROFILE_POINT_FIGURE"),
        "ai_analysis": os.getenv("TELEGRAM_AI_ANALYSIS"),
        "pump_detection": os.getenv("TELEGRAM_PUMP_DETECTION"),
        "dump_detection": os.getenv("TELEGRAM_DUMP_DETECTION"),
        "orderbook_analysis": os.getenv("TELEGRAM_ORDERBOOK_ANALYSIS"),
        "consensus_signals": os.getenv("TELEGRAM_CONSENSUS_SIGNALS")
    }
    
    print("📊 Configuration Comparison:")
    print(f"{'Algorithm':<30} {'Old (Hardcoded)':<20} {'New (.env)':<20} {'Status'}")
    print("-" * 80)
    
    for algorithm in old_config.keys():
        old_val = old_config[algorithm]
        new_val = new_config[algorithm]
        status = "✅ SAME" if old_val == new_val else "🔄 CHANGED"
        
        print(f"{algorithm:<30} {old_val:<20} {new_val:<20} {status}")
    
    # Check new additions
    new_additions = {
        "money_flow": os.getenv("TELEGRAM_MONEY_FLOW"),
        "whale_detection": os.getenv("TELEGRAM_WHALE_DETECTION"),
        "manipulation_detection": os.getenv("TELEGRAM_MANIPULATION_DETECTION"),
        "cross_asset": os.getenv("TELEGRAM_CROSS_ASSET")
    }
    
    print(f"\n🆕 New Additions:")
    for name, chat_id in new_additions.items():
        print(f"  ✅ {name}: {chat_id}")

def show_env_file_content():
    """Show relevant .env file content"""
    print("\n📄 === .ENV FILE CONTENT ===")
    print("=" * 50)
    
    try:
        with open(".env", "r") as f:
            lines = f.readlines()
        
        print("📋 Telegram Configuration in .env:")
        telegram_lines = [line.strip() for line in lines if line.startswith("TELEGRAM_")]
        
        for line in telegram_lines:
            if "=" in line:
                key, value = line.split("=", 1)
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def main():
    """Main test function"""
    print("📱 === TELEGRAM NOTIFIER .ENV INTEGRATION TEST ===")
    print("🎯 Testing telegram_notifier reading configuration from .env file")
    print()
    
    # Run all tests
    test1 = test_env_loading()
    test2 = test_telegram_notifier_initialization()
    test3 = test_chat_routing()
    test4 = show_env_file_content()
    
    # Compare configurations
    compare_old_vs_new_config()
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS")
    print("=" * 50)
    
    tests = [
        ("Environment Loading", test1),
        ("Notifier Initialization", test2),
        ("Chat Routing", test3),
        ("ENV File Reading", test4)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ TELEGRAM NOTIFIER .ENV INTEGRATION STATUS:")
        print("  • Environment variables: LOADED")
        print("  • Specialized chats: CONFIGURED from .env")
        print("  • Chat routing: WORKING with .env values")
        print("  • Hardcoded addresses: REMOVED")
        print("  • Configuration: CENTRALIZED in .env")
        print("\n💡 BENEFITS:")
        print("  • Easy configuration management")
        print("  • No hardcoded addresses in code")
        print("  • Environment-specific settings")
        print("  • Better security and flexibility")
        print("\n🚀 TELEGRAM NOTIFIER READY WITH .ENV CONFIG!")
        
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("  Please check the configuration and try again.")
        print("  Make sure .env file exists and contains all required variables.")

if __name__ == "__main__":
    main()
