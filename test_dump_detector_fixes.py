#!/usr/bin/env python3
"""
🔧 Test Dump Detector Fixes
Test the enhanced dump detector with None/zero value handling
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
sys.path.append('.')

def test_dump_detector_fixes():
    """Test the dump detector fixes for None/zero values"""
    print("🔧 Testing Dump Detector Fixes")
    print("=" * 60)
    
    try:
        from dump_detector import UltraEarlyDumpDetector
        
        # Create detector
        detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=0.75,
            pre_dump_lookback=60,
            whale_threshold=100000,
            min_confidence=0.70
        )
        
        print("✅ Dump detector created successfully")
        
        # Test 1: Create test data with potential None/NaN values
        print("\n📊 Test 1: Creating test market data with edge cases...")
        
        # Create OHLCV data with some NaN values
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
        
        ohlcv_1m = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(105, 115, 100),
            'low': np.random.uniform(95, 105, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        })
        
        # Introduce some NaN values to test handling
        ohlcv_1m.loc[10:12, 'close'] = np.nan
        ohlcv_1m.loc[20:22, 'volume'] = 0  # Zero volume
        ohlcv_1m.loc[30, 'close'] = np.inf  # Infinite value
        
        print(f"   📈 Created OHLCV data: {len(ohlcv_1m)} rows")
        print(f"   ⚠️ NaN values in close: {ohlcv_1m['close'].isna().sum()}")
        print(f"   ⚠️ Zero values in volume: {(ohlcv_1m['volume'] == 0).sum()}")
        print(f"   ⚠️ Infinite values: {np.isinf(ohlcv_1m['close']).sum()}")
        
        # Test 2: Test momentum decay detection with problematic data
        print("\n📊 Test 2: Testing momentum decay detection...")
        
        try:
            momentum_score = detector._detect_momentum_decay(ohlcv_1m, ohlcv_1m)
            print(f"   ✅ Momentum decay score: {momentum_score:.3f}")
            
            # Verify score is valid
            if 0.0 <= momentum_score <= 1.0:
                print(f"   ✅ Score is in valid range [0.0, 1.0]")
            else:
                print(f"   ❌ Score out of range: {momentum_score}")
                
        except Exception as e:
            print(f"   ❌ Momentum decay test failed: {e}")
        
        # Test 3: Test whale selling detection with empty data
        print("\n📊 Test 3: Testing whale selling detection with empty data...")
        
        try:
            # Test with empty whale transactions
            whale_score = detector._detect_whale_selling([])
            print(f"   ✅ Whale selling score (empty): {whale_score:.3f}")
            
            # Test with None whale transactions
            whale_score_none = detector._detect_whale_selling(None)
            print(f"   ✅ Whale selling score (None): {whale_score_none:.3f}")
            
        except Exception as e:
            print(f"   ❌ Whale selling test failed: {e}")
        
        # Test 4: Test orderbook analysis with missing data
        print("\n📊 Test 4: Testing orderbook analysis with missing data...")
        
        try:
            # Test with empty orderbook
            orderbook_signals = detector._analyze_orderbook_deterioration({}, 100.0)
            print(f"   ✅ Orderbook signals (empty): {len(orderbook_signals)} signals")
            
            # Test with None orderbook
            orderbook_signals_none = detector._analyze_orderbook_deterioration(None, 100.0)
            print(f"   ✅ Orderbook signals (None): {len(orderbook_signals_none)} signals")
            
            # Verify all scores are valid
            for signal_name, score in orderbook_signals.items():
                if 0.0 <= score <= 1.0:
                    print(f"   ✅ {signal_name}: {score:.3f} (valid)")
                else:
                    print(f"   ❌ {signal_name}: {score:.3f} (invalid)")
                    
        except Exception as e:
            print(f"   ❌ Orderbook analysis test failed: {e}")
        
        # Test 5: Test dump probability calculation with mixed data
        print("\n📊 Test 5: Testing dump probability calculation...")
        
        try:
            # Create test signals with various edge cases
            test_signals = {
                "pre_dump_patterns": {
                    "volume_divergence": 0.0,
                    "price_deterioration": None,  # None value
                    "momentum_decay": 0.104,
                    "support_weakness": np.nan,  # NaN value
                    "distribution_detected": 0.0
                },
                "whale_smart_money": {
                    "whale_selling": 0.050,
                    "smart_money_exit": 0.0,
                    "institutional_pressure": np.inf  # Infinite value
                },
                "orderbook_deterioration": {
                    "orderbook_imbalance": 0.020,
                    "bid_support_weakness": 1.000,
                    "ask_wall_building": 0.0
                },
                "technical_breakdown": {},  # Empty dict
                "volume_momentum": None  # None category
            }
            
            dump_probability = detector._calculate_ultra_early_dump_probability(test_signals)
            print(f"   ✅ Dump probability: {dump_probability:.3f}")
            
            confidence_score = detector._calculate_confidence_score(test_signals)
            print(f"   ✅ Confidence score: {confidence_score:.3f}")
            
            # Verify scores are valid
            if 0.0 <= dump_probability <= 1.0:
                print(f"   ✅ Dump probability in valid range")
            else:
                print(f"   ❌ Dump probability out of range: {dump_probability}")
                
            if 0.0 <= confidence_score <= 1.0:
                print(f"   ✅ Confidence score in valid range")
            else:
                print(f"   ❌ Confidence score out of range: {confidence_score}")
                
        except Exception as e:
            print(f"   ❌ Dump probability test failed: {e}")
        
        # Test 6: Test safe extraction methods
        print("\n📊 Test 6: Testing safe extraction methods...")
        
        try:
            # Test safe mean extraction
            test_dict = {"a": 0.5, "b": None, "c": np.nan, "d": 0.8, "e": np.inf}
            safe_mean = detector._safe_mean_extraction(test_dict)
            print(f"   ✅ Safe mean extraction: {safe_mean:.3f}")
            
            # Test safe value extraction
            safe_value = detector._safe_value_extraction(test_dict, "a", 0.0)
            print(f"   ✅ Safe value extraction (valid): {safe_value:.3f}")
            
            safe_value_none = detector._safe_value_extraction(test_dict, "b", 0.1)
            print(f"   ✅ Safe value extraction (None): {safe_value_none:.3f}")
            
            safe_value_missing = detector._safe_value_extraction(test_dict, "missing", 0.2)
            print(f"   ✅ Safe value extraction (missing): {safe_value_missing:.3f}")
            
        except Exception as e:
            print(f"   ❌ Safe extraction test failed: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 All dump detector fix tests completed!")
        print("✅ Enhanced error handling implemented")
        print("✅ None/NaN/Inf value handling added")
        print("✅ Zero division protection implemented")
        print("✅ Safe value extraction methods added")
        print("✅ Baseline scores for missing data")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dump_detector_fixes()
    sys.exit(0 if success else 1)
