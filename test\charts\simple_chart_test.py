#!/usr/bin/env python3
"""
🧪 Simple Chart Test
Test basic chart functionality
"""

import os
import sys

def test_matplotlib():
    """Test matplotlib import."""
    try:
        print("Testing matplotlib import...")
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend
        import matplotlib.pyplot as plt
        print("✅ Matplotlib imported successfully")
        return True
    except Exception as e:
        print(f"❌ Matplotlib import failed: {e}")
        return False

def test_basic_chart():
    """Test basic chart creation."""
    try:
        print("Testing basic chart creation...")
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        import numpy as np
        
        # Create simple chart
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, 'b-', linewidth=2)
        
        # Save chart
        output_path = "test_chart.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        if os.path.exists(output_path):
            print(f"✅ Basic chart created: {output_path}")
            os.remove(output_path)  # Clean up
            return True
        else:
            print("❌ Chart file not created")
            return False
            
    except Exception as e:
        print(f"❌ Basic chart creation failed: {e}")
        return False

def test_clean_candlestick():
    """Test clean candlestick chart."""
    try:
        print("Testing clean candlestick chart...")
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Create sample data
        periods = 50
        dates = pd.date_range(start=datetime.now() - timedelta(hours=periods), periods=periods, freq='1H')
        
        # Generate price data
        base_price = 0.32
        np.random.seed(42)
        price_changes = np.random.normal(0, 0.002, periods)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(0.001, new_price))
        
        # Create OHLCV data
        ohlcv_data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            close = price
            open_price = close * (1 + np.random.normal(0, 0.001))
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.002)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.002)))
            
            ohlcv_data.append({
                'timestamp': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
            })
        
        df = pd.DataFrame(ohlcv_data)
        df.set_index('timestamp', inplace=True)
        
        # Create clean chart
        fig, ax = plt.subplots(1, 1, figsize=(14, 8), facecolor='white')
        ax.set_facecolor('white')
        
        # Draw candlesticks
        for i, (timestamp, row) in enumerate(df.iterrows()):
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            
            # Colors
            if close_price >= open_price:
                body_color = '#00C851'  # Green
                wick_color = '#00C851'
            else:
                body_color = '#FF4444'  # Red
                wick_color = '#FF4444'
            
            # Draw wick
            ax.plot([i, i], [low_price, high_price], color=wick_color, linewidth=1.2, alpha=0.8)
            
            # Draw body
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            if body_height > 0:
                rect = patches.Rectangle((i-0.4, body_bottom), 0.8, body_height, 
                                       facecolor=body_color, edgecolor=body_color, 
                                       linewidth=0.8, alpha=0.9)
                ax.add_patch(rect)
            else:
                ax.plot([i-0.4, i+0.4], [open_price, open_price], color=wick_color, linewidth=1.5)
        
        # Add signal lines
        current_price = df['close'].iloc[-1]
        ax.axhline(y=current_price, color='#007BFF', linewidth=3, alpha=0.9)  # Current price
        ax.axhline(y=current_price * 1.05, color='#28A745', linewidth=2.5, alpha=0.8, linestyle='--')  # TP
        ax.axhline(y=current_price * 0.98, color='#DC3545', linewidth=2.5, alpha=0.8, linestyle='--')  # SL
        
        # Clean styling
        ax.set_xlim(-0.5, len(df) - 0.5)
        ax.set_ylim(df['low'].min() * 0.999, df['high'].max() * 1.001)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.set_xlabel('')
        ax.set_ylabel('')
        ax.grid(False)
        for spine in ax.spines.values():
            spine.set_visible(False)
        
        # Save
        plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
        output_path = "clean_candlestick_test.png"
        plt.savefig(output_path, dpi=200, bbox_inches='tight', pad_inches=0, 
                   facecolor='white', edgecolor='none', format='png')
        plt.close(fig)
        
        if os.path.exists(output_path):
            print(f"✅ Clean candlestick chart created: {output_path}")
            print(f"📊 Chart features:")
            print(f"  - {len(df)} candlesticks")
            print(f"  - Current price line (blue)")
            print(f"  - Take profit line (green dashed)")
            print(f"  - Stop loss line (red dashed)")
            print(f"  - No text or labels")
            print(f"  - Clean white background")
            return True
        else:
            print("❌ Clean candlestick chart not created")
            return False
            
    except Exception as e:
        print(f"❌ Clean candlestick chart failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Simple Chart Test Suite")
    print("=" * 40)
    
    test1 = test_matplotlib()
    test2 = test_basic_chart()
    test3 = test_clean_candlestick()
    
    print(f"\n🎯 Test Results:")
    print(f"✅ Matplotlib: {'PASS' if test1 else 'FAIL'}")
    print(f"✅ Basic Chart: {'PASS' if test2 else 'FAIL'}")
    print(f"✅ Clean Candlestick: {'PASS' if test3 else 'FAIL'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"🚀 Chart generation is working!")
    else:
        print(f"\n❌ SOME TESTS FAILED!")
