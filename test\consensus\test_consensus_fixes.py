#!/usr/bin/env python3
"""
🧪 Test Consensus Signal Fixes
Test the fixes for:
1. Signal quality metrics (strength, overall_quality)
2. Chart generation for consensus signals
3. Volume Profile and Orderbook analyzer NONE signal fixes
"""

import os
import sys
import time
import traceback
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_consensus_fixes():
    """🧪 Test all consensus signal fixes."""
    try:
        print("🧪 Testing Consensus Signal Fixes...")
        print("=" * 60)
        
        # Import main components
        from main_bot import TradingBot
        
        print("✅ Successfully imported TradingBot")
        
        # Initialize bot
        print("🤖 Initializing bot...")
        bot = TradingBot()
        
        print("✅ Bot initialized successfully")
        
        # Test 1: Check signal quality calculation
        print("\n📊 Test 1: Signal Quality Calculation")
        print("-" * 40)
        
        # Mock data for testing
        consensus_confidence = 0.85
        tp_sl_confidence = 0.75
        ai_confidence = 0.80
        tp_sl_methods = ["AI_Ensemble", "Fibonacci", "Volume_Profile"]
        risk_reward_ratio = 2.5
        volume_spike_detected = True
        consensus_score = 0.82
        
        # Calculate signal strength (same logic as in main_bot.py)
        signal_strength = 0.0
        signal_strength += min(0.3, consensus_confidence * 0.3)
        signal_strength += min(0.2, ai_confidence * 0.2)
        signal_strength += min(0.2, tp_sl_confidence * 0.2)
        signal_strength += min(0.15, len(tp_sl_methods) / 10 * 0.15)
        signal_strength += min(0.1, risk_reward_ratio / 5 * 0.1)
        signal_strength += 0.05 if volume_spike_detected else 0
        
        # Calculate overall quality
        overall_quality = 0.0
        overall_quality += signal_strength * 0.4
        overall_quality += consensus_score * 0.3
        overall_quality += min(0.2, 6 / 6 * 0.2)  # 6 algorithms
        overall_quality += 0.1  # AI enhancement bonus
        
        signal_strength = max(0.0, min(1.0, signal_strength))
        overall_quality = max(0.0, min(1.0, overall_quality))
        
        print(f"✅ Signal Strength: {signal_strength:.3f}")
        print(f"✅ Overall Quality: {overall_quality:.3f}")
        print(f"✅ TP/SL Methods Count: {len(tp_sl_methods)}")
        
        if signal_strength > 0 and overall_quality > 0:
            print("✅ Signal quality calculation working correctly!")
        else:
            print("❌ Signal quality calculation failed!")
            
        # Test 2: Check chart generator availability
        print("\n📊 Test 2: Chart Generator Availability")
        print("-" * 40)
        
        if hasattr(bot, 'chart_generator'):
            print("✅ Chart generator available")
            print(f"   Type: {type(bot.chart_generator)}")
            
            # Check chart config
            if hasattr(bot, 'chart_config'):
                print(f"✅ Chart config: {bot.chart_config}")
                if bot.chart_config.get('enabled', False):
                    print("✅ Chart generation enabled")
                else:
                    print("⚠️ Chart generation disabled")
            else:
                print("❌ Chart config not found")
        else:
            print("❌ Chart generator not available")
            
        # Test 3: Check analyzer availability
        print("\n📊 Test 3: Analyzer Availability")
        print("-" * 40)
        
        analyzers = [
            ('volume_profile_analyzer', 'Volume Profile'),
            ('orderbook_analyzer', 'Orderbook'),
            ('consensus_analyzer', 'Consensus')
        ]
        
        for attr_name, display_name in analyzers:
            if hasattr(bot, attr_name):
                print(f"✅ {display_name} analyzer available")
            else:
                print(f"❌ {display_name} analyzer not available")
                
        # Test 4: Test forced signal generation
        print("\n📊 Test 4: Forced Signal Generation Logic")
        print("-" * 40)
        
        # Test orderbook forced signal logic
        print("Testing orderbook forced signal logic:")
        bid_ask_ratio = 1.2  # Bullish
        if bid_ask_ratio > 1.0:
            forced_signal = "BUY"
            forced_confidence = min(0.4, bid_ask_ratio - 1.0)
        else:
            forced_signal = "SELL"
            forced_confidence = min(0.4, 1.0 - bid_ask_ratio)
            
        print(f"  Bid/Ask Ratio: {bid_ask_ratio}")
        print(f"  Forced Signal: {forced_signal}")
        print(f"  Forced Confidence: {forced_confidence:.3f}")
        
        # Test volume profile forced signal logic
        print("\nTesting volume profile forced signal logic:")
        current_price = 100.0
        vpoc_price = 95.0
        
        if current_price > vpoc_price:
            vp_forced_signal = "SELL"
            vp_forced_confidence = 0.3
        else:
            vp_forced_signal = "BUY"
            vp_forced_confidence = 0.3
            
        print(f"  Current Price: {current_price}")
        print(f"  VPOC Price: {vpoc_price}")
        print(f"  Forced Signal: {vp_forced_signal}")
        print(f"  Forced Confidence: {vp_forced_confidence:.3f}")
        
        print("\n🎉 All tests completed successfully!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_consensus_fixes()
    if success:
        print("✅ All fixes are working correctly!")
        sys.exit(0)
    else:
        print("❌ Some fixes need attention!")
        sys.exit(1)
