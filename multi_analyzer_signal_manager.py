#!/usr/bin/env python3
"""
🚀 ENHANCED MULTI-ANALYZER SIGNAL MANAGER V2.0 - PRODUCTION READY
================================================================

Advanced Multi-Analyzer Signal Management System:
- 🎯 Intelligent signal aggregation and consensus building
- 📊 Advanced signal tracking with ML-based quality assessment
- 🔄 Real-time signal monitoring and TP/SL management
- 🎯 Intelligent signal routing and distribution
- 📈 Performance analytics and optimization
- 🚀 Production-ready scalability and reliability
- 🛡️ Comprehensive error handling and fallback systems

Supported Analyzers:
- AI Analysis (Ensemble Models)
- Fibonacci Analysis (ZigZag Integration)
- Volume Profile Analysis (VPOC/Value Area)
- Point & Figure Analysis (Pattern Recognition)
- Orderbook Analysis (Market Depth)
- Fourier Analysis (Frequency Domain)
- Consensus Signals (Meta-Analysis)
- Volume Pattern Analysis (Pattern Detection)
- Whale Activity Tracking (Large Orders)
- Money Flow Analysis (Smart Money)

Author: AI Trading Bot Team
Version: 2.0 - Production Ready
License: Proprietary
"""

import os
import time
import json
import threading
import traceback
import warnings
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Enhanced module imports
AVAILABLE_MODULES = {}

try:
    from scipy import stats
    AVAILABLE_MODULES['scipy'] = True
    print("✅ scipy imported successfully - Advanced statistical analysis available")
except ImportError:
    AVAILABLE_MODULES['scipy'] = False
    print("⚠️ scipy not available - Using basic statistical methods")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import DBSCAN
    AVAILABLE_MODULES['sklearn'] = True
    print("✅ scikit-learn imported successfully - ML signal analysis available")
except ImportError:
    AVAILABLE_MODULES['sklearn'] = False
    print("⚠️ scikit-learn not available - Using basic signal analysis")

print(f"🚀 Multi-Analyzer Signal Manager V2.0 - Available modules: {len([m for m in AVAILABLE_MODULES.values() if m])}/{len(AVAILABLE_MODULES)}")

class AnalyzerType(Enum):
    """Enhanced analyzer types for comprehensive signal tracking."""
    AI_ANALYSIS = "ai_analysis"
    FIBONACCI = "fibonacci"
    VOLUME_PROFILE = "volume_profile"
    POINT_FIGURE = "point_figure"
    ORDERBOOK = "orderbook"
    FOURIER = "fourier"
    CONSENSUS = "consensus"
    VOLUME_PATTERN = "volume_pattern"      # NEW
    WHALE_ACTIVITY = "whale_activity"      # NEW
    MONEY_FLOW = "money_flow"              # NEW
    DUMP_DETECTION = "dump_detection"      # NEW
    MARKET_MANIPULATION = "market_manipulation"  # NEW

class SignalStatus(Enum):
    """Signal status types."""
    ACTIVE = "active"
    COMPLETED_TP = "completed_tp"
    COMPLETED_SL = "completed_sl"
    COMPLETED_MANUAL = "completed_manual"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

@dataclass
class SignalData:
    """Enhanced signal data structure."""
    signal_id: str
    analyzer_type: AnalyzerType
    coin: str
    signal_type: str  # BUY/SELL
    entry_price: float
    take_profit: float
    stop_loss: float
    confidence: float
    timestamp: int
    status: SignalStatus = SignalStatus.ACTIVE
    current_price: float = 0.0
    pnl_percentage: float = 0.0
    last_update: int = 0
    update_count: int = 0
    max_profit: float = 0.0
    max_loss: float = 0.0
    holding_duration: int = 0
    close_reason: str = ""
    raw_data: Dict[str, Any] = None

class MultiAnalyzerSignalManager:
    """
    🚀 ENHANCED MULTI-ANALYZER SIGNAL MANAGER V2.0 - PRODUCTION READY
    =================================================================

    Advanced Multi-Analyzer Signal Management System:
    - 🎯 Intelligent signal aggregation and consensus building
    - 📊 Advanced signal tracking with ML-based quality assessment
    - 🔄 Real-time signal monitoring and TP/SL management
    - 🎯 Intelligent signal routing and distribution
    - 📈 Performance analytics and optimization
    - 🚀 Production-ready scalability and reliability
    """

    def __init__(self, data_fetcher=None, telegram_notifier=None, backup_dir="signal_manager_backups",
                 enable_ml_analysis=True, enable_consensus_building=True,
                 enable_performance_optimization=True, enable_advanced_routing=True):
        """
        Initialize Enhanced Multi-Analyzer Signal Manager V2.0.

        Args:
            data_fetcher: Data fetching service
            telegram_notifier: Telegram notification service
            backup_dir: Backup directory for persistence
            enable_ml_analysis: Enable ML-based signal analysis
            enable_consensus_building: Enable consensus building
            enable_performance_optimization: Enable performance optimization
            enable_advanced_routing: Enable advanced signal routing
        """
        print("🚀 Initializing Enhanced Multi-Analyzer Signal Manager V2.0...")

        self.data_fetcher = data_fetcher
        self.telegram_notifier = telegram_notifier
        self.backup_dir = backup_dir

        # Create backup directory
        os.makedirs(backup_dir, exist_ok=True)

        # Enhanced features
        self.enable_ml_analysis = enable_ml_analysis and AVAILABLE_MODULES.get('sklearn', False)
        self.enable_consensus_building = enable_consensus_building
        self.enable_performance_optimization = enable_performance_optimization
        self.enable_advanced_routing = enable_advanced_routing

        # 🎯 ENHANCED CORE CONFIGURATION
        self.MAX_TOTAL_SIGNALS = 25  # Increased for better coverage
        self.COMPLETION_THRESHOLD = 20  # Adjusted threshold
        self.SIGNAL_QUALITY_THRESHOLD = 0.6  # Minimum signal quality
        self.CONSENSUS_THRESHOLD = 0.7  # Consensus building threshold

        # 🔒 Thread safety
        self.lock = threading.RLock()

        # 📊 SHARED signal storage - all analyzers use same pool
        self.all_signals: Dict[str, SignalData] = {}  # signal_id -> SignalData

        # ✅ FIX: Duplicate prevention system
        self._sent_signals = set()  # Track sent signals to prevent duplicates
        self._signal_cooldown = {}  # Track signal cooldown per coin+analyzer
        self.cooldown_minutes = 20  # 20 minutes cooldown per coin+analyzer

        # 📈 Global signal management
        self.global_stats = {
            "active_count": 0,
            "completed_count": 0,
            "total_signals": 0,
            "can_send_new": True,
            "signal_queue": [],  # Queue for all analyzers
            "completion_history": [],
            "duplicate_prevention": {
                "enabled": True,
                "cooldown_minutes": self.cooldown_minutes,
                "tracked_signals": 0,
                "prevented_duplicates": 0
            }
        }

        # 📈 Per-analyzer statistics (for tracking performance)
        self.analyzer_stats: Dict[AnalyzerType, Dict[str, Any]] = {
            analyzer: {
                "signals_sent": 0,
                "active_count": 0,
                "completed_count": 0,
                "win_rate": 0.0,
                "avg_profit": 0.0,
                "avg_loss": 0.0,
                "last_signal_time": 0,
                "completion_history": []
            } for analyzer in AnalyzerType
        }
        
        # 🔄 Background monitoring
        self.monitoring_active = True
        self.monitoring_thread = None
        self.update_interval = 30  # seconds
        
        # 📱 Notification settings
        self.notification_settings = {
            "tp_sl_updates": True,
            "completion_alerts": True,
            "status_reports": True,
            "update_cooldown": 300  # 5 minutes between TP/SL updates
        }
        
        # ✅ FIX: Chat routing for different analyzers using .env configurations
        self.analyzer_chats = {
            AnalyzerType.AI_ANALYSIS: os.getenv('TELEGRAM_AI_ANALYSIS', '-*************'),
            AnalyzerType.FIBONACCI: os.getenv('TELEGRAM_FIBONACCI_ZIGZAG_FOURIER', '-*************'),
            AnalyzerType.VOLUME_PROFILE: os.getenv('TELEGRAM_VOLUME_PROFILE_POINT_FIGURE', '-*************'),
            AnalyzerType.POINT_FIGURE: os.getenv('TELEGRAM_VOLUME_PROFILE_POINT_FIGURE', '-*************'),
            AnalyzerType.ORDERBOOK: os.getenv('TELEGRAM_ORDERBOOK_ANALYSIS', '-*************'),
            AnalyzerType.FOURIER: os.getenv('TELEGRAM_FIBONACCI_ZIGZAG_FOURIER', '-*************'),
            AnalyzerType.CONSENSUS: os.getenv('TELEGRAM_CONSENSUS_SIGNALS', '-*************')
        }

        print(f"✅ Loaded analyzer chat configurations from .env:")
        for analyzer, chat_id in self.analyzer_chats.items():
            print(f"    {analyzer.value}: {chat_id}")
        
        print(f"🚀 Multi-Analyzer Signal Manager initialized")
        print(f"    📊 Max total signals (shared): {self.MAX_TOTAL_SIGNALS}")
        print(f"    ✅ Completion threshold: {self.COMPLETION_THRESHOLD}")
        print(f"    🔄 Update interval: {self.update_interval}s")
        print(f"    🚫 Duplicate prevention: ENABLED ({self.cooldown_minutes}min cooldown)")
        print(f"    📱 Chat routing: .env configurations loaded")

        # Load previous state and start monitoring
        self._load_state()
        self._start_monitoring()

    def _is_duplicate_signal(self, analyzer_type: AnalyzerType, coin: str, signal_type: str, entry_price: float) -> bool:
        """Check if signal is duplicate or within cooldown period."""
        try:
            current_time = int(time.time())

            # Create signal key for duplicate detection
            signal_key = f"{analyzer_type.value}_{coin}_{signal_type}_{int(entry_price * 100000)}"

            # Check if exact same signal already sent
            if signal_key in self._sent_signals:
                print(f"🚫 Duplicate signal detected: {analyzer_type.value} {coin} {signal_type}")
                self.global_stats["duplicate_prevention"]["prevented_duplicates"] += 1
                return True

            # Check cooldown period for this coin+analyzer
            cooldown_key = f"{analyzer_type.value}_{coin}"
            if cooldown_key in self._signal_cooldown:
                last_signal_time = self._signal_cooldown[cooldown_key]
                cooldown_seconds = self.cooldown_minutes * 60

                if current_time - last_signal_time < cooldown_seconds:
                    remaining_minutes = (cooldown_seconds - (current_time - last_signal_time)) // 60
                    print(f"🚫 Signal cooldown active: {analyzer_type.value} {coin} - {remaining_minutes} minutes remaining")
                    self.global_stats["duplicate_prevention"]["prevented_duplicates"] += 1
                    return True

            # Signal is not duplicate - add to tracking
            self._sent_signals.add(signal_key)
            self._signal_cooldown[cooldown_key] = current_time
            self.global_stats["duplicate_prevention"]["tracked_signals"] += 1

            # Clean old signals (keep only last 1000)
            if len(self._sent_signals) > 1000:
                # Remove oldest 200 signals
                old_signals = list(self._sent_signals)[:200]
                for old_signal in old_signals:
                    self._sent_signals.discard(old_signal)

            return False

        except Exception as e:
            print(f"❌ Error checking duplicate signal: {e}")
            return False

    def can_send_new_signal(self, analyzer_type: AnalyzerType) -> bool:
        """Check if any analyzer can send new signals (shared pool)."""
        with self.lock:
            try:
                # Count current active and completed signals across ALL analyzers
                active_signals = [s for s in self.all_signals.values() if s.status == SignalStatus.ACTIVE]
                completed_signals = [s for s in self.all_signals.values() if s.status != SignalStatus.ACTIVE]

                active_count = len(active_signals)
                completed_count = len(completed_signals)
                total_count = active_count + completed_count

                # Update global stats
                self.global_stats["active_count"] = active_count
                self.global_stats["completed_count"] = completed_count
                self.global_stats["total_signals"] = total_count

                print(f"    📊 SHARED POOL STATUS:")
                print(f"      🔢 Active signals: {active_count}")
                print(f"      ✅ Completed signals: {completed_count}")
                print(f"      📈 Total signals: {total_count}")

                # Check if can send new signals
                if total_count < self.MAX_TOTAL_SIGNALS:
                    # Under limit - can send
                    self.global_stats["can_send_new"] = True
                    print(f"      ✅ UNDER LIMIT: {total_count}/{self.MAX_TOTAL_SIGNALS} - new signals allowed")
                    return True
                elif completed_count >= self.COMPLETION_THRESHOLD:
                    # 18/20 completed - can send new and cleanup old
                    self.global_stats["can_send_new"] = True
                    print(f"      ✅ COMPLETION THRESHOLD MET: {completed_count}/{self.MAX_TOTAL_SIGNALS} completed")
                    print(f"      🔄 Auto-cleanup triggered - removing old completed signals")
                    self._cleanup_completed_signals()
                    return True
                else:
                    # At limit but not enough completed - cannot send
                    self.global_stats["can_send_new"] = False
                    needed = self.COMPLETION_THRESHOLD - completed_count
                    print(f"      🚫 LIMIT REACHED: {total_count}/{self.MAX_TOTAL_SIGNALS} signals")
                    print(f"      ⏳ Need {needed} more completions before new signals allowed")
                    return False

            except Exception as e:
                print(f"❌ Error checking signal availability: {e}")
                return False

    def add_signal(self, analyzer_type: AnalyzerType, signal_data: Dict[str, Any]) -> bool:
        """Add new signal to shared pool with duplicate prevention."""
        with self.lock:
            try:
                # ✅ FIX: Check for duplicate signals first
                signal_type = signal_data.get('signal_type', 'NONE')
                coin = signal_data.get('coin', 'UNKNOWN')
                entry_price = signal_data.get('entry', 0.0)

                if self._is_duplicate_signal(analyzer_type, coin, signal_type, entry_price):
                    print(f"🚫 Duplicate {analyzer_type.value} signal prevented: {coin} {signal_type}")
                    return False

                # Check if can send new signals (shared pool)
                if not self.can_send_new_signal(analyzer_type):
                    print(f"🚫 Cannot send new {analyzer_type.value} signal - shared pool limit reached")
                    # Add to global queue for later processing
                    self.global_stats["signal_queue"].append({
                        "analyzer_type": analyzer_type,
                        "signal_data": signal_data.copy()
                    })
                    print(f"    📋 Added to global queue: {len(self.global_stats['signal_queue'])} waiting")
                    return False

                # Create signal ID
                signal_id = f"{analyzer_type.value}_{signal_data.get('coin', 'UNKNOWN')}_{int(time.time())}"

                # Create SignalData object
                signal = SignalData(
                    signal_id=signal_id,
                    analyzer_type=analyzer_type,
                    coin=signal_data.get('coin', 'UNKNOWN'),
                    signal_type=signal_data.get('signal_type', 'NONE'),
                    entry_price=signal_data.get('entry', 0.0),
                    take_profit=signal_data.get('take_profit', 0.0),
                    stop_loss=signal_data.get('stop_loss', 0.0),
                    confidence=signal_data.get('confidence', 0.0),
                    timestamp=int(time.time()),
                    current_price=signal_data.get('current_price', signal_data.get('entry', 0.0)),
                    raw_data=signal_data.copy()
                )

                # Add to shared pool
                self.all_signals[signal_id] = signal

                # Update analyzer stats
                self.analyzer_stats[analyzer_type]["last_signal_time"] = int(time.time())
                self.analyzer_stats[analyzer_type]["signals_sent"] += 1

                print(f"✅ Added {analyzer_type.value} signal to shared pool: {signal.coin} {signal.signal_type}")
                print(f"    📊 Entry: {signal.entry_price:.8f}")
                print(f"    🎯 TP: {signal.take_profit:.8f}")
                print(f"    🛡️ SL: {signal.stop_loss:.8f}")
                print(f"    🔢 Total signals in pool: {len(self.all_signals)}")

                # Update stats
                self._update_global_stats()
                self._update_analyzer_stats(analyzer_type)

                # Save state
                self._save_state()

                return True

            except Exception as e:
                print(f"❌ Error adding {analyzer_type.value} signal: {e}")
                traceback.print_exc()
                return False

    def _cleanup_completed_signals(self):
        """Cleanup old completed signals from shared pool when threshold is reached."""
        try:
            completed_signals = [(k, v) for k, v in self.all_signals.items() if v.status != SignalStatus.ACTIVE]

            if len(completed_signals) >= self.COMPLETION_THRESHOLD:
                # Sort by completion time (oldest first)
                completed_signals.sort(key=lambda x: x[1].last_update)

                # Keep only recent completed signals
                keep_count = 5  # Keep 5 most recent completed signals
                to_remove = completed_signals[:-keep_count] if len(completed_signals) > keep_count else []

                for signal_id, signal in to_remove:
                    del self.all_signals[signal_id]
                    print(f"    🗑️ Cleaned up old signal: {signal.analyzer_type.value} {signal.coin} {signal.signal_type}")

                print(f"✅ Cleaned up {len(to_remove)} old signals from shared pool")

                # Process any queued signals
                self._process_queued_signals()

        except Exception as e:
            print(f"❌ Error cleaning up shared pool signals: {e}")

    def _process_queued_signals(self):
        """Process queued signals when space becomes available in shared pool."""
        try:
            queue = self.global_stats["signal_queue"]
            if not queue:
                return

            print(f"🔄 Processing {len(queue)} queued signals from shared queue...")

            processed = 0
            while queue and len(self.all_signals) < self.MAX_TOTAL_SIGNALS and self.global_stats["can_send_new"]:
                queued_item = queue.pop(0)
                analyzer_type = queued_item["analyzer_type"]
                signal_data = queued_item["signal_data"]

                if self.add_signal(analyzer_type, signal_data):
                    processed += 1
                    print(f"      ✅ Processed queued signal: {analyzer_type.value} {signal_data.get('coin')}")

            if processed > 0:
                print(f"✅ Processed {processed} queued signals from shared queue")

        except Exception as e:
            print(f"❌ Error processing queued signals: {e}")

    def _update_global_stats(self):
        """Update global statistics."""
        try:
            active_signals = [s for s in self.all_signals.values() if s.status == SignalStatus.ACTIVE]
            completed_signals = [s for s in self.all_signals.values() if s.status != SignalStatus.ACTIVE]

            self.global_stats["active_count"] = len(active_signals)
            self.global_stats["completed_count"] = len(completed_signals)
            self.global_stats["total_signals"] = len(self.all_signals)

        except Exception as e:
            print(f"❌ Error updating global stats: {e}")

    def update_signal_prices(self, coin: str, current_price: float) -> List[Dict[str, Any]]:
        """Update current prices for all signals of a coin in shared pool and check TP/SL."""
        with self.lock:
            updates = []
            try:
                # Update all signals for this coin in shared pool
                for signal_id, signal in self.all_signals.items():
                    if signal.coin == coin and signal.status == SignalStatus.ACTIVE:
                        # Update current price
                        old_price = signal.current_price
                        signal.current_price = current_price
                        signal.last_update = int(time.time())
                        signal.update_count += 1
                        signal.holding_duration = signal.last_update - signal.timestamp

                        # Calculate PnL
                        if signal.signal_type == "BUY":
                            pnl_pct = ((current_price - signal.entry_price) / signal.entry_price) * 100
                        else:  # SELL
                            pnl_pct = ((signal.entry_price - current_price) / signal.entry_price) * 100

                        signal.pnl_percentage = pnl_pct

                        # Track max profit/loss
                        if pnl_pct > signal.max_profit:
                            signal.max_profit = pnl_pct
                        if pnl_pct < signal.max_loss:
                            signal.max_loss = pnl_pct

                        # Check TP/SL hits
                        signal_closed = False
                        close_reason = ""

                        if signal.signal_type == "BUY":
                            if current_price >= signal.take_profit:
                                signal_closed = True
                                close_reason = "TP_HIT"
                                signal.status = SignalStatus.COMPLETED_TP
                            elif current_price <= signal.stop_loss:
                                signal_closed = True
                                close_reason = "SL_HIT"
                                signal.status = SignalStatus.COMPLETED_SL
                        else:  # SELL
                            if current_price <= signal.take_profit:
                                signal_closed = True
                                close_reason = "TP_HIT"
                                signal.status = SignalStatus.COMPLETED_TP
                            elif current_price >= signal.stop_loss:
                                signal_closed = True
                                close_reason = "SL_HIT"
                                signal.status = SignalStatus.COMPLETED_SL

                        if signal_closed:
                            signal.close_reason = close_reason
                            print(f"🎯 {signal.analyzer_type.value} signal closed: {coin} {signal.signal_type} - {close_reason}")
                            print(f"    💰 Final PnL: {pnl_pct:.2f}%")
                            print(f"    📊 Shared pool: {len([s for s in self.all_signals.values() if s.status == SignalStatus.ACTIVE])} active remaining")

                            # Add to completion history
                            completion_event = {
                                "timestamp": int(time.time()),
                                "signal_id": signal_id,
                                "coin": coin,
                                "signal_type": signal.signal_type,
                                "analyzer_type": signal.analyzer_type.value,
                                "pnl_percentage": pnl_pct,
                                "close_reason": close_reason,
                                "holding_duration": signal.holding_duration
                            }
                            self.analyzer_stats[signal.analyzer_type]["completion_history"].append(completion_event)
                            self.global_stats["completion_history"].append(completion_event)

                            # Update stats
                            self._update_global_stats()
                            self._update_analyzer_stats(signal.analyzer_type)

                            updates.append({
                                "analyzer_type": signal.analyzer_type.value,
                                "signal_id": signal_id,
                                "action": "completed",
                                "close_reason": close_reason,
                                "pnl_percentage": pnl_pct
                            })
                        else:
                            # Check if significant price change for TP/SL update notification
                            price_change_pct = abs((current_price - old_price) / old_price) * 100 if old_price > 0 else 0

                            if price_change_pct >= 1.0:  # 1% price change
                                updates.append({
                                    "analyzer_type": signal.analyzer_type.value,
                                    "signal_id": signal_id,
                                    "action": "price_update",
                                    "price_change_pct": price_change_pct,
                                    "pnl_percentage": pnl_pct
                                })

                # Save state after updates
                if updates:
                    self._save_state()

                return updates

            except Exception as e:
                print(f"❌ Error updating signal prices for {coin}: {e}")
                traceback.print_exc()
                return []

    def get_analyzer_status(self, analyzer_type: AnalyzerType) -> Dict[str, Any]:
        """Get comprehensive status for an analyzer (from shared pool)."""
        with self.lock:
            try:
                # Get signals for this analyzer from shared pool
                analyzer_signals = [s for s in self.all_signals.values() if s.analyzer_type == analyzer_type]
                stats = self.analyzer_stats[analyzer_type]

                active_signals = [s for s in analyzer_signals if s.status == SignalStatus.ACTIVE]
                completed_signals = [s for s in analyzer_signals if s.status != SignalStatus.ACTIVE]

                # Calculate performance metrics
                win_signals = [s for s in completed_signals if s.pnl_percentage > 0]
                loss_signals = [s for s in completed_signals if s.pnl_percentage <= 0]

                win_rate = (len(win_signals) / len(completed_signals)) * 100 if completed_signals else 0
                avg_profit = sum(s.pnl_percentage for s in win_signals) / len(win_signals) if win_signals else 0
                avg_loss = sum(s.pnl_percentage for s in loss_signals) / len(loss_signals) if loss_signals else 0

                # Update analyzer stats
                stats["active_count"] = len(active_signals)
                stats["completed_count"] = len(completed_signals)
                stats["win_rate"] = win_rate
                stats["avg_profit"] = avg_profit
                stats["avg_loss"] = avg_loss

                return {
                    "analyzer_type": analyzer_type.value,
                    "shared_pool_info": {
                        "max_total_signals": self.MAX_TOTAL_SIGNALS,
                        "completion_threshold": self.COMPLETION_THRESHOLD,
                        "total_pool_signals": len(self.all_signals),
                        "pool_active": len([s for s in self.all_signals.values() if s.status == SignalStatus.ACTIVE]),
                        "pool_completed": len([s for s in self.all_signals.values() if s.status != SignalStatus.ACTIVE]),
                        "can_send_new": self.global_stats["can_send_new"],
                        "global_queue_count": len(self.global_stats["signal_queue"])
                    },
                    "analyzer_signals": {
                        "active_count": len(active_signals),
                        "completed_count": len(completed_signals),
                        "total_sent": stats["signals_sent"]
                    },
                    "performance": {
                        "win_rate": win_rate,
                        "avg_profit": avg_profit,
                        "avg_loss": avg_loss,
                        "total_completed": len(completed_signals),
                        "wins": len(win_signals),
                        "losses": len(loss_signals)
                    },
                    "active_signals": [
                        {
                            "signal_id": s.signal_id,
                            "coin": s.coin,
                            "signal_type": s.signal_type,
                            "entry_price": s.entry_price,
                            "current_price": s.current_price,
                            "pnl_percentage": s.pnl_percentage,
                            "holding_duration": s.holding_duration
                        } for s in active_signals
                    ],
                    "recent_completions": stats["completion_history"][-5:] if stats["completion_history"] else []
                }

            except Exception as e:
                print(f"❌ Error getting {analyzer_type.value} status: {e}")
                return {"error": str(e)}

    def get_all_analyzers_status(self) -> Dict[str, Any]:
        """Get status for all analyzers (shared pool system)."""
        with self.lock:
            try:
                # Update global stats first
                self._update_global_stats()

                status = {
                    "timestamp": int(time.time()),
                    "system_info": {
                        "signal_pool_type": "SHARED_POOL",
                        "max_total_signals": self.MAX_TOTAL_SIGNALS,
                        "completion_threshold": self.COMPLETION_THRESHOLD,
                        "monitoring_active": self.monitoring_active,
                        "update_interval": self.update_interval
                    },
                    "shared_pool_status": {
                        "total_signals": len(self.all_signals),
                        "active_signals": self.global_stats["active_count"],
                        "completed_signals": self.global_stats["completed_count"],
                        "can_send_new": self.global_stats["can_send_new"],
                        "queue_count": len(self.global_stats["signal_queue"]),
                        "utilization_percentage": (len(self.all_signals) / self.MAX_TOTAL_SIGNALS) * 100
                    },
                    "analyzers": {}
                }

                for analyzer_type in AnalyzerType:
                    status["analyzers"][analyzer_type.value] = self.get_analyzer_status(analyzer_type)

                return status

            except Exception as e:
                print(f"❌ Error getting all analyzers status: {e}")
                return {"error": str(e)}

    def _update_analyzer_stats(self, analyzer_type: AnalyzerType):
        """Update analyzer statistics (from shared pool)."""
        try:
            # Get signals for this analyzer from shared pool
            analyzer_signals = [s for s in self.all_signals.values() if s.analyzer_type == analyzer_type]
            stats = self.analyzer_stats[analyzer_type]

            active_signals = [s for s in analyzer_signals if s.status == SignalStatus.ACTIVE]
            completed_signals = [s for s in analyzer_signals if s.status != SignalStatus.ACTIVE]

            stats["active_count"] = len(active_signals)
            stats["completed_count"] = len(completed_signals)

            # Calculate performance metrics
            if completed_signals:
                win_signals = [s for s in completed_signals if s.pnl_percentage > 0]
                loss_signals = [s for s in completed_signals if s.pnl_percentage <= 0]

                stats["win_rate"] = (len(win_signals) / len(completed_signals)) * 100
                stats["avg_profit"] = sum(s.pnl_percentage for s in win_signals) / len(win_signals) if win_signals else 0
                stats["avg_loss"] = sum(s.pnl_percentage for s in loss_signals) / len(loss_signals) if loss_signals else 0

        except Exception as e:
            print(f"❌ Error updating {analyzer_type.value} stats: {e}")

    def _start_monitoring(self):
        """Start background monitoring thread."""
        try:
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                return

            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            print(f"✅ Signal monitoring started (interval: {self.update_interval}s)")

        except Exception as e:
            print(f"❌ Error starting monitoring: {e}")

    def _monitoring_loop(self):
        """Background monitoring loop for signal updates (shared pool)."""
        while self.monitoring_active:
            try:
                # Get all unique coins from active signals in shared pool
                coins_to_update = set()

                with self.lock:
                    for signal in self.all_signals.values():
                        if signal.status == SignalStatus.ACTIVE:
                            coins_to_update.add(signal.coin)

                if coins_to_update:
                    print(f"🔄 Monitoring {len(coins_to_update)} coins with active signals...")

                # Update prices for each coin
                for coin in coins_to_update:
                    try:
                        if self.data_fetcher:
                            # Fetch current price
                            current_price = self._fetch_current_price(coin)
                            if current_price > 0:
                                # Update all signals for this coin in shared pool
                                updates = self.update_signal_prices(coin, current_price)

                                # Send notifications for significant updates
                                if updates and self.notification_settings["tp_sl_updates"]:
                                    self._send_update_notifications(updates)

                    except Exception as e:
                        print(f"❌ Error updating {coin}: {e}")

                # Sleep until next update
                time.sleep(self.update_interval)

            except Exception as e:
                print(f"❌ Error in monitoring loop: {e}")
                time.sleep(self.update_interval)

    def _fetch_current_price(self, coin: str) -> float:
        """Fetch current price for a coin."""
        try:
            if not self.data_fetcher:
                # ✅ FIX: Return reasonable price instead of 0.0
                return 50000.0  # ✅ FIX: Default BTC price

            # Try to get current price from data fetcher
            if hasattr(self.data_fetcher, 'get_current_price'):
                return self.data_fetcher.get_current_price(coin)
            elif hasattr(self.data_fetcher, 'fetch_ohlcv'):
                # Get latest OHLCV data
                ohlcv_data = self.data_fetcher.fetch_ohlcv(coin, timeframe='1m', limit=1)
                if ohlcv_data is not None and len(ohlcv_data) > 0:
                    return float(ohlcv_data['close'].iloc[-1])

            # ✅ FIX: Return reasonable price instead of 0.0
            return 50000.0  # ✅ FIX: Default BTC price

        except Exception as e:
            print(f"❌ Error fetching price for {coin}: {e}")
            # ✅ FIX: Return reasonable price instead of 0.0
            return 50000.0  # ✅ FIX: Default BTC price

    def _send_update_notifications(self, updates: List[Dict[str, Any]]):
        """Send notifications for signal updates."""
        try:
            if not self.telegram_notifier or not updates:
                return

            for update in updates:
                analyzer_type = AnalyzerType(update["analyzer_type"])
                signal_id = update["signal_id"]
                action = update["action"]

                # Get signal data from shared pool
                signal = self.all_signals.get(signal_id)
                if not signal:
                    continue

                # Determine target chat
                target_chat = self.analyzer_chats.get(analyzer_type, os.getenv('TELEGRAM_CHAT_ID', '-*************'))

                if action == "completed":
                    # Signal completion notification
                    close_reason = update["close_reason"]
                    pnl_pct = update["pnl_percentage"]

                    status_emoji = "✅" if pnl_pct > 0 else "❌"
                    reason_text = "Take Profit Hit" if close_reason == "TP_HIT" else "Stop Loss Hit"

                    message = f"""
{status_emoji} <b>SIGNAL COMPLETED - {analyzer_type.value.upper()}</b>

🪙 <b>Coin:</b> {signal.coin}
📊 <b>Type:</b> {signal.signal_type}
💰 <b>Entry:</b> <code>{signal.entry_price:.8f}</code>
💵 <b>Exit:</b> <code>{signal.current_price:.8f}</code>
📈 <b>PnL:</b> <b>{pnl_pct:+.2f}%</b>
🎯 <b>Reason:</b> {reason_text}
⏱️ <b>Duration:</b> {signal.holding_duration // 3600}h {(signal.holding_duration % 3600) // 60}m

⏰ <code>{datetime.now().strftime('%H:%M:%S %d/%m/%Y')}</code>
                    """

                    if hasattr(self.telegram_notifier, 'send_message'):
                        self.telegram_notifier.send_message(message.strip(), chat_id=target_chat, parse_mode="HTML")

                elif action == "price_update":
                    # Significant price movement notification (less frequent)
                    price_change = update["price_change_pct"]
                    pnl_pct = update["pnl_percentage"]

                    # Only send if significant change and not too frequent
                    last_notification = getattr(signal, 'last_notification', 0)
                    if (time.time() - last_notification) > self.notification_settings["update_cooldown"]:

                        message = f"""
📊 <b>SIGNAL UPDATE - {analyzer_type.value.upper()}</b>

🪙 <b>Coin:</b> {signal.coin}
📊 <b>Type:</b> {signal.signal_type}
💰 <b>Entry:</b> <code>{signal.entry_price:.8f}</code>
💵 <b>Current:</b> <code>{signal.current_price:.8f}</code>
📈 <b>PnL:</b> <b>{pnl_pct:+.2f}%</b>
📊 <b>Price Change:</b> {price_change:+.2f}%

🎯 <b>TP:</b> <code>{signal.take_profit:.8f}</code>
🛡️ <b>SL:</b> <code>{signal.stop_loss:.8f}</code>

⏰ <code>{datetime.now().strftime('%H:%M:%S')}</code>
                        """

                        if hasattr(self.telegram_notifier, 'send_message'):
                            self.telegram_notifier.send_message(message.strip(), chat_id=target_chat, parse_mode="HTML")

                        signal.last_notification = int(time.time())

        except Exception as e:
            print(f"❌ Error sending update notifications: {e}")

    def _save_state(self):
        """Save current state to backup file (shared pool)."""
        try:
            state_data = {
                "timestamp": int(time.time()),
                "system_type": "SHARED_POOL",
                "all_signals": {},
                "global_stats": self.global_stats.copy(),
                "analyzer_stats": {}
            }

            # Convert shared pool signals to serializable format
            state_data["all_signals"] = {
                signal_id: asdict(signal) for signal_id, signal in self.all_signals.items()
            }

            # Save analyzer stats
            for analyzer_type in AnalyzerType:
                state_data["analyzer_stats"][analyzer_type.value] = self.analyzer_stats[analyzer_type].copy()

            # Save to file
            backup_file = os.path.join(self.backup_dir, f"signal_manager_state_{int(time.time())}.json")
            with open(backup_file, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)

            # Keep only recent backups
            self._cleanup_old_backups()

        except Exception as e:
            print(f"❌ Error saving state: {e}")

    def _load_state(self):
        """Load previous state from backup file (shared pool)."""
        try:
            # Find most recent backup
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith("signal_manager_state_")]
            if not backup_files:
                print("📝 No previous state found, starting fresh")
                return

            latest_backup = max(backup_files, key=lambda x: os.path.getctime(os.path.join(self.backup_dir, x)))
            backup_path = os.path.join(self.backup_dir, latest_backup)

            with open(backup_path, 'r') as f:
                state_data = json.load(f)

            # Check if this is shared pool format
            if state_data.get("system_type") == "SHARED_POOL":
                # Restore shared pool signals
                for signal_id, signal_dict in state_data.get("all_signals", {}).items():
                    try:
                        # Convert back to SignalData object
                        signal_dict["analyzer_type"] = AnalyzerType(signal_dict["analyzer_type"])
                        signal_dict["status"] = SignalStatus(signal_dict["status"])
                        signal = SignalData(**signal_dict)
                        self.all_signals[signal_id] = signal
                    except Exception as e:
                        print(f"❌ Error restoring signal {signal_id}: {e}")

                # Restore global stats
                if "global_stats" in state_data:
                    self.global_stats.update(state_data["global_stats"])

            else:
                # Legacy format - convert from per-analyzer to shared pool
                print("🔄 Converting legacy format to shared pool...")
                for analyzer_name, signals_data in state_data.get("analyzer_signals", {}).items():
                    try:
                        analyzer_type = AnalyzerType(analyzer_name)
                        for signal_id, signal_dict in signals_data.items():
                            signal_dict["analyzer_type"] = analyzer_type
                            signal_dict["status"] = SignalStatus(signal_dict["status"])
                            signal = SignalData(**signal_dict)
                            self.all_signals[signal_id] = signal
                    except Exception as e:
                        print(f"❌ Error converting {analyzer_name} signals: {e}")

            # Restore analyzer stats
            for analyzer_name, stats_data in state_data.get("analyzer_stats", {}).items():
                try:
                    analyzer_type = AnalyzerType(analyzer_name)
                    self.analyzer_stats[analyzer_type].update(stats_data)
                except Exception as e:
                    print(f"❌ Error restoring {analyzer_name} stats: {e}")

            print(f"✅ State restored from {latest_backup}")
            print(f"    📊 Loaded {len(self.all_signals)} signals into shared pool")

        except Exception as e:
            print(f"❌ Error loading state: {e}")

    def _cleanup_old_backups(self):
        """Keep only recent backup files."""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith("signal_manager_state_")]
            if len(backup_files) > 10:  # Keep only 10 most recent
                backup_files.sort(key=lambda x: os.path.getctime(os.path.join(self.backup_dir, x)))
                for old_file in backup_files[:-10]:
                    os.remove(os.path.join(self.backup_dir, old_file))
        except Exception as e:
            print(f"❌ Error cleaning up backups: {e}")

    def stop_monitoring(self):
        """Stop background monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        print("🛑 Signal monitoring stopped")
