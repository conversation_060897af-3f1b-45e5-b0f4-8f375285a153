#!/usr/bin/env python3
"""
🧪 TEST ULTRA TRACKER V3.0 SIGNAL LIMIT SYSTEM
Test để kiểm tra hệ thống giới hạn 20 tín hiệu cho tất cả pool phân tích
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from trade_tracker import TradeTracker
    print("✅ TradeTracker imported successfully")
except ImportError as e:
    print(f"❌ Failed to import TradeTracker: {e}")
    sys.exit(1)

try:
    from signal_manager_integration import SignalManagerIntegration
    print("✅ SignalManagerIntegration imported successfully")
except ImportError as e:
    print(f"❌ Failed to import SignalManagerIntegration: {e}")
    sys.exit(1)

def test_signal_limit_logic():
    """Test signal limit logic without full TradeTracker initialization"""
    print("🧪 === TESTING SIGNAL LIMIT LOGIC ===")
    print(f"⏰ Test started at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")

    # Test signal management logic
    signal_management = {
        "max_signals": 20,
        "completion_threshold": 18,
        "completed_count": 0,
        "active_count": 0,
        "allow_new_signals": True
    }

    active_signals = []
    completed_signals = []
    
    def can_send_new_signal():
        """Simulate Ultra Tracker signal limit logic"""
        total_signals = len(active_signals) + len(completed_signals)
        max_signals = signal_management["max_signals"]
        completion_threshold = signal_management["completion_threshold"]
        completed_count = signal_management["completed_count"]

        # Rule 1: Under 20 signals - allow
        if total_signals < max_signals:
            return True

        # Rule 2: At 20 signals - need 18/20 completed
        if total_signals >= max_signals:
            if completed_count >= completion_threshold:
                return True
            else:
                return False

        return False

    print(f"\n📊 Initial Status:")
    print(f"  • Max signals: {signal_management['max_signals']}")
    print(f"  • Completion threshold: {signal_management['completion_threshold']}")
    print(f"  • Active signals: {len(active_signals)}")
    print(f"  • Completed signals: {len(completed_signals)}")
    print(f"  • Can send new signals: {can_send_new_signal()}")

    # Test 1: Add signals up to limit
    print(f"\n🧪 TEST 1: Adding signals up to 20-signal limit")
    test_coins = [
        "BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT",
        "BNBUSDT", "XRPUSDT", "LTCUSDT", "BCHUSDT", "EOSUSDT",
        "TRXUSDT", "XLMUSDT", "ATOMUSDT", "VETUSDT", "FILUSDT",
        "THETAUSDT", "ICXUSDT", "ONTUSDT", "ZILUSDT", "BATUSDT"
    ]

    signals_added = 0
    for i, coin in enumerate(test_coins):
        if signals_added >= 20:
            break

        # Create test signal
        signal_data = {
            "signal_id": f"TEST_{coin}_{int(time.time())}_{i}",
            "coin": coin,
            "signal_type": "BUY" if i % 2 == 0 else "SELL",
            "entry": 100.0 + i,
            "take_profit": 110.0 + i,
            "stop_loss": 95.0 + i,
            "risk_reward_ratio": 2.0,
            "timestamp": time.time(),
            "analyzer_type": ["ai", "fibonacci", "volume_profile", "point_figure", "fourier"][i % 5],
            "status": "ACTIVE"
        }

        # Check if can send signal
        can_send = can_send_new_signal()
        print(f"  Signal {signals_added + 1}: {coin} - Can send: {'✅' if can_send else '❌'}")

        if can_send:
            active_signals.append(signal_data)
            signals_added += 1
            print(f"    ✅ Added signal {signals_added}/20")
        else:
            print(f"    🚫 Signal blocked by limit")
            break

    print(f"\n📊 After adding signals:")
    print(f"  • Active signals: {len(active_signals)}")
    print(f"  • Total signals: {len(active_signals) + len(completed_signals)}")
    print(f"  • Can send new signals: {can_send_new_signal()}")
    
    # Test 2: Try to add 21st signal (should be blocked)
    print(f"\n🧪 TEST 2: Trying to add 21st signal (should be blocked)")

    can_send_extra = can_send_new_signal()
    print(f"  Can send 21st signal: {'✅' if can_send_extra else '❌'}")

    if can_send_extra:
        print(f"  ❌ ERROR: System should block 21st signal!")
    else:
        print(f"  ✅ SUCCESS: 21st signal correctly blocked")

    # Test 3: Complete some signals to test threshold
    print(f"\n🧪 TEST 3: Completing signals to test 18/20 threshold")

    # Complete first 18 signals
    completed_count = 0
    for signal in active_signals[:18]:
        # Simulate signal completion
        completed_signals.append(signal)
        completed_count += 1
        print(f"    ✅ Completed signal {completed_count}: {signal['coin']}")

    # Remove completed signals from active list
    active_signals = active_signals[18:]

    # Update signal management
    signal_management["completed_count"] = completed_count

    print(f"\n📊 After completing 18 signals:")
    print(f"  • Active signals: {len(active_signals)}")
    print(f"  • Completed signals: {len(completed_signals)}")
    print(f"  • Completion count: {signal_management['completed_count']}")
    print(f"  • Can send new signals: {can_send_new_signal()}")

    # Test 4: Try to add new signal after threshold met
    print(f"\n🧪 TEST 4: Adding new signal after 18/20 threshold met")

    new_signal = {
        "signal_id": f"TEST_NEW_{int(time.time())}",
        "coin": "NEWUSDT",
        "signal_type": "BUY",
        "entry": 300.0,
        "take_profit": 330.0,
        "stop_loss": 285.0,
        "risk_reward_ratio": 2.0,
        "timestamp": time.time(),
        "analyzer_type": "test",
        "status": "ACTIVE"
    }

    can_send_new = can_send_new_signal()
    print(f"  Can send new signal after threshold: {'✅' if can_send_new else '❌'}")

    if can_send_new:
        active_signals.append(new_signal)
        print(f"  ✅ SUCCESS: New signal added after threshold met")
    else:
        print(f"  ❌ ERROR: Should allow new signals after 18/20 threshold")

    # Final status
    print(f"\n📊 FINAL STATUS:")
    print(f"  • Active signals: {len(active_signals)}")
    print(f"  • Completed signals: {len(completed_signals)}")
    print(f"  • Total signals: {len(active_signals) + len(completed_signals)}")
    print(f"  • Completion count: {signal_management['completed_count']}")
    print(f"  • Can send new signals: {can_send_new_signal()}")

    print(f"\n✅ Signal limit logic test completed!")
    print(f"⏰ Test finished at: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}")

if __name__ == "__main__":
    test_signal_limit_logic()
