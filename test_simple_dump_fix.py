#!/usr/bin/env python3
"""
🔧 SIMPLE DUMP DETECTOR TEST
============================

Simple test để kiểm tra dump detector parameter fix.
"""

def test_dump_detector_simple():
    """Simple test for dump detector."""
    print("🔧 SIMPLE DUMP DETECTOR TEST")
    print("=" * 50)
    
    try:
        print("📊 Testing dump_detector import...")
        from dump_detector import UltraEarlyDumpDetector
        print("  ✅ Import successful")
        
        print("📊 Testing initialization with correct parameters...")
        dump_detector = UltraEarlyDumpDetector(
            ultra_early_sensitivity=0.7,
            whale_threshold=50000
        )
        print("  ✅ Initialization successful!")
        print(f"  📊 Type: {type(dump_detector)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_dump_detector_simple()
    
    if success:
        print(f"\n🎉 DUMP DETECTOR FIX SUCCESSFUL!")
        print(f"✅ Parameter name corrected: 'sensitivity' → 'ultra_early_sensitivity'")
        print(f"✅ Dump detector can now be initialized")
        print(f"\n🚀 EXPECTED RESULT:")
        print(f"When you run the main bot, you should now see:")
        print(f"  ✅ Dump Detector: Connected to consensus")
        print(f"  ✅ Pump Detector: Connected to consensus")
        print(f"  📊 Total algorithms: 8 (instead of 6)")
        print(f"  🎯 Both detectors contributing to consensus analysis")
    else:
        print(f"\n❌ DUMP DETECTOR FIX FAILED!")
        print(f"🔧 Check dump_detector.py for correct parameter names")
