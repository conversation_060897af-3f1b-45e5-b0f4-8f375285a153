#!/usr/bin/env python3
"""
🧪 TEST CHART GENERATOR INITIALIZATION
=====================================

Test để kiểm tra xem chart generator có khởi tạo được không.
"""

import os
import sys
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_generator_init():
    """Test chart generator initialization."""
    print("🧪 TESTING CHART GENERATOR INITIALIZATION")
    print("=" * 50)
    
    try:
        # Test import chart_generator module
        print("📦 Testing chart_generator module import...")
        import chart_generator
        print("✅ chart_generator module imported successfully")
        
        # Test EnhancedChartGenerator class
        print("🎨 Testing EnhancedChartGenerator class...")
        chart_gen_class = getattr(chart_generator, 'EnhancedChartGenerator', None)
        print(f"✅ EnhancedChartGenerator class found: {chart_gen_class is not None}")
        
        if chart_gen_class:
            print(f"📊 Class type: {type(chart_gen_class)}")
            print(f"📊 Class name: {chart_gen_class.__name__}")
            
            # Test initialization with minimal parameters
            print("🚀 Testing chart generator initialization...")
            try:
                chart_gen = chart_gen_class(
                    output_dir="test_charts",
                    quality="high",
                    enable_watermark=True,
                    enable_multi_timeframe=True,
                    enable_technical_overlays=True,
                    enable_volume_profile_overlay=True,
                    enable_fibonacci_overlay=True,
                    cleanup_hours=24
                )
                print("✅ Chart generator initialized successfully!")
                print(f"📊 Chart generator type: {type(chart_gen)}")
                print(f"📊 Output directory: {chart_gen.output_dir}")
                
                # Test some methods
                print("🔧 Testing chart generator methods...")
                methods_to_test = [
                    'generate_fibonacci_chart',
                    'generate_volume_profile_chart',
                    'generate_point_figure_chart',
                    'generate_fourier_chart',
                    'generate_ai_analysis_chart',
                    'generate_dump_alert_chart'
                ]
                
                for method_name in methods_to_test:
                    has_method = hasattr(chart_gen, method_name)
                    print(f"  📊 {method_name}: {'✅ Available' if has_method else '❌ Missing'}")
                
                return chart_gen
                
            except Exception as init_error:
                print(f"❌ Chart generator initialization failed: {init_error}")
                traceback.print_exc()
                return None
        else:
            print("❌ EnhancedChartGenerator class not found")
            return None
            
    except Exception as e:
        print(f"❌ Chart generator test failed: {e}")
        traceback.print_exc()
        return None

def test_chart_generation():
    """Test actual chart generation."""
    print("\n🎨 TESTING CHART GENERATION")
    print("=" * 50)
    
    chart_gen = test_chart_generator_init()
    if not chart_gen:
        print("❌ Cannot test chart generation - initialization failed")
        return False
    
    try:
        # Create sample data
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Create sample OHLCV data
        dates = pd.date_range(start=datetime.now() - timedelta(hours=100), periods=100, freq='1H')
        sample_data = pd.DataFrame({
            'timestamp': dates,
            'open': [50000 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'high': [50100 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'low': [49900 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'close': [50050 + i * 10 + np.random.randn() * 50 for i in range(100)],
            'volume': [1000 + i * 5 + np.random.randn() * 100 for i in range(100)]
        })
        sample_data.set_index('timestamp', inplace=True)
        
        current_price = 51000.0
        coin = "BTCUSDT"
        
        print("📊 Testing Fibonacci chart generation...")
        try:
            fibonacci_data = {
                "status": "success",
                "retracement_levels": [
                    {"ratio": 0.236, "price": current_price * 0.976},
                    {"ratio": 0.382, "price": current_price * 0.962},
                    {"ratio": 0.618, "price": current_price * 0.938}
                ],
                "extension_levels": [
                    {"ratio": 1.618, "price": current_price * 1.062},
                    {"ratio": 2.618, "price": current_price * 1.124}
                ],
                "confluence_zones": [],
                "pivot_high": current_price * 1.05,
                "pivot_low": current_price * 0.95,
                "trend_direction": "UPTREND",
                "calculation_method": "enhanced_automatic",
                "confidence": 0.8,
                "signal_strength": "STRONG",
                "signals": {"overall_signal": "BUY", "confidence": 0.8}
            }
            
            chart_path = chart_gen.generate_fibonacci_chart(coin, fibonacci_data, sample_data, current_price)
            print(f"📊 Fibonacci chart result: {'✅ SUCCESS' if chart_path else '❌ FAILED'}")
            if chart_path:
                print(f"    📁 Chart path: {chart_path}")
                print(f"    📁 File exists: {os.path.exists(chart_path)}")
        except Exception as e:
            print(f"❌ Fibonacci chart generation failed: {e}")
        
        print("\n🎯 CHART GENERATION TEST COMPLETED")
        return True
        
    except Exception as e:
        print(f"❌ Chart generation test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_chart_generation()
