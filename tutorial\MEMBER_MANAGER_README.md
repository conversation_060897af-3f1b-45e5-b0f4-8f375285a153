# 👥 HỆ THỐNG QUẢN LÝ THÀNH VIÊN TELEGRAM

## 📋 **TỔNG QUAN**

Hệ thống quản lý thành viên Telegram tự động với các tính năng:
- ✅ **Chào mừng thành viên mới** với cảnh báo đầy đủ
- ⏰ **Bộ đếm thời gian 60 ngày** trial tự động
- 🚨 **Cảnh báo hết hạn** (7, 3, 1 ngày trước)
- 💰 **Thông tin donation** với địa chỉ ví BNB
- 👑 **Admin commands** để quản lý thành viên
- 🔄 **Tự động xóa** thành viên hết hạn

---

## 📁 **CÁC FILE TRONG HỆ THỐNG**

### 1. **telegram_member_manager.py**
- 🤖 Core member management system
- 📊 Database SQLite để lưu thông tin
- ⏰ Background tasks cho countdown
- 💰 Donation info integration

### 2. **integrate_member_manager.py**
- 🔗 Script tích hợp vào main bot
- 🛠️ Auto-integration methods
- 🧪 Testing và validation

### 3. **telegram_webhook_handler.py**
- 🔗 Webhook handler cho Telegram events
- 👥 Phát hiện new/left members
- 🔄 Real-time event processing

### 4. **member_admin_commands.py**
- 👑 Admin commands cho quản lý
- 📊 Statistics và reporting
- ⏰ Manual member extension

---

## 🎯 **NHÓM ĐƯỢC QUẢN LÝ**

### **📊 Trading Signals Group**
- **Chat ID**: `-1002301937119`
- **Mô tả**: Nhóm tín hiệu trading AI
- **Trial**: 60 ngày

### **📈 Premium Analysis Group**
- **Chat ID**: `-1002395637657`
- **Mô tả**: Nhóm phân tích chuyên sâu
- **Trial**: 60 ngày

---

## 💰 **THÔNG TIN DONATION**

### **🏦 Địa Chỉ Ví**
```
******************************************
```

### **🌐 Chi Tiết**
- **Mạng**: BNB Smart Chain (BEP20)
- **Loại coin**: USDT
- **QR Code**: Có thể thêm sau

---

## 🚀 **CÁCH SỬ DỤNG**

### **Bước 1: Cài Đặt Dependencies**
```bash
pip install sqlite3 flask threading
```

### **Bước 2: Chạy Integration**
```bash
python integrate_member_manager.py
```

### **Bước 3: Thiết Lập Webhook (Tùy Chọn)**
```bash
python telegram_webhook_handler.py
```

### **Bước 4: Cấu Hình Admin**
```python
# Trong member_admin_commands.py
self.admin_users = [
    123456789,  # Admin 1 User ID
    987654321,  # Admin 2 User ID
]
```

---

## 👑 **ADMIN COMMANDS**

### **📊 Thống Kê**
```
/stats
```
- Hiển thị số lượng thành viên tất cả nhóm
- Active, expired, expiring soon members
- Real-time statistics

### **⏰ Gia Hạn Thành Viên**
```
/extend <user_id> <days>
```
**Ví dụ:**
```
/extend 123456789 30
```
- Gia hạn trial cho user cụ thể
- Tối đa 365 ngày mỗi lần
- Tự động gửi thông báo gia hạn

### **💰 Thông Tin Donation**
```
/donation
```
- Gửi thông tin ví donation
- Địa chỉ BNB wallet
- Hướng dẫn donation

### **👥 Quản Lý Thành Viên**
```
/members
```
- Thông tin quản lý thành viên
- Danh sách commands có sẵn

### **❓ Trợ Giúp Admin**
```
/help_admin
```
- Hướng dẫn sử dụng admin commands
- Cú pháp và ví dụ

---

## 🔄 **QUY TRÌNH TỰ ĐỘNG**

### **👋 Thành Viên Mới Tham Gia**
1. **Phát hiện**: Webhook/polling detect new member
2. **Lưu DB**: Thêm vào database với trial 60 ngày
3. **Chào mừng**: Gửi welcome message với:
   - Cảnh báo trading risks
   - Thông tin trial period
   - Donation wallet address
   - Quy tắc nhóm

### **⚠️ Cảnh Báo Hết Hạn**
- **7 ngày trước**: First warning
- **3 ngày trước**: Second warning  
- **1 ngày trước**: Final warning
- **Hết hạn**: Expiration notice + auto remove

### **💰 Gia Hạn Sau Donation**
1. **User donation** vào wallet
2. **Admin verify** transaction
3. **Admin extend** bằng `/extend` command
4. **Auto notification** cho user

---

## 📊 **DATABASE SCHEMA**

### **Members Table**
```sql
CREATE TABLE members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    username TEXT,
    first_name TEXT,
    last_name TEXT,
    chat_id TEXT NOT NULL,
    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    trial_end_date TIMESTAMP,
    status TEXT DEFAULT 'active',
    warnings_sent INTEGER DEFAULT 0,
    last_warning_date TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Indexes**
- `idx_user_chat`: (user_id, chat_id)
- `idx_trial_end`: (trial_end_date)
- `idx_status`: (status)

---

## 🔧 **CẤU HÌNH**

### **Managed Groups**
```python
self.managed_groups = {
    "-1002301937119": {
        "name": "Trading Signals Group",
        "description": "Nhóm tín hiệu trading AI",
        "trial_days": 60
    },
    "-1002395637657": {
        "name": "Premium Analysis Group", 
        "description": "Nhóm phân tích chuyên sâu",
        "trial_days": 60
    }
}
```

### **Donation Info**
```python
self.donation_info = {
    "wallet_address": "******************************************",
    "network": "BNB Smart Chain (BEP20)",
    "currency": "USDT",
    "qr_code_url": None
}
```

### **Background Tasks**
- **Frequency**: Mỗi giờ check expired members
- **Threading**: Non-blocking background worker
- **Error Recovery**: Auto retry sau 5 phút nếu lỗi

---

## 📱 **WELCOME MESSAGE TEMPLATE**

```
🎉 CHÀO MỪNG @username!

👋 Chào mừng bạn đến với Trading Signals Group!

🚨 THÔNG BÁO QUAN TRỌNG 🚨

⏰ THỜI GIAN TRIAL:
├ 📅 Bắt đầu: 15/06/2025 22:19
├ 📅 Kết thúc: 14/08/2025 22:19
└ ⏳ Thời gian còn lại: 60 ngày

⚠️ CẢNH BÁO QUAN TRỌNG:
├ 🤖 Tất cả tín hiệu chỉ mang tính THAM KHẢO
├ 💰 Bạn TỰ CHỊU TRÁCH NHIỆM về mọi quyết định giao dịch
├ 📉 Crypto có RỦI RO CỰC KỲ CAO - có thể mất toàn bộ vốn
├ 🧠 Hãy DYOR (Do Your Own Research) trước khi giao dịch
└ 💡 CHỈ ĐẦU TƯ số tiền bạn có thể chấp nhận mất

💰 HỖ TRỢ PHÁT TRIỂN:
├ 🏦 Ví USDT (BEP20): ******************************************
├ 🌐 Mạng: BNB Smart Chain (BEP20)
├ 💝 Donation giúp duy trì và phát triển bot
└ 🙏 Cảm ơn sự ủng hộ của bạn!

Chúc bạn giao dịch thành công và an toàn! 🚀
```

---

## 🧪 **TESTING**

### **Test Member Manager**
```bash
python telegram_member_manager.py
```

### **Test Integration**
```bash
python integrate_member_manager.py
```

### **Manual Testing**
1. Add test user to database
2. Check welcome message
3. Test expiration warnings
4. Verify admin commands
5. Test donation info

---

## 🔍 **MONITORING**

### **Log Files**
- Member activities
- Warning notifications
- Admin command usage
- Error tracking

### **Statistics Tracking**
- New members per day
- Expiration rates
- Extension rates
- Donation conversions

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

**1. Database không tạo được:**
```bash
# Check permissions
chmod 755 .
# Check SQLite installation
python -c "import sqlite3; print('SQLite OK')"
```

**2. Webhook không hoạt động:**
```bash
# Check Flask installation
pip install flask
# Test webhook endpoint
curl -X POST http://localhost:5000/health
```

**3. Background tasks không chạy:**
```python
# Check threading
import threading
print(f"Active threads: {threading.active_count()}")
```

### **Debug Commands**
```bash
# Check database
sqlite3 telegram_members.db ".tables"
sqlite3 telegram_members.db "SELECT * FROM members LIMIT 5;"

# Test member stats
python -c "from telegram_member_manager import TelegramMemberManager; m=TelegramMemberManager(); print(m.get_member_stats())"
```

---

## 🎯 **FUTURE ENHANCEMENTS**

### **Planned Features**
- 🌍 **Multi-language support**: Hỗ trợ nhiều ngôn ngữ
- 📊 **Advanced analytics**: Dashboard thống kê chi tiết
- 🎁 **Referral system**: Hệ thống giới thiệu bạn bè
- 💳 **Payment integration**: Tích hợp payment gateway
- 🤖 **AI chatbot**: Bot trả lời tự động

### **Technical Improvements**
- 🔄 **Redis caching**: Cache cho performance
- 📡 **API endpoints**: REST API cho external access
- 🔐 **Enhanced security**: 2FA cho admin
- 📱 **Mobile app**: App quản lý thành viên
- ☁️ **Cloud deployment**: Deploy lên cloud

---

## 📞 **HỖ TRỢ**

### **Liên Hệ**
- 📧 **Email**: [Your support email]
- 💬 **Telegram**: [Your support channel]
- 📚 **Documentation**: Xem file này
- 🐛 **Bug Reports**: [Your issue tracker]

---

**🎉 HỆ THỐNG QUẢN LÝ THÀNH VIÊN ĐÃ SẴN SÀNG SỬ DỤNG! 👥**
