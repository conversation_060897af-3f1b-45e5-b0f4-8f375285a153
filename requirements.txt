# 🚀 ENHANCED TRADING BOT V5.0 - PRODUCTION DEPENDENCIES
# =====================================================
# Complete dependency list for Advanced AI-powered Trading Bot
# Author: AI Trading Bot Team
# Version: 5.0 - Production Ready

# ============================================================================
# 📊 CORE DATA PROCESSING & ANALYSIS
# ============================================================================
numpy>=1.24.3,<2.0.0
pandas>=2.0.2,<3.0.0
scipy>=1.10.0,<2.0.0
ta-lib>=0.4.25  # Technical Analysis Library

# ============================================================================
# 🧠 MACHINE LEARNING & AI MODELS
# ============================================================================
scikit-learn>=1.2.2,<2.0.0
xgboost>=1.7.0,<2.0.0
lightgbm>=3.3.5,<4.0.0

# Deep Learning (Optional - for advanced AI models)
tensorflow>=2.12.0,<3.0.0; python_version>="3.8"
torch>=2.0.0,<3.0.0; python_version>="3.8"
transformers>=4.20.0,<5.0.0; python_version>="3.8"

# ============================================================================
# 📈 FINANCIAL DATA & TRADING
# ============================================================================
ccxt>=4.0.0,<5.0.0  # Cryptocurrency Exchange Trading Library
yfinance>=0.2.0,<1.0.0  # Yahoo Finance API
python-binance>=1.0.17,<2.0.0  # Binance API

# ============================================================================
# 📊 VISUALIZATION & CHARTING
# ============================================================================
matplotlib>=3.7.1,<4.0.0
mplfinance>=0.12.9b7
plotly>=5.14.0,<6.0.0
seaborn>=0.12.0,<1.0.0
kaleido>=0.2.1  # For plotly static image export

# ============================================================================
# 🌐 NETWORKING & API COMMUNICATION
# ============================================================================
requests>=2.31.0,<3.0.0
aiohttp>=3.8.0,<4.0.0
websockets>=11.0.0,<12.0.0
urllib3>=1.26.0,<3.0.0

# ============================================================================
# 📱 TELEGRAM BOT INTEGRATION
# ============================================================================
python-telegram-bot>=20.0,<21.0
telethon>=1.28.0,<2.0.0  # For advanced Telegram features
pyrogram>=2.0.0,<3.0.0  # Alternative Telegram client

# ============================================================================
# 🔧 CONFIGURATION & ENVIRONMENT
# ============================================================================
python-dotenv>=1.0.0,<2.0.0
pyyaml>=6.0,<7.0
configparser>=5.3.0,<6.0.0
toml>=0.10.2,<1.0.0

# ============================================================================
# 🗄️ DATABASE & STORAGE
# ============================================================================
sqlite3  # Built-in Python module
sqlalchemy>=2.0.0,<3.0.0
redis>=4.5.0,<5.0.0  # For caching and session storage

# ============================================================================
# 🔒 SECURITY & ENCRYPTION
# ============================================================================
cryptography>=40.0.0,<42.0.0
bcrypt>=4.0.0,<5.0.0
pyjwt>=2.6.0,<3.0.0

# ============================================================================
# 🖼️ IMAGE PROCESSING & QR CODES
# ============================================================================
Pillow>=9.5.0,<11.0.0
qrcode>=7.4.0,<8.0.0
opencv-python>=4.7.0,<5.0.0  # For advanced image processing

# ============================================================================
# 📝 LOGGING & MONITORING
# ============================================================================
loguru>=0.7.0,<1.0.0  # Enhanced logging
psutil>=5.9.0,<6.0.0  # System monitoring
prometheus-client>=0.16.0,<1.0.0  # Metrics collection

# ============================================================================
# ⚡ PERFORMANCE & OPTIMIZATION
# ============================================================================
numba>=0.57.0,<1.0.0  # JIT compilation for numerical functions
cython>=0.29.0,<1.0.0  # C extensions for performance
joblib>=1.2.0,<2.0.0  # Parallel processing

# ============================================================================
# 🧪 TESTING & DEVELOPMENT
# ============================================================================
pytest>=7.3.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.0.0,<5.0.0
black>=23.0.0,<24.0.0  # Code formatting
flake8>=6.0.0,<7.0.0  # Code linting

# ============================================================================
# 📊 DATA VALIDATION & SERIALIZATION
# ============================================================================
pydantic>=1.10.0,<3.0.0
marshmallow>=3.19.0,<4.0.0
jsonschema>=4.17.0,<5.0.0

# ============================================================================
# 🌐 WEB FRAMEWORK (For Dashboard)
# ============================================================================
flask>=2.3.0,<3.0.0
flask-cors>=4.0.0,<5.0.0
gunicorn>=20.1.0,<21.0.0  # WSGI server

# ============================================================================
# 📅 DATE & TIME HANDLING
# ============================================================================
python-dateutil>=2.8.0,<3.0.0
pytz>=2023.3
arrow>=1.2.0,<2.0.0

# ============================================================================
# 🔄 ASYNC & CONCURRENCY
# ============================================================================
asyncio  # Built-in Python module
concurrent-futures>=3.1.1; python_version<"3.2"
threading  # Built-in Python module

# ============================================================================
# 📦 PACKAGE MANAGEMENT & UTILITIES
# ============================================================================
packaging>=23.0,<24.0
setuptools>=67.0.0,<69.0.0
wheel>=0.40.0,<1.0.0

# ============================================================================
# 🌍 INTERNATIONALIZATION
# ============================================================================
babel>=2.12.0,<3.0.0  # For multi-language support

# ============================================================================
# 📊 STATISTICAL ANALYSIS
# ============================================================================
statsmodels>=0.14.0,<1.0.0
arch>=5.3.0,<6.0.0  # ARCH/GARCH models for volatility

# ============================================================================
# 🌊 WAVELET ANALYSIS & SIGNAL PROCESSING
# ============================================================================
PyWavelets>=1.4.1,<2.0.0  # Advanced wavelet analysis for Fourier Analyzer
pywt>=1.4.1,<2.0.0  # Alternative PyWavelets import name

# ============================================================================
# 🔍 PATTERN RECOGNITION
# ============================================================================
opencv-contrib-python>=4.7.0,<5.0.0  # Additional OpenCV modules
scikit-image>=0.20.0,<1.0.0

# ============================================================================
# 💾 BACKUP & COMPRESSION
# ============================================================================
zipfile  # Built-in Python module
tarfile  # Built-in Python module
lz4>=4.3.0,<5.0.0  # Fast compression
zstandard>=0.21.0,<1.0.0  # High compression ratio
