#!/usr/bin/env python3
"""
🔧 SIGNAL INTEGRATION FIX TEST
Test that signal integration and trade tracker issues are fixed
"""

import sys
import os
from unittest.mock import Mock

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_integration_fix():
    """Test signal integration fixes."""
    print("🔧 TESTING SIGNAL INTEGRATION FIXES")
    print("=" * 50)
    
    try:
        # Test 1: Import MainBotSignalIntegration
        print("\n🔍 TEST 1: Import MainBotSignalIntegration")
        
        from main_bot_signal_integration import MainBotSignalIntegration
        print("✅ MainBotSignalIntegration imported successfully")
        
        # Test 2: Initialize with mock main bot
        print("\n🔍 TEST 2: Initialize with mock main bot")
        
        mock_main_bot = Mock()
        mock_main_bot.notifier = None
        mock_main_bot.data_fetcher = None
        mock_main_bot.tracker = None
        
        signal_integration = MainBotSignalIntegration(mock_main_bot)
        
        print("✅ MainBotSignalIntegration initialized successfully")
        
        # Test 3: Check signal_manager attribute
        print("\n🔍 TEST 3: Check signal_manager attribute")
        
        has_signal_manager = hasattr(signal_integration, 'signal_manager')
        print(f"   📊 Has signal_manager: {has_signal_manager}")
        
        if has_signal_manager:
            print("✅ signal_manager attribute exists")
        else:
            print("❌ signal_manager attribute missing")
            return False
        
        # Test 4: Check backward compatibility
        print("\n🔍 TEST 4: Check backward compatibility")
        
        has_signal_integration = hasattr(signal_integration, 'signal_integration')
        print(f"   📊 Has signal_integration: {has_signal_integration}")
        
        if has_signal_integration:
            print("✅ signal_integration attribute exists (backward compatibility)")
        else:
            print("❌ signal_integration attribute missing")
            return False
        
        # Test 5: Check if they point to same object
        print("\n🔍 TEST 5: Check object consistency")
        
        if signal_integration.signal_manager == signal_integration.signal_integration:
            print("✅ signal_manager and signal_integration point to same object")
        else:
            print("❌ signal_manager and signal_integration are different objects")
            return False
        
        print("\n" + "=" * 50)
        print("🎯 SIGNAL INTEGRATION FIX TEST SUMMARY")
        print("=" * 50)
        print("✅ All tests passed!")
        print("\n🔧 Fix Verification:")
        print("  ✅ MainBotSignalIntegration imports correctly")
        print("  ✅ signal_manager attribute exists")
        print("  ✅ signal_integration attribute exists (backward compatibility)")
        print("  ✅ Both attributes point to same object")
        print("\n📊 Expected Production Behavior:")
        print("  - No more 'signal_manager' attribute errors")
        print("  - Signal integration works properly")
        print("  - Backward compatibility maintained")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_tracker_availability():
    """Test trade tracker availability."""
    print("\n🔧 TESTING TRADE TRACKER AVAILABILITY")
    print("=" * 50)
    
    try:
        # Test 1: Check CORE_MODULES
        print("\n🔍 TEST 1: Check CORE_MODULES")
        
        # Import main_bot to check CORE_MODULES
        import main_bot
        
        has_trade_tracker = 'trade_tracker' in main_bot.CORE_MODULES
        print(f"   📊 trade_tracker in CORE_MODULES: {has_trade_tracker}")
        
        if has_trade_tracker:
            print("✅ trade_tracker module available in CORE_MODULES")
        else:
            print("❌ trade_tracker module not in CORE_MODULES")
            return False
        
        # Test 2: Check TradeTracker class
        print("\n🔍 TEST 2: Check TradeTracker class")
        
        TradeTracker = main_bot.CORE_MODULES['trade_tracker'].TradeTracker
        print("✅ TradeTracker class accessible")
        
        # Test 3: Test initialization with minimal dependencies
        print("\n🔍 TEST 3: Test initialization with minimal dependencies")
        
        tracker = TradeTracker(
            notifier=None,  # Should work with None
            data_fetcher=None,  # Should work with None
            data_logger=None,  # Should work with None
            max_active_signals=5,
            backup_interval=60
        )
        
        print("✅ TradeTracker initializes with None dependencies")
        print(f"   📊 Notifications enabled: {tracker.notifications_enabled}")
        
        # Test 4: Test core functionality
        print("\n🔍 TEST 4: Test core functionality")
        
        # Test signal addition
        test_signal = {
            'coin': 'BTC/USDT',
            'signal_type': 'BUY',
            'entry': 50000.0,
            'take_profit': 52000.0,
            'stop_loss': 48000.0,
            'confidence': 0.8,
            'analyzer_type': 'test',
            'timestamp': 1234567890
        }
        
        result = tracker.add_signal(test_signal)
        
        if result:
            print("✅ Signal addition works")
            print(f"   📊 Active signals: {len(tracker.active_signals)}")
        else:
            print("❌ Signal addition failed")
        
        print("\n" + "=" * 50)
        print("🎯 TRADE TRACKER AVAILABILITY TEST SUMMARY")
        print("=" * 50)
        print("✅ All tests passed!")
        print("\n🔧 Fix Verification:")
        print("  ✅ trade_tracker module available")
        print("  ✅ TradeTracker class accessible")
        print("  ✅ Initializes with None dependencies")
        print("  ✅ Core functionality works")
        print("\n📊 Expected Production Behavior:")
        print("  - Trade tracker will be initialized")
        print("  - No more 'Trade tracker not available' messages")
        print("  - Signal tracking will work")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 STARTING SIGNAL INTEGRATION FIX VERIFICATION")
    print("=" * 60)
    
    # Test signal integration fix
    integration_success = test_signal_integration_fix()
    
    # Test trade tracker availability
    tracker_success = test_trade_tracker_availability()
    
    overall_success = integration_success and tracker_success
    
    print(f"\n🎯 FINAL RESULTS: {'SUCCESS' if overall_success else 'FAILED'}")
    
    if overall_success:
        print("🎉 SIGNAL INTEGRATION AND TRADE TRACKER FIXES SUCCESSFUL!")
        print("\n✅ Production ready:")
        print("  🔧 No more signal_manager attribute errors")
        print("  📊 Trade Tracker will be initialized")
        print("  🚀 Signal tracking will work properly")
        print("  📱 No more NoneType notification errors")
        print("  ✅ All core functionality preserved")
    else:
        print("❌ Signal integration and trade tracker fixes need attention")
    
    print(f"\n🎯 Final result: {'SUCCESS' if overall_success else 'FAILED'}")
    sys.exit(0 if overall_success else 1)
