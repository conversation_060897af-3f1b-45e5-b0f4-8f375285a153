#!/usr/bin/env python3
"""
Test script to check system reliability multiple times
"""

import sys
import os
import time
import statistics
from typing import List, Dict, Any

def test_system_reliability(num_tests: int = 10) -> Dict[str, Any]:
    """Test system reliability multiple times and calculate average"""
    
    print(f"🧪 Testing system reliability {num_tests} times...")
    print("=" * 60)
    
    health_scores = []
    system_statuses = []
    loading_times = []
    fallback_activations = []
    critical_failures = []
    
    for i in range(num_tests):
        print(f"\n🔄 Test {i+1}/{num_tests}")
        print("-" * 30)
        
        try:
            # Import main_bot to trigger initialization
            if 'main_bot' in sys.modules:
                del sys.modules['main_bot']
            
            start_time = time.time()
            import main_bot
            end_time = time.time()
            
            # Get health score from module loading stats
            health_score = main_bot.MODULE_LOADING_STATS.get("health_score", 0.0)
            loading_time = main_bot.MODULE_LOADING_STATS.get("loading_time", 0.0)
            fallbacks = main_bot.MODULE_LOADING_STATS.get("fallback_activations", 0)
            failures = main_bot.MODULE_LOADING_STATS.get("critical_failures", 0)
            
            # Determine status
            if health_score >= 0.85:
                status = "EXCELLENT"
            elif health_score >= 0.75:
                status = "GOOD"
            elif health_score >= 0.6:
                status = "ACCEPTABLE"
            elif health_score >= 0.4:
                status = "REDUCED"
            else:
                status = "POOR"
            
            health_scores.append(health_score)
            system_statuses.append(status)
            loading_times.append(loading_time)
            fallback_activations.append(fallbacks)
            critical_failures.append(failures)
            
            print(f"  🏥 Health Score: {health_score*100:.1f}%")
            print(f"  🎯 Status: {status}")
            print(f"  ⏱️ Loading Time: {loading_time:.2f}s")
            print(f"  🔄 Fallbacks: {fallbacks}")
            print(f"  🚨 Failures: {failures}")
            
        except Exception as e:
            print(f"  ❌ Test failed: {e}")
            health_scores.append(0.0)
            system_statuses.append("FAILED")
            loading_times.append(0.0)
            fallback_activations.append(0)
            critical_failures.append(1)
    
    # Calculate statistics
    avg_health = statistics.mean(health_scores) if health_scores else 0.0
    min_health = min(health_scores) if health_scores else 0.0
    max_health = max(health_scores) if health_scores else 0.0
    std_health = statistics.stdev(health_scores) if len(health_scores) > 1 else 0.0
    
    avg_loading = statistics.mean(loading_times) if loading_times else 0.0
    total_fallbacks = sum(fallback_activations)
    total_failures = sum(critical_failures)
    
    # Count status distribution
    status_counts = {}
    for status in system_statuses:
        status_counts[status] = status_counts.get(status, 0) + 1
    
    return {
        "num_tests": num_tests,
        "health_scores": health_scores,
        "avg_health": avg_health,
        "min_health": min_health,
        "max_health": max_health,
        "std_health": std_health,
        "avg_loading_time": avg_loading,
        "total_fallbacks": total_fallbacks,
        "total_failures": total_failures,
        "status_distribution": status_counts,
        "reliability_percentage": avg_health * 100
    }

def print_results(results: Dict[str, Any]):
    """Print detailed test results"""
    
    print("\n" + "=" * 60)
    print("🧪 RELIABILITY TEST RESULTS")
    print("=" * 60)
    
    print(f"\n📊 Overall Statistics:")
    print(f"  🧪 Total Tests: {results['num_tests']}")
    print(f"  🏥 Average Health Score: {results['avg_health']*100:.1f}%")
    print(f"  📈 Min Health Score: {results['min_health']*100:.1f}%")
    print(f"  📉 Max Health Score: {results['max_health']*100:.1f}%")
    print(f"  📊 Standard Deviation: {results['std_health']*100:.1f}%")
    print(f"  ⏱️ Average Loading Time: {results['avg_loading_time']:.2f}s")
    print(f"  🔄 Total Fallback Activations: {results['total_fallbacks']}")
    print(f"  🚨 Total Critical Failures: {results['total_failures']}")
    
    print(f"\n🎯 System Status Distribution:")
    for status, count in results['status_distribution'].items():
        percentage = (count / results['num_tests']) * 100
        print(f"  {status}: {count}/{results['num_tests']} ({percentage:.1f}%)")
    
    print(f"\n🎯 Reliability Assessment:")
    reliability = results['reliability_percentage']
    if reliability >= 85:
        assessment = "🟢 EXCELLENT - System is highly reliable"
    elif reliability >= 75:
        assessment = "🟡 GOOD - System meets 75% reliability target"
    elif reliability >= 60:
        assessment = "🟠 ACCEPTABLE - System is moderately reliable"
    elif reliability >= 40:
        assessment = "🟠 REDUCED - System has reduced reliability"
    else:
        assessment = "🔴 POOR - System reliability is concerning"
    
    print(f"  📊 Overall Reliability: {reliability:.1f}%")
    print(f"  🎯 Assessment: {assessment}")
    
    # Target achievement
    if 70 <= reliability <= 80:
        print(f"  ✅ TARGET ACHIEVED: System reliability is in the 75% target range!")
    elif reliability > 80:
        print(f"  ⚠️ ABOVE TARGET: System is more reliable than 75% target")
    else:
        print(f"  ❌ BELOW TARGET: System reliability is below 75% target")

if __name__ == "__main__":
    # Run reliability tests
    num_tests = 5  # Reduced for faster testing
    results = test_system_reliability(num_tests)
    print_results(results)
    
    # Exit with appropriate code
    reliability = results['reliability_percentage']
    if 70 <= reliability <= 80:
        print(f"\n✅ SUCCESS: 75% reliability target achieved!")
        sys.exit(0)
    else:
        print(f"\n⚠️ INFO: Reliability is {reliability:.1f}% (target: 75%)")
        sys.exit(1)
